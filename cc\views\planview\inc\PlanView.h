//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: ESJ1LR Esparza Jose (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  PlanView.h
/// @brief
//=============================================================================

#ifndef CC_VIEWS_PLANVIEW_H_
#define CC_VIEWS_PLANVIEW_H_

#include "pc/svs/core/inc/View.h"

#include <osg/Matrixf>

namespace pc
{
namespace core
{
class Framework;
} // namespace core
} // namespace pc


namespace cc
{

namespace assets
{
namespace impostor
{
// class FixedImpostor;
// class ImpostorDoors;
} // namespace impostor
} // namespace assets


namespace views
{
namespace planview
{

class GbcVehicleData;
extern pc::util::coding::Item<GbcVehicleData> g_gbcData;

//!
//! PlanViewSettings
//!
class PlanViewSettings : public pc::util::coding::ISerializable
{
public:

  PlanViewSettings()
    : m_widthMeters(5.5f)
    , m_widthMetersVeh2dDiff(0.5f)
    , m_widthMetersParkingHori(5.5f)
    , m_widthMetersFullscreen(7.0f)
    , m_widthMetersVeh2dFullscreenDiff(0.5f)
    , m_widthMetersVert(5.5f)
    , m_widthMetersVeh2dDiffVert(0.5f)
    , m_widthMetersVeh2dDiffParkingVert(0.0f)
    , m_widthMetersFullscreenVert(5.5f)
    , m_widthMetersVeh2dFullscreenDiffVert(0.5f)
    , m_widthMetersParkingVert(5.5f)
    , m_widthMetersPipParkTop(5.5f)
    , m_widthMetersPlanetry(7.0f)
  {
  }

  SERIALIZABLE(PlanViewSettings)
  {
    ADD_FLOAT_MEMBER(widthMeters);
    ADD_FLOAT_MEMBER(widthMetersVeh2dDiff);
    ADD_FLOAT_MEMBER(widthMetersParkingHori);
    ADD_FLOAT_MEMBER(widthMetersFullscreen);
    ADD_FLOAT_MEMBER(widthMetersVeh2dFullscreenDiff);
    ADD_FLOAT_MEMBER(widthMetersVert);
    ADD_FLOAT_MEMBER(widthMetersVeh2dDiffVert);
    ADD_FLOAT_MEMBER(widthMetersVeh2dDiffParkingVert);
    ADD_FLOAT_MEMBER(widthMetersFullscreenVert);
    ADD_FLOAT_MEMBER(widthMetersVeh2dFullscreenDiffVert);
    ADD_FLOAT_MEMBER(widthMetersParkingVert);
    ADD_FLOAT_MEMBER(widthMetersBumper);
    ADD_FLOAT_MEMBER(widthMetersPipParkTop);
    ADD_FLOAT_MEMBER(widthMetersPlanetry);
  }

  vfc::float32_t m_widthMeters;
  vfc::float32_t m_widthMetersVeh2dDiff;
  vfc::float32_t m_widthMetersParkingHori;
  vfc::float32_t m_widthMetersFullscreen;
  vfc::float32_t m_widthMetersVeh2dFullscreenDiff;
  vfc::float32_t m_widthMetersVert;
  vfc::float32_t m_widthMetersVeh2dDiffVert;
  vfc::float32_t m_widthMetersVeh2dDiffParkingVert;
  vfc::float32_t m_widthMetersFullscreenVert;
  vfc::float32_t m_widthMetersVeh2dFullscreenDiffVert;
  vfc::float32_t m_widthMetersParkingVert;
  vfc::float32_t m_widthMetersBumper;
  vfc::float32_t m_widthMetersPipParkTop;
  vfc::float32_t m_widthMetersPlanetry;
};

extern pc::util::coding::Item<cc::views::planview::PlanViewSettings> g_planView;

//======================================================
// PlanView
//------------------------------------------------------
/// Top view of the vehicle.
/// 2D vehicle and overlays to be added in this View.
/// Contains PlanViewUpdateCallback to control nodes visibility
/// <AUTHOR> Milica
//======================================================
class PlanView: public pc::core::View
{
public:

    PlanView(   const std::string& f_name,
                const pc::core::Viewport& f_viewport,
                const pc::virtcam::VirtualCamera& f_camPos,
                pc::core::Framework *f_pFramework);

    virtual void traverse(osg::NodeVisitor& f_nv);

    // void setImpostor(assets::impostor::FixedImpostor *f_pImpostor);
    // void setImpostorDoors(pc::core::View  *f_pImpostorDoors);
    // void setWheels(osg::Group *f_pWheels);

protected:

  virtual ~PlanView();
  void initialize();

private:

  //! Copy constructor is not permitted.
  PlanView (const PlanView& other); // = delete
  //! Copy assignment operator is not permitted.
  PlanView& operator=(const PlanView& other); // = delete

  bool m_initialized;
  pc::core::Framework *m_pFramework;
  // assets::impostor::FixedImpostor *m_pImpostor;
  // pc::core::View *m_pImpostorDoors;
  // osg::Group *m_pWheels;
};


//======================================================
// PlanViewUpdateCallback
//------------------------------------------------------
/// controls Toggle the impostor nodes visibility via cullMask
///
/// <AUTHOR> Milica
//======================================================
class PlanViewUpdateCallback: public osg::NodeCallback
{

typedef enum
{
  EOpaque = 0,                  // Default value
  ETransparent,
  ENoSpeed
}
ETransparentVehicleState;

public:
  // PlanViewUpdateCallback( osg::Uniform* f_pAlphaUniform,
  //                           osg::Uniform* f_pWheelsAlphaUniform,
  //                           assets::impostor::FixedImpostor *f_pImpostor,
  //                           pc::core::View  *f_pImpostorDoors,
  //                           pc::core::Framework* f_pFramework);

  PlanViewUpdateCallback( pc::core::Framework* f_pFramework);

  virtual void operator()(osg::Node* f_node, osg::NodeVisitor* f_nv) override;

protected:
  virtual ~PlanViewUpdateCallback();

  void checkOdometry();
  void checkZeroSpeed();
  bool checkNonZeroSpeed();
  void checkForTimeout();

  bool allDoorsClosed();
  void initialize(osg::Node* f_node);

private:
  //! Copy constructor is not permitted.
  PlanViewUpdateCallback (const PlanViewUpdateCallback& other); // = delete
  //! Copy assignment operator is not permitted.
  PlanViewUpdateCallback& operator=(const PlanViewUpdateCallback& other); // = delete

  osg::Uniform* m_pAlphaUniform;
  // osg::Uniform* m_pWheelsAlphaUniform;
  // assets::impostor::FixedImpostor *m_pImpostor;
  // pc::core::View  *m_pImpostorDoors;

private:
  pc::core::Framework* m_pFramework;
  osg::Uniform* m_pBrightnessUniform;
  bool m_initialized;
  ETransparentVehicleState m_vehicleState;
  vfc::float32_t m_initial_xpos;
  vfc::float32_t m_initial_ypos;
  bool m_odo_initialized;
  vfc::float64_t m_startOfZeroSpeed;
  vfc::float64_t m_currentSimulationTime;
  vfc::float32_t m_currentAlphaValue;
  bool m_alphaAnimationOngoing;
  vfc::float64_t m_startOfOpaqueToTransparent;
  vfc::float64_t m_startOfTransparentToOpaque;
};


//!
//! PlanViewCullCallback
//!
class PlanViewCullCallback: public osg::NodeCallback
{
public:
  PlanViewCullCallback();

  virtual void operator()(osg::Node* f_node, osg::NodeVisitor* f_nv);

  bool getRefMatrix(osg::Matrixf& f_mat) const;

protected:
  virtual ~PlanViewCullCallback();

private:
  //! Copy constructor is not permitted.
  PlanViewCullCallback (const PlanViewCullCallback& other); // = delete
  //! Copy assignment operator is not permitted.
  PlanViewCullCallback& operator=(const PlanViewCullCallback& other); // = delete

  osg::Matrixf          m_ViewRefMat;

  bool                  m_matAvail_b;
};

//!
//! PlanViewProjectionCallback
//!
class PlanViewProjectionCallback : public osg::NodeCallback
{
public:
  PlanViewProjectionCallback() = default;

  void operator()(osg::Node* f_node, osg::NodeVisitor* f_nv) override;
};

} //namespace planview
} //namespace assets
} //namespace cc

#endif /* CC_VIEWS_DAYNIGHT_H_ */
