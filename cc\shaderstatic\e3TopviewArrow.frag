//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>sch GmbH. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

precision highp float;

varying vec2 v_texCoord;
varying vec4 v_color;
uniform sampler2D u_tex0;
uniform float alpha;

void main()
{
  vec4 l_color = texture2D(u_tex0, v_texCoord);
  l_color *= v_color;
  gl_FragColor = vec4(l_color*max(0.4, alpha));
}
