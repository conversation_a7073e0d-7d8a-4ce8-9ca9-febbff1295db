/// @copyright (C) 2023 Robert <PERSON> GmbH.
/// The reproduction, distribution and utilization of this file as well as the
/// communication of its contents to others without express authorization is
/// prohibited. Offenders will be held liable for the payment of damages. All
/// rights reserved in the event of the grant of a patent, utility model or
/// design.
/// @file

#ifndef RBP_VIS_IMP_CHAMAELEONBASESIGNALS_H
#define RBP_VIS_IMP_CHAMAELEONBASESIGNALS_H

#include "pc/svs/daddy/inc/BaseDaddyTypes.h"
#include "pc/svs/imp/chamaeleon/inc/chamaeleon_data.hpp"
#include "pc/svs/imp/chamaeleon/inc/chamaeleon_tabs.hpp"

namespace rbp
{
namespace vis
{
namespace imp
{
namespace chamaeleon
{

///
/// ChamaeleonBaseSignals
///
class ChamaeleonBaseSignals
{
public:
    enum class EDegradationState : vfc::int32_t
    {
        NOMINAL,
        DEGRADED,
        NUMBER_OF_DEGRADATION_STATES
    };

    ChamaeleonBaseSignals() = default;

    ~ChamaeleonBaseSignals() = default;

    void setParameters(const ChamaeleonBaseSignalsData& f_params = ChamaeleonBaseSignalsData{});

    void setDoorState(const pc::daddy::DoorStateDaddy* const f_doorState);

    void setMirrorState(const pc::daddy::MirrorStateDaddy* const f_mirrorState);

    void setDegradationState(const pc::daddy::CameraDegradationMaskDaddy* const f_degradationState);

    void setCalibState(const pc::daddy::CalibrationStsDaddy* f_calibState);

    bool isWorldROIvalid(ETabWorldOverlapROI f_worldOverlapRoi) const;

    osg::Vec4f getWorldROIValidityMask() const;

    pc::daddy::ECarDoorState getCarDoorState(ETabWorldOverlapROI f_worldOverlapRoi) const;

    pc::daddy::EMirrorState getMirrorState(ETabWorldOverlapROI f_worldOverlapRoi) const;

    EDegradationState getDegradationState(ETabWorldOverlapROI f_worldOverlapRoi) const;

    bool getCalibState(ETabWorldOverlapROI f_worldOverlapRoi) const;    

    bool getCalibState() const;

private:
    ChamaeleonBaseSignals(const ChamaeleonBaseSignals&)            = delete;
    ChamaeleonBaseSignals& operator=(const ChamaeleonBaseSignals&) = delete;

    struct CBaseSignalsRoiStatus
    {
        pc::daddy::ECarDoorState m_doorState{pc::daddy::ECarDoorState::CARDOORSTATE_CLOSED};
        pc::daddy::EMirrorState  m_mirrorState{pc::daddy::EMirrorState::MIRRORSTATE_NOT_FLAPPED};
        EDegradationState        m_degradationState{EDegradationState::NOMINAL};
        bool                     m_calibState{true};
    };

    TArrayWorldOverlap<CBaseSignalsRoiStatus> m_roiStatus{};
    bool m_ignoreCarDoorState{false};
    bool m_ignoreMirrorState{false};
    bool m_ignoreDegradationState{false};
    bool m_ignoreCalibState{true};
    bool m_calibState{true};
};

} // namespace chamaeleon
} // namespace imp
} // namespace vis
} // namespace rbp

#endif // RBP_VIS_IMP_CHAMAELEONBASESIGNALS_H
