//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON> GmbH. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#ifndef CC_FACTORY_CUSTOMRENDERMANAGER_H
#define CC_FACTORY_CUSTOMRENDERMANAGER_H

#include "pc/svs/factory/inc/RenderManager.h"

namespace cc
{
namespace factory
{

class CustomRenderManager : public pc::factory::RenderManager
{
public:
  CustomRenderManager(
    pc::factory::RenderManagerRegistry* f_registry,
    cc::virtcam::VirtualCamEnum f_virtCam = cc::virtcam::NUMBER_OF_VIRT_CAMS,
    rbp::vis::imp::chamaeleon::EChamaeleonView f_settingChamaeleon = rbp::vis::imp::chamaeleon::EChamaeleonView::NO_CHAMAELEON,
    rbp::vis::imp::sh::ESharpnessView f_settingSharpnessHarmonization = rbp::vis::imp::sh::ESharpnessView::FIXED,
    rbp::vis::imp::tnf::ETnfView f_settingTemporalNoiseFilter = rbp::vis::imp::tnf::ETnfView::DEFAULT)
  : RenderManager(f_registry, f_virtCam, f_settingChamaeleon, f_settingSharpnessHarmonization, f_settingTemporalNoiseFilter)
  {
  }

  void update(osg::NodeVisitor* f_nv) override;

protected:
  ~CustomRenderManager() = default;
};

} // namespace factory
} // namespace cc

#endif // CC_FACTORY_CUSTOMRENDERMANAGER_H