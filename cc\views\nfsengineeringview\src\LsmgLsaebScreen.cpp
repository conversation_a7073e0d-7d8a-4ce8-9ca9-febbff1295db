//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: MADCSZH MAO Donny (CC-DA/EAV2-CN)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  LsmgLsaebScreen.cpp
/// @brief
//=============================================================================

#include "pc/svs/core/inc/Framework.h"
#include "pc/svs/core/inc/SystemConf.h"
#include "pc/svs/core/inc/ShaderManager.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"
#include "pc/svs/util/math/inc/FloatComp.h"
#include "pc/svs/util/math/inc/CommonMath.h"

#include "cc/views/daynightview/inc/DayNightView.h"
#include "cc/views/nfsengineeringview/inc/LsmgLsaebScreen.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"

#include "osg/LineWidth"
#include "osg/Geometry"

#include <iomanip>
#include <iostream>
#include <sstream>

namespace cc
{
namespace views
{
namespace nfsengineeringview
{

constexpr vfc::float32_t g_lineWidth    = 3.0f;            // to define the width line dividing LSMG and LSAEB
constexpr vfc::float32_t g_lineWidthSec = 2.0f;            // to define the width of lines dividing the LSMG sectors
constexpr vfc::float32_t g_lineLength   = 1.5f;            // to define the length of lines dividing the LSMG sectors (unit: meter)

EngSectorData::EngSectorData()
  : m_leftBorderRefPoint   (0.0f, 0.0f)
  , m_leftBorderRefPointEnd(0.0f, 0.0f)
  , m_leftBorderDir        (0.0f, 0.0f)
  , m_currentDistance      (0.0f)
{
}

// EngSectorData::~EngSectorData() = default;


//!
//! LsmgLsaebScreen
//!
LsmgLsaebScreen::LsmgLsaebScreen(cc::views::planview::PlanViewCullCallback* f_cullcallback)
  : pc::views::engineeringview::EngineeringScreen("LSMG LSAEB screen") // PRQA S 4050
  , m_counterCurrentDaddy(0u)
  , m_counterCurrentAeb(0u)
  , m_counterCurrentDist(0u)
  , m_engZoneLayout(new cc::core::CustomZoneLayout)
  , m_engSectors{}
  , m_screenSectors{}
  , m_pTextBoxDist{}
  , m_planViewCullCall(f_cullcallback)
  , m_sectorDrawn_b(false)
  , m_ussZoneNum(static_cast<vfc::uint32_t>(cc::target::sysconf::E_ULTRASONIC_NUM_ZONES))
{
  osg::StateSet* const l_stateSet = getOrCreateStateSet();
  l_stateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF); //PRQA S 3143
  pc::core::TextureShaderProgramDescriptor l_basicTexShader("colorText");
  l_basicTexShader.apply(l_stateSet);  // PRQA S 3803

  const vfc::float32_t l_planViewWidth  = static_cast<vfc::float32_t>(cc::core::g_views->m_planViewport.m_size.x());
  const vfc::float32_t l_wholeHeight    = static_cast<vfc::float32_t>(pc::core::g_systemConf->m_mainViewport.m_size.y());

  vfc::float32_t l_startXMG  = l_planViewWidth;
  vfc::float32_t l_startXAEB = l_planViewWidth * 2.0f;
  const osg::Vec4f l_colorText(1.0f, 1.0f, 1.0f, 1.0f);

  // fit the left hand dirve of right hand drive logic
  const bool lhd = pc::vehicle::g_mechanicalData->m_leftHandDrive;
  if (!lhd)
  {
    l_startXMG  = 0.0f;
    l_startXAEB = l_planViewWidth;
  }

  // Textbox ID 0~31: the obstacle sector distances
  m_pTextBoxDist.clear();
  for (vfc::uint32_t i = 0u; i < m_ussZoneNum; ++i)
  {
    const osg::Vec3f l_pos(0.0f, l_wholeHeight, 0.0f);                                  // default position, need to be updated with setSectorDistTextPos()
    m_pTextBoxDist.push_back(new pc::views::engineeringview::KeyValueTextBox(l_pos, l_colorText, 12.0f, 0u));
    addTextBox(m_pTextBoxDist[i]);
    m_pTextBoxDist[i]->getOrCreateEntry("");  // PRQA S 3803
  }

  // get the sector positions and directions in vehicle coorinate
  defineEngViewSectorData();

  const osg::Vec3f l_posLgmg(l_startXMG + 20.0f, l_wholeHeight - 60.0f, 0.0f);
  const osg::Vec3f l_posLgaeb(l_startXAEB + 20.0f, l_wholeHeight - 60.0f, 0.0f);
  const osg::Vec3f l_posSignal(l_startXAEB + 20.0f, l_wholeHeight - 150.0f, 0.0f);

  // Textbox ID 32~33: the view names (LSMS & LSAEB)
  pc::views::engineeringview::KeyValueTextBox * const l_pTextBoxLgmg
    = new pc::views::engineeringview::KeyValueTextBox(l_posLgmg, l_colorText, 30.0f, 24u);
  addTextBox(l_pTextBoxLgmg);
  l_pTextBoxLgmg->getOrCreateEntry("LSMG")->setValue("");

  pc::views::engineeringview::KeyValueTextBox * const l_pTextBoxLgaeb
    = new pc::views::engineeringview::KeyValueTextBox(l_posLgaeb, l_colorText, 30.0f, 24u);
  addTextBox(l_pTextBoxLgaeb);
  l_pTextBoxLgaeb->getOrCreateEntry("LSAEB")->setValue("");

  // Textbox ID 34: the LSAEB signals
  pc::views::engineeringview::KeyValueTextBox * const l_pTextBoxSignals
    = new pc::views::engineeringview::KeyValueTextBox(l_posSignal, l_colorText, 18.0f, 2u);
  addTextBox(l_pTextBoxSignals);
  l_pTextBoxSignals->getOrCreateEntry("AEB OPMODE = ")->setValue("");
  l_pTextBoxSignals->getOrCreateEntry("  ")->setValue("N/A");                    // use another empty line to show string to fit the screen
  l_pTextBoxSignals->getOrCreateEntry(" ")->setValue("");                        // insert a blank line
  l_pTextBoxSignals->getOrCreateEntry("Dist2Collision = ")->setValue("N/A");
}

LsmgLsaebScreen::~LsmgLsaebScreen() = default;

void LsmgLsaebScreen::defineEngViewSectorData()
{
  // get the left borader positions and directions from UltrasonicZoneLayout
  // the positions in m_engSectors is from vehicle coordinate in reality
  m_engSectors = std::vector<EngSectorData>(m_ussZoneNum);

  for (vfc::uint32_t i = 0u; i < m_engSectors.size(); i++)
  {
    m_engSectors[i].m_leftBorderRefPoint    = m_engZoneLayout->getLeftBorderLine(i).m_innerPoint;
    m_engSectors[i].m_leftBorderRefPointEnd = m_engZoneLayout->getLeftBorderLine(i).m_outerPoint;
    m_engSectors[i].m_leftBorderDir         = m_engZoneLayout->getLeftBorderLine(i).m_direction;
  }

}

void LsmgLsaebScreen::drawEngViewSectors(const osg::Matrixf& f_mat)
{
  // define and draw the lines based on plan viewport positions
  if (!m_sectorDrawn_b)
  {
    m_screenSectors = std::vector<EngSectorData>(m_ussZoneNum);
    pc::views::engineeringview::Line* L = nullptr;

    for (vfc::uint32_t i = 0u; i < m_screenSectors.size(); i++)
    {
      const osg::Vec3f l_temp0(m_engSectors[i].m_leftBorderRefPoint, 0.0f);
      const osg::Vec3f l_temp1(m_engSectors[i].m_leftBorderDir * g_lineLength + m_engSectors[i].m_leftBorderRefPoint, 0.0f);

      // tansform the positions from vehicle coordinate to plan view scene
      osg::Vec3f l_screenCoord0 = l_temp0 * f_mat;
      osg::Vec3f l_screenCoord1 = l_temp1 * f_mat;
      m_screenSectors[i].m_leftBorderRefPoint.x() = l_screenCoord0.x();
      m_screenSectors[i].m_leftBorderRefPoint.y() = l_screenCoord0.y();
      m_screenSectors[i].m_leftBorderRefPointEnd.x() = l_screenCoord1.x();
      m_screenSectors[i].m_leftBorderRefPointEnd.y() = l_screenCoord1.y();
      L = new pc::views::engineeringview::Line(
        osg::Vec3f(m_screenSectors[i].m_leftBorderRefPoint.x(), m_screenSectors[i].m_leftBorderRefPoint.y(), 0),
        osg::Vec3f(m_screenSectors[i].m_leftBorderRefPointEnd.x(), m_screenSectors[i].m_leftBorderRefPointEnd.y(), 0),
        osg::Vec4f(0.0f, 0.0f, 0.0f, 1.0f),
        g_lineWidthSec);
      addLine(L);
    }

    setSectorDistTextPos();
    m_sectorDrawn_b = true;
  }

}

void LsmgLsaebScreen::setSectorDistTextPos()
{
  // set the distance textbox position
  for (vfc::uint32_t iC = 0u; iC < m_pTextBoxDist.size(); ++iC)
  {
    vfc::float32_t l_startDistX = static_cast<vfc::float32_t>(cc::core::g_views->m_planViewport.m_size.x());  // PRQA S 3803
    vfc::float32_t l_startDistY = static_cast<vfc::float32_t>(pc::core::g_systemConf->m_mainViewport.m_size.y());  // PRQA S 3803

    //  set the right boarder number of iC
    vfc::uint32_t iC_right = 0u;
    if (0u != iC)
    {
      iC_right = iC - 1u;
    }
    else
    {
      iC_right = static_cast<vfc::uint32_t>(m_pTextBoxDist.size() - 1u);
    }

    l_startDistX = (m_screenSectors[iC].m_leftBorderRefPoint.x()
                  + m_screenSectors[iC].m_leftBorderRefPointEnd.x()
                  + m_screenSectors[iC_right].m_leftBorderRefPoint.x()
                  + m_screenSectors[iC_right].m_leftBorderRefPointEnd.x())
                  / 4.0f - 12.0f;
    l_startDistY = (m_screenSectors[iC].m_leftBorderRefPoint.y()
                  + m_screenSectors[iC].m_leftBorderRefPointEnd.y()
                  + m_screenSectors[iC_right].m_leftBorderRefPoint.y()
                  + m_screenSectors[iC_right].m_leftBorderRefPointEnd.y())
                  / 4.0f;

    const osg::Vec3f l_posDist(l_startDistX, l_startDistY, 0.0f);
    m_pTextBoxDist[iC]->setTextBoxStartPos(l_posDist);

  }
}

std::string LsmgLsaebScreen::getAebOpModeString(cc::daddy::EAebVmcOpMode f_aebMode)
{
  // map the m_aebVmcOpMode value to corresponding strings
  std::string l_retString;
  switch (f_aebMode)
  {
    case cc::daddy::AEB_MGR_OPMODE_OFF:
    {
      l_retString = "AEB is currently not available";
      break;
    }
    case cc::daddy::AEB_MGR_OPMODE_INACTIVE:
    {
      l_retString = "AEB is available but no active";
      break;
    }
    case cc::daddy::AEB_MGR_OPMODE_PREFILL:
    {
      l_retString = "Prefill is active";
      break;
    }
    case cc::daddy::AEB_MGR_OPMODE_AEB:
    {
      l_retString = "Emergency brake is active";
      break;
    }
    case cc::daddy::AEB_MGR_OPMODE_DRIVEROVERRIDE:
    {
      l_retString = "Emergency brake is finished with driver override";
      break;
    }
    case cc::daddy::AEB_MGR_OPMODE_FINISH:
    {
      l_retString = "Emergency brake is finished without driver override";
      break;
    }
    case cc::daddy::AEB_MGR_OPMODE_ERROR:
    {
      l_retString = "AEB is in error mode";
      break;
    }
    default:
    {
      l_retString = "N/A";
      break;
    }
  }

  return l_retString;
}

bool LsmgLsaebScreen::update(pc::core::Framework* f_framework)
{
  if (nullptr == f_framework)
  {
    return false;
  }
  // get the transform matrix from plan viw Cullcallback
  osg::Matrixf l_mat;
  const bool l_ret_b = m_planViewCullCall->getRefMatrix(l_mat);
  // transform positions from vehicle to plan viewport positions
  if (l_ret_b)
  {
     drawEngViewSectors(l_mat);
  }

  bool l_exchange_b = false;

  if (true == f_framework->asCustomFramework()->m_customUltrasonicDataReceiver.isConnected())
  {
    const pc::daddy::UltrasonicDataDaddy* const l_pDataDaddy = f_framework->asCustomFramework()->m_customUltrasonicDataReceiver.getData();

    if ((nullptr != l_pDataDaddy) && (l_pDataDaddy->m_sequenceNumber != m_counterCurrentDaddy))
    {
      m_counterCurrentDaddy = l_pDataDaddy->m_sequenceNumber;
      const pc::vehicle::UltrasonicData& l_ultrasonicData = l_pDataDaddy->m_Data;

      for (vfc::uint32_t i = 0u; i < m_engSectors.size(); i++)
      {
        if (isLess(l_ultrasonicData[i].getDistance(), std::numeric_limits<vfc::float32_t>::max()))
        {
          m_engSectors[i].m_currentDistance = l_ultrasonicData[i].getDistance();
          std::stringstream sTempDist;
          // set the unit meters into mm
          sTempDist << std::fixed << std::setprecision(0) << m_engSectors[i].m_currentDistance * 1000.0f;  // PRQA S 3803
          const std::string sDist = sTempDist.str();
          m_pTextBoxDist[i]->getOrCreateEntry("")->setValue(sDist);
        }
        else
        {
          // set the default text
          m_pTextBoxDist[i]->getOrCreateEntry("")->setValue("-");
        }
      }

      l_exchange_b = true;
    }
  }

  pc::views::engineeringview::TextBox *const l_pTextBox = getTextBox(34u);

  if (true == f_framework->asCustomFramework()->m_aebVmcOpMode_ReceiverPort.isConnected())
  {
    const cc::daddy::AebVmcOpModeDaddy_t* const l_pAebDaddy = f_framework->asCustomFramework()->m_aebVmcOpMode_ReceiverPort.getData();

    if ((nullptr != l_pAebDaddy) && (l_pAebDaddy->m_sequenceNumber != m_counterCurrentAeb))
    {
      m_counterCurrentAeb = l_pAebDaddy->m_sequenceNumber;
      const cc::daddy::EAebVmcOpMode& l_aebVmcOpMode = l_pAebDaddy->m_Data;

      // set the string, "  " is the line below the "AEB OPMODE = " line
      pc::views::engineeringview::TextBox::Entry *const l_pTextBoxAebMode = l_pTextBox->getOrCreateEntry("  ");
      const std::string l_sAebMode = getAebOpModeString(l_aebVmcOpMode);
      l_pTextBoxAebMode->setValue(l_sAebMode);

      l_exchange_b = true;
    }
  }

  if (true == f_framework->asCustomFramework()->m_distanceToStop_ReceiverPort.isConnected())
  {
    const cc::daddy::DistanceToStopDaddy* const l_pDist2Stop = f_framework->asCustomFramework()->m_distanceToStop_ReceiverPort.getData();

    if ((nullptr != l_pDist2Stop) && (l_pDist2Stop->m_sequenceNumber != m_counterCurrentDist))
    {
      m_counterCurrentDist = l_pDist2Stop->m_sequenceNumber;
      const vfc::float32_t l_dist2Stop = l_pDist2Stop->m_Data;

      pc::views::engineeringview::TextBox::Entry *const l_pTextBoxDist2Stop = l_pTextBox->getOrCreateEntry("Dist2Collision = ");
      l_pTextBoxDist2Stop->setValue(l_dist2Stop);

      l_exchange_b = true;
    }
  }

  return l_exchange_b;
}


} // namespace nfsengineeringview
} // namespace views
} // namespace cc

