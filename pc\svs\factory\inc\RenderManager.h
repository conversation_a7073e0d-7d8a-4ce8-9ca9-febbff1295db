//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON> GmbH. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#ifndef FACTORY_MANAGER_H
#define FACTORY_MANAGER_H


#include "pc/svs/core/inc/SystemConf.h"
#include "pc/svs/daddy/inc/BaseDaddyTypes.h"
#include "pc/svs/factory/inc/CameraDependentShaderConfigurator.h"
#include "pc/svs/factory/inc/CameraImageShaders.h"
#include "pc/svs/factory/inc/ShaderSelectionLogic.h"
#include "pc/svs/factory/inc/StitchingLinesManager.h"
#include "pc/svs/factory/inc/SV3DNode.h"
#include "pc/svs/factory/inc/SV3DStateGraph.h"
#include "pc/svs/util/common/inc/StaticArray.h"
#include "pc/svs/imp/chamaeleon/inc/chamaeleon_manager.hpp"
#include "pc/svs/imp/sh/inc/sharpness_harmonization_manager.hpp"
#include "pc/svs/imp/tnf/inc/temporal_noise_filter_manager.hpp"


#include "cc/virtcam/inc/CameraPositions.h"

#include <osg/NodeCallback>
#include <array>

namespace pc
{
namespace core
{
class Framework;
} // namespace core
namespace factory
{
class RenderManagerRegistry;
class FadeFactorUniformUpdateCallback;

// typedef vfc::TFixedVector<core::sysconf::Cameras, core::sysconf::E_SYSTEM_CONFIG_NUMBER_OF_CAMERAS> CamerasContainer;
typedef pc::util::StaticArray< pc::core::sysconf::Cameras, pc::core::sysconf::E_SYSTEM_CONFIG_NUMBER_OF_CAMERAS > CamerasContainer;


static const pc::core::sysconf::Cameras TheCamerasOrdered [4] = {
  pc::core::sysconf::FRONT_CAMERA,
  pc::core::sysconf::RIGHT_CAMERA,
  pc::core::sysconf::REAR_CAMERA,
  pc::core::sysconf::LEFT_CAMERA
};


//!
//! RenderManagerSettings
//!
class RenderManagerSettings : public util::coding::ISerializable
{
public:

  RenderManagerSettings()
    : m_depthWriteFloor(false)
    , m_depthWriteWall(true)
    , m_animationDuration(0.5f)
    , m_camDisabledColor(0.6f, 0.6f, 0.6f, 1.0f)
    , m_camOffColor(0.0f, 0.0f, 0.0f, 1.0f)
    , m_shaderPath("pc/svs/factory/ConfigurableShaders/")
  {
  }

  SERIALIZABLE(RenderManagerSettings)
  {
    ADD_BOOL_MEMBER(depthWriteFloor);
    ADD_BOOL_MEMBER(depthWriteWall);
    ADD_FLOAT_MEMBER(animationDuration);
    ADD_MEMBER(osg::Vec4f, camDisabledColor);
    ADD_MEMBER(osg::Vec4f, camOffColor);
    ADD_STRING_MEMBER(shaderPath);
  }

  bool m_depthWriteFloor;
  bool m_depthWriteWall;
  float m_animationDuration;
  osg::Vec4f m_camDisabledColor;
  osg::Vec4f m_camOffColor;
  std::string m_shaderPath;

};

extern util::coding::Item<RenderManagerSettings> g_renderManagerSettings;


//!
//! RenderManager
//!
class RenderManager : public osg::Referenced
{
public:

  enum ShadingStyle
  {
    SHADING_STYLE_FLOOR = 0,
    SHADING_STYLE_WALL = 1
  };

  RenderManager(
    RenderManagerRegistry* f_registry,
    unsigned int f_virtCamId,
    rbp::vis::imp::chamaeleon::EChamaeleonView f_settingChamaeleon = rbp::vis::imp::chamaeleon::EChamaeleonView::NO_CHAMAELEON,
    rbp::vis::imp::sh::ESharpnessView f_settingSharpnessHarmonization = rbp::vis::imp::sh::ESharpnessView::FIXED,
    rbp::vis::imp::tnf::ETnfView f_settingTemporalNoiseFilter = rbp::vis::imp::tnf::ETnfView::DEFAULT);

  RenderManager(
    RenderManagerRegistry* f_registry,
    const pc::virtcam::VirtualCamera*   f_virtCam,
    rbp::vis::imp::chamaeleon::EChamaeleonView f_settingChamaeleon = rbp::vis::imp::chamaeleon::EChamaeleonView::NO_CHAMAELEON,
    rbp::vis::imp::sh::ESharpnessView f_settingSharpnessHarmonization = rbp::vis::imp::sh::ESharpnessView::FIXED,
    rbp::vis::imp::tnf::ETnfView f_settingTemporalNoiseFilter = rbp::vis::imp::tnf::ETnfView::DEFAULT);

  /**
   * Specifies whether to enable depth writes for all shaders managed by this RenderManager.
   */
  void enableDepthWrite(ShadingStyle f_shadingStyle, bool f_enable);
  bool isDepthWriteEnabled(ShadingStyle f_shadingStyle) const;

  /**
   * Specifies whether to apply the model matrix "u_dynamicWallModelMatrix" (which transforms from model to DIN70k) for stitching line calculations; if the model matrix is disabled, all vertex coordinates are assumed to be in DIN70k already.
   * This is a global property for all shaders managed by this RenderManager
   */
  void enableDynamicWallModelMatrix(bool f_enable);
  bool isDynamicWallModelMatrixEnabled() const;

  /**
   * Specifies whether to use mixfactors to modulate stitching-line based blending.
   * This is a global property for all shaders managed by this RenderManager
   */
  void enableBorderBlending(bool f_enable);
  bool isBorderBlendingEnabled() const;

  //!
  //! @brief Retrieve the RenderManger mapped to the given osg::Camera
  //!
  //! @param f_camera
  //! @return RenderManager*
  //!
  static RenderManager* get(const osg::Camera* f_camera);

  //!
  //! @brief Adds a mapping of the given osg::Camera to this RenderManager instance
  //!
  //! @param f_camera
  //!
  void assignCamera(const osg::Camera* f_camera);

  SV3DStateGraph& getStateGraph(unsigned int f_shadingStyle);
  const SV3DStateGraph& getStateGraph(unsigned int f_shadingStyle) const;

  //!
  //! @brief Query the degradation mask
  //!
  //! @param f_target if true the target mask will be returned else the current mask
  //! @return the degradation mask based on the input parameter f_target
  //!
  unsigned int getDegradationMask(bool f_target = true) const;

  //!
  //! @brief Query the deactivation mask
  //!
  //! @param f_target if true the target mask will be returned else the current mask
  //! @return the deactivation mask based in the input parameter f_target
  //!
  unsigned int getDeactivationMask(bool f_target = true) const;

  //! helper utility that generates a uniform called "u_FadeFactor" connected with an update callback for animating u_FadeFactor
  osg::Uniform* getOrCreateFadeFactorUniform(
    pc::core::sysconf::Cameras f_TheCamera,
    pc::core::sysconf::CameraArea f_CameraArea);

  FadeFactorUniformUpdateCallback* getFadeFactorUpdateCallback(pc::core::sysconf::CameraArea f_CameraArea) const;

  //! called back by the Uniform callback, if the animation has finished
  void animationEnded(pc::core::sysconf::Cameras f_Camera);

  //! getter function for the members
  StitchingLinesManager* getStitchMng();

  ShaderSelectionLogic* getShaderLogic();

  CameraDependentShaderConfigurator* getShaderConfigurator();

  //!
  //! @brief Updates the RenderManager's internals state.
  //! This method is intended to be called by the @ref pc.factory.RenderManagerRegistry
  //! which will forward the osg::UpdateVisitor to the registered RenderManager instances
  //!
  //! @param f_nv the UpdateVisitor
  //!
  virtual void update(osg::NodeVisitor* f_nv);

  virtual SV3DStateGraph& getWallStateGraph() {return m_stateGraphWall;}
  virtual SV3DStateGraph& getFloorStateGraph() {return m_stateGraphFloor;}
  virtual const SV3DStateGraph& getWallStateGraph() const {return m_stateGraphWall;}
  virtual const SV3DStateGraph& getFloorStateGraph() const {return m_stateGraphFloor;}

  // member to handle the stitching line depending on its internal state
  StitchingLinesManager m_stitchLineMng;
  // members to handle the generation of the shader
  ShaderSelectionLogic m_shaderSelectionLogic;
  // to be reviewed who is doing what in this code, ShaderSelectionLogic vs CameraDependentShaderConfigurator
  // but first put it in the right class
  CameraDependentShaderConfigurator m_cameraDependentShaderConfigurator;

protected:

  virtual ~RenderManager() = default;

  virtual void updateInputData(const osg::FrameStamp* f_frameStamp);

  virtual void setupStateGraph(SV3DStateGraph& f_stateGraph);

  void setUniformTwoCamerasForAllDrawables(int f_StateNumber, osg::StateSet * f_pTheState);

  void onCameraOnOffEvent(unsigned int f_OldActivationMask, unsigned int f_NewActivationMask);

  bool isAnimationRunningForCameraAreaOrNeighbours(unsigned int f_cameraId) const;

  void startAnimationOnUniformCallbacks(
    pc::core::sysconf::CameraArea f_LeftArea,
    pc::core::sysconf::CameraArea f_MiddleArea,
    pc::core::sysconf::CameraArea f_RightArea,
    bool f_Ascending);

  bool isAnimationRunningOnAffectedCameraOrNeighbours(
     unsigned int f_OldBitMask,
     unsigned int f_NewBitMask) const;

  void startAnimationForCamera(pc::core::sysconf::Cameras f_TheCamera);

  void followCurrentCamState();

  typedef std::array< osg::ref_ptr<osg::Uniform>, pc::core::sysconf::ALL_CAMERAS_AREA > CameraAreaUniformArray;

  RenderManagerRegistry* m_registry;
  cc::virtcam::VirtualCamEnum m_virtCam;

  bool m_initialized;


  unsigned int m_degradationMaskCurrent;
  unsigned int m_degradationMaskTarget;
  unsigned int m_deactivationMaskCurrent;
  unsigned int m_deactivationMaskTarget;

  CameraAreaUniformArray     m_fadeFactorUniforms;
  osg::ref_ptr<osg::Uniform> m_camDisabledColorUniform;
  osg::ref_ptr<osg::Uniform> m_camOffColorUniform;

  SV3DStateGraph m_stateGraphWall;
  SV3DStateGraph m_stateGraphFloor;

  // member to handle the luminance and color harmonization depending on its internal state
  rbp::vis::imp::chamaeleon::ChamaeleonManager m_chamaeleonMng;
  // member to handle the sharpness harmonization depending on its internal state
  rbp::vis::imp::sh::SharpnessHarmonizationManager m_sharpnessHarmonizationMng;
  // member to handle the temporal noise filter depending on its internal state
  rbp::vis::imp::tnf::TemporalNoiseFilterManager m_temporalNoiseFilterMng;
};


//!
//! RenderManagerRegistry
//!
class RenderManagerRegistry : public osg::NodeCallback
{
public:

  RenderManagerRegistry(pc::core::Framework* f_framework);

  pc::core::Framework* getFramework()
  {
    return m_framework;
  }

  void setDefaultRenderManager(RenderManager* f_renderManager)
  {
    m_defaultRenderManager = f_renderManager;
  }

  void addMapping(const osg::Camera* f_camera, RenderManager* f_renderManager);

  RenderManager* find(const osg::Camera* f_camera) const;

  void registerManager(RenderManager* f_renderManager);

  void operator() (osg::Node* f_node, osg::NodeVisitor* f_nv) override;

protected:

  typedef std::set< osg::ref_ptr<RenderManager> > RenderManagerSet;
  typedef std::map< const osg::Camera*, RenderManager* > CameraRenderManagerMap;

  virtual ~RenderManagerRegistry() = default;

  pc::core::Framework* m_framework; //!< Pointer to Framework
  RenderManager* m_defaultRenderManager; //!< Pointer to default RenderManager
  unsigned int m_lastUpdate; //!< track FrameNumber to avoid multiple invocations per update traversal
  CameraRenderManagerMap m_mappings; //!< Maps an osg::Camera to a RenderManager
  RenderManagerSet m_renderManagers; //!< Unique list of all RenderManager instances

};


} // namespace factory
} // namespace pc

#endif //FACTORY_MANAGER_H
