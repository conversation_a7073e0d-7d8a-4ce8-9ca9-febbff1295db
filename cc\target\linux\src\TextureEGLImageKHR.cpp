//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include <iostream>
//#include "cc/imc/qualcomm/qcengine/inc/TextureEGLImageKHR.h"
#include "cc/target/linux/inc/TextureEGLImageKHR.h"

namespace pc
{
namespace target
{
namespace qualcomm
{

//#define EGL_IMG_DEBUG

/// @deviation NRCS2_076
/// Rule QACPP-4.3.0-3803
/// message: "The return value of this function call is not used."

TextureEGLImageKHR::TextureEGLImageKHR()
  : m_textureId(0)
  , m_eglImage(nullptr)
  , m_minificationFilter(osg::Texture::NEAREST)
  , m_magnificationFilter(osg::Texture::LINEAR)
{
}

TextureEGLImageKHR::TextureEGLImageKHR(EGLImageKHR f_eglImage, osg::Texture::FilterMode f_minificationFilter, osg::Texture::FilterMode f_magnificationFilter)
  : m_textureId(0)
  , m_eglImage(f_eglImage)
  , m_minificationFilter(f_minificationFilter)
  , m_magnificationFilter(f_magnificationFilter)
{
}

TextureEGLImageKHR::TextureEGLImageKHR(const TextureEGLImageKHR& f_other, const osg::CopyOp& f_copyop)
  : osg::StateAttribute(f_other, f_copyop)
  , m_textureId(0)
  , m_eglImage(f_other.m_eglImage)
  , m_minificationFilter(f_other.m_minificationFilter)
  , m_magnificationFilter(f_other.m_magnificationFilter)
{
}

void TextureEGLImageKHR::setEGLImage(EGLImageKHR /*f_eglImage*/)
{
  //m_eglImage = f_eglImage;
}

void TextureEGLImageKHR::setAndroidTextureID(GLuint f_textureId)
{
  m_textureId = f_textureId;
}

//must be called in render thread
int TextureEGLImageKHR::deleteGLTexture()
{
  // if (m_textureId > 0)
  // {
  //   glDeleteTextures(1, &m_textureId);
  //   m_textureId = 0;
  // }

  return 0;
}

void TextureEGLImageKHR::apply(osg::State& /* f_state */) const
{
// #ifdef EGL_IMG_DEBUG
//   std::cout << "apply EGLImage" << std::endl;  // PRQA S 3803
// #endif
//   if(true == valid())
//   {
//     if (0 == m_textureId)
//     {
//         glGenTextures(1, &m_textureId);
//     }

// #ifdef EGL_IMG_DEBUG
//     std::cout << "DMABufTexture apply texture extension\nbind texture ID: " << m_textureId << std::endl;  // PRQA S 3803
// #endif
//     //tell the GPU, that it could be a YUV texture
//     glBindTexture(GL_TEXTURE_EXTERNAL_OES, m_textureId);
//     glTexParameteri(GL_TEXTURE_EXTERNAL_OES, GL_TEXTURE_MIN_FILTER, m_minificationFilter);
//     glTexParameteri(GL_TEXTURE_EXTERNAL_OES, GL_TEXTURE_MAG_FILTER, m_magnificationFilter);

//     //glEGLImageTargetTexture2DOES(GL_TEXTURE_EXTERNAL_OES, m_eglImage);
// #ifdef EGL_IMG_DEBUG
//     GLenum l_glErr = glGetError();
//     if(GL_NO_ERROR != l_glErr)
//     {
//       std::cerr << "EGLImage texture issue " << l_glErr << std::endl;  // PRQA S 3803
//     }
// #endif
//   }

   if(m_textureId > 0)
   {
      glBindTexture(GL_TEXTURE_EXTERNAL_OES, m_textureId);
      glTexParameteri(GL_TEXTURE_EXTERNAL_OES, GL_TEXTURE_MIN_FILTER, m_minificationFilter);
      glTexParameteri(GL_TEXTURE_EXTERNAL_OES, GL_TEXTURE_MAG_FILTER, m_magnificationFilter);
      glTexParameteri(GL_TEXTURE_EXTERNAL_OES, GL_TEXTURE_WRAP_S, GL_CLAMP_TO_EDGE);
      glTexParameteri(GL_TEXTURE_EXTERNAL_OES, GL_TEXTURE_WRAP_T, GL_CLAMP_TO_EDGE);
   }
}

//!
//! Compares if two DMABufTexture are identical
//!
//! @return Zero if type of StateAttr is of kind DMABufTexture and
//!         texture identifier of stateAttr is identical
//!
int TextureEGLImageKHR::compare(const osg::StateAttribute& f_stateAttr) const
{
  // check the types are equal and then create the rhs variable
  // used by the COMPARE_StateAttribute_Parameter macros below.
  COMPARE_StateAttribute_Types(TextureEGLImageKHR, f_stateAttr);

  // compare Texture IDs
  if (m_eglImage != rhs.m_eglImage)
  {
    return m_eglImage < rhs.m_eglImage ? -1 : 1;
  }
  if (m_minificationFilter != rhs.m_minificationFilter)
  {
    return m_minificationFilter < rhs.m_minificationFilter ? -1 : 1;
  }
  if (m_magnificationFilter != rhs.m_magnificationFilter)
  {
    return m_magnificationFilter < rhs.m_magnificationFilter ? -1 : 1;
  }

  // passed all above comparisions - should be equal
  return 0;
}

bool TextureEGLImageKHR::valid() const
{
  //return nullptr != m_eglImage;
  return (m_textureId > 0);
}

bool TextureEGLImageKHR::isTextureAttribute() const
{
  return true;
}


void TextureEGLImageKHR::setMinificationFilter(osg::Texture::FilterMode f_minificationFilter)
{
  m_minificationFilter = f_minificationFilter;
}

void TextureEGLImageKHR::setMagnificationFilter(osg::Texture::FilterMode f_magnificationFilter)
{
  m_magnificationFilter = f_magnificationFilter;
}

osg::Texture::FilterMode TextureEGLImageKHR::getMinificationFilter() const
{
  return m_minificationFilter;
}

osg::Texture::FilterMode TextureEGLImageKHR::getMagnificationFilter() const
{
  return m_magnificationFilter;
}


} // namespace ultrascale
} // target
} // namespace pc

