/// @copyright (C) 2023 Robert <PERSON> GmbH.
/// The reproduction, distribution and utilization of this file as well as the
/// communication of its contents to others without express authorization is
/// prohibited. Offenders will be held liable for the payment of damages. All
/// rights reserved in the event of the grant of a patent, utility model or
/// design.
/// @file

#include "pc/svs/imp/chamaeleon/inc/chamaeleon_wb_gains.hpp"
#include "pc/svs/imp/common/inc/common_tabs.hpp"

namespace rbp
{
namespace vis
{
namespace imp
{
namespace chamaeleon
{
ChamaeleonWbGains::ChamaeleonWbGains()
    : m_wbRawGains{1.F, 1.F, 1.F, 1.F, 1.F, 1.F, 1.F, 1.F, 1.F, 1.F, 1.F, 1.F}
    , m_minGain{0.6F}
    , m_maxGain{1.4F}
    , m_exponent{1.F}
{
    setParameters();
}

void ChamaeleonWbGains::setParameters(const ChamaeleonWbGainsData& f_params)
{
    m_minGain  = vfc::clampValueToMinMax(0.01F, f_params.m_minGain, 1.F);
    m_maxGain  = vfc::clampValueToMinMax(1.F, f_params.m_maxGain, 2.F);
    m_exponent = vfc::clampValueToMinMax(0.F, f_params.m_exponent, 10.F);
}

vfc::float32_t ChamaeleonWbGains::setAndClampWbGain(vfc::uint32_t f_wbGain) const
{
    constexpr vfc::float32_t l_defaultIspWbGain{512.F};
    return vfc::pow(
        vfc::clampValueToMinMax(m_minGain, static_cast<vfc::float32_t>(f_wbGain) / l_defaultIspWbGain, m_maxGain),
        m_exponent);
}

//------------------------------------------------------------------------------------------------------------------
/// @brief Retrieves the corresponding single camera area based on the system camera.
///
/// @param[in] f_sysCam The system camera for which to retrieve the single camera area.
/// @return The corresponding single camera area enumeration value.
//------------------------------------------------------------------------------------------------------------------
static pc::factory::SingleCamArea getSingleCam(pc::core::sysconf::Cameras f_sysCam)
{
    switch (f_sysCam)
    {
    case pc::core::sysconf::Cameras::FRONT_CAMERA:
    {
        return pc::factory::SingleCamArea::SINGLE_CAM_FRONT;
    }
    case pc::core::sysconf::Cameras::RIGHT_CAMERA:
    {
        return pc::factory::SingleCamArea::SINGLE_CAM_RIGHT;
    }
    case pc::core::sysconf::Cameras::REAR_CAMERA:
    {
        return pc::factory::SingleCamArea::SINGLE_CAM_REAR;
    }
    case pc::core::sysconf::Cameras::LEFT_CAMERA:
    {
        return pc::factory::SingleCamArea::SINGLE_CAM_LEFT;
    }
    default:
    {
        return pc::factory::SingleCamArea::NUM_SINGLE_CAM_AREAS;
    }
    }
}

void ChamaeleonWbGains::setIspData(const pc::daddy::ISPDataDaddy* const f_ispDataReceiverData)
{
    for (const auto camera : common::CCameraIndex::ALL)
    {
        for (const auto chan : CColorSpaceRGBIndex::ALL)
        {
            const pc::factory::SingleCamArea singleCam{getSingleCam(camera.asEnum())};
            m_wbRawGains(static_cast<vfc::int32_t>(singleCam), static_cast<vfc::int32_t>(common::ESRGB::R)) =
                setAndClampWbGain(f_ispDataReceiverData->m_Data.m_ispData[camera].m_wbGains[common::ESRGB::R]);
            m_wbRawGains(static_cast<vfc::int32_t>(singleCam), static_cast<vfc::int32_t>(common::ESRGB::G)) =
                setAndClampWbGain(f_ispDataReceiverData->m_Data.m_ispData[camera].m_wbGains[common::ESRGB::G]);
            m_wbRawGains(static_cast<vfc::int32_t>(singleCam), static_cast<vfc::int32_t>(common::ESRGB::B)) =
                setAndClampWbGain(f_ispDataReceiverData->m_Data.m_ispData[camera].m_wbGains[common::ESRGB::B]);
        }
    }
}

//------------------------------------------------------------------------------------------------------------------
/// @brief Calculates the polynomial coefficients for white balance gains.
///
/// @param[in] f_gainsLeft The array of left white balance gains for each color channel.
/// @param[in] f_gainsRight The array of right white balance gains for each color channel.
//------------------------------------------------------------------------------------------------------------------
static vfc::float32_t calcWbGainsCoeff(vfc::float32_t f_gainsLeft, vfc::float32_t f_gainsRight)
{
    const vfc::float32_t l_average{(f_gainsLeft + f_gainsRight) / 2.F};
    const vfc::float32_t l_chanGain{l_average / f_gainsLeft};

    return l_chanGain;
}

static vfc::float32_t
getValueMat4x3(const osg::Matrix4x3& f_mat, pc::factory::SingleCamArea f_singleCam, ETabColorSpaceRGB f_chan)
{
    return f_mat(static_cast<vfc::int32_t>(f_singleCam), static_cast<vfc::int32_t>(f_chan));
}

//------------------------------------------------------------------------------------------------------------------
/// @brief Updates the inverse white balance gain coefficients for the side camera.
///
/// @param[in]  f_wbGains The array of white balance gains for each single camera.
/// @param[out] f_inverseWbgCoeffSideCam The array to store the inverse white balance gain coefficients for the side
/// camera.
//------------------------------------------------------------------------------------------------------------------
static void updateSideCam(const osg::Matrix4x3& f_wbRawGains, ArraySingleCamVec3f& f_wbgSideCam)
{
    for (const auto singleCamArea : common::CSingleCamAreaIndex::ALL)
    {
        for (const auto chan : CColorSpaceRGBIndex::ALL)
        {
            if (pc::factory::SingleCamArea::SINGLE_CAM_RIGHT == singleCamArea.asEnum())
            {
                f_wbgSideCam[singleCamArea][chan.asInteger()] = calcWbGainsCoeff(
                    getValueMat4x3(f_wbRawGains, pc::factory::SingleCamArea::SINGLE_CAM_RIGHT, chan.asEnum()),
                    getValueMat4x3(f_wbRawGains, pc::factory::SingleCamArea::SINGLE_CAM_LEFT, chan.asEnum()));
            }
            else if (pc::factory::SingleCamArea::SINGLE_CAM_LEFT == singleCamArea.asEnum())
            {
                f_wbgSideCam[singleCamArea][chan.asInteger()] = calcWbGainsCoeff(
                    getValueMat4x3(f_wbRawGains, pc::factory::SingleCamArea::SINGLE_CAM_LEFT, chan.asEnum()),
                    getValueMat4x3(f_wbRawGains, pc::factory::SingleCamArea::SINGLE_CAM_RIGHT, chan.asEnum()));
            }
            else
            {
                f_wbgSideCam[singleCamArea][chan.asInteger()] = ChamaeleonWbGains::DEFAULT_WB_GAIN;
            }
        }
    }
}

void ChamaeleonWbGains::update()
{
    updateSideCam(m_wbRawGains, m_wbgSideCam);
}

} // namespace chamaeleon
} // namespace imp
} // namespace vis
} // namespace rbp
