//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: ESJ1LR Esparza Jose (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  PlanView.cpp
/// @brief
//=============================================================================

#include "cc/views/planview/inc/PlanView.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/imgui/inc/imgui_manager.h"

// #include "cc/assets/impostor/inc/FixedImpostor.h"
#include "cc/assets/common/inc/Vehicle2D.h"

#include "osgUtil/CullVisitor"

#include <osg/StateSet>
#include <osg/Scissor>

#define DISABLE_GBC 0

namespace cc
{
namespace views
{
namespace planview
{

pc::util::coding::Item<cc::views::planview::PlanViewSettings> g_planView("PlanView");

//!
//! PlanView
//!
PlanView::PlanView( const std::string& f_name,
                    const pc::core::Viewport& f_viewport,
                    const pc::virtcam::VirtualCamera& f_camPos,
                    pc::core::Framework *f_pFramework)
    : pc::core::View( f_name, f_viewport, f_camPos )
    , m_initialized(false)
    , m_pFramework(f_pFramework)
{
    const osg::ref_ptr<osg::StateSet> l_stateSet = getOrCreateStateSet();
    l_stateSet->setMode(GL_SCISSOR_TEST, osg::StateAttribute::OFF);
}

PlanView::~PlanView() = default;

// void PlanView::setImpostor(assets::impostor::FixedImpostor *f_pImpostor)
// {
//     m_pImpostor = f_pImpostor;
//     addChild( m_pImpostor );  // PRQA S 3803
// }

// void PlanView::setWheels(osg::Group *f_pWheels)
// {
//   m_pWheels = f_pWheels;
//   addChild( m_pWheels );  // PRQA S 3803
// }

// void PlanView::setImpostorDoors(pc::core::View *f_pImpostorDoors)
// {
//     m_pImpostorDoors = f_pImpostorDoors;
//     addChild( m_pImpostorDoors );  // PRQA S 3803
// }

void PlanView::initialize()
{
    // osg::Uniform* l_pAlphaUniform = getOrCreateStateSet()->getOrCreateUniform("gbc", osg::Uniform::FLOAT);
    // osg::Uniform* l_pWheelsAlphaUniform = m_pWheels->getOrCreateStateSet()->getOrCreateUniform("VehicleTransparency", osg::Uniform::FLOAT);

    // addUpdateCallback( new PlanViewUpdateCallback( l_pAlphaUniform,
    //                                                 l_pWheelsAlphaUniform,
    //                                                 m_pImpostor,
    //                                                 m_pImpostorDoors,
    //                                                 m_pFramework) );

    addUpdateCallback( new PlanViewUpdateCallback( m_pFramework) );

    m_initialized = true;

    osg::Uniform* const l_brightness = getOrCreateStateSet()->getOrCreateUniform("brightness", osg::Uniform::FLOAT);
    l_brightness->set(1.f);//PRQA S 3803

}

void PlanView::traverse(osg::NodeVisitor& f_nv)
{
    if ((osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType()))
    {
        if (!m_initialized)
        {
            initialize();
            m_initialized = true;
        }
    }
    pc::core::View::traverse(f_nv);
}



//!
//! GbcVehicleData
//!
class GbcVehicleData : public pc::util::coding::ISerializable
{
public:

  GbcVehicleData()
    : m_minDrivenDistX(7.0f)
    , m_minDrivenDistY(1.5f)
    , m_noSpeedTimeout(5.f)
    , m_transpAlpha(0.4f)
    , m_OpaqueToTransparentAnimationTime(5.f)
    , m_TransparentToOpaqueAnimationTime(1.f)
  {
  }

  SERIALIZABLE(GbcVehicleData)  //PRQA S 2428
  {
    ADD_MEMBER(vfc::float32_t, minDrivenDistX);
    ADD_MEMBER(vfc::float32_t, minDrivenDistY);
    ADD_MEMBER(vfc::float32_t, noSpeedTimeout);
    ADD_MEMBER(vfc::float32_t, transpAlpha );
    ADD_MEMBER(vfc::float32_t, OpaqueToTransparentAnimationTime);
    ADD_MEMBER(vfc::float32_t, TransparentToOpaqueAnimationTime);
  }

  vfc::float32_t m_minDrivenDistX;
  vfc::float32_t m_minDrivenDistY;
  vfc::float32_t m_noSpeedTimeout;
  vfc::float32_t m_transpAlpha;
  vfc::float32_t m_OpaqueToTransparentAnimationTime;
  vfc::float32_t m_TransparentToOpaqueAnimationTime;
};


pc::util::coding::Item<GbcVehicleData> g_gbcData("GbcVehicle");


//!
//! PlanViewUpdateCallback
//!
// PlanViewUpdateCallback::PlanViewUpdateCallback( osg::Uniform* f_pAlphaUniform,
//                             osg::Uniform* f_pWheelsAlphaUniform,
//                             assets::impostor::FixedImpostor *f_pImpostor,
//                             pc::core::View *f_pImpostorDoors,
//                             pc::core::Framework* f_pFramework )
//   : m_pAlphaUniform(f_pAlphaUniform)
//   , m_pWheelsAlphaUniform(f_pWheelsAlphaUniform)
//   , m_pImpostor(f_pImpostor)
//   , m_pImpostorDoors(f_pImpostorDoors)
//   , m_pFramework(f_pFramework)
//   , m_vehicleState(EOpaque)
//   , m_initial_xpos(0.f)
//   , m_initial_ypos(0.f)
//   , m_odo_initialized(false)
//   , m_startOfZeroSpeed(0.0)
//   , m_currentSimulationTime(0.0)
//   , m_currentAlphaValue(1.f)
//   , m_alphaAnimationOngoing(false)
//   , m_startOfOpaqueToTransparent(0.0)
//   , m_startOfTransparentToOpaque(0.0)
// {

// }

PlanViewUpdateCallback::PlanViewUpdateCallback( pc::core::Framework* f_pFramework )
    : m_pAlphaUniform(nullptr)
    , m_pFramework(f_pFramework)
    , m_pBrightnessUniform(nullptr)
    , m_initialized(false)
    , m_vehicleState(EOpaque)
    , m_initial_xpos(0.0f)
    , m_initial_ypos(0.0f)
    , m_odo_initialized(false)
    , m_startOfZeroSpeed(0.0)
    , m_currentSimulationTime(0.0)
    , m_currentAlphaValue(0.0f)
    , m_alphaAnimationOngoing(false)
    , m_startOfOpaqueToTransparent(0.0)
    , m_startOfTransparentToOpaque(0.0)
{
}

PlanViewUpdateCallback::~PlanViewUpdateCallback() = default;

void PlanViewUpdateCallback::initialize(osg::Node* f_node)
{
    if (f_node != nullptr)
    {
        m_pBrightnessUniform = f_node->getOrCreateStateSet()->getOrCreateUniform("brightness", osg::Uniform::FLOAT);
    }
}


void PlanViewUpdateCallback::operator()(osg::Node* f_node, osg::NodeVisitor* f_nv)
{
    if ((f_node == nullptr) || (f_nv == nullptr))
    {
        return;
    }
    // Initialize callback -> calls getOrCreateUniform
    if (!m_initialized)
    {
        initialize(f_node);
        m_initialized = true;
    }

    // check for day/night mode - it is needed for setting a light/dark impostor culling mask
    // bool l_isDay = false;
    // cc::core::CustomFramework* l_pCustomFramework = m_pFramework->asCustomFramework();
    // if ( l_pCustomFramework->m_VehicleLightsReceiver.isConnected() )
    // {
    //     const cc::daddy::CustomVehicleLightsDaddy* l_pData = l_pCustomFramework->m_VehicleLightsReceiver.getData();
    //     if (0 != l_pData)
    //     {
    //         // LowBeamReq = 1 indicates LOW ambient light/ night profile of the vehicle model
    //         // LowBeamReq = 0 indicates DAY ambient light/ day profile of the vehicle model
    //         if (false == l_pData->m_Data.m_lowBeamIndication)
    //         {
    //             l_isDay = true;
    //         }
    //         else
    //         {
    //             l_isDay = false;
    //         }
    //     }
    // }

    // culling mask - initialize everything as active, and de-activate whichever nodes are not required
    vfc::uint32_t l_cullMask = ~0u;

    // Doors Open/Closed toggle a single-node impostor or a multi-node impostor (containing doors, etc)
    if ( !allDoorsClosed() ) // doors open
    {
        // Single impostor -> off
        l_cullMask = l_cullMask & ~( static_cast<vfc::uint32_t>(cc::assets::common::CustomVehicleModel2D::NIGHT_FULL_CAR_IMPOSTOR_MASK) );
    }
    else // doors closed
    {
        // impostor with dynamic parts -> off
        l_cullMask = l_cullMask & ~( static_cast<vfc::uint32_t>(cc::assets::common::CustomVehicleModel2D::NIGHT_DYNAMIC_PARTS_IMPOSTOR) );
    }

    // Brightness uniform based on day/night signal.
    // The reference model is a "dark" impostor. In order to make it lighter, the RGB channels are scaled up by factor 1.5
    // This is controlled by the vfc::float32_t uniform "brightness"
    if(m_pBrightnessUniform != nullptr)
    {
        // if(true == l_isDay)
        // {
        // m_pBrightnessUniform->set(1.5f);//PRQA S 3803  //code is unreachable because l_isDay is always false;
        // }
        // else
        // {
        m_pBrightnessUniform->set(1.0f);//PRQA S 3803
        // }
    }

    // set cull mask
    f_node->asCamera()->setCullMask( l_cullMask );

    traverse(f_node, f_nv);

    // m_currentSimulationTime = f_nv->getFrameStamp()->getSimulationTime();

    // const cc::daddy::GbcVehicleTransparency_t* l_pGbcTransp =  m_pFramework->asCustomFramework()->m_GbcVehicleTransparency_ReceiverPort.getData();

    // if ( !allDoorsClosed() )
    // {
    //     m_odo_initialized = false; // after a door is open, a full length drive is needed
    //     m_vehicleState = EOpaque;
    //     m_currentAlphaValue =  1.0f;
    //     m_alphaAnimationOngoing = false;
    //     m_pAlphaUniform->set(m_currentAlphaValue);
    //     m_pAlphaUniform->set(l_pGbcTransp->m_Data);  // PRQA S 3803
    //     m_pWheelsAlphaUniform->set(m_currentAlphaValue);  // PRQA S 3803
    //     m_pImpostorDoors->setNodeMask(~0u);
    //     m_pImpostor->useTextureWithoutDoors(true);
    //     traverse(f_node, f_nv);
    //     return;
    // }
    // else
    // {
    //     m_pImpostorDoors->setNodeMask(0u);
    //     m_pImpostor->useTextureWithoutDoors(false);
    // }

#if DISABLE_GBC
    m_vehicleState = EOpaque;
#endif

    //! Check BCC signal for GBC Status
    // const cc::daddy::PIVI_LSMSettings_t* l_pLSMSettingsDaddy =  m_pFramework->asCustomFramework()->m_LSMSettings_ReceiverPort.getData();
    // if (0 != l_pLSMSettingsDaddy)
    // {
    //   if(0 == l_pLSMSettingsDaddy->m_Data.m_GlassBottomCar_u8)
    //   {
    //       m_vehicleState = EOpaque;
    //       m_currentAlphaValue = 1.0;
    //       m_alphaAnimationOngoing = false;
    //   }
    // }

    // switch (m_vehicleState)
    // {
    //     case EOpaque:
    //     {
    //         if(m_alphaAnimationOngoing)
    //         {
    //             m_currentAlphaValue = g_gbcData->m_transpAlpha + (1.f - g_gbcData->m_transpAlpha)*(static_cast<vfc::float32_t>(m_currentSimulationTime) - static_cast<vfc::float32_t>(m_startOfTransparentToOpaque))/g_gbcData->m_TransparentToOpaqueAnimationTime;
    //             if (m_currentAlphaValue > 1.0f)
    //             {
    //                 m_currentAlphaValue = 1.0f;
    //                 m_alphaAnimationOngoing = false;
    //             }
    //         }
    //         checkOdometry();
    //     } break;
    //     case ETransparent:
    //     {
    //         if(m_alphaAnimationOngoing)
    //         {
    //             m_currentAlphaValue = 1.f + (g_gbcData->m_transpAlpha - 1.f)*(static_cast<vfc::float32_t>(m_currentSimulationTime) - static_cast<vfc::float32_t>(m_startOfOpaqueToTransparent))/g_gbcData->m_OpaqueToTransparentAnimationTime;
    //             if (m_currentAlphaValue < g_gbcData->m_transpAlpha)
    //             {
    //                 m_currentAlphaValue = g_gbcData->m_transpAlpha;
    //                 m_alphaAnimationOngoing = false;
    //             }
    //         }
    //         checkZeroSpeed();
    //     } break;
    //     case ENoSpeed:
    //     {
    //         if(m_alphaAnimationOngoing)
    //         {
    //             m_currentAlphaValue = 1.f + (g_gbcData->m_transpAlpha - 1.f)*(static_cast<vfc::float32_t>(m_currentSimulationTime) - static_cast<vfc::float32_t>(m_startOfOpaqueToTransparent))/g_gbcData->m_OpaqueToTransparentAnimationTime;
    //             if (m_currentAlphaValue < g_gbcData->m_transpAlpha)
    //             {
    //                 m_currentAlphaValue = g_gbcData->m_transpAlpha;
    //                 m_alphaAnimationOngoing = false;
    //             }
    //         }
    //         if (!checkNonZeroSpeed())
    //         {
    //             checkForTimeout();
    //         }
    //     }break;
    //     default:
    //     {
    //         //
    //     }break;
    // }

    // // m_pAlphaUniform->set(m_currentAlphaValue);
    // m_pAlphaUniform->set(l_pGbcTransp->m_Data);  // PRQA S 3803
    // m_pWheelsAlphaUniform->set(m_currentAlphaValue);  // PRQA S 3803
    // traverse(f_node, f_nv);
}


void PlanViewUpdateCallback::checkOdometry()
{
    const pc::daddy::OdometryDataDaddy* const l_pDataDaddy =  m_pFramework->m_odometryReceiver.getData();
    if (nullptr != l_pDataDaddy)
    {
      const vfc::float32_t l_new_xpos = l_pDataDaddy->m_Data.m_xPos.value();
      const vfc::float32_t l_new_ypos = l_pDataDaddy->m_Data.m_yPos.value();

      if (!m_odo_initialized)
      {
        m_initial_xpos = l_new_xpos;
        m_initial_ypos = l_new_ypos;
        m_odo_initialized = true;
      }

      if ( (std::abs( l_new_xpos-m_initial_xpos ) > g_gbcData->m_minDrivenDistX) ||
           (std::abs( l_new_ypos-m_initial_ypos ) > g_gbcData->m_minDrivenDistY) )
      {
          m_startOfOpaqueToTransparent = m_currentSimulationTime;
          m_alphaAnimationOngoing = true;
          m_vehicleState = ETransparent;
      }
    }
    else
    {
        // keep waiting
    }
}

void PlanViewUpdateCallback::checkZeroSpeed()
{
    const pc::daddy::SpeedDaddy* const l_pDataDaddy =  m_pFramework->m_speedReceiver.getData();
    if (nullptr != l_pDataDaddy)
    {
      if ( std::abs(l_pDataDaddy->m_Data) < 1e-3 ) // no movement
      {
          m_startOfZeroSpeed = m_currentSimulationTime;
          m_alphaAnimationOngoing = true;
          m_vehicleState = ENoSpeed;
      }
      else
      {
        // keep waiting
      }
    }
}


bool PlanViewUpdateCallback::checkNonZeroSpeed()
{
    const pc::daddy::SpeedDaddy* const l_pDataDaddy =  m_pFramework->m_speedReceiver.getData();
    if (nullptr != l_pDataDaddy)
    {
      if ( std::abs(l_pDataDaddy->m_Data) > 0.1f ) // some movement
      {
          m_vehicleState = ETransparent;
          return true;
      }
      else
      {
        // keep waiting
      }
    }

    return false;
}


void PlanViewUpdateCallback::checkForTimeout()
{
    if ( (m_currentSimulationTime-m_startOfZeroSpeed) > g_gbcData->m_noSpeedTimeout )
    {
        m_startOfTransparentToOpaque = m_currentSimulationTime;
        m_alphaAnimationOngoing = true;
        m_vehicleState = EOpaque;
        m_odo_initialized = false;
    }
    else
    {
        // keep waiting
    }
}

bool PlanViewUpdateCallback::allDoorsClosed()
{
  const pc::daddy::DoorAnimationStateDaddy* const l_pData = m_pFramework->m_doorAnimationStateReceiver.getData();
  bool l_allDoorsClosed = true;

  if (nullptr!=l_pData)
  {
      if (  l_pData->m_Data.AnimationOngoingOrOpenFL          ||
            l_pData->m_Data.AnimationOngoingOrOpenFR          ||
            l_pData->m_Data.AnimationOngoingOrOpenRL          ||
            l_pData->m_Data.AnimationOngoingOrOpenRR          ||
            l_pData->m_Data.AnimationOngoingOrOpenTrunk       ||
            l_pData->m_Data.AnimationOngoingOrOpenMirrorLeft  ||
            l_pData->m_Data.AnimationOngoingOrOpenMirrorRight  )
            {
              l_allDoorsClosed = false;
            }
  }
  return l_allDoorsClosed;
}


//!
//! PlanViewCullCallback
//!
PlanViewCullCallback::PlanViewCullCallback()
: m_ViewRefMat() // PRQA S 2323 // PRQA S 4052
, m_matAvail_b(false) // PRQA S 2323
{
}

PlanViewCullCallback::~PlanViewCullCallback() = default;

void PlanViewCullCallback::operator()(osg::Node* f_node, osg::NodeVisitor* f_nv)
{

    if (f_node != nullptr && f_nv != nullptr)
    {
    if (osg::NodeVisitor::CULL_VISITOR == f_nv->getVisitorType())
    {
        osgUtil::CullVisitor* const l_cv = static_cast<osgUtil::CullVisitor*> (f_nv);

        const osg::RefMatrix* const l_refMat = l_cv->getMVPW();
        m_ViewRefMat = *l_refMat;

        m_matAvail_b = true;
    }
    traverse(f_node, f_nv);
}
}

bool PlanViewCullCallback::getRefMatrix(osg::Matrixf& f_mat) const
{
    if (m_matAvail_b)
    {
        f_mat =  m_ViewRefMat;
        return true;
    }
    else
    {
        return false;
    }
}


void PlanViewProjectionCallback::operator()(osg::Node* f_node, osg::NodeVisitor* f_nv)
{
  using cc::core::g_views;
  const vfc::float32_t l_total_width_meters_parking_vert_vehicle2d  = g_planView->m_widthMetersParkingVert - g_planView->m_widthMetersVeh2dDiffParkingVert;
  const vfc::float32_t l_total_length_meters_parking_vert_vehicle2d = static_cast<vfc::float32_t>(g_views->m_vertParkingPlanViewport.m_size.x()) *
                                                   l_total_width_meters_parking_vert_vehicle2d /
                                                   static_cast<vfc::float32_t>(g_views->m_vertParkingPlanViewport.m_size.y());
  if (IMGUI_GET_CHECKBOX_BOOL("Settings", "Vehicle2DUpdateSize"))
  {
    f_node->asCamera()->setProjectionMatrixAsOrtho2D( // PRQA S 2880
      - l_total_length_meters_parking_vert_vehicle2d / 2.0f,
      l_total_length_meters_parking_vert_vehicle2d / 2.0f,
      - l_total_width_meters_parking_vert_vehicle2d /2.0f,
      l_total_width_meters_parking_vert_vehicle2d /2.0f
    );
  }
  traverse(f_node, f_nv);
}


} // namespace PlanView
} // namespace assets
} // namespace cc
