#ifndef RENDERNATIVE_COMDATASTRUCTURE_H
#define RENDERNATIVE_COMDATASTRUCTURE_H
#include <string>
#include <list>

enum class InitFeedBack{
    Success = 0,
    Failed_Can_Not_Call = 1,
    Failed_Config_File = 2,
};

enum class DeInitFeedBack{
    Success = 0,
    Failed_Can_Not_Call = 1,
};

struct CameraConfig{
    std::string internalParamPath; //相机内参文件路径 Cam4Lens
    int CamWidth; //相机分辨率（宽）
    int CamHeight; //相机分辨率（高）
};

enum class PlatformType {
    UNKNOWN
};

enum class ProjectType {
    UNKNOWN
};

struct VehicleConfig {
    std::string calibPath; //标定外参路径
    std::string assetsPath; //资源 Assets 目录
    std::string usbDumpPath; // usbdump 路径
    std::string resourcePath; //自定义资源路径
    std::string vehicleType; //车型
    std::string subVehicleType; //子车型
    int screenWidth; //屏幕分辨率（宽）
    int screenHeight; //屏幕分辨率（高）
    PlatformType platform; //平台
    ProjectType project; //项目
};

struct ServiceConfig {
    std::list<std::shared_ptr<CameraConfig>> mCameraConfigs;
    std::shared_ptr<VehicleConfig> mVehicleConfig;
};


enum class vehicleModelView {
    EScreenID_NO_CHANGE = 0,             // Default value
    EScreenID_NO_VIDEO_USER = 1,
    EScreenID_LSMG = 2,
    EScreenID_CONTEXT_ON_ROAD = 3,
    EScreenID_CONTEXT_OFF_ROAD = 4,
    EScreenID_CONTEXT_TOWING = 5,
    EScreenID_CONTEXT_JAPANESE = 6,
    EScreenID_CONTEXT_THREAT = 7,
    EScreenID_SINGLE_FRONT_NORMAL = 8,
    EScreenID_SINGLE_STB = 9,
    EScreenID_SINGLE_FRONT_JUNCTION = 10,
    EScreenID_SINGLE_REAR_NORMAL_OFF_ROAD = 11,
    EScreenID_PARK_ASSIST_FRONT = 12,
    EScreenID_SINGLE_FRONT_RAW = 13,
    EScreenID_SINGLE_REAR_NORMAL_ON_ROAD = 14,
    EScreenID_SINGLE_REAR_JUNCTION = 15,
    EScreenID_SINGLE_REAR_RAW_DIAG = 16,
    EScreenID_SINGLE_REAR_HITCH = 17,
    EScreenID_SINGLE_REAR_HITCH_ZOOM = 18,
    EScreenID_PARK_ASSIST_REAR = 19,
    EScreenID_SINGLE_REAR_TRAILER = 20,
    EScreenID_PERSPECTIVE_KL = 21,
    EScreenID_PARK_ASSIST_FRONT_JAP = 22,
    EScreenID_PARK_ASSIST_REAR_JAP = 23,
    EScreenID_SINGLE_ML_RAW = 24,
    EScreenID_PERSPECTIVE_KR = 25,
    EScreenID_SINGLE_LEFT = 26,
    EScreenID_SINGLE_RIGHT = 27,
    EScreenID_SINGLE_MR_RAW = 28,
    EScreenID_THREAT_FRONT = 29,
    EScreenID_THREAT_REAR = 30,
    EScreenID_WHEEL_FRONT_DUAL = 31,
    EScreenID_PERSPECTIVE_FL = 32,
    EScreenID_PERSPECTIVE_FR = 33,
    EScreenID_PERSPECTIVE_RL = 34,
    EScreenID_PERSPECTIVE_RR = 35,
    EScreenID_DUAL_FRONT_ML = 36,
    EScreenID_DUAL_FRONT_MR = 37,
    EScreenID_PERSPECTIVE_PRE = 38,
    EScreenID_PERSPECTIVE_PLE = 39,
    EScreenID_DUAL_FRONT_JAP = 40,
    EScreenID_CAM_CALIB_ENG = 41,
    EScreenID_DUAL_REAR_JAP = 42,
    EScreenID_TOW_ASSIST_ENG = 43,
    EScreenID_LSM_ENG = 44,
    EScreenID_WHEEL_REAR_DUAL = 45,
    EScreenID_LSMG_LSAEB_ENG = 46,
    EScreenID_FULL_SCREEN = 47,
    EScreenID_TRIPLE_ML_FV_MR = 48,
    EScreenID_TRIPLE_REAR_ML_MR_TOW_ASSIST = 49,
    EScreenID_QUAD_RAW = 50,
    EScreenID_NO_VIDEO_SYSTEM = 51,
    EScreenID_PERSPECTIVE_PFR = 52,
    EScreenID_PERSPECTIVE_PRI = 53,
    EScreenID_BACK = 54,
    EScreenID_PRK_MODE_SELECT = 55,
    EScreenID_PRK_SEARCHING = 56,
    EScreenID_PRK_CONFIRMING = 57,
    EScreenID_VERT_SINGLE_FRONT = 58,
    EScreenID_VERT_SINGLE_REAR = 59,
    EScreenID_VERT_SINGLE_FRONT_JUNCTION = 60,
    EScreenID_VERT_SINGLE_REAR_JUNCTION = 61,
    EScreenID_VERT_SINGLE_LEFT = 62,
    EScreenID_VERT_SINGLE_RIGHT = 63,
    EScreenID_VERT_WHEEL_FRONT_DUAL = 64,
    EScreenID_VERT_WHEEL_REAR_DUAL = 65,
    EScreenID_VERT_PLANVIEW_WITH_SEPARATOR = 66,
    EScreenID_VERT_PERSPECTIVE_PFR = 67,
    EScreenID_VERT_PERSPECTIVE_PRE = 68,
    EScreenID_VERT_PERSPECTIVE_RL = 69,
    EScreenID_VERT_PERSPECTIVE_RR = 70,
    EScreenID_VERT_PERSPECTIVE_FL = 71,
    EScreenID_VERT_PERSPECTIVE_FR = 72,
    EScreenID_VERT_SINGLE_STB = 73,
    EScreenID_HORI_PARKING_FRONT = 74,
    EScreenID_HORI_PARKING_REAR = 75,
    EScreenID_VERT_PARKING_FRONT = 76,
    EScreenID_VERT_PARKING_REAR = 77,
    EScreenID_PLANVIEW_WITH_SEPARATOR = 78,
    EScreenID_REMOTE_SCREEN_FRONT = 79,
    EScreenID_REMOTE_SCREEN_REAR = 80,
    EScreenID_REMOTE_SCREEN_LEFT = 81,
    EScreenID_REMOTE_SCREEN_RIGHT = 82,
    EScreenID_DEBUG = 83,
    EScreenID_LEFTRIGHTVIEW_FRONT_VIEW = 84,
    EScreenID_LEFTRIGHTVIEW_REAR_VIEW = 85,
    EScreenID_VERT_PLAN_FRONT_ENLARGE__FRONT_MAIN__LEFT_SIDE = 86,
    EScreenID_VERT_PLAN_FRONT_ENLARGE__FRONT_MAIN__RIGHT_SIDE = 87,
    EScreenID_VERT_PLAN_REAR_ENLARGE__REAR_MAIN__LEFT_SIDE = 88,
    EScreenID_VERT_PLAN_REAR_ENLARGE__REAR_MAIN__RIGHT_SIDE = 89,
    EScreenID_VERT_FULLSCREEN = 90,
    EScreenID_VERT_FULLSCREEN_FRONT_ENLARGE = 91,
    EScreenID_VERT_FULLSCREEN_REAR_ENLARGE = 92,
    EScreenID_VERT_PLAN__DUAL_FRONT_WHEEL__FRONT_SIDE = 93,
    EScreenID_VERT_PLAN__DUAL_FRONT_WHEEL__REAR_SIDE = 94,
    EScreenID_VERT_PLAN__DUAL_REAR_WHEEL__FRONT_SIDE = 95,
    EScreenID_VERT_PLAN__DUAL_REAR_WHEEL__REAR_SIDE = 96,
    EScreenID_VERT_PLAN__DUAL_WHEEL__FRONT_SIDE = 97,
    EScreenID_VERT_PLAN__DUAL_WHEEL__REAR_SIDE = 98,
    EScreenID_VERT_PERSPECTIVE_FL_R = 99,
    EScreenID_VERT_PERSPECTIVE_FL_L = 100,
    EScreenID_VERT_PERSPECTIVE_FR_R = 101,
    EScreenID_VERT_PERSPECTIVE_FR_L = 102,
    EScreenID_VERT_PERSPECTIVE_RL_R = 103,
    EScreenID_VERT_PERSPECTIVE_RL_L = 104,
    EScreenID_VERT_PERSPECTIVE_RR_R = 105,
    EScreenID_VERT_PERSPECTIVE_RR_L = 106,
    EScreenID_VERT_2D_FRONTVIEW_RIGHTVIEW = 107,
    EScreenID_VERT_2D_FRONTVIEW_LEFTVIEW = 108,
    EScreenID_VERT_2D_REARVIEW_RIGHTVIEW = 109,
    EScreenID_VERT_2D_REARVIEW_LEFTVIEW = 110,
    EScreenID_MODEL_F_VIEW_ENLARGEMENT = 111,
    EScreenID_MODEL_B_VIEW_ENLARGEMENT = 112,
    EScreenID_FULLSCREEN = 113,
    EScreenID_FULLSCREEN_FRONT_ENLARGE = 114,
    EScreenID_FULLSCREEN_REAR_ENLARGE = 115,
    EScreenID_FRONT_BUMPER = 116,
    EScreenID_REAR_BUMPER = 117,
    EScreenID_ULTRA_WIDE_SURROUND_VIEW = 118,
    EScreenID_PLANETARY_VIEW = 119,
    EScreenID_SUPER_TRANSPARENT_VIEW = 120,
    EScreenID_SINGLE_REAR_NORMAL_ON_ROAD_SGHL_L = 121,
    EScreenID_MODEL_B_VIEW_ENLARGEMENT_SGHL_L = 122,
    EScreenID_IMAGE_IN_IMAGE_LEFT = 123,
    EScreenID_IMAGE_IN_IMAGE_RIGHT = 124,
    EScreenID_IMAGE_IN_IMAGE_LEFT_RIGHT = 125,
    EScreenID_IMAGE_IN_IMAGE_PLANVIEW = 126,
    EScreenID_AVM3D_VIEW_FULL_SCREEN_2D_VOT = 127,
    VIEW_MODE_END
};

enum EAvm3dView
{
    AVM3D_VIEW_NONE = -1,

    AVM3D_VIEW_FULL_SCREEN_2D_FRONT_RIGHT = 0,      // 0：2D前视+右视
    AVM3D_VIEW_FULL_SCREEN_2D_FRONT_LEFT,           // 1：2D前视+左视
    AVM3D_VIEW_FULL_SCREEN_2D_REAR_RIGHT,           // 2：2D后视+右视
    AVM3D_VIEW_FULL_SCREEN_2D_REAR_LEFT,            // 3：2D后视+左视
    AVM3D_VIEW_FULL_SCREEN_2D_WIDE_FRONT_LEFT,      // 4：2D前广角+左视
    AVM3D_VIEW_FULL_SCREEN_2D_WIDE_FRONT_RIGHT,     // 5：2D前广角+右视
    AVM3D_VIEW_FULL_SCREEN_2D_WIDE_REAR_LEFT,       // 6：2D后广角+左视
    AVM3D_VIEW_FULL_SCREEN_2D_WIDE_REAR_RIGHT,      // 7：2D后广角+右视
    AVM3D_VIEW_FULL_SCREEN_3D_LEFT_REAR_RIGHT,      // 8：3D左后+右视
    AVM3D_VIEW_FULL_SCREEN_3D_RIGHT_REAR_RIGHT,     // 9：3D右后+右视
    AVM3D_VIEW_FULL_SCREEN_3D_REAR_RIGHT,           // 10：3D后+右视
    AVM3D_VIEW_FULL_SCREEN_3D_LEFT_RIGHT,           // 11：3D左+右视
    AVM3D_VIEW_FULL_SCREEN_3D_RIGHT_RIGHT,          // 12：3D右+右视
    AVM3D_VIEW_FULL_SCREEN_3D_LEFT_FRONT_RIGHT,     // 13：3D左前+右视
    AVM3D_VIEW_FULL_SCREEN_3D_RIGHT_FRONT_RIGHT,    // 14：3D右前+右视
    AVM3D_VIEW_FULL_SCREEN_3D_FRONT_RIGHT,          // 15：3D前+右视
    AVM3D_VIEW_FULL_SCREEN_3D_LEFT_REAR_LEFT,       // 16：3D左后+左视
    AVM3D_VIEW_FULL_SCREEN_3D_RIGHT_REAR_LEFT,      // 17：3D右后+左视
    AVM3D_VIEW_FULL_SCREEN_3D_REAR_LEFT,            // 18：3D后+左视
    AVM3D_VIEW_FULL_SCREEN_3D_LEFT_LEFT,            // 19：3D左+左视
    AVM3D_VIEW_FULL_SCREEN_3D_RIGHT_LEFT,           // 20：3D右+左视
    AVM3D_VIEW_FULL_SCREEN_3D_LEFT_FRONT_LEFT,      // 21：3D左前+左视
    AVM3D_VIEW_FULL_SCREEN_3D_RIGHT_FRONT_LEFT,     // 22：3D右前+左视
    AVM3D_VIEW_FULL_SCREEN_3D_FRONT_LEFT,           // 23：3D前+左视

    AVM3D_VIEW_FULL_SCREEN_3D_TOP_VIEW_FRONT,    // 24：车头俯瞰放大
    AVM3D_VIEW_FULL_SCREEN_3D_TOP_VIEW_REAR,     // 25：车尾俯瞰放大
    AVM3D_VIEW_FULL_SCREEN_2D_TOP_VIEW_FULL,     // 26：2D俯瞰全屏
    AVM3D_VIEW_FULL_SCREEN_ASTEROID_FULL,        // 27：小行星视角
    AVM3D_VIEW_FULL_SCREEN_3D_FULL,              // 28：3D全屏
    AVM3D_VIEW_FULL_SCREEN_3D_WIDE_FULL,         // 29：3D超广角

    AVM3D_VIEW_FULL_SCREEN_2D_FRONT_HUBVIEW_FRONT,    // 30：AVM2D全景 前双视+前 --双视图
    AVM3D_VIEW_FULL_SCREEN_2D_FULL_HUBVIEW_FRONT,     // 31：AVM2D全景 全双视+前 --双视图
    AVM3D_VIEW_FULL_SCREEN_2D_REAR_HUBVIEW_FRONT,     // 32：AVM2D全景 后双视+前 --双视图
    AVM3D_VIEW_FULL_SCREEN_2D_FRONT_HUBVIEW_REAR,     // 33：AVM2D全景 前双视+后 --双视图
    AVM3D_VIEW_FULL_SCREEN_2D_FULL_HUBVIEW_REAR,      // 34：AVM2D全景 全双视+后 --双视图
    AVM3D_VIEW_FULL_SCREEN_2D_REAR_HUBVIEW_REAR,      // 35：AVM2D全景 后双视+后 --双视图

    AVM3D_VIEW_FULL_SCREEN_2D_FRONT_FRONT_HUBVIEW,         // 36：AVM2D全景 前+前双视 --三视图
    AVM3D_VIEW_FULL_SCREEN_2D_FRONT_FULL_HUBVIEW,          // 37：AVM2D全景 前+全双视 --三视图 (not used)
    AVM3D_VIEW_FULL_SCREEN_2D_FRONT_REAR_HUBVIEW,          // 38：AVM2D全景 前+后双视 --三视图
    AVM3D_VIEW_FULL_SCREEN_2D_REAR_FRONT_HUBVIEW,          // 39：AVM2D全景 后+前双视 --三视图
    AVM3D_VIEW_FULL_SCREEN_2D_REAR_FULL_HUBVIEW,           // 40：AVM2D全景 后+全双视 --三视图 (not used)
    AVM3D_VIEW_FULL_SCREEN_2D_REAR_REAR_HUBVIEW,           // 41：AVM2D全景 后+后双视 --三视图
    AVM3D_VIEW_FULL_SCREEN_2D_WIDE_FRONT_FRONT_HUBVIEW,    // 42：AVM2D全景 前广+前双视 --三视图
    AVM3D_VIEW_FULL_SCREEN_2D_WIDE_FRONT_FULL_HUBVIEW,     // 43：AVM2D全景 前广+全双视 --三视图 (not used)
    AVM3D_VIEW_FULL_SCREEN_2D_WIDE_FRONT_REAR_HUBVIEW,     // 44：AVM2D全景 前广+后双视 --三视图
    AVM3D_VIEW_FULL_SCREEN_2D_WIDE_REAR_FRONT_HUBVIEW,     // 45：AVM2D全景 后广+前双视 --三视图
    AVM3D_VIEW_FULL_SCREEN_2D_WIDE_REAR_FULL_HUBVIEW,      // 46：AVM2D全景 后广+全双视 --三视图 (not used)
    AVM3D_VIEW_FULL_SCREEN_2D_WIDE_REAR_REAR_HUBVIEW,      // 47：AVM2D全景 后广+后双视 --三视图

    AVM3D_VIEW_FLOAT_WINDOW_TOP,                 // 48：画中画俯瞰图
    AVM3D_VIEW_FLOAT_WINDOW_LEFT,                // 49：画中画左视图
    AVM3D_VIEW_FLOAT_WINDOW_RIGHT,               // 50：画中画右视图
    AVM3D_VIEW_FLOAT_WINDOW_BOARD_LEFT,          // 51：画中画左视图仪表
    AVM3D_VIEW_FLOAT_WINDOW_BOARD_RIGHT,         // 52：画中画右视图仪表
    AVM3D_VIEW_FLOAT_WINDOW_TOP_ZOOMIN_FRONT,    // 53：画中画俯瞰图前轮轨放大
    AVM3D_VIEW_FLOAT_WINDOW_TOP_ZOOMIN_REAR,     // 54：画中画俯瞰图后轮轨放大
    AVM3D_VIEW_FULL_SCREEN_2D_FRONT_ZOOM_LEFT,     // 55：车头俯瞰放大+左视
    AVM3D_VIEW_FULL_SCREEN_2D_FRONT_ZOOM_RIGHT,    // 56：车头俯瞰放大+右视
    AVM3D_VIEW_FULL_SCREEN_2D_REAR_ZOOM_LEFT,      // 57：车尾俯瞰放大+左视
    AVM3D_VIEW_FULL_SCREEN_2D_REAR_ZOOM_RIGHT,     // 58：车尾俯瞰放大+右视

    AVM3D_VIEW_REMOTEVISION_FRONT,    // 59: 千里眼前视
    AVM3D_VIEW_REMOTEVISION_REAR,     // 60: 千里眼后视
    AVM3D_VIEW_REMOTEVISION_LEFT,     // 61: 千里眼左视
    AVM3D_VIEW_REMOTEVISION_RIGHT,    // 62: 千里眼右视

    AVM3D_VIEW_FULL_SCREEN_2D_VOT,    // 63: 2DVOT(原地掉头)视图

    AVM3D_VIEW_APA_AERIAL,    // 64: APA鸟瞰图
    AVM3D_VIEW_APA_SELECT,    // 65: APA自选
    AVM3D_VIEW_APA_FRONT,     // 66: APA视图-前视图
    AVM3D_VIEW_APA_BACK,      // 67: APA视图-后视图

    // 3D全景
    AVM3D_VIEW_FULL_SCREEN_3D_ANGLE_LEFT,     // 68：3D保持最后一次的角度+左视
    AVM3D_VIEW_FULL_SCREEN_3D_ANGLE_RIGHT,    // 69：3D保持最后一次的角度+右视

    // 轮毂视图(三视图)
    AVM3D_VIEW_FULL_SCREEN_3D_LEFT_REAR_FRONT_HUBVIEW,      // 70：3D左后+前轮毂 --三视图
    AVM3D_VIEW_FULL_SCREEN_3D_RIGHT_REAR_FRONT_HUBVIEW,     // 71：3D右后+前轮毂 --三视图
    AVM3D_VIEW_FULL_SCREEN_3D_REAR_FRONT_HUBVIEW,           // 72：3D后+前轮毂   --三视图
    AVM3D_VIEW_FULL_SCREEN_3D_LEFT_FRONT_HUBVIEW,           // 73：3D左+前轮毂   --三视图
    AVM3D_VIEW_FULL_SCREEN_3D_RIGHT_FRONT_HUBVIEW,          // 74：3D右+前轮毂   --三视图
    AVM3D_VIEW_FULL_SCREEN_3D_LEFT_FRONT_FRONT_HUBVIEW,     // 75：3D左前+前轮毂 --三视图
    AVM3D_VIEW_FULL_SCREEN_3D_RIGHT_FRONT_FRONT_HUBVIEW,    // 76：3D右前+前轮毂 --三视图
    AVM3D_VIEW_FULL_SCREEN_3D_FRONT_FRONT_HUBVIEW,          // 77：3D前+前轮毂   --三视图
    AVM3D_VIEW_FULL_SCREEN_3D_LEFT_REAR_REAR_HUBVIEW,       // 78：3D左后+后轮毂 --三视图
    AVM3D_VIEW_FULL_SCREEN_3D_RIGHT_REAR_REAR_HUBVIEW,      // 79：3D右后+后轮毂 --三视图
    AVM3D_VIEW_FULL_SCREEN_3D_REAR_REAR_HUBVIEW,            // 80：3D后+后轮毂   --三视图
    AVM3D_VIEW_FULL_SCREEN_3D_LEFT_REAR_HUBVIEW,            // 81：3D左+后轮毂   --三视图
    AVM3D_VIEW_FULL_SCREEN_3D_RIGHT_REAR_HUBVIEW,           // 82：3D右+后轮毂   --三视图
    AVM3D_VIEW_FULL_SCREEN_3D_LEFT_FRONT_REAR_HUBVIEW,      // 83：3D左前+后轮毂 --三视图
    AVM3D_VIEW_FULL_SCREEN_3D_RIGHT_FRONT_REAR_HUBVIEW,     // 84：3D右前+后轮毂 --三视图
    AVM3D_VIEW_FULL_SCREEN_3D_FRONT_REAR_HUBVIEW,           // 85：3D前+后轮毂   --三视图
    AVM3D_VIEW_FULL_SCREEN_3D_ANGLE_FRONT_HUBVIEW,          // 86：3D保持最后一次的角度+前轮毂 --三视图
    AVM3D_VIEW_FULL_SCREEN_3D_ANGLE_REAR_HUBVIEW,           // 87：3D保持最后一次的角度+后轮毂 --三视图

    AVM3D_VIEW_FULL_SCREEN_2D_FRONT_ZOOM_FRONT_HUBVIEW,    // 88：车头俯瞰放大+前轮毂 --三视图
    AVM3D_VIEW_FULL_SCREEN_2D_REAR_ZOOM_REAR_HUBVIEW,      // 89：车尾俯瞰放大+后轮毂 --三视图

    AVM3D_VIEW_FULL_SCREEN_2D_FRONT_RIGHT_REAR,          // 90：2D前视+右后视
    AVM3D_VIEW_FULL_SCREEN_2D_FRONT_LEFT_REAR,           // 91：2D前视+左后视
    AVM3D_VIEW_FULL_SCREEN_2D_REAR_RIGHT_REAR,           // 92：2D后视+右后视
    AVM3D_VIEW_FULL_SCREEN_2D_REAR_LEFT_REAR,            // 93：2D后视+左后视
    AVM3D_VIEW_FULL_SCREEN_2D_WIDE_FRONT_LEFT_REAR,      // 94：2D前广角+左后视
    AVM3D_VIEW_FULL_SCREEN_2D_WIDE_FRONT_RIGHT_REAR,     // 95：2D前广角+右后视
    AVM3D_VIEW_FULL_SCREEN_2D_WIDE_REAR_LEFT_REAR,       // 96：2D后广角+左后视
    AVM3D_VIEW_FULL_SCREEN_2D_WIDE_REAR_RIGHT_REAR,      // 97：2D后广角+右后视
    AVM3D_VIEW_FULL_SCREEN_3D_LEFT_REAR_RIGHT_REAR,      // 98：3D左后+右后视
    AVM3D_VIEW_FULL_SCREEN_3D_RIGHT_REAR_RIGHT_REAR,     // 99：3D右后+右后视
    AVM3D_VIEW_FULL_SCREEN_3D_REAR_RIGHT_REAR,           // 100：3D后+右后视
    AVM3D_VIEW_FULL_SCREEN_3D_LEFT_RIGHT_REAR,           // 101：3D左+右后视
    AVM3D_VIEW_FULL_SCREEN_3D_RIGHT_RIGHT_REAR,          // 102：3D右+右后视
    AVM3D_VIEW_FULL_SCREEN_3D_LEFT_FRONT_RIGHT_REAR,     // 103：3D左前+右后视
    AVM3D_VIEW_FULL_SCREEN_3D_RIGHT_FRONT_RIGHT_REAR,    // 104：3D右前+右后视
    AVM3D_VIEW_FULL_SCREEN_3D_FRONT_RIGHT_REAR,          // 105：3D前+右后视
    AVM3D_VIEW_FULL_SCREEN_3D_LEFT_REAR_LEFT_REAR,       // 106：3D左后+左后视
    AVM3D_VIEW_FULL_SCREEN_3D_RIGHT_REAR_LEFT_REAR,      // 107：3D右后+左后视
    AVM3D_VIEW_FULL_SCREEN_3D_REAR_LEFT_REAR,            // 108：3D后+左后视
    AVM3D_VIEW_FULL_SCREEN_3D_LEFT_LEFT_REAR,            // 109：3D左+左后视
    AVM3D_VIEW_FULL_SCREEN_3D_RIGHT_LEFT_REAR,           // 110：3D右+左后视
    AVM3D_VIEW_FULL_SCREEN_3D_LEFT_FRONT_LEFT_REAR,      // 111：3D左前+左后视
    AVM3D_VIEW_FULL_SCREEN_3D_RIGHT_FRONT_LEFT_REAR,     // 112：3D右前+左后视
    AVM3D_VIEW_FULL_SCREEN_3D_FRONT_LEFT_REAR,           // 113：3D前+左后视

    AVM3D_VIEW_FULL_SCREEN_2D_FRONT_ZOOM_LEFT_REAR,     // 114：车头俯瞰放大+左后视
    AVM3D_VIEW_FULL_SCREEN_2D_FRONT_ZOOM_RIGHT_REAR,    // 115：车头俯瞰放大+右后视
    AVM3D_VIEW_FULL_SCREEN_2D_REAR_ZOOM_LEFT_REAR,      // 116：车尾俯瞰放大+左后视
    AVM3D_VIEW_FULL_SCREEN_2D_REAR_ZOOM_RIGHT_REAR,     // 117：车尾俯瞰放大+右后视

    AVM3D_VIEW_FULL_SCREEN_3D_ANGLE_LEFT_REAR,     // 118：3D保持最后一次的角度+左后视
    AVM3D_VIEW_FULL_SCREEN_3D_ANGLE_RIGHT_REAR,    // 119：3D保持最后一次的角度+右后视

    AVM3D_VIEW_FULL_SCREEN_2D_HORIZONTAL_MOVE,     // 120：2D横移视图
    AVM3D_VIEW_APA_FRONT_HUBVIEW , // 121: APA视图-窄道功能-前双轮毂
    AVM3D_VIEW_FULL_SCREEN_2D_CRAB_FRONT, //122: 蟹行视图前
    AVM3D_VIEW_FULL_SCREEN_2D_CRAB_REAR, //123: 蟹行视图后
    AVM3D_VIEW_SETTING,            // 124：设置界面

    AVM3D_VIEW_ID_MAX,

    /*------------------------------------------------------分割线------------------------------------------------------*/
    /*-----------------------------------------------st2横屏项目算法视图id-----------------------------------------------*/
    AVM_VIEW_LANDSCAPE_FULL_SCREEN_2D_FRONT = 10000, // 10000: AVM2D全景前视
    AVM_VIEW_LANDSCAPE_FULL_SCREEN_2D_REAR, // 10001: AVM2D全景后视
    AVM_VIEW_LANDSCAPE_FULL_SCREEN_2D_LEFT_FRONT, // 10002: AVM2D全景-左前视图
    AVM_VIEW_LANDSCAPE_FULL_SCREEN_2D_LEFT_REAR, // 10003: AVM2D全景-左后视图
    AVM_VIEW_LANDSCAPE_FULL_SCREEN_2D_RIGHT_FRONT, // 10004: AVM2D全景-右前视图
    AVM_VIEW_LANDSCAPE_FULL_SCREEN_2D_RIGHT_REAR, // 10005: AVM2D全景-右后视图
    AVM_VIEW_LANDSCAPE_FULL_SCREEN_2D_FRONT_HUBVIEW_FRONT, // 10006: AVM2D全景-前+前双视---三视图
    AVM_VIEW_LANDSCAPE_FULL_SCREEN_2D_FRONT_HUBVIEW_REAR, // 10007: AVM2D全景-前+后双视---三视图
    AVM_VIEW_LANDSCAPE_FULL_SCREEN_2D_REAR_HUBVIEW_FRONT, // 10008: AVM2D全景-后+前双视---三视图
    AVM_VIEW_LANDSCAPE_FULL_SCREEN_2D_REAR_HUBVIEW_REAR, // 10009: AVM2D全景-后+后双视---三视图
    AVM_VIEW_LANDSCAPE_FULL_SCREEN_2D_HUBVIEW_FRONT, // 10010: AVM2D全景-前双视---双视图
    AVM_VIEW_LANDSCAPE_FULL_SCREEN_2D_HUBVIEW_FULL, // 10011: AVM2D全景-全双视---双视图
    AVM_VIEW_LANDSCAPE_FULL_SCREEN_2D_HUBVIEW_REAR, // 10012: AVM2D全景-后双视---双视图
    AVM_VIEW_LANDSCAPE_FULL_SCREEN_2D_FRONT_ZOOM_LEFT_REAR, // 10013: AVM2D全景-车头俯瞰放大
    AVM_VIEW_LANDSCAPE_FULL_SCREEN_2D_FRONT_ZOOM_RIGHT_REAR, // 10014: AVM2D全景-车尾俯瞰放大
    AVM_VIEW_LANDSCAPE_FULL_SCREEN_3D, // 10015: AVM3D视图
    AVM_VIEW_LANDSCAPE_FLOAT_WINDOW_TOP, // 10016: 画中画俯瞰图
    AVM_VIEW_LANDSCAPE_FLOAT_WINDOW_LEFT_FRONT, // 10017: 画中画左前视图
    AVM_VIEW_LANDSCAPE_FLOAT_WINDOW_LEFT_REAR, // 10018: 画中画左后视图
    AVM_VIEW_LANDSCAPE_FLOAT_WINDOW_RIGHT_FRONT, // 10019: 画中画右前视图
    AVM_VIEW_LANDSCAPE_FLOAT_WINDOW_RIGHT_REAR, // 10020: 画中画右后视图
    AVM_VIEW_LANDSCAPE_APA_DRIVING_AERIAL, // 10021: APA鸟瞰图---APA行车
    AVM_VIEW_LANDSCAPE_APA_DRIVING_FRONT, // 10022: APA视图-前视图---APA行车
    AVM_VIEW_LANDSCAPE_APA_DRIVING_BACK, // 10023: APA视图-后视图---APA行车
    AVM_VIEW_LANDSCAPE_APA_PARK_FRONT_HUBVIEW_FRONT, // 10024: APA单视图-前+前双视---APA泊车
    AVM_VIEW_LANDSCAPE_APA_PARK_FRONT_HUBVIEW_REAR, // 10025: APA单视图-前+后双视---APA泊车
    AVM_VIEW_LANDSCAPE_APA_PARK_REAR_HUBVIEW_FRONT, // 10026: APA单视图-后+前双视---APA泊车
    AVM_VIEW_LANDSCAPE_APA_PARK_REAR_HUBVIEW_REAR, // 10027: APA单视图-后+后双视---APA泊车
    AVM_VIEW_LANDSCAPE_APA_PARK_AERIAL, // 10028: APA俯视图---APA泊车
    AVM_VIEW_LANDSCAPE_REMOTEVISION_FRONT, // 10029: 千里眼前视
    AVM_VIEW_LANDSCAPE_REMOTEVISION_REAR, // 10030: 千里眼后视
    AVM_VIEW_LANDSCAPE_REMOTEVISION_LEFT, // 10031: 千里眼左视
    AVM_VIEW_LANDSCAPE_REMOTEVISION_RIGHT, // 10032: 千里眼右视
    AVM_VIEW_LANDSCAPE_FULL_SCREEN_2D_ROOF_FRONT, // 10033: AVM2D测高
    AVM_VIEW_LANDSCAPE_ID_MAX,
};

enum EWindowId
{
    E_WINDOW_ID__MAIN = 0, //中控
    E_WINDOW_ID__EXT,
    E_WINDOW_ID__BSA_L_REAR,
    E_WINDOW_ID__BSA_R_REAR,
    E_WINDOW_ID__BUTT /*invalid value*/
};

enum class AVMTouchEventType {
    AVM_TOUCH_SINGLE_MOVE   = 0,
    AVM_TOUCH_DOUBLE_MOVE   = 1,
    AVM_TOUCH_UP            = 2,
    AVM_TOUCH_DOWN          = 3,
    AVM_TOUCH_DOUBLE_START  = 4,
    AVM_TOUCH_DOUBLE_FINISH = 5,
};

enum class CalibrationMode {
    AUTOMATIC_CALIBRATION, //自动标定
    ROAD_CALIBRATION, //road 标定
};

enum class CarDoorId
{
    AVM_DOOR_FRONT_LEFT, // 左前门
    AVM_DOOR_FRONT_RIGHT, // 右前门
    AVM_DOOR_BACK_LEFT, // 左后门
    AVM_DOOR_BACK_RIGHT, // 右后门
    AVM_DOOR_FRONT_COVER, // 前引擎盖
    AVM_DOOR_BACK_COVER, // 后备箱盖
};

enum class CarDoorStatus {
    AVM_CLOSE, AVM_OPEN, AVM_NONE
};

enum class CarGuideLine {
    NO_HAVE_LINE,
    FRONT_LINE,
    REAR_LINE,
    NO_HAVE_FRONT_LINE,
    NO_HAVE_REAR_LINE,
    TRAIL_CAR_LINE,
    ALL_GUIDE_LINE,
    NO_TRAIL_CAR_LINE,
    FRONT_AND_TRAIL_LINE,
    REAR_AND_TRAIL_LINE,
    ALL_AND_TRAIL_LINE,
    NO_FRONT_AND_TRAIL_LINE,
    NO_REAR_AND_TRAIL_LINE,
    NO_ALL_AND_TRAIL_LINE
};

enum class AVMLightType
{
    AVM_LIGHT_LEFT, // 左转向灯
    AVM_LIGHT_RIGHT, // 右转向灯
    AVM_LIGHT_DAYTIME, // 昼行灯
    AVM_LIGHT_NEAR, // 近光灯
    AVM_LIGHT_FAR, // 远光灯
    AVM_LIGHT_FRONT_POSITION, // 前位置灯
    AVM_LIGHT_REAR_POSITION, // 后位置灯
    AVM_LIGHT_PROFILE, // 示廓灯
    AVM_LIGHT_FRONT_PROFILE, // 前示廓灯
    AVM_LIGHT_BACK_PROFILE, // 后示廓灯
    AVM_LIGHT_TAG, // 牌照灯
    AVM_LIGHT_REVERSE, // 倒车灯
    AVM_LIGHT_REAR_FOG, // 后雾灯
    AVM_LIGHT_BRAKE, // 制动灯常亮
    AVM_LIGHT_DOUBLE, // 双闪
    AVM_LIGHT_DAYTIME_LEFT, // 左昼行灯
    AVM_LIGHT_DAYTIME_RIGHT, // 右昼行灯
    AVM_LIGHT_WELPRE, // 大灯迎宾预览
    AVM_LIGHT_LEFT_CORNER, //左角灯
    AVM_LIGHT_RIGHT_CORNER, //右角灯
    AVM_LIGHT_BRAKE_FLASH, //制动灯闪烁
    AVM_LIGHT_MAX, // 灯数目
};

enum class AVMState
{
    AVM_CLOSE, AVM_OPEN, AVM_NONE
};

enum class AVMRearViewMirrorState
{
    UNFOLDED, FOLDED, NONE
};

enum class CarGearStatus
{
    E_ALR_GEAR__P = 1,
    E_ALR_GEAR__R,
    E_ALR_GEAR__N,
    E_ALR_GEAR__D,
};

enum class CarColor {
    CAMOUFLAGE_GREEN = 0,
    DEMON_BLACK,
    SUNRISE_GOLD,
    SNOW_WHITE,
    BLACK_GOLD,
    SGHC_HERMES_GREEN,
    SGHC_OBSIDIAN_BLACK,
    SGHC_SNOW_WHITHE,
    MAX_COLOR,
};

enum class AVMParkMode
{
    AVM_PARK_EXIT, // 退出泊车模式
    AVM_PARK_360, // 360 模式
    AVM_PARK_LIMITED_HORIZONTAL, // 非360 模式,水平
    AVM_PARK_LIMITED_VERTICAL, // 非360 模式，垂直
    AVM_PARK_LIMITED_OBLIQUE, // 非360 模式，倾斜45 度
    AVM_PARK_PARKING_START, // 控车阶段，开始泊入目标车位
    AVM_PARK_PARKING_FINISH, // 泊入目标车位完成
    AVM_PARK_NON_STATIONARY, // 车辆处于非静止状态，不显示自选车位框
};

enum AVMParkSlotState
{
    AVM_PARK_SLOT_UNAVAILABLE, // 自选车位不可泊
    AVM_PARK_SLOT_AVAILABLE, // 自选车位可泊
    AVM_PARK_SLOT_UNAVAILABLE_BARRIER, //自选车位有障碍物
};

enum AVMDoorType
{
    AVM_DOOR_FRONT_LEFT,     // 左前门
    AVM_DOOR_FRONT_RIGHT,    // 右前门
    AVM_DOOR_BACK_LEFT,      // 左后门
    AVM_DOOR_BACK_RIGHT,     // 右后门
    AVM_DOOR_FRONT_COVER,    // 前引擎盖
    AVM_DOOR_BACK_COVER,     // 后备箱盖
    AVM_REARVIEW_MIRROR,    // 后视镜
};

enum AVMZoomLevel
{
    AVM_ZOOM_LEVEL0, // 缩小等级3 50%, 请直接设置为3D 广角视图，不要用这个值
    AVM_ZOOM_LEVEL1, // 缩小等级2 70%
    AVM_ZOOM_LEVEL2, // 缩小等级1 85%
    AVM_ZOOM_LEVEL3, // 无缩放
    AVM_ZOOM_LEVEL4, // 放大等级1 110%
    AVM_ZOOM_LEVEL5, // 放大等级2 125%
    AVM_ZOOM_LEVEL6, // 放大等级3 150%
};

enum AVMLogType
{
AVM_LOG_ERROR,
AVM_LOG_WARN,
AVM_LOG_INFO,
AVM_LOG_DEBUG
};

struct Point  // TODO, not defined in the pdf
{
    float m_x;
    float m_y;
};
struct AVMParkingSlotVertex
{
    Point point1; // point1 和point4 之间留有开口
    Point point2;
    Point point3;
    Point point4; // point1 和point4 之间留有开口
};

enum class CanChangeSizeView
{
    TOP_2D_VIEW, // TOP_2D_VIEW 需要改变 2D 俯瞰图的视野大小
    ALL_3D_VIEW, // ALL_3D_VIEW 需要改变所有 3D 视图的视野大小
};


enum class OptionalSlotType {
    HORIZONTAL_SLOT, // 水平车位
    VERTICAL_SLOT, // 垂直车位
    OBLIQUE_SLOT, // 斜向车位
};

enum class CameraName {
    FrontCamera,
    RearCamera,
    LeftCamera,
    RightCamera
};

class onStatusChangeCb {
public:
    virtual ~onStatusChangeCb() = default;
    virtual void onStatusChange(int status) = 0;
};

class onMagicChangedCb {
public:
    virtual ~onMagicChangedCb() = default;
    virtual void onMagicChanged(int status) = 0;
};
enum class WheelRailMode {
    FRONT_OPEN, //开启前轮轨
    REAR_OPEN, //开启后轮轨
    CLOSE, //关闭轮轨模式
};

enum class CanMoveView {
    TOP_VIEW, //俯视角
    SINGLE_VIEW //单视角
};

enum class AVMAnimationCallback
{
    AVM_ANI_SLIDE_LEFT = 0,         // 划动归位左视图
    AVM_ANI_SLIDE_RIGHT,            // 划动归位右视图
    AVM_ANI_DEFAULT_FULL_START,     // 默认鸟瞰图到全屏鸟瞰图开始
    AVM_ANI_DEFAULT_FULL_STOP,      // 默认鸟瞰图到全屏鸟瞰图结束
    AVM_ANI_FULL_DEFAULT_START,     // 全屏鸟瞰图到默认鸟瞰图开始
    AVM_ANI_FULL_DEFAULT_STOP,      // 全屏鸟瞰图到默认鸟瞰图结束
    AVM_ANI_SLIDE_FRONT_HUBVIEW,    // 划动归位前轮毂
    AVM_ANI_SLIDE_REAR_HUBVIEW,     // 划动归位后轮毂
};

struct CameraParameter
{
    char name[32];
    float cx;
    float cy;
    float fx;
    float fy;
    float k1;
    float k2;
    float k3;
    float k4;
};

enum class AVMTransparentMode
{
    AVM_FULLY_TRANSPARENT, // 3D 晶透，2D 微透
    AVM_SIMPLE, // 3D 极简，2D 全透
    AVM_NOT_TRANSPARENT, // 不透
};

struct BoundCoordinate {
    // A
    int x1 = 0;
    int y1 = 0;
    // B
    int x2 = 0;
    int y2 = 0;
    // C
    int x3 = 0;
    int y3 = 0;
    // D
    int x4 = 0;
    int y4 = 0;
};

struct CrabAngle
{
    int   isShowguideline;   //是否画轨迹线 1:显示, 0:不显示
    float angle;             //智能蟹行角度（"正"顺时针/"负"逆时针）
    // float maxAngle;          //最大蟹行角度
    float leftrearwheelangle; //左后轮实际转动角度
    float rightrearwheelangle; //右后轮实际转动角度
};

enum class VotState
{
    VOT_INVALID,
    VOT_NOREQUEST,
    VOT_REQUEST,
    VOT_RESERVED
};

enum class VotDirRotation
{
    VOT_ROTATION_INIT,
    VOT_ROTATION_CENTROID_ANTICLOCKWISE,
    VOT_ROTATION_CENTROID_CLOCKWISE,
    VOT_ROTATION_REAR_AXLE_CENTER_ANTICLOCKWISE,
    VOT_ROTATION_REAR_AXLE_CENTER_CLOCKWISE,
    VOT_ROTATION_LEFT_FRONT_WHEEL_ANTICLOCKWISE,
    VOT_ROTATION_LEFT_FRONT_WHEEL_CLOCKWISE,
    VOT_ROTATION_RIGHT_FRONT_WHEEL_ANTICLOCKWISE,
    VOT_ROTATION_RIGHT_FRONT_WHEEL_CLOCKWISE,
    VOT_ROTATION_LEFT_REAR_WHEEL_ANTICLOCKWISE,
    VOT_ROTATION_LEFT_REAR_WHEEL_CLOCKWISE,
    VOT_ROTATION_RIGHT_REAR_WHEEL_ANTICLOCKWISE,
    VOT_ROTATION_RIGHT_REAR_WHEEL_CLOCKWISE,
    VOT_ROTATION_RESERVED
};

enum class CarLogoColor
{
    SILVER,
    GOLD,
    MAX_COLOR
};

#endif //RENDERNATIVE_COMDATASTRUCTURE_H
