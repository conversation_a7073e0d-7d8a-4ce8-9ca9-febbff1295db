//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by Robert <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================

#include "cc/views/callback/inc/TrailerTrajectoryUpdateCallback.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/util/logging/inc/LoggingContexts.h"

using pc::util::logging::g_AppContext;
namespace cc
{
namespace views
{
namespace callback
{

TrailerTrajectoryUpdateCallback::TrailerTrajectoryUpdateCallback(pc::core::Framework* f_pFramework)
    : m_pFramework(f_pFramework)
{
}

// TrailerTrajectoryUpdateCallback::~TrailerTrajectoryUpdateCallback()
// {
// }

void TrailerTrajectoryUpdateCallback::onToggleTrailerMode(pc::core::View* f_view)
{
    if (nullptr == f_view)
    {
        return;
    }
    f_view->setAssetValue(cc::core::AssetId::EASSETS_TRAJECTORY_OUTERMOST_LINES, !m_isTrailerMode);
    f_view->setAssetValue(cc::core::AssetId::EASSETS_TRAJECTORY_OUTERMOST_LINES_COLORFUL, !m_isTrailerMode);
    f_view->setAssetValue(cc::core::AssetId::EASSETS_TRAJECTORY_WHEEL_TRACKS, !m_isTrailerMode);
    f_view->setAssetValue(cc::core::AssetId::EASSETS_TRAJECTORY_ACTION_POINTS, !m_isTrailerMode);
    f_view->setAssetValue(cc::core::AssetId::EASSETS_TRAJECTORY_DL1, !m_isTrailerMode);
    f_view->setAssetValue(cc::core::AssetId::EASSETS_TRAJECTORY_COVERPLATE, !m_isTrailerMode);
    f_view->setAssetValue(cc::core::AssetId::EASSETS_TRAJECTORY_REFLINE, !m_isTrailerMode);
    f_view->setAssetValue(cc::core::AssetId::EASSETS_FISHEYE_OUTERMOST_LINES, !m_isTrailerMode);
    f_view->setAssetValue(cc::core::AssetId::EASSETS_FISHEYE_OUTERMOST_LINES_COLORFUL, !m_isTrailerMode);
    f_view->setAssetValue(cc::core::AssetId::EASSETS_FISHEYE_WHEELTRACKS, !m_isTrailerMode);
    f_view->setAssetValue(cc::core::AssetId::EASSETS_FISHEYE_COVERPLATE, !m_isTrailerMode);
    f_view->setAssetValue(cc::core::AssetId::EASSETS_FISHEYE_DL1, !m_isTrailerMode);
    f_view->setAssetValue(cc::core::AssetId::EASSETS_FISHEYE_DL1_COLORFUL, !m_isTrailerMode);
    f_view->setAssetValue(cc::core::AssetId::EASSETS_FISHEYE_RCTAOVERLAY, !m_isTrailerMode);

    f_view->setAssetValue(cc::core::AssetId::EASSETS_TRAJECTORY_TRAILER_HITCH, m_isTrailerMode);
    f_view->setAssetValue(cc::core::AssetId::EASSETS_TRAJECTORY_TRAILER_TRAJECTORY, m_isTrailerMode);
    f_view->setAssetValue(cc::core::AssetId::EASSETS_HITCH_ASSIST, m_isTrailerMode);
}

void TrailerTrajectoryUpdateCallback::setEnable(pc::core::View* f_view, vfc::int32_t f_assetId, bool f_enable)
{
    if (nullptr == f_view)
    {
        return;
    }
    const auto asset = f_view->getAsset(static_cast<cc::core::AssetId>(f_assetId));
    if (asset != nullptr)
    {
        f_view->setAssetValue(static_cast<cc::core::AssetId>(f_assetId), f_enable);
    }
}

void TrailerTrajectoryUpdateCallback::operator()(osg::Node* f_node, osg::NodeVisitor* f_nv)
{
    if (nullptr == f_node)
    {
        return;
    }
    if (f_nv->getVisitorType() == osg::NodeVisitor::UPDATE_VISITOR)
    {
        // set the virtual camera position to match the front SatCam
        if (!m_initialized)
        {
            XLOG_INFO(g_AppContext, "TrailerTrajectoryUpdateCallback: initializing m_isTrailerMode: "
                                       << m_isTrailerMode << ((m_isTrailerMode) ? " showing " : " not showing ")
                                       << "trailer trajectory" );
            const auto view = dynamic_cast<pc::core::View*>(f_node);
            if (view != nullptr)
            {
                onToggleTrailerMode(view);
            }
            m_initialized = true;
        }
        if (m_pFramework->asCustomFramework()->m_HUTrailerButtonReceiver.hasData())
        {
            const bool previousTrailerMode = m_isTrailerMode;
            m_isTrailerMode          = m_pFramework->asCustomFramework()->m_HUTrailerButtonReceiver.getData()->m_Data;

            if (m_isTrailerMode != previousTrailerMode)
            {
                XLOG_INFO(g_AppContext
                    , "TrailerTrajectoryUpdateCallback: new m_isTrailerMode received: " << m_isTrailerMode
                    << ((m_isTrailerMode) ? " showing " : " not showing ") << "trailer trajectory");
                const auto view = dynamic_cast<pc::core::View*>(f_node);
                if (view != nullptr)
                {
                    onToggleTrailerMode(view);
                }
            }
        }
    }

    traverse(f_node, f_nv);
}

} // namespace callback
} // namespace views
} // namespace cc
