//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: BYD
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: NVA2HC NGUYEN DUC THIEN Van (CN/ESC-EPA1)
//  Department: CN/ESC
//=============================================================================
/// @swcomponent SVS BYD
/// @file  DynamicGearOverlays.cpp
/// @brief
//=============================================================================

#include "pc/svs/core/inc/Framework.h"
#include "pc/svs/core/inc/SystemConf.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"
#include "pc/svs/core/inc/ShaderManager.h"

#include "cc/assets/dynamicgearoverlay/inc/dynamicgearoverlay.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "pc/svs/util/math/inc/FloatComp.h"
#include "osg/LineWidth"
#include "osg/BlendFunc"

#include <iomanip>
#include <iostream>
#include <sstream>

namespace cc
{
namespace assets
{
namespace dynamicgearoverlay
{

pc::util::coding::Item<DynamicGearSettings> g_dynamicgearSettings("DynamicGear");

//!
//! DynamicGearOverlays
//!
DynamicGearOverlays::DynamicGearOverlays(cc::core::CustomFramework* f_customFramework)
  : osg::MatrixTransform() // PRQA S 4050
  , m_cull(false)
  , m_hide(false)
  , m_sequenceNumber(0u)
  , m_DynamicGearVertexArray(nullptr)
  , m_lastUpdate(0LL)
  , m_customFramework(f_customFramework)

{
    //osg::StateSet* l_stateSet = getOrCreateStateSet();
    //l_stateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF); // PRQA S 3143
    //pc::core::TextureShaderProgramDescriptor l_basicTexShader("colorText");
    //l_basicTexShader.apply(l_stateSet);  // PRQA S 3803

    setName("DynamicGearOverlays");

    DynamicGear* const l_DynamicGear = new DynamicGear(f_customFramework);
    osg::MatrixTransform::addChild(l_DynamicGear);


    // set to none-render by default
    //setHide(false);

    setNumChildrenRequiringUpdateTraversal(getNumChildrenRequiringUpdateTraversal() + 1);

}

DynamicGearOverlays::~DynamicGearOverlays() = default;

bool DynamicGearOverlays::update(pc::core::Framework* /*f_framework*/)
{
    // todo
    return false;
}


class DynamicGearCullCallback : public osg::Drawable::CullCallback
{
public:

  explicit DynamicGearCullCallback(DynamicGear* f_DynamicGear)
    : m_DynamicGear(f_DynamicGear)
  {
  }

  bool cull(osg::NodeVisitor* /* f_nv */, osg::Drawable* /* f_drawable */, osg::RenderInfo* /* f_renderInfo */) const override    // PRQA S 2120
  {
    return m_DynamicGear->isCulled();
  }

protected:

  DynamicGear* m_DynamicGear;

};

//!
//! DynamicGearHideCallback
//!
class DynamicGearHideCallback : public DynamicGearCullCallback
{
public:

  explicit DynamicGearHideCallback(DynamicGear* f_DynamicGear)
    : DynamicGearCullCallback(f_DynamicGear)
  {
  }

  bool cull(osg::NodeVisitor* /* f_nv */, osg::Drawable* /* f_drawable */, osg::RenderInfo* /* f_renderInfo */) const override
  {
    if(m_DynamicGear->isCulled() || m_DynamicGear->isHidden())
    {
      return true;
    }
    else
    {
      return false;
    }
  }

};


DynamicGear::DynamicGear(cc::core::CustomFramework* f_customFramework)
    : osg::Geode()
    , m_cull(false)
    , m_hide(false)
    , m_theme(cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI)
    , m_requireUpdateGeode(false)
    , m_DynamicGearVertexArray(new osg::Vec3Array(static_cast<vfc::uint32_t>(CIRCLE_NUM_SEGMENTS)*4u))
    , m_position(g_dynamicgearSettings->m_positionHori)
    , m_PtrColor(new osg::Vec4Array(1u))
    , m_color(g_dynamicgearSettings->m_color)
    , m_customFramework(f_customFramework)
    , m_progress_percentage(0.0f)
    , m_isNewStep(false)
    , m_APG_Output_MoveNumber(0u)
    , m_APG_Output_TravelDistDesired_max(5000u)
    , m_modifiedCount(~0u)
{
    osg::Vec4Array* const l_Color = new osg::Vec4Array(static_cast<vfc::uint32_t>(CIRCLE_NUM_SEGMENTS)*4u);
    for (vfc::uint32_t i = 0u; i < static_cast<vfc::uint32_t>(CIRCLE_NUM_SEGMENTS)*4u; i=i+4)
    {
      (*l_Color)[i] = m_color;
    }
    //draw circle
    //define vertex array
    drawCircle();
    //! create fill geometry
    osg::Geometry* const l_Circle = pc::util::osgx::createGeometry("DynamicGearOverlay");
    m_DynamicGearVertexArray->dirty();
    l_Circle->setVertexArray(m_DynamicGearVertexArray);
    l_Circle->addPrimitiveSet(new osg::DrawArrays(osg::PrimitiveSet::QUAD_STRIP, 0, static_cast<vfc::uint32_t>(CIRCLE_NUM_SEGMENTS)*4)); // PRQA S 3803
    //l_Circle->setCullCallback(new DynamicGearHideCallback(this));
    l_Circle->setColorArray(l_Color, osg::Array::BIND_PER_VERTEX);

    osg::StateSet* const l_commonStateSet = getOrCreateStateSet();
    l_commonStateSet->setMode(GL_BLEND, osg::StateAttribute::ON); // PRQA S 3143
    //l_commonStateSet->setAttribute(new osg::BlendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA));
    l_commonStateSet->setRenderingHint(osg::StateSet::TRANSPARENT_BIN);
    l_commonStateSet->setRenderBinDetails(core::RENDERBIN_ORDER_OVERLAYS, "RenderBin");

    l_commonStateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF); // PRQA S 3143

    //! disable depth buffer write
    // osg::Depth* l_depth = new osg::Depth;
    // l_depth->setWriteMask(false);
    // l_commonStateSet->setAttributeAndModes(l_depth, osg::StateAttribute::OFF); // PRQA S 3143
    // l_commonStateSet->setAttribute(new osg::LineWidth(20));

    // pc::core::BasicShaderProgramDescriptor l_dynamichGearShader("dynamichGear",
    // "dynamichGear");
    // l_dynamichGearShader.apply(l_commonStateSet);    // PRQA S 3803

    l_Circle->setStateSet(l_commonStateSet);

    osg::Geode::addDrawable(l_Circle); // PRQA S 3803
}


void DynamicGear::drawCircle()
{

  vfc::float32_t l_startAngle = 0.f;
  vfc::float32_t l_horiToVertScaling = 1.0f;

  if (m_theme == cc::target::common::EThemeTypeHU::ETHEME_TYPE_VERT)
  {
    l_startAngle += static_cast<vfc::float32_t>(0.5*osg::PI); // rotate 90 degree
    l_horiToVertScaling = g_dynamicgearSettings->m_horiToVertScaling;
  }

  const vfc::float32_t l_delta = 2.0f * static_cast<vfc::float32_t>(osg::PI) / static_cast<vfc::float32_t> (CIRCLE_NUM_SEGMENTS);
  const vfc::float32_t l_outerRadius = g_dynamicgearSettings->m_outerRadius*l_horiToVertScaling;
  const vfc::float32_t l_innerRadius = g_dynamicgearSettings->m_innerRadius*l_horiToVertScaling;
  const vfc::float32_t l_innerPerentage = l_innerRadius/l_outerRadius;


  for (vfc::uint32_t i = 0u; i < static_cast<vfc::uint32_t>(CIRCLE_NUM_SEGMENTS)*4u; i=i+4)
  {
    const vfc::float32_t l_angle1 = static_cast<vfc::float32_t>(i)*0.25f        * l_delta + l_startAngle;
    const vfc::float32_t l_angle2 = (static_cast<vfc::float32_t>(i)*0.25f+1.0f) * l_delta + l_startAngle;
    const vfc::float32_t l_x1 = l_outerRadius * std::cos(l_angle1);
    const vfc::float32_t l_y1 = l_outerRadius * std::sin(l_angle1);
    const vfc::float32_t l_x2 = l_outerRadius * std::cos(l_angle2);
    const vfc::float32_t l_y2 = l_outerRadius * std::sin(l_angle2);

    (*m_DynamicGearVertexArray)[i]   = osg::Vec3f(m_position.x() + l_x1*l_innerPerentage, m_position.y() + l_y1*l_innerPerentage, 0.0f);
    (*m_DynamicGearVertexArray)[i+1u] = osg::Vec3f(m_position.x() + l_x1,                  m_position.y() + l_y1,                  0.0f);
    (*m_DynamicGearVertexArray)[i+2u] = osg::Vec3f(m_position.x() + l_x2*l_innerPerentage, m_position.y() + l_y2*l_innerPerentage, 0.0f);
    (*m_DynamicGearVertexArray)[i+3u] = osg::Vec3f(m_position.x() + l_x2,                  m_position.y() + l_y2,                  0.0f);
  }
//   return m_DynamicGearVertexArray;

}


DynamicGear::~DynamicGear() = default;


void DynamicGear::update()
{
  vfc::uint8_t   l_CarmoveNumber           = 0u;
  vfc::int16_t   l_CartravelDistDesire     = 0;
  bool           l_dynamicGearStatus       = false;
  cc::target::common::EThemeTypeHU   l_curThemeType            = cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI;

  if (m_modifiedCount != g_dynamicgearSettings->getModifiedCount())
  {
    m_position = (m_theme == cc::target::common::EThemeTypeHU::ETHEME_TYPE_VERT) ? g_dynamicgearSettings->m_positionVert : g_dynamicgearSettings->m_positionHori;
    m_requireUpdateGeode = true;
    m_modifiedCount = g_dynamicgearSettings->getModifiedCount();
  }

  if (m_customFramework->m_dynamicGearStatus_ReceiverPort.hasData())
  {
    const cc::daddy::DynamicGearActive_t* const l_dynamicGearStatusPtr = m_customFramework->m_dynamicGearStatus_ReceiverPort.getData();
    l_dynamicGearStatus = l_dynamicGearStatusPtr->m_Data;
  }

  if (m_customFramework->m_SVSRotateStatusDaddy_Receiver.hasData())
  {
    const cc::daddy::SVSRotateStatusDaddy_t* const l_themeType = m_customFramework->m_SVSRotateStatusDaddy_Receiver.getData();
    l_curThemeType = static_cast<cc::target::common::EThemeTypeHU>(l_themeType->m_Data); // PRQA S 3013
  }

  if(m_customFramework->m_parkCarmoveNumberReceiver.hasData())
  {
    const cc::daddy::ParkCarmoveNumberDaddy_t* const l_parkCarmoveNumber = m_customFramework->m_parkCarmoveNumberReceiver.getData();
    l_CarmoveNumber = l_parkCarmoveNumber->m_Data;
  }

  if (m_customFramework->m_parkCartravelDistDesiredReceiver.hasData())
  {
    const cc::daddy::ParkCartravelDistDesiredDaddy_t* const l_parkCartravelDistDesire = m_customFramework->m_parkCartravelDistDesiredReceiver.getData();
    l_CartravelDistDesire = l_parkCartravelDistDesire->m_Data;
  }

  if (l_dynamicGearStatus == true)
  {
    setNodeMask(~0u);
  }
  else
  {
    setNodeMask(0u);
    m_progress_percentage = 0.0f;
  }

  if (l_curThemeType != m_theme)
  {
    m_position = (l_curThemeType == cc::target::common::EThemeTypeHU::ETHEME_TYPE_VERT) ? g_dynamicgearSettings->m_positionVert : g_dynamicgearSettings->m_positionHori;
    m_theme = l_curThemeType;
    m_requireUpdateGeode = true;
  }

  // refresh condition
  if (l_CarmoveNumber == 0u)
  {
    m_APG_Output_MoveNumber = l_CarmoveNumber;
    m_progress_percentage = 0.0f;
  }

  // Adding fexible condition to prevent issue from APA.
  // Actually we expect that l_CarmoveNumber = m_APG_Output_MoveNumber + 1
  if (l_CarmoveNumber > m_APG_Output_MoveNumber)
  {
    m_APG_Output_TravelDistDesired_max = l_CartravelDistDesire;
    m_APG_Output_MoveNumber = m_APG_Output_MoveNumber + 1;  //PRQA S 3010
    m_isNewStep = true;
    m_progress_percentage = 0.0f;
  }

  if ( m_isNewStep == true )
  {
      if(std::abs(l_CartravelDistDesire) > std::abs(m_APG_Output_TravelDistDesired_max) )
      {
        //m_isNewStep = false;
        // during vehicle test, we found that the current distance could be greater than the max distance for the short time
        // so, SVS shall skip the bigger value for work-around solution

        // do nothing
      }
      else if (l_CarmoveNumber == 0u) // refresh condition
      {
        m_isNewStep = false;
        m_APG_Output_MoveNumber = l_CarmoveNumber;
      }
      else
      {
        if (m_APG_Output_TravelDistDesired_max != 0u)
        {
           m_progress_percentage = 1.0f - (static_cast<vfc::float32_t>(l_CartravelDistDesire) / static_cast<vfc::float32_t>(m_APG_Output_TravelDistDesired_max));
        }
        else
        {
          m_progress_percentage = 0.0f;
          // wating for receiving the first frame have maximum travel distance
          m_APG_Output_TravelDistDesired_max = l_CartravelDistDesire;
        }
      }
  }

  osg::Geometry*  const l_CircleGeometry  = getDrawable(0u)->asGeometry();


  if (m_requireUpdateGeode == true)
  {
    osg::Geometry*  const l_geometry  = getDrawable(0u)->asGeometry();
    this->drawCircle();
    l_geometry->getVertexArray()->dirty();
    l_geometry->dirtyBound();
    m_requireUpdateGeode = false;
  }


  osg::Vec4Array* const l_colors   = static_cast<osg::Vec4Array*> (l_CircleGeometry->getColorArray());

  // clear color
  for (vfc::uint32_t i = 0u; i < static_cast<vfc::uint32_t>(CIRCLE_NUM_SEGMENTS)*4u; i=i+4)
  {
    (*l_colors)[i]   = osg::Vec4f(0.0f, 1.0f, 0.0f, 0.0f);
    (*l_colors)[i+1u] = osg::Vec4f(0.0f, 1.0f, 0.0f, 0.0f);
    (*l_colors)[i+2u] = osg::Vec4f(0.0f, 1.0f, 0.0f, 0.0f);
    (*l_colors)[i+3u] = osg::Vec4f(0.0f, 1.0f, 0.0f, 0.0f);
  }

  // update color

  vfc::uint32_t l_numOfShowingSegment = 0u;

  if (isGreater(m_progress_percentage,0.99f))
  {
    l_numOfShowingSegment = CIRCLE_NUM_SEGMENTS;
  }
  else
  {
    l_numOfShowingSegment = m_progress_percentage*static_cast<vfc::float32_t>(CIRCLE_NUM_SEGMENTS);
  }

  for (vfc::uint32_t i = 0u; i < l_numOfShowingSegment*4u; i=i+4)
  {
    const vfc::uint32_t index = (g_dynamicgearSettings->m_clockwise) ? ( static_cast<vfc::uint32_t>(CIRCLE_NUM_SEGMENTS) - 1u) * 4u - i : i;
    (*l_colors)[index]    = osg::Vec4f(g_dynamicgearSettings->m_color);
    (*l_colors)[index+1u] = osg::Vec4f(g_dynamicgearSettings->m_color);
    (*l_colors)[index+2u] = osg::Vec4f(g_dynamicgearSettings->m_color);
    (*l_colors)[index+3u] = osg::Vec4f(g_dynamicgearSettings->m_color);
  }

  l_colors->dirty();
  //l_CircleGeometry->dirtyBound();

}


void DynamicGearOverlays::traverse(osg::NodeVisitor& f_nv) // PRQA S 6043
{
    //if (m_framework && (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType()))
    if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
    {

      DynamicGear* const l_DynamicGear = dynamic_cast<DynamicGear*> (getChild(0)); // PRQA S 3077  // PRQA S 3400
      if (l_DynamicGear != nullptr)
      {
        l_DynamicGear->update();

      }
    }

    osg::MatrixTransform::traverse(f_nv);
}


} // namespace dynamicgearoverlay
} // namespace assets
} // namespace cc

