#===============================================================================
# Copyright (c) 2017 by <PERSON>. All rights reserved.
# This file is property of Robert <PERSON> GmbH. Any unauthorized copy, use or
# distribution is an offensive act against international law and may be
# prosecuted under federal law. Its content is company confidential.
#===============================================================================

include hw/build/module_head.mk

SOURCEFILES = \
chamaeleon.cpp \
chamaeleon.cpp \
chamaeleon_base_signals.cpp \
chamaeleon_data.cpp \
chamaeleon_estimator.cpp \
chamaeleon_manager.cpp \
chamaeleon_manager_lines.cpp \
chamaeleon_roi_render_manager.cpp \
chamaeleon_rois.cpp \
chamaeleon_wb_gains.cpp \
visu/chamaeleon_visu.cpp \
visu/chamaeleon_visu_rois.cpp \

BINARY = object

include hw/build/$(COMPILER_NAME)/module_tail.mk
