@echo off

IF EXIST "cc\vehicle_model" (
    rd cc\vehicle_model
)

::CONFIGURATION
setlocal

set DEFAULT_OEM=byd
set DEFAULT_BUILD=osgb
set DEFAULT_VARIANT=SGHL_U8L

:: DEFAULT_REPO_PATH should be changed to your repository root path
set DEFAULT_REPO_PATH=D:\01_workspace\01_svs

:: print help
echo ************************************ USAGE *********************************************
echo  *
echo  *   model.cmd OEM VARIANT BUILD
echo  *       OEM     'gac byd hm'                   DEFAULT: %DEFAULT_OEM%
echo  *       VARIANT 'HCEF STEX STES MHRC A13 A18Y' DEFAULT: %DEFAULT_VARIANT%
echo  *       BUILD   'osg osgb'                     DEFAULT: %DEFAULT_BUILD%
echo  *   repo path: %DEFAULT_REPO_PATH%, PLEASE ADAPT THIS PATH TO YOUR CONTEXT!
echo  *
echo ****************************************************************************************


:: OEM
IF NOT "%1"=="" (
    set OEM=%1
    echo                                OEM=%1
) ELSE (
    set OEM=%DEFAULT_OEM%
    echo OEM     not set, using default OEM=%DEFAULT_OEM%
)

:: VARIANT
IF NOT "%2"=="" (
    set VARIANT=%2
    echo                                VARIANT=%2
) ELSE (
    set VARIANT=%DEFAULT_VARIANT%
    echo VARIANT not set, using default VARIANT=%DEFAULT_VARIANT%
)

::BUILD
IF NOT "%3"=="" (
    set BUILD=%3
    echo                                BUILD=%3
) ELSE (
    echo BUILD   not set, using default BUILD=%DEFAULT_BUILD%
    set BUILD=%DEFAULT_BUILD%
)

::Absolute path to vehicle model repo

set VEHICLEPATH=%DEFAULT_REPO_PATH%\cc_vehiclemodel_%OEM%
echo                                VEHICLEPATH: %VEHICLEPATH%

::LINK

IF "%BUILD%"=="osg" (
    mklink /J cc\vehicle_model %VEHICLEPATH%\cc\models_dev\%VARIANT%
    IF EXIST "cc\vehicle_model\ui" rd cc\vehicle_model\ui
    mklink /J cc\vehicle_model\ui %VEHICLEPATH%\cc\models\%VARIANT%\ui
    @REM mklink /J cc\vehicle_model\ui \ws\SVS\BYD\cc_ui_byd
) ELSE (
    mklink /J cc\vehicle_model %VEHICLEPATH%\cc\models\%VARIANT%
)

endlocal