/// @copyright (C) 2023 Robert <PERSON> GmbH.
/// The reproduction, distribution and utilization of this file as well as the
/// communication of its contents to others without express authorization is
/// prohibited. Offenders will be held liable for the payment of damages. All
/// rights reserved in the event of the grant of a patent, utility model or
/// design.
/// @file

#ifndef PC_SVS_IMP_VISUCHAMAELEONVISU_H
#define PC_SVS_IMP_VISUCHAMAELEONVISU_H

#include "pc/svs/imp/chamaeleon/inc/chamaeleon.hpp"
#include "pc/svs/imp/chamaeleon/inc/visu/chamaeleon_visu_rois.hpp"

#include "osg/Switch"

namespace rbp
{
namespace vis
{
namespace imp
{
namespace chamaeleon
{

class Chamaeleon;

namespace visu
{

///
/// ChamaeleonVisu
///
// qacpp-2119-R1: OSG API is intentionally declaring the destructors as protected
class ChamaeleonVisu : public osg::Switch // PRQA S 2119 # R1
{
public:
    explicit ChamaeleonVisu(const Chamaeleon* f_pCham);

    ~ChamaeleonVisu() override = default;

    void traverse(osg::NodeVisitor& f_nv) override;

    void setParameters(const ChamaeleonVisuData& f_params);

private:
    ChamaeleonVisu();
    ChamaeleonVisu(const ChamaeleonVisu&)            = delete;
    ChamaeleonVisu& operator=(const ChamaeleonVisu&) = delete;

    osg::ref_ptr<ChamaeleonVisuRois> m_chamaeleonVisuRois{nullptr};
};

} // namespace visu
} // namespace chamaeleon
} // namespace imp
} // namespace vis
} // namespace rbp

#endif // PC_SVS_IMP_VISUCHAMAELEONVISU_H
