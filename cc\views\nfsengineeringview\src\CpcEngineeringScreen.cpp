//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include "cc/util/logging/inc/LoggingContexts.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/svs/core/inc/Framework.h"
#include "pc/svs/core/inc/ShaderManager.h"
#include "pc/svs/util/osgx/inc/Utils.h"

#include "cc/assets/ECALprogressoverlay/inc/ECALprogressoverlay.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/cpc/inc/defines.hpp"
#include "cc/views/nfsengineeringview/inc/CpcEngineeringScreen.h"

#define DEBUG_CPC_ENGINEERING_SCREEN 1

using cc::util::logging::g_CpcContext;

namespace cc
{
namespace views
{
namespace nfsengineeringview
{

pc::util::coding::Item<CpcEngineeringScreenCodingParams> g_settings("CPCEngineeringScreenSettings");

std::string CpcEngineeringScreen::getCameraName(cc::cpc::CameraIndex f_camera)
{
    switch (f_camera)
    {
    case cc::cpc::CAMERA_FRONT:
    {
        return "FRONT";
    }
    case cc::cpc::CAMERA_REAR:
    {
        return "REAR";
    }
    case cc::cpc::CAMERA_LEFT:
    {
        return "LEFT";
    }
    case cc::cpc::CAMERA_RIGHT:
    {
        return "RIGHT";
    }
    default:
    {
        return "UNKNOWN";
    }
    }
}
osg::Vec3Array* CpcEngineeringScreen::createMarkerVertexArray(vfc::uint32_t f_numSegments, vfc::float32_t f_radius)
{
    osg::Vec3Array* const l_vertexArray = new osg::Vec3Array{f_numSegments};
    l_vertexArray->at(0u)         = {0.0f, 0.0f, 0.0f};

    const vfc::float32_t l_delta =
        2.0f * static_cast<vfc::float32_t>(osg::PI) / static_cast<vfc::float32_t>(f_numSegments);
    for (vfc::uint32_t i = 0u; i < f_numSegments - 1u; ++i)
    {
        if (i != (f_numSegments >> 1u) - 1u)
        {
            const vfc::float32_t l_angle    = static_cast<vfc::float32_t>(i) * l_delta;
            const vfc::float32_t l_x        = f_radius * std::cos(l_angle);
            const vfc::float32_t l_y        = f_radius * std::sin(l_angle);
            l_vertexArray->at(i + 1u) = {l_x, l_y, 0.0f};
        }
    }
    return l_vertexArray;
}

osg::Vec3Array* CpcEngineeringScreen::createCircleVertexArray(vfc::uint32_t f_numSegments, vfc::float32_t f_radius)
{
    osg::Vec3Array* const l_vertexArray = new osg::Vec3Array{f_numSegments};
    const vfc::float32_t  l_delta = 2.0f * static_cast<vfc::float32_t>(osg::PI) / static_cast<vfc::float32_t>(f_numSegments);

    for (vfc::uint32_t i = 0u; i < f_numSegments; ++i)
    {
        const vfc::float32_t l_angle = static_cast<vfc::float32_t>(i) * l_delta;
        const vfc::float32_t l_x     = f_radius * std::cos(l_angle);
        const vfc::float32_t l_y     = f_radius * std::sin(l_angle);
        l_vertexArray->at(i)   = {l_x, l_y, 0.0f};
    }

    return l_vertexArray;
}

osg::Vec2 CpcEngineeringScreen::getPosition(cc::assets::ECALprogressoverlay::CameraPosition f_position)
{
    const osg::Vec2 cpcViewportSize = {
        static_cast<vfc::float32_t>(cc::core::g_views->m_cpcViewport.m_size.x()),
        static_cast<vfc::float32_t>(cc::core::g_views->m_cpcViewport.m_size.y())};

    const osg::Vec2 cpcViewportOffset = {
        static_cast<vfc::float32_t>(cc::core::g_views->m_cpcViewport.m_origin.x()),
        static_cast<vfc::float32_t>(cc::core::g_views->m_cpcViewport.m_origin.y())};

    constexpr vfc::float32_t HEIGHT_OFFSET = 5.75f; // percentage

    switch (f_position)
    {
    case cc::assets::ECALprogressoverlay::CameraPosition::TOP_LEFT:
    {
        return cpcViewportOffset +
               osg::componentMultiply(osg::Vec2f{0.0f, HEIGHT_OFFSET + 75.0f} / 100.0f, cpcViewportSize);
    }
    case cc::assets::ECALprogressoverlay::CameraPosition::TOP_RIGHT:
    {
        return cpcViewportOffset +
               osg::componentMultiply(osg::Vec2f{50.0f, HEIGHT_OFFSET + 75.0f} / 100.0f, cpcViewportSize);
    }
    case cc::assets::ECALprogressoverlay::CameraPosition::BOTTOM_LEFT:
    {
        return cpcViewportOffset +
               osg::componentMultiply(osg::Vec2f{0.0f, HEIGHT_OFFSET + 25.0f} / 100.0f, cpcViewportSize);
    }
    case cc::assets::ECALprogressoverlay::CameraPosition::BOTTOM_RIGHT:
    {
        return cpcViewportOffset +
               osg::componentMultiply(osg::Vec2f{50.0f, HEIGHT_OFFSET + 25.0f} / 100.0f, cpcViewportSize);
    }
    default:
    {
        XLOG_ERROR(
            g_CpcContext, "CpcEngineeringScreen getPosition: invalid index " << static_cast<vfc::int32_t>(f_position));
        return {};
    }
    }
}

osg::Vec2 CpcEngineeringScreen::getVersionPosition(cc::views::nfsengineeringview::VersionPosition f_position)
{
    const osg::Vec2 cpcViewportSize = {
        static_cast<vfc::float32_t>(cc::core::g_views->m_cpcViewport.m_size.x()),
        static_cast<vfc::float32_t>(cc::core::g_views->m_cpcViewport.m_size.y())};

    const osg::Vec2 cpcViewportOffset = {
        static_cast<vfc::float32_t>(cc::core::g_views->m_cpcViewport.m_origin.x()),
        static_cast<vfc::float32_t>(cc::core::g_views->m_cpcViewport.m_origin.y())};

    constexpr vfc::float32_t HEIGHT_OFFSET = 8.0f; // percentage

    switch (f_position)
    {
    case cc::views::nfsengineeringview::VersionPosition::VERSION_TOP_LEFT:
    {
        return cpcViewportOffset +
               osg::componentMultiply(osg::Vec2f{0.0f, HEIGHT_OFFSET + 75.0f} / 100.0f, cpcViewportSize);
    }
    case cc::views::nfsengineeringview::VersionPosition::VERSION_TOP_RIGHT:
    {
        return cpcViewportOffset +
               osg::componentMultiply(osg::Vec2f{50.0f, HEIGHT_OFFSET + 75.0f} / 100.0f, cpcViewportSize);
    }
    case cc::views::nfsengineeringview::VersionPosition::VERSION_BOTTOM_LEFT:
    {
        return cpcViewportOffset +
               osg::componentMultiply(osg::Vec2f{0.0f, HEIGHT_OFFSET + 25.0f} / 100.0f, cpcViewportSize);
    }
    case cc::views::nfsengineeringview::VersionPosition::VERSION_BOTTOM_RIGHT:
    {
        return cpcViewportOffset +
               osg::componentMultiply(osg::Vec2f{50.0f, HEIGHT_OFFSET + 25.0f} / 100.0f, cpcViewportSize);
    }
    default:
    {
        XLOG_ERROR(
            g_CpcContext,
            "CpcEngineeringScreen getVersionPosition: invalid index " << static_cast<vfc::int32_t>(f_position));
        return {};
    }
    }
}

osg::Vec2 CpcEngineeringScreen::getPositionByCameraId(cc::cpc::CameraIndex f_camera)
{
    switch (f_camera)
    {
    case cc::cpc::CAMERA_FRONT:
    {
        return getPosition(cc::assets::ECALprogressoverlay::CameraPosition::TOP_LEFT);
    }
    case cc::cpc::CAMERA_REAR:
    {
        return getPosition(cc::assets::ECALprogressoverlay::CameraPosition::TOP_RIGHT);
    }
    case cc::cpc::CAMERA_LEFT:
    {
        return getPosition(cc::assets::ECALprogressoverlay::CameraPosition::BOTTOM_LEFT);
    }
    case cc::cpc::CAMERA_RIGHT:
    {
        return getPosition(cc::assets::ECALprogressoverlay::CameraPosition::BOTTOM_RIGHT);
    }

    default:
    {
        XLOG_ERROR(
            g_CpcContext,
            "CpcEngineeringScreen getPositionByCameraId: invalid index " << static_cast<vfc::int32_t>(f_camera));
        return {};
    }
    }
}

osg::Vec2f CpcEngineeringScreen::getMarkerOffset(cc::assets::ECALprogressoverlay::CameraPosition f_position)
{
    const osg::Vec2 cpcViewportSize = {
        static_cast<vfc::float32_t>(cc::core::g_views->m_cpcViewport.m_size.x()),
        static_cast<vfc::float32_t>(cc::core::g_views->m_cpcViewport.m_size.y())};
    const osg::Vec2 cpcViewportOffset = {
        static_cast<vfc::float32_t>(cc::core::g_views->m_cpcViewport.m_origin.x()),
        static_cast<vfc::float32_t>(cc::core::g_views->m_cpcViewport.m_origin.y())};
    switch (f_position)
    {
    case cc::assets::ECALprogressoverlay::CameraPosition::TOP_LEFT:
    {
        return cpcViewportOffset + osg::componentMultiply(osg::Vec2f{0.0f, 50.0f} / 100.0f, cpcViewportSize);
    }
    case cc::assets::ECALprogressoverlay::CameraPosition::TOP_RIGHT:
    {
        return cpcViewportOffset + osg::componentMultiply(osg::Vec2f{50.0f, 50.0f} / 100.0f, cpcViewportSize);
    }
    case cc::assets::ECALprogressoverlay::CameraPosition::BOTTOM_LEFT:
    {
        return cpcViewportOffset + osg::componentMultiply(osg::Vec2f{0.0f, 0.0f} / 100.0f, cpcViewportSize);
    }
    case cc::assets::ECALprogressoverlay::CameraPosition::BOTTOM_RIGHT:
    {
        return cpcViewportOffset + osg::componentMultiply(osg::Vec2f{50.0f, 0.0f} / 100.0f, cpcViewportSize);
    }
    default:
    {
        XLOG_ERROR(
            g_CpcContext, "CpcEngineeringScreen getPosition: invalid index " << static_cast<vfc::int32_t>(f_position));
        return {}; // PRQA S 2880
    }
    }
}

osg::Vec2f CpcEngineeringScreen::getMarkerOffsetByCameraId(cc::cpc::CameraIndex f_camera)
{
    switch (f_camera)
    {
    case cc::cpc::CAMERA_FRONT:
    {
        return getMarkerOffset(cc::assets::ECALprogressoverlay::CameraPosition::TOP_LEFT);
    }
    case cc::cpc::CAMERA_REAR:
    {
        return getMarkerOffset(cc::assets::ECALprogressoverlay::CameraPosition::TOP_RIGHT);
    }
    case cc::cpc::CAMERA_LEFT:
    {
        return getMarkerOffset(cc::assets::ECALprogressoverlay::CameraPosition::BOTTOM_LEFT);
    }
    case cc::cpc::CAMERA_RIGHT:
    {
        return getMarkerOffset(cc::assets::ECALprogressoverlay::CameraPosition::BOTTOM_RIGHT);
    }
    default:
    {
        XLOG_ERROR(
            g_CpcContext,
            "CpcEngineeringScreen getMarkerOffsetByCameraId: invalid index " << static_cast<vfc::int32_t>(f_camera));
        return {};
    }
    }
}

//! CullCallback
template <typename T>
class CullCallback : public osg::Drawable::CullCallback
{
public:
    explicit CullCallback(T* f_cullObject)
        : m_cullObject(f_cullObject)
    {
    }
    bool cull(osg::NodeVisitor* /* f_nv */, osg::Drawable* /* f_drawable */, osg::RenderInfo* /* f_renderInfo */)
        const override // PRQA S 2120
    {
        return m_cullObject->isCulled();
    }

private:
    T* m_cullObject;
};

osg::Vec3Array* CircleMarker::m_markerVertexArray =
    CpcEngineeringScreen::createMarkerVertexArray(MARKER_SEGMENT, g_settings->m_markerRadius);
osg::Vec3Array* CircleMarker::m_circleVertexArray =
    CpcEngineeringScreen::createCircleVertexArray(CIRCLE_SEGMENT, g_settings->m_markerRadius);

//! CircleMarker
CircleMarker::CircleMarker(MarkerColor f_color, vfc::float32_t /*f_circleRadius*/, vfc::float32_t /*f_markerRadius*/)
    : osg::Geode()
    , m_cull(false)
    , m_color(f_color)
    , m_dirty(true)
{
    osg::Vec4Array* const l_color = new osg::Vec4Array(1u);

    (*l_color)[0u] = g_colors[m_color];
    //! Marker Geometry
    osg::Geometry* const l_markerGeometry = pc::util::osgx::createGeometry("Marker");
    l_markerGeometry->setVertexArray(m_markerVertexArray);
    l_markerGeometry->addPrimitiveSet(new osg::DrawArrays(osg::PrimitiveSet::TRIANGLE_FAN, 0, MARKER_SEGMENT));
    l_markerGeometry->setColorArray(l_color, osg::Array::BIND_OVERALL);
    l_markerGeometry->setCullCallback(new CullCallback<CircleMarker>(this));
    osg::Geode::addDrawable(l_markerGeometry);

    //! Circle Geometry
    osg::Geometry* const l_circleGeometry = pc::util::osgx::createGeometry("Circle");
    l_circleGeometry->setVertexArray(m_circleVertexArray);
    l_circleGeometry->addPrimitiveSet(new osg::DrawArrays(osg::PrimitiveSet::LINE_LOOP, 0, CIRCLE_SEGMENT));
    l_circleGeometry->setColorArray(l_color, osg::Array::BIND_OVERALL);
    l_circleGeometry->setCullCallback(new CullCallback<CircleMarker>(this));
    osg::Geode::addDrawable(l_circleGeometry);

    //! Shader
    osg::StateSet*                           const l_pStateSet = this->getOrCreateStateSet();
    pc::core::TextureShaderProgramDescriptor l_basicTexShader("basicColor");
    l_basicTexShader.apply(l_pStateSet);
    l_pStateSet->setMode(GL_BLEND, osg::StateAttribute::ON);
    l_pStateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF);

    // m_colorUniform = l_pStateSet->getOrCreateUniform("v_FragColor", osg::Uniform::FLOAT_VEC4);
}

void CircleMarker::setColor(MarkerColor f_color)
{
    if (f_color != m_color)
    {
        m_color = f_color;
        m_dirty = true;
        update();
    }
}

void CircleMarker::update()
{
    if (!m_dirty)
    {
        return;
    }

    for (vfc::uint32_t i = 0u; i < getNumDrawables(); ++i)
    {
        osg::Geometry* const geo = getDrawable(i)->asGeometry();
        // osg::Vec3Array* vertexArray = static_cast<osg::Vec3Array*>(geo->getVertexArray());
        // vertexArray->dirty();

        osg::Vec4Array* const color = static_cast<osg::Vec4Array*>(geo->getColorArray());
        (*color)[0u]          = g_colors[m_color];
        geo->setColorArray(color, osg::Array::BIND_OVERALL);
        // color->dirty();
        // geo->dirtyBound();
    }

    m_dirty = false;
}

//! CpcEngineeringScreen
CpcEngineeringScreen::CpcEngineeringScreen(const std::string& f_name)
    : pc::views::engineeringview::EngineeringScreen(f_name)
    , m_cull(true)
    , m_sequenceNumber(65535u)
    , m_cameraMarkerList()
{
    // Text boxes
    for (vfc::uint32_t i = 0u; i < cc::cpc::NUMBER_OF_CAMERAS; ++i)
    {
        auto* l_textbox = static_cast<pc::views::engineeringview::KeyValueTextBox*>(getTextBox(i));
        if (l_textbox == nullptr)
        {
            l_textbox = new pc::views::engineeringview::KeyValueTextBox{
                {getPositionByCameraId(static_cast<cc::cpc::CameraIndex>(i)), 0.0f},
                g_settings->m_textBoxColor,
                g_settings->m_textBoxCharSize,
                g_settings->m_textBoxKeyWidth};
            l_textbox->getDrawable(0u)->setCullCallback(new CullCallback<CpcEngineeringScreen>(this));
            addTextBox(l_textbox);
            l_textbox->getOrCreateEntry(getCameraName(static_cast<cc::cpc::CameraIndex>(i)))->setValue("");
        }
    }

    for (vfc::uint32_t i = 0u; i < cc::cpc::NUMBER_OF_CAMERAS; ++i)
    {
        auto* const l_versionTextBox = new pc::views::engineeringview::KeyValueTextBox{
            {getVersionPosition(static_cast<cc::views::nfsengineeringview::VersionPosition>(i)), 0.0f},
            g_settings->m_versionColor,
            g_settings->m_versionCharSize,
            g_settings->m_textBoxKeyWidth};
        l_versionTextBox->getDrawable(0u)->setCullCallback(new CullCallback<CpcEngineeringScreen>(this));
        addTextBox(l_versionTextBox);
        l_versionTextBox->getOrCreateEntry("version: ")->setValue(g_settings->m_version);
    }

    // Cpc output corners
    for (vfc::uint32_t cameraId = 0u; cameraId < cc::cpc::NUMBER_OF_CAMERAS; ++cameraId)
    {
        m_cameraMarkerList[cameraId] = new CircleMarkerList();

        for (vfc::uint32_t i = 0u; i < m_cameraMarkerList[cameraId]->size(); ++i)
        {
            auto* const l_circleMarker                = new CircleMarker;
            m_cameraMarkerList[cameraId]->at(i) = new osg::MatrixTransform();
            m_cameraMarkerList[cameraId]->at(i)->setReferenceFrame(osg::Transform::ABSOLUTE_RF);
            m_cameraMarkerList[cameraId]->at(i)->addChild(l_circleMarker);
            osg::Group::addChild(m_cameraMarkerList[cameraId]->at(i));
        }
    }
}

bool CpcEngineeringScreen::update(pc::core::Framework* f_framework)
{
    if (nullptr == f_framework)
    {
        return false;
    }
    bool  l_dataChanged      = false;
    auto* const l_pCustomFramework = f_framework->asCustomFramework();

    if (!l_pCustomFramework->m_cpcToSvsOverlay_ReceiverPort.hasData())
    {
        XLOG_ERROR(g_CpcContext, "CpcEngineeringScreen::update: m_cpcToSvsOverlay_ReceiverPort has no data");
        m_cull = true;
        setAllMarkerCull(true);
        return l_dataChanged;
    }

    // if (!l_pCustomFramework->m_svsOverlayToCpc_ReceiverPort.hasData())
    // {
    //   XLOG_ERROR_OS(g_CpcContext) << "CpcEngineeringScreen::update: m_svsOverlayToCpc_ReceiverPort has no data" <<
    //   XLOG_ENDL; m_cull = true; setAllMarkerCull(true); return l_dataChanged;
    // }

    // const auto l_svsToCpcContainer = l_pCustomFramework->m_svsOverlayToCpc_ReceiverPort.getData();
    // auto l_svsToCpcData = l_svsToCpcContainer->m_Data;
    // if (l_svsToCpcData.m_isCancelButtonPressed || l_svsToCpcData.m_isRecalibrateButtonPressed)
    // {
    //   // CPC need some time to trigger its runnable so while button pressed status still valid
    //   // We need to manually clear the result as there's no data comes from CPC yet
    //   m_cull = true;
    //   setAllMarkerCull(true);
    //   return l_dataChanged;
    // }

    // if (!l_pCustomFramework->m_CpcOverlaySwitch_Receiver.hasData())
    // {
    //   XLOG_ERROR_OS(g_CpcContext) << "CpcEngineeringScreen::update() m_CpcOverlaySwitch_Receiver has no data" <<
    //   XLOG_ENDL; return l_dataChanged;
    // }
    // const auto& l_cpcOverlaySwitch = l_pCustomFramework->m_CpcOverlaySwitch_Receiver.getData()->m_Data;
    // if (!l_cpcOverlaySwitch)
    // {
    //   XLOG_DEBUG_OS(g_CpcContext) << "CpcEngineeringScreen::update() - CPC is not triggered" << XLOG_ENDL;
    //   return l_dataChanged;
    // }

    // update textboxes
    const auto l_cpcToSvsContainer = l_pCustomFramework->m_cpcToSvsOverlay_ReceiverPort.getData();
    if (m_sequenceNumber == l_cpcToSvsContainer->m_sequenceNumber)
    {
        // no update
        return l_dataChanged;
    }
    else
    {
        l_dataChanged    = true;
        m_sequenceNumber = l_cpcToSvsContainer->m_sequenceNumber;
    }

    // enable textbox overlay
    m_cull = false;

    XLOG_INFO(g_CpcContext, "CpcEngineeringScreen::update: updating");

    const auto& l_pCalibStatus = l_pCustomFramework->m_cpcToSvsOverlay_ReceiverPort.getData()->m_Data;
    for (vfc::uint32_t i = 0u; i < cc::cpc::NUMBER_OF_CAMERAS; ++i)
    {
        auto* const l_textbox = static_cast<pc::views::engineeringview::KeyValueTextBox*>(getTextBox(i));
        l_textbox->getOrCreateEntry(getCameraName(static_cast<cc::cpc::CameraIndex>(i)))
            ->setValue(
                std::string("\n") + std::string(
                                        l_pCalibStatus.m_cameras[i].m_outputString.data(),
                                        l_pCalibStatus.m_cameras[i].m_sizeOfString)); // PRQA S 3000
    }

    // update markers
    for (vfc::uint32_t cameraId = 0u; cameraId < cc::cpc::NUMBER_OF_CAMERAS; ++cameraId)
    {
        auto*        const l_cornerPoints     = l_pCalibStatus.m_cameras[cameraId].m_outputPoints.data();
        const vfc::int32_t l_numOfValidPoints = l_pCalibStatus.m_cameras[cameraId].m_sizeOfPoints;

#if DEBUG_CPC_ENGINEERING_SCREEN
        std::ostringstream os;
        os << std::setw(5) << getCameraName(static_cast<cc::cpc::CameraIndex>(cameraId)) << ": ";
#endif
        // process valid points
        for (vfc::uint32_t i = 0; i < static_cast<vfc::uint32_t>(l_numOfValidPoints); ++i)
        {
            const auto& l_cornerPoint = l_cornerPoints[i];
            const osg::Vec2f  l_offset      = getMarkerOffsetByCameraId(static_cast<cc::cpc::CameraIndex>(cameraId));
            osg::Vec2f  l_position =
                osg::Vec2f{static_cast<vfc::float32_t>(l_cornerPoint.x), static_cast<vfc::float32_t>(l_cornerPoint.y)};

            if (cameraId == cc::cpc::CAMERA_FRONT) // PRQA S 3000
            {
                // flip y
                l_position.y() =
                    static_cast<vfc::float32_t>(cc::target::sysconf::E_IMAGE_FORMAT_TEXTURE_HEIGHT) - l_position.y();
            }

            if (cameraId == cc::cpc::CAMERA_REAR) // PRQA S 3000
            {
                // flip y
                l_position.y() =
                    static_cast<vfc::float32_t>(cc::target::sysconf::E_IMAGE_FORMAT_TEXTURE_HEIGHT) - l_position.y();
            }

            if (cameraId == cc::cpc::CAMERA_LEFT) // PRQA S 3000
            {
                // flip x and y
                l_position.x() =
                    static_cast<vfc::float32_t>(cc::target::sysconf::E_IMAGE_FORMAT_TEXTURE_WIDTH) - l_position.x();
                l_position.y() =
                    static_cast<vfc::float32_t>(cc::target::sysconf::E_IMAGE_FORMAT_TEXTURE_HEIGHT) - l_position.y();
            }

            if (cameraId == cc::cpc::CAMERA_RIGHT) // PRQA S 3000
            {
                // flip y
                l_position.y() =
                    static_cast<vfc::float32_t>(cc::target::sysconf::E_IMAGE_FORMAT_TEXTURE_HEIGHT) - l_position.y();
            }

            const osg::Vec2f l_screenScale = {
                static_cast<vfc::float32_t>(cc::core::g_views->m_cpcViewport.m_size.x()) / 2.0f /
                    static_cast<vfc::float32_t>(
                        cc::target::sysconf::E_IMAGE_FORMAT_TEXTURE_WIDTH), // TODO: replace 1356 by rawfisheye width
                static_cast<vfc::float32_t>(cc::core::g_views->m_cpcViewport.m_size.y()) / 2.0f /
                    static_cast<vfc::float32_t>(
                        cc::target::sysconf::E_IMAGE_FORMAT_TEXTURE_HEIGHT) // TODO: replace 974 by rawfisheye height
            };

            osg::Vec2f l_screenPosition = osg::componentMultiply(l_position, l_screenScale) + l_offset;

#if DEBUG_CPC_ENGINEERING_SCREEN
            os << "[" << i << ": " << std::setw(5) << l_screenPosition.x() << ", " << std::setw(5)
               << l_screenPosition.y() << "] ";
#endif
            m_cameraMarkerList[cameraId]->at(i)->setMatrix(osg::Matrix::translate(osg::Vec3f{l_screenPosition, 0.0f}));
            auto* const l_circleMarker = static_cast<CircleMarker*>(m_cameraMarkerList[cameraId]->at(i)->getChild(0u));
            l_circleMarker->setColor(static_cast<MarkerColor>(i % ( static_cast<vfc::uint32_t>(NUM_COLORS) - 1u)));
            l_circleMarker->setCull(false);
        }

#if DEBUG_CPC_ENGINEERING_SCREEN
        XLOG_INFO(g_CpcContext, os.str());
#endif
        // cull all invalid points
        for (vfc::uint32_t i = static_cast<vfc::uint32_t>(l_numOfValidPoints);
             i < l_pCalibStatus.m_cameras[cameraId].m_outputPoints.size();
             ++i)
        {
            auto* const l_circleMarker = static_cast<CircleMarker*>(m_cameraMarkerList[cameraId]->at(i)->getChild(0u));
            l_circleMarker->setCull(true);
        }
    }

    return l_dataChanged;
}

void CpcEngineeringScreen::setAllMarkerCull(bool f_cull)
{
    for (vfc::uint32_t cameraId = 0u; cameraId < cc::cpc::NUMBER_OF_CAMERAS; ++cameraId)
    {
        for (vfc::uint32_t i = 0u; i < m_cameraMarkerList[cameraId]->size(); ++i)
        {
            auto* const l_circleMarker = static_cast<CircleMarker*>(m_cameraMarkerList[cameraId]->at(i)->getChild(0u));
            l_circleMarker->setCull(f_cull);
        }
    }
}

} // namespace nfsengineeringview
} // namespace views
} // namespace cc
