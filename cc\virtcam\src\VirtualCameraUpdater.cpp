//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON> GmbH. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include "cc/virtcam/inc/VirtualCameraUpdater.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/virtcam/inc/CameraPositions.h"

#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/coding/inc/ISerializable.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h"
#include "pc/svs/util/math/inc/FloatComp.h"

#include <cassert> // PRQA S 1060

#include "cc/imgui/inc/imgui_manager.h"

#define GET_PORT_DATA(dataDaddy, port, allPortHaveDataFlag)                                                            \
    const auto dataDaddy  = (port).getData();  /* PRQA S 1030 */                                                       \
    (allPortHaveDataFlag) = (allPortHaveDataFlag) && ((dataDaddy) != nullptr);                                         \
    if ((dataDaddy) == nullptr)                                                                                        \
    {                                                                                                                  \
        XLOG_ERROR(g_AppContext, #port << " doesn't have data!\n");                                                    \
    }

using pc::util::logging::g_AppContext;

namespace cc
{
namespace virtcam
{

pc::util::coding::Item<VirtualCameraParameters> g_virtualCamParam("VirtualCamParam");

cc::virtcam::VirtualCameraUpdater::VirtualCameraUpdater(pc::core::Framework* f_framework) // PRQA S 4206
    : HemisphereCameraUpdater() // PRQA S 2323
    , m_framework(f_framework) // PRQA S 2323
    , m_timeoutClock(6.0f) // PRQA S 2323
    , m_center() // PRQA S 2323
{
}

void VirtualCameraUpdater::init()
{
    setElevationRange(g_virtualCamParam->m_minElevation, g_virtualCamParam->m_maxElevation);
    setZoomRange(g_virtualCamParam->m_minDistance, g_virtualCamParam->m_maxDistance);
}

osg::Matrix VirtualCameraUpdater::getViewMatrix(
    vfc::float32_t    f_elevation,
    vfc::float32_t    f_azimuth,
    vfc::float32_t    f_distance,
    const osg::Vec3f& f_hemisphereCenter)
{
    return HemisphereCameraUpdater::getViewMatrix(f_elevation, f_azimuth, f_distance, f_hemisphereCenter);
}

void VirtualCameraUpdater::reset()
{
    HemisphereCameraUpdater::reset();
}

void VirtualCameraUpdater::logViewMatrix()
{
#if defined(TARGET_STANDALONE) && defined(ENABLE_IMGUI)
    static int virtualViewId = 0;
    if (m_framework->asCustomFramework()->isImguiEnabled())
    {
        ImGui::Begin("VirtualCameraUpdater-Matrix");
        ImGui::SliderInt("CamPosition", &virtualViewId, 0, 5);

        const pc::virtcam::VirtualCamera* camPos;
        switch (virtualViewId)
        {
        default:
        case 0:
            camPos = &cc::virtcam::g_positions->m_virtualParkSearching;
            break;
        case 1:
            camPos = &cc::virtcam::g_positions->m_virtualParkConfirm;
            break;
        case 2:
            camPos = &cc::virtcam::g_positions->m_virtualParkGuidance;
            break;
        case 3:
            camPos = &cc::virtcam::g_positions->m_virtualParkCompleteCross;
            break;
        case 4:
            camPos = &cc::virtcam::g_positions->m_virtualParkCompletePara;
            break;
        case 5:
            camPos = &cc::virtcam::g_positions->m_virtualParkCompleteDiagonal;
            break;
        }
        osg::Matrix searchingPositionMatrix;
        searchingPositionMatrix.makeLookAt(camPos->m_eye, camPos->m_up, camPos->m_center);
        osg::Vec3f     l_center;
        vfc::float32_t l_elevation = 0.0f;
        vfc::float32_t l_azimuth   = 0.0f;
        vfc::float32_t l_distance  = 0.0f;
        projectCameraToParametricDomain(
            searchingPositionMatrix,
            l_center,
            l_elevation,
            l_azimuth,
            l_distance,
            IMGUI_GET_CHECKBOX_BOOL("VirtualCameraUpdater", "svsCenter"));
        IMGUI_LOG(
            "VirtualCameraUpdater-Matrix",
            "l_center",
            std::to_string(l_center.x()) + " " + std::to_string(l_center.y()) + " " + std::to_string(l_center.z()));
        IMGUI_LOG("VirtualCameraUpdater-Matrix", "l_elevation", l_elevation);
        IMGUI_LOG("VirtualCameraUpdater-Matrix", "l_azimuth", l_azimuth);
        IMGUI_LOG("VirtualCameraUpdater-Matrix", "l_distance", l_distance);

        if (ImGui::Button("SetReference"))
        {
            reset();
            normalizeAndClampParameters(l_elevation, l_azimuth, l_distance);
            rotateTo(l_elevation, l_azimuth);
            zoomTo(l_distance);
        }
        ImGui::End();
    }
#endif
}

void VirtualCameraUpdater::updateCamera(osg::Camera* f_camera, const osg::FrameStamp* f_frameStamp)
{
    using namespace cc::target::common;
    if (m_modifiedCount != g_virtualCamParam->getModifiedCount())
    {
        init();
        m_modifiedCount = g_virtualCamParam->getModifiedCount();
    }

    m_timeoutClock.checkTimeout();
    updateInput(f_camera);

    if (m_updateReferenceView)
    {
        if (m_zoomComplete)
        {
            reset();
            m_zoomComplete = false;
            m_distance     = 0.5f * m_distance; // zoom twice
            m_elevation    = static_cast<vfc::float32_t>(osg::DegreesToRadians(30.0));
        }
        normalizeAndClampParameters(m_elevation, m_azimuth, m_distance);
        rotateTo(m_elevation, m_azimuth);
        zoomTo(m_distance);
        setHemisphereCenter(m_center);
        m_updateReferenceView = false;
        m_referenceAzimuth    = m_azimuth;
        m_referenceElevation  = m_elevation;
        m_referenceZoom       = m_distance;
    }

    const bool l_parkoutConfirm = (m_parkingStage == EParkingStage::ParkOut) && (m_parkMode == EParkingMode::Apa) &&
                            (m_apaStatus == EApaStatus::ParkAssistStandby);
    if (m_newTouchData && (!l_parkoutConfirm))
    {
        m_resetToDefault = false;
        m_timeoutClock.reset();
        checkAndHandleDragGesture(f_camera);
        checkAndHandleZoomGesture(f_camera);
    }

    if (!m_resetToDefault && m_timeoutClock.isTimeout() && !m_completeStage)
    {
        m_resetToDefault = true;
        reset();
        resetToReferenceView();
    }

    HemisphereCameraUpdater::updateCamera(f_camera, f_frameStamp);
}

bool VirtualCameraUpdater::updateInput(osg::Camera* f_camera)
{
    bool allPortsHaveData = true;
    const auto customFramework  = m_framework->asCustomFramework();
    GET_PORT_DATA(hmiDataContainer, customFramework->m_hmiDataReceiver, allPortsHaveData)
    GET_PORT_DATA(touchStatusContainer, customFramework->m_HUTouchTypeReceiver, allPortsHaveData)
    GET_PORT_DATA(parkHmiContainer, customFramework->m_ParkhmiToSvs_ReceiverPort, allPortsHaveData)

    if (!allPortsHaveData)
    {
        return false;
    }

    m_newTouchData =
        customFramework->m_hmiDataReceiver.hasNewData() || customFramework->m_HUTouchTypeReceiver.hasNewData();
    m_huX          = static_cast<vfc::uint32_t>(hmiDataContainer->m_Data.m_huX);
    m_huY          = static_cast<vfc::uint32_t>(hmiDataContainer->m_Data.m_huY);
    m_huY          = cc::core::g_views->m_usableCanvasViewport.m_size.y() - m_huY; // convert to bottom left
    m_touchStatus  = static_cast<TouchStatus>(touchStatusContainer->m_Data);
    m_parkingStage = parkHmiContainer->m_Data.m_parkingStage;
    m_apaStatus    = parkHmiContainer->m_Data.m_apaStatus;
    m_parkingStage = parkHmiContainer->m_Data.m_parkingStage;
    m_parkMode     = parkHmiContainer->m_Data.m_parkMode;

    getZoomRange(m_distanceMin, m_distanceMax);
    getElevationRange(m_elevationMin, m_elevationMax);
    projectCameraToParametricDomain(f_camera, m_center, m_elevation, m_azimuth, m_distance, false);

    // clang-format off
    IMGUI_LOG("VirtualCameraUpdater", "m_center", std::to_string(m_center.x()) + " " + std::to_string(m_center.y()) + " " + std::to_string(m_center.z()));
    IMGUI_LOG("VirtualCameraUpdater", "m_elevation", m_elevation);
    IMGUI_LOG("VirtualCameraUpdater", "m_referenceElevation", m_referenceElevation);
    IMGUI_LOG("VirtualCameraUpdater", "m_azimuth", m_azimuth);
    IMGUI_LOG("VirtualCameraUpdater", "m_referenceAzimuth", m_referenceAzimuth);
    IMGUI_LOG("VirtualCameraUpdater", "m_distance", m_distance);
    IMGUI_LOG("VirtualCameraUpdater", "m_referenceZoom", m_referenceZoom);
    IMGUI_LOG("VirtualCameraUpdater", "m_distanceMin", m_distanceMin);
    IMGUI_LOG("VirtualCameraUpdater", "m_distanceMax", m_distanceMax);
    IMGUI_LOG("VirtualCameraUpdater", "m_elevationMin", m_elevationMin);
    IMGUI_LOG("VirtualCameraUpdater", "m_elevationMax", m_elevationMax);
    IMGUI_LOG("VirtualCameraUpdater", "m_elevation", osg::RadiansToDegrees((m_elevation)));
    IMGUI_LOG("VirtualCameraUpdater", "currentZoomLevel", getCurrentZoomLevel());
    IMGUI_LOG("VirtualCameraUpdater", "m_resetToDefault", m_resetToDefault);
    IMGUI_LOG("VirtualCameraUpdater", "m_timeoutClock", m_timeoutClock.getElapsedTime());
    // clang-format on
    return true;
}

void VirtualCameraUpdater::checkAndHandleDragGesture(osg::Camera* f_camera)
{
    if (f_camera == nullptr)
    {
        return;
    }
    auto const viewport = f_camera->getViewport();
    auto const insideX  = (m_huX >= viewport->x() && m_huX <= viewport->x() + viewport->width()); // PRQA S 3011
    auto const insideY  = (m_huY >= viewport->y() && m_huY <= viewport->y() + viewport->height()); // PRQA S 3011

    const bool touchInsideViewport = (insideX && insideY);
    const bool touchBeginValid     = (m_touchStatus == TouchStatus::Down && touchInsideViewport);
    const bool touchMoveValid      = (m_touchStatus == TouchStatus::Move && touchInsideViewport);
    const bool touchInvalid        = m_touchStatus == TouchStatus::Up || m_touchStatus == TouchStatus::Invalid;

    if ((touchBeginValid || touchMoveValid) && !m_moving)
    {
        m_moving         = true;
        m_startAzimuth   = m_azimuth;
        m_startElevation = m_elevation;
        m_startX         = m_huX;
        m_startY         = m_huY;
    }

    if ((touchBeginValid || touchMoveValid) && m_moving)
    {
        constexpr vfc::float64_t axis2Range    = 3.141592653589793;
        const auto           currentHuX    = static_cast<vfc::float32_t>(m_huX);
        const auto           currentHuY    = static_cast<vfc::float32_t>(m_huY);
        const auto           startHuX      = static_cast<vfc::float32_t>(m_startX);
        const auto           startHuY      = static_cast<vfc::float32_t>(m_startY);
        const vfc::float32_t deltaX        = (currentHuX - startHuX) / static_cast<vfc::float32_t>(viewport->width());
        const vfc::float32_t deltaY        = -(currentHuY - startHuY) / static_cast<vfc::float32_t>(viewport->height());
        const vfc::float32_t dragElevation = deltaY * (m_elevationMax - m_elevationMin);
        const vfc::float32_t dragAzimuth   = deltaX * static_cast<vfc::float32_t>(axis2Range);
        vfc::float32_t newElevation  = m_startElevation + dragElevation;
        vfc::float32_t newAzimuth    = m_startAzimuth + dragAzimuth;
        vfc::float32_t temp = 0.0f;
        newElevation = (newElevation < m_elevationMin)   ? m_elevationMin
                       : (newElevation > m_elevationMax) ? m_elevationMax
                                                         : newElevation;
        normalizeAndClampParameters(newElevation, newAzimuth, temp);
        rotateTo(newElevation, newAzimuth);
    }

    if (touchInvalid && m_moving)
    {
        m_moving = false;
    }
}

void VirtualCameraUpdater::checkAndHandleZoomGesture(osg::Camera* f_camera)
{
    if (nullptr == f_camera)
	{
		return;
	}
	const auto viewport            = f_camera->getViewport();
    const auto insideX             = (m_huX >= viewport->x() && m_huX <= viewport->x() + viewport->width());
    const auto insideY             = (m_huY >= viewport->y() && m_huY <= viewport->y() + viewport->height());
    const bool touchInsideViewport = (insideX && insideY);
    const bool zoomIn              = (m_touchStatus == TouchStatus::ScrollUp && touchInsideViewport);
    const bool zoomOut             = (m_touchStatus == TouchStatus::ScrollDown && touchInsideViewport);

    if (zoomIn)
    {
        zoomTo(getCurrentZoomLevel() + g_virtualCamParam->m_zoomStep);
    }

    if (zoomOut)
    {
        zoomTo(getCurrentZoomLevel() - g_virtualCamParam->m_zoomStep);
    }
}

} // namespace virtcam
} // namespace cc
