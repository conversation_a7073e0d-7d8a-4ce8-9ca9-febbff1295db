<?xml version="1.0" encoding="UTF-8"?>
<CodingParameters>
  <System>
    <mainViewport>
      <origin>0 0</origin>
      <size>720 1920</size>
    </mainViewport>
    <isCameraCombine>1</isCameraCombine>  <!-- 0: 4 camera texture; 1: 1 combined camera texure-->
    <samples>4</samples>
    <defaultModeScissorTest>1</defaultModeScissorTest>
  </System>
  <Framework>
    <backgroundColor>0.09 0.137 0.2 1.0</backgroundColor>
  </Framework>
  <FrontFisheyeAssist>
    <virtualYaw>0</virtualYaw>
    <virtualPitch>15</virtualPitch>
    <virtualRoll>0</virtualRoll>
  </FrontFisheyeAssist>
  <RearFisheyeAssist>
    <virtualYaw>180</virtualYaw>
    <virtualPitch>35</virtualPitch>
    <virtualRoll>0</virtualRoll>
  </RearFisheyeAssist>
  <FrontFisheye>
    <virtualYaw>0</virtualYaw>
    <virtualPitch>15</virtualPitch>
    <virtualRoll>0</virtualRoll>
  </FrontFisheye>
  <RearFisheye>
    <virtualYaw>180</virtualYaw>
    <virtualPitch>35</virtualPitch>
    <virtualRoll>0</virtualRoll>
  </RearFisheye>
  <RemoteFrontFisheye>
    <virtualYaw>0</virtualYaw>
    <virtualPitch>15</virtualPitch>
    <virtualRoll>0</virtualRoll>
  </RemoteFrontFisheye>
  <RemoteRearFisheye>
    <virtualYaw>180</virtualYaw>
    <virtualPitch>35</virtualPitch>
    <virtualRoll>0</virtualRoll>
  </RemoteRearFisheye>
  <RemoteLeftFisheye>
    <virtualYaw>65</virtualYaw>
    <virtualPitch>40</virtualPitch>
    <virtualRoll>60</virtualRoll>
  </RemoteLeftFisheye>
  <RemoteRightFisheye>
    <virtualYaw>270</virtualYaw>
    <virtualPitch>45</virtualPitch>
    <virtualRoll>270</virtualRoll>
  </RemoteRightFisheye>
  <LeftFisheye>
    <virtualYaw>65</virtualYaw>
    <virtualPitch>40</virtualPitch>
    <virtualRoll>60</virtualRoll>
  </LeftFisheye>
  <RightFisheye>
    <virtualYaw>270</virtualYaw>
    <virtualPitch>45</virtualPitch>
    <virtualRoll>270</virtualRoll>
  </RightFisheye>
  <LeftFisheye5x>
    <virtualYaw>65</virtualYaw>
    <virtualPitch>40</virtualPitch>
    <virtualRoll>60</virtualRoll>
  </LeftFisheye5x>
  <RightFisheye5x>
    <virtualYaw>270</virtualYaw>
    <virtualPitch>45</virtualPitch>
    <virtualRoll>270</virtualRoll>
  </RightFisheye5x>
  <LeftRearFisheye5x>
    <virtualYaw>70</virtualYaw>
    <virtualPitch>40</virtualPitch>
    <virtualRoll>60</virtualRoll>
  </LeftRearFisheye5x>
  <RightRearFisheye5x>
    <virtualYaw>280</virtualYaw>
    <virtualPitch>45</virtualPitch>
    <virtualRoll>270</virtualRoll>
  </RightRearFisheye5x>
  <FrontPanoFisheye>
    <virtualYaw>0</virtualYaw>
    <virtualPitch>30</virtualPitch>
    <virtualRoll>0</virtualRoll>
  </FrontPanoFisheye>
  <RearPanoFisheye>
    <virtualYaw>180</virtualYaw>
    <virtualPitch>40</virtualPitch>
    <virtualRoll>0</virtualRoll>
  </RearPanoFisheye>
  <!--
  <FrontFisheyeModel>
    <horizontalHalfFov>90</horizontalHalfFov>
  </FrontFisheyeModel>
  <RearFisheyeModel>
    <horizontalHalfFov>90</horizontalHalfFov>
  </RearFisheyeModel> -->
  <!-- Horizontal partial unfisheye model -->
  <!-- For Assist front and rear view setting in D area in hori mode-->
  <PartialUnfishFrontAssist>
    <modelSettings>
      <horizontalHalfFov>73</horizontalHalfFov>
    </modelSettings>
    <delta>0.85</delta>
  </PartialUnfishFrontAssist>
  <PartialUnfishRearAssist>
    <modelSettings>
      <horizontalHalfFov>73</horizontalHalfFov>
    </modelSettings>
    <delta>0.85</delta>
  </PartialUnfishRearAssist>
  <PartialUnfishFront>
    <modelSettings>
      <horizontalHalfFov>68</horizontalHalfFov>
    </modelSettings>
    <delta>1.0</delta>
  </PartialUnfishFront>
  <PartialUnfishRear>
    <modelSettings>
      <horizontalHalfFov>68</horizontalHalfFov>
    </modelSettings>
    <delta>1.0</delta>
  </PartialUnfishRear>
  <PartialUnfishRemoteFront>
    <modelSettings>
      <horizontalHalfFov>70</horizontalHalfFov>
    </modelSettings>
    <delta>0.85</delta>
  </PartialUnfishRemoteFront>
  <PartialUnfishRemoteRear>
    <modelSettings>
      <horizontalHalfFov>70</horizontalHalfFov>
    </modelSettings>
    <delta>0.85</delta>
  </PartialUnfishRemoteRear>
  <PartialUnfishLeft>
    <modelSettings>
      <horizontalHalfFov>70</horizontalHalfFov>
    </modelSettings>
    <delta>0.7</delta>
  </PartialUnfishLeft>
  <PartialUnfishRight>
    <modelSettings>
      <horizontalHalfFov>70</horizontalHalfFov>
    </modelSettings>
    <delta>0.7</delta>
  </PartialUnfishRight>
  <PartialUnfishLeft5x>
    <modelSettings>
      <horizontalHalfFov>70</horizontalHalfFov>
    </modelSettings>
    <delta>0.7</delta>
  </PartialUnfishLeft5x>
  <PartialUnfishRight5x>
    <modelSettings>
      <horizontalHalfFov>70</horizontalHalfFov>
    </modelSettings>
    <delta>0.7</delta>
  </PartialUnfishRight5x>
  <PartialUnfishRearLeft5x>
    <modelSettings>
      <horizontalHalfFov>65</horizontalHalfFov>
    </modelSettings>
    <delta>0.7</delta>
  </PartialUnfishRearLeft5x>
  <PartialUnfishRearRight5x>
    <modelSettings>
      <horizontalHalfFov>75</horizontalHalfFov>
    </modelSettings>
    <delta>0.7</delta>
  </PartialUnfishRearRight5x>
  <PartialUnfishRemoteLeft>
    <modelSettings>
      <horizontalHalfFov>90</horizontalHalfFov>
    </modelSettings>
    <delta>1.0</delta>
  </PartialUnfishRemoteLeft>
  <PartialUnfishRemoteRight>
    <modelSettings>
      <horizontalHalfFov>90</horizontalHalfFov>
    </modelSettings>
    <delta>1.0</delta>
  </PartialUnfishRemoteRight>
  <!-- For Assist front and rear view setting in D area in vert mode-->
  <PartialUnfishFrontVert>
    <modelSettings>
      <horizontalHalfFov>70</horizontalHalfFov>
    </modelSettings>
    <delta>0.85</delta>
  </PartialUnfishFrontVert>
  <PartialUnfishRearVert>
    <modelSettings>
      <horizontalHalfFov>68</horizontalHalfFov>
    </modelSettings>
    <delta>0.85</delta>
  </PartialUnfishRearVert>
  <PartialUnfishLeftVert>
    <modelSettings>
      <horizontalHalfFov>70</horizontalHalfFov>
    </modelSettings>
    <delta>0.7</delta>
  </PartialUnfishLeftVert>
  <PartialUnfishRightVert>
    <modelSettings>
      <horizontalHalfFov>70</horizontalHalfFov>
    </modelSettings>
    <delta>0.7</delta>
  </PartialUnfishRightVert>

  <PartialUnfishFrontVert5x>
    <modelSettings>
      <horizontalHalfFov>70</horizontalHalfFov>
    </modelSettings>
    <delta>0.85</delta>
  </PartialUnfishFrontVert5x>
  <PartialUnfishRearVert5x>
    <modelSettings>
      <horizontalHalfFov>68</horizontalHalfFov>
    </modelSettings>
    <delta>0.85</delta>
  </PartialUnfishRearVert5x>
  <PartialUnfishLeftVert5x>
    <modelSettings>
      <horizontalHalfFov>70</horizontalHalfFov>
    </modelSettings>
    <delta>0.7</delta>
  </PartialUnfishLeftVert5x>
  <PartialUnfishRightVert5x>
    <modelSettings>
      <horizontalHalfFov>70</horizontalHalfFov>
    </modelSettings>
    <delta>0.7</delta>
  </PartialUnfishRightVert5x>


  <!-- Horizontal partial unfisheye model -->
  <PartialUnfishFrontPano>
    <modelSettings>
      <horizontalHalfFov>85</horizontalHalfFov>
    </modelSettings>
    <delta>1.0</delta>
  </PartialUnfishFrontPano>
  <PartialUnfishRearPano>
    <modelSettings>
      <horizontalHalfFov>85</horizontalHalfFov>
    </modelSettings>
    <delta>1.0</delta>
  </PartialUnfishRearPano>

  <PartialUnfishFrontPanoVert>
    <modelSettings>
      <horizontalHalfFov>70</horizontalHalfFov>
    </modelSettings>
    <delta>0.7</delta>
  </PartialUnfishFrontPanoVert>
  <PartialUnfishRearPanoVert>
    <modelSettings>
      <horizontalHalfFov>70</horizontalHalfFov>
    </modelSettings>
    <delta>0.7</delta>
  </PartialUnfishRearPanoVert>

  <VertFrontFisheye>
    <virtualYaw>0</virtualYaw>
    <virtualPitch>20</virtualPitch>
    <virtualRoll>90</virtualRoll>
  </VertFrontFisheye>
  <VertRearFisheye>
    <virtualYaw>180</virtualYaw>
    <virtualPitch>35</virtualPitch>
    <virtualRoll>270</virtualRoll>
  </VertRearFisheye>
  <VertFrontPanoFisheye>
    <virtualYaw>0</virtualYaw>
    <virtualPitch>20</virtualPitch>
    <virtualRoll>90</virtualRoll>
  </VertFrontPanoFisheye>
  <VertRearPanoFisheye>
    <virtualYaw>180</virtualYaw>
    <virtualPitch>35</virtualPitch>
    <virtualRoll>270</virtualRoll>
  </VertRearPanoFisheye>
  <VertLeftFisheye>
    <virtualYaw>90</virtualYaw>
    <virtualPitch>45</virtualPitch>
    <virtualRoll>180</virtualRoll>
  </VertLeftFisheye>
  <VertRightFisheye>
    <virtualYaw>270</virtualYaw>
    <virtualPitch>45</virtualPitch>
    <virtualRoll>0</virtualRoll>
  </VertRightFisheye>

  <VertFrontFisheye5x>
    <virtualYaw>0</virtualYaw>
    <virtualPitch>20</virtualPitch>
    <virtualRoll>90</virtualRoll>
  </VertFrontFisheye5x>
  <VertRearFisheye5x>
    <virtualYaw>180</virtualYaw>
    <virtualPitch>35</virtualPitch>
    <virtualRoll>270</virtualRoll>
  </VertRearFisheye5x>
  <VertLeftFisheye5x>
    <virtualYaw>90</virtualYaw>
    <virtualPitch>45</virtualPitch>
    <virtualRoll>180</virtualRoll>
  </VertLeftFisheye5x>
  <VertRightFisheye5x>
    <virtualYaw>270</virtualYaw>
    <virtualPitch>45</virtualPitch>
    <virtualRoll>0</virtualRoll>
  </VertRightFisheye5x>

  <FisheyeCropSettingsFront>
    <cropBounds>-1.0 1.0 -1.0 1.0</cropBounds>
  </FisheyeCropSettingsFront>
  <FisheyeCropSettingsRear>
    <cropBounds>-1.0 1.0 -1.0 1.0</cropBounds>
  </FisheyeCropSettingsRear>
  <FisheyeCropSettingsFrontAssistView>
    <cropBounds>-1.0 1.0 -1.0 1.0</cropBounds>  <!-- order: left, right, bottom, top -->
  </FisheyeCropSettingsFrontAssistView>
  <FisheyeCropSettingsRearAssistView>
    <cropBounds>-1.0 1.0 -1.0 1.0</cropBounds>
  </FisheyeCropSettingsRearAssistView>
  <FisheyeCropSettingsFrontView>
    <cropBounds>-1.0 1.0 -1.0 1.0</cropBounds>  <!-- order: left, right, bottom, top -->
  </FisheyeCropSettingsFrontView>
  <FisheyeCropSettingsRearView>
    <cropBounds>-1.0 1.0 -1.0 1.0</cropBounds>
  </FisheyeCropSettingsRearView>
  <FisheyeCropSettingsRemoteFrontView>
    <cropBounds>-0.95 0.95 -0.9 0.65</cropBounds>  <!-- order: left, right, bottom, top -->
  </FisheyeCropSettingsRemoteFrontView>
  <FisheyeCropSettingsRemoteRearView>
    <cropBounds>-1.0 1.0 -0.65 0.75</cropBounds>
  </FisheyeCropSettingsRemoteRearView>
  <FisheyeCropSettingsFrontPano>
    <cropBounds>-0.9 0.9 -0.8 1.0</cropBounds>
  </FisheyeCropSettingsFrontPano>
  <FisheyeCropSettingsRearPano>
    <cropBounds>-0.9 0.9 -0.8 1.0</cropBounds>
  </FisheyeCropSettingsRearPano>
  <FisheyeCropSettingsLeftView>
    <cropBounds>-0.75 0.70 -1.0 1.0</cropBounds>
  </FisheyeCropSettingsLeftView>
  <FisheyeCropSettingRightView>
    <cropBounds>-1.0 1.0 -1.0 1.0</cropBounds>
  </FisheyeCropSettingRightView>
  <FisheyeCropSettingsLeftView5x>
    <cropBounds>-0.75 0.70 -1.0 1.0</cropBounds>
  </FisheyeCropSettingsLeftView5x>
  <FisheyeCropSettingRightView5x>
    <cropBounds>-1.0 1.0 -1.0 1.0</cropBounds>
  </FisheyeCropSettingRightView5x>
  <FisheyeCropSettingsLeftRearView5x>
    <cropBounds>-0.75 0.70 -1.0 0.8</cropBounds>
  </FisheyeCropSettingsLeftRearView5x>
  <FisheyeCropSettingRightRearView5x>
    <cropBounds>-1.0 1.0 -1.0 0.8</cropBounds>
  </FisheyeCropSettingRightRearView5x>
  <FisheyeCropSettingsRemoteLeftView>
    <cropBounds>-0.40 0.48 -0.6 0.43</cropBounds>
  </FisheyeCropSettingsRemoteLeftView>
  <FisheyeCropSettingRemoteRightView>
    <cropBounds>-0.46 0.40 -0.6 0.43</cropBounds>
  </FisheyeCropSettingRemoteRightView>

  <FisheyeCropSettingsFrontVert>
    <cropBounds>-0.5 0.8 -0.65 0.65</cropBounds>
  </FisheyeCropSettingsFrontVert>
  <FisheyeCropSettingsRearVert>
    <cropBounds>-0.8 0.5 -0.65 0.65</cropBounds>
  </FisheyeCropSettingsRearVert>
  <FisheyeCropSettingsFrontPanoVert>
    <cropBounds>-0.75 0.75 -0.75 0.75</cropBounds>
  </FisheyeCropSettingsFrontPanoVert>
  <FisheyeCropSettingsRearPanoVert>
    <cropBounds>-0.74 0.74 -0.75 0.73</cropBounds>
  </FisheyeCropSettingsRearPanoVert>
  <FisheyeCropSettingsLeftViewVert>
    <cropBounds>-1.0 1.0 -1.0 1.0</cropBounds>
  </FisheyeCropSettingsLeftViewVert>
  <FisheyeCropSettingRightViewVert>
    <cropBounds>-1.0 1.0 -1.0 1.0</cropBounds>
  </FisheyeCropSettingRightViewVert>

  <FisheyeCropSettingsFrontVert5x>
    <cropBounds>-0.5 0.8 -0.65 0.65</cropBounds>
  </FisheyeCropSettingsFrontVert5x>
  <FisheyeCropSettingsRearVert5x>
    <cropBounds>-0.8 0.5 -0.65 0.65</cropBounds>
  </FisheyeCropSettingsRearVert5x>
  <FisheyeCropSettingsLeftViewVert5x>
    <cropBounds>-1.0 1.0 -1.0 1.0</cropBounds>
  </FisheyeCropSettingsLeftViewVert5x>
  <FisheyeCropSettingRightViewVert5x>
    <cropBounds>-1.0 1.0 -1.0 1.0</cropBounds>
  </FisheyeCropSettingRightViewVert5x>

  <Floor>
    <size>22 16</size>
    <resolution>0.2 0.2</resolution>
    <baseplateFilePath>cc/vehicle_model/basePlate.osg</baseplateFilePath>
    <stripedBasePlateTransparency>0.25</stripedBasePlateTransparency>
    <solidBasePlateColor>0.0 1.0 0.0</solidBasePlateColor>
  </Floor>
  <AudiFloorPlateRenderer>
    <texturedBasePlateFadeoutSpeedMS> 500 </texturedBasePlateFadeoutSpeedMS>
  </AudiFloorPlateRenderer>
  <AudiFloorPlateStateHandler>
    <maxHistoryStandstillDuration> 3.0 </maxHistoryStandstillDuration>
    <enableHistoricGroundplane> 1 </enableHistoricGroundplane>
  </AudiFloorPlateStateHandler>
  <BlurredFloorPlate>
    <resolution> 0.1 </resolution>
    <downsamplingLevels> 0 </downsamplingLevels>
    <nonPowerOfTwo> 1 </nonPowerOfTwo>
    <textureStretch> 0.2 </textureStretch>
  </BlurredFloorPlate>
  <FrontFloor>
    <size>7 12.4</size>
    <standardResolution>0.2 0.2</standardResolution>
    <areaHighResolution>3.0 4.5 -1.0 1.0</areaHighResolution>
    <highResolution>0.04 0.1</highResolution>
  </FrontFloor>
  <RearFloor>
    <size>6.5 12.4</size>
    <standardResolution>0.2 0.2</standardResolution>
    <areaHighResolution>-0.5 -2.5 -1.0 1.0</areaHighResolution>
    <highResolution>0.04 0.1</highResolution>
  </RearFloor>
  <CustomFloorPlate>
    <rearRightCorner>-1.40 -1.30</rearRightCorner>
    <frontLeftCorner> 4.20  1.30</frontLeftCorner>
    <odoTuningThreshold_Velocity>0.1</odoTuningThreshold_Velocity>
    <odoTuningThreshold_Yaw>0.1</odoTuningThreshold_Yaw>
    <odoTuningThreshold_Acceleration>15.0</odoTuningThreshold_Acceleration>
    <odoCompensate_Forward_Accelerate_k>0.1</odoCompensate_Forward_Accelerate_k>
    <odoCompensate_Forward_Accelerate_b>0.0</odoCompensate_Forward_Accelerate_b>
    <odoCompensate_Forward_Decelerate_k>0.05</odoCompensate_Forward_Decelerate_k>
    <odoCompensate_Forward_Decelerate_b>0.0</odoCompensate_Forward_Decelerate_b>
    <odoCompensate_Backward_Accelerate_k>-0.15</odoCompensate_Backward_Accelerate_k>
    <odoCompensate_Backward_Accelerate_b>0.0</odoCompensate_Backward_Accelerate_b>
    <odoCompensate_Backward_Decelerate_k>-0.05</odoCompensate_Backward_Decelerate_k>
    <odoCompensate_Backward_Decelerate_b>0.0</odoCompensate_Backward_Decelerate_b>
  </CustomFloorPlate>
  <MovingFloorPlate>
    <snapshotRearLeftCorner>-3.0 2.0 0.0</snapshotRearLeftCorner>
    <snapshotFrontRightCorner>6.0 -2.0 0.0</snapshotFrontRightCorner>
    <historytRearLeftCorner>-6.0 5.0 0.0</historytRearLeftCorner>
    <historytFrontRightCorner>9.0 -5.0 0.0</historytFrontRightCorner>
    <snapshotTextureWidth>512</snapshotTextureWidth>
    <snapshotTextureHeight>1024</snapshotTextureHeight>
    <historyTextureWidth>512</historyTextureWidth>
    <historyTextureHeight>1024</historyTextureHeight>
  </MovingFloorPlate>
  <Masking>
    <filename>cc/vehicle_model/masks.bin</filename>
  </Masking>
  <BaseDataSync>
    <updateMask>3</updateMask>
    <minDeltaExtCalibPos>0.02</minDeltaExtCalibPos>
    <minDeltaExtCalibAng>0.1</minDeltaExtCalibAng>
    <majorDeltaExtCalibAng>0.25</majorDeltaExtCalibAng>
    <timeDeltaMinorCalibUpdate_ms>2000</timeDeltaMinorCalibUpdate_ms>
    <timeDeltaMajorCalibUpdate_ms>500</timeDeltaMajorCalibUpdate_ms>
  </BaseDataSync>
  <Stitching>
    <frontRight>
      <min>290</min>
      <max>350</max>
    </frontRight>
    <rearRight>
      <min>190</min>
      <max>235</max>
    </rearRight>
    <rearLeft>
      <min>125</min>
      <max>170</max>
    </rearLeft>
    <frontLeft>
      <min>10</min>
      <max>70</max>
    </frontLeft>
  </Stitching>
  <VehicleModel>
    <modelFilePath>cc/vehicle_model/vehicle.osgb</modelFilePath>
    <componentNames>
      <frontLeftDoorMirror>LeftSideMirror</frontLeftDoorMirror>
      <frontRightDoorMirror>RightSideMirror</frontRightDoorMirror>
      <floorPlane>floor</floorPlane>
      <interior>interior1</interior>
    </componentNames>
  </VehicleModel>
  <CustomVehicleModel2D>
    <modelFilePath>cc/vehicle_model/vehicle2D.osgb</modelFilePath>
  </CustomVehicleModel2D>
  <TransparentVehicleModel>  <!-- for vehicle 3D -->
    <enableTransparency>1</enableTransparency>
    <targetTransparency>0.6</targetTransparency> <!-- 0.0 is transparency -->
    <transparencyStep>0.35</transparencyStep>
    <waitCycleInit>1</waitCycleInit> <!-- minimum value is 0 -->
    <waitCycle>35</waitCycle> <!-- minimum value is 1, waitCycle=waitCycleInit is better -->
  </TransparentVehicleModel>
  <CustomVehicleModel>
    <lightStateFilePath>cc/vehicle_model/lightstate.json</lightStateFilePath>
    <doorWarningTrans>0.6</doorWarningTrans>
    <transparency>0.9</transparency>  <!-- 0: non transpareny, 1: transparency -->
    <transparencyInterior>1.0</transparencyInterior>
    <halfTransparency>0.45</halfTransparency>  <!-- 0: non transpareny, 1: transparency -->
    <halfTransparencyInterior>0.5</halfTransparencyInterior>
  </CustomVehicleModel>
  <WipingIndicator>
    <period>0.3</period>
    <sustain>0.3</sustain>
    <delay>0.3</delay>
    <offset>0.0</offset>
  </WipingIndicator>
  <BlinkingIndicator>
    <blinkPeriod>10</blinkPeriod>
  </BlinkingIndicator>
  <DoorAnimation>
    <animDurationSec>2.0</animDurationSec>
  </DoorAnimation>
  <CameraFlight>
    <timeMin_s>0.5</timeMin_s>
    <timeMax_s>1.2</timeMax_s>
    <flightSpeed>3.0</flightSpeed>
  </CameraFlight>
  <CamerFlightSpeed>
    <coefficientCamerFlightSpeed>1.0</coefficientCamerFlightSpeed>  <!-- 1.0 is default setting as platform -->
    <deltaSample>0.05</deltaSample>
    <flightTypeCentered>1</flightTypeCentered>
    <centeredWarnDist>0.5</centeredWarnDist> <!-- meter, try to reduce it as much as posible -->
  </CamerFlightSpeed>
  <ParkingVirtCamerFlightSpeed>
    <coefficientCamerFlightSpeed>0.25</coefficientCamerFlightSpeed>  <!-- 1.0 is default setting as platform -->
    <deltaSample>0.005</deltaSample>
    <flightTypeCentered>1</flightTypeCentered>
    <centeredWarnDist>0.5</centeredWarnDist> <!-- meter, try to reduce it as much as posible -->
  </ParkingVirtCamerFlightSpeed>
  <RenderManager>
    <depthWriteFloor>0</depthWriteFloor>
    <depthWriteWall>0</depthWriteWall>
    <animationDuration>0.5</animationDuration>
    <camDisabledColor>0.6 0.6 0.6 1.0</camDisabledColor>
    <camOffColor>0.05 0.05 0.05 1.0</camOffColor>
  </RenderManager>
  <RenderOrder>
    <floor>10</floor>
    <wall>20</wall>
  </RenderOrder>
  <!-- Vehicle MechanicalData for DENZA MRHC -->
  <VehicleMechanicalData>
    <wheelbase>2.90</wheelbase>
    <wheelRadius>0.23</wheelRadius>
    <trackFront>1.64</trackFront> <!-- Front wheel distance-->
    <trackRear>1.64</trackRear>
    <wheelWidthFront>0.22</wheelWidthFront>
    <wheelWidthRear>0.22</wheelWidthRear>
    <axleToBumperDistanceFront>0.976</axleToBumperDistanceFront>
    <axleToBumperDistanceRear>1.102</axleToBumperDistanceRear>
    <width>1.91</width>
    <widthWithMirrors>2.125</widthWithMirrors>
    <height>1.495</height>
    <trailerBallPosition>-1.28 0.0 0.43</trailerBallPosition>
    <leftHandDrive>1</leftHandDrive>
    <automaticGearBox>1</automaticGearBox>
    <trailerHitch>0</trailerHitch>
  </VehicleMechanicalData> -->
  <Vehicle2DWheels>
    <wheelSize>0.8 0.27</wheelSize>
  </Vehicle2DWheels>
  <CrabWheel>
    <fadePeriod>1.0</fadePeriod>
    <wheelSize>0.8 0.3</wheelSize>
  </CrabWheel>
  <ViewModeStateMachine>
    <distTrigIn_m>0.5</distTrigIn_m>
    <distTrigOut_m>1.0</distTrigOut_m>
    <speedTrigIn_kph>15</speedTrigIn_kph>
    <speedTrigOut_kph>20</speedTrigOut_kph>
    <steeringAngleTrigIn>225</steeringAngleTrigIn>
    <steeringAngleTrigOut>90</steeringAngleTrigOut>
    <unavlMsgDisplay_Timeout>5000</unavlMsgDisplay_Timeout>
    <threatDuration>5000</threatDuration>
    <parkingScreen_Delay_ms>500</parkingScreen_Delay_ms>
    <parkingGuidanceScreen_Delay_ms>100</parkingGuidanceScreen_Delay_ms>
    <firstShowReq_Delay_ms>200</firstShowReq_Delay_ms>
    <sm_Delay_ms>1000</sm_Delay_ms> <!-- smaller than 100,000 msec -->
    <sm_IndicatorBackToFrontView_Delay_ms>300</sm_IndicatorBackToFrontView_Delay_ms>
    <sm_CpcShow_Delay_s>8</sm_CpcShow_Delay_s>
    <vehicleModels>15</vehicleModels> <!-- STEX: 6.0; STHX: 8.0; MREC: 2.0; MRHC: 3.0; HCEM: 15; HCHN: 18; STESA: 16; STHPA: 17 -->
  </ViewModeStateMachine>
  <!-- For PST -->
  <DAIUltrasonicZones><!-- V206 -->
    <frontArea1> 3.280  0.860  3.280  2.870</frontArea1>
    <frontArea2> 3.490  0.860  4.005  2.750</frontArea2>
    <frontArea3> 3.700  0.860  4.730  2.630</frontArea3>
    <frontArea4> 3.840  0.670  5.290  1.895</frontArea4>
    <frontArea5> 3.980  0.480  5.850  1.160</frontArea5>
    <frontArea6> 3.980  0.240  5.850  0.580</frontArea6>
    <leftArea1> -0.125  0.860 -0.125  2.530</leftArea1>
    <leftArea2>  0.343  0.860  0.343  2.530</leftArea2>
    <leftArea3>  0.810  0.860  0.810  2.530</leftArea3>
    <leftArea4>  1.210  0.860  1.210  2.530</leftArea4>
    <leftArea5>  1.610  0.860  1.610  2.530</leftArea5>
    <leftArea6>  2.015  0.860  2.015  2.530</leftArea6>
    <leftArea7>  2.420  0.860  2.420  2.530</leftArea7>
    <leftArea8>  2.850  0.860  2.850  2.530</leftArea8>
    <rearArea1> -0.348 -0.860 -0.848 -2.880</rearArea1>
    <rearArea2> -0.570 -0.860 -1.570 -2.710</rearArea2>
    <rearArea3> -0.825 -0.710 -2.148 -1.985</rearArea3>
    <rearArea4> -1.080 -0.560 -2.725 -1.260</rearArea4>
    <rearArea5> -1.106 -0.280 -2.725 -0.630</rearArea5>
    <rearArea6> -1.133  0.000 -2.725  0.000</rearArea6>
  </DAIUltrasonicZones>
  <!-- UltrasonicZones for GAC A18 from ECU coding -->
  <UltrasonicZones>
    <zone00middleLine> 4.03344   0.447033  6.28094   0.550533</zone00middleLine>
    <zone01middleLine> 3.70735   0.905058  4.89885   1.18556</zone01middleLine>
    <zone02middleLine> 3.09392   0.958852  3.09392   2.21635</zone02middleLine>
    <zone03middleLine> 2.1634    0.941238   2.1634  3.10624</zone03middleLine>
    <zone04middleLine> 1.33162   0.946705  1.33162   3.11171</zone04middleLine>
    <zone05middleLine> 0.308804  0.935373  0.308804  3.10037</zone05middleLine>
    <zone06middleLine>-0.716521  0.889217 -1.85152   2.08672</zone06middleLine>
    <zone07middleLine>-1.12468  0.362821 -3.34968   0.477821</zone07middleLine>
    <zone08middleLine>-1.12468  -0.362821 -3.34968   -0.477821</zone08middleLine>
    <zone09middleLine>-0.716521 -0.889217 -1.85152  -2.08672</zone09middleLine>
    <zone10middleLine> 0.308804 -0.935373  0.308804 -3.10037</zone10middleLine>
    <zone11middleLine> 1.33162  -0.946705     1.33162  -3.11171</zone11middleLine>
    <zone12middleLine> 2.1634  -0.941238     2.1634  -3.10624</zone12middleLine>
    <zone13middleLine> 3.09392  -0.958852  3.09392  -2.21635</zone13middleLine>
    <zone14middleLine> 3.70735  -0.905058  4.89885  -1.18556</zone14middleLine>
    <zone15middleLine> 4.03395  -0.442585  6.28144  -0.550085</zone15middleLine>
    <zone00leftBorderLine> 3.7565   0.88353  6.1395   1.09453</zone00leftBorderLine>
    <zone01leftBorderLine> 3.60854   0.942979  3.60854   1.29298</zone01leftBorderLine>
    <zone02leftBorderLine> 2.57929   0.94911  2.57929   3.11411</zone02leftBorderLine>
    <zone03leftBorderLine> 1.74751   0.943066  1.74751   3.10807</zone03leftBorderLine>
    <zone04leftBorderLine> 0.915733  0.939778     0.915733  3.10478</zone04leftBorderLine>
    <zone05leftBorderLine> -0.298123  0.945861   -0.298123  3.11086</zone05leftBorderLine>
    <zone06leftBorderLine>-1.03369  0.722327 -3.30369   0.952327</zone06leftBorderLine>
    <zone07leftBorderLine>-1.146     0.0      -3.326     0.000</zone07leftBorderLine>
    <zone08leftBorderLine>-1.03369 -0.722327 -3.30369  -0.952327</zone08leftBorderLine>
    <zone09leftBorderLine>-0.298123  -0.945861  -0.298123 -3.11086</zone09leftBorderLine>
    <zone10leftBorderLine> 0.915733 -0.939778     0.915733 -3.10478</zone10leftBorderLine>
    <zone11leftBorderLine> 1.74751  -0.943066     1.74751  -3.10807</zone11leftBorderLine>
    <zone12leftBorderLine> 2.57929  -0.94911  2.57929  -3.11411</zone12leftBorderLine>
    <zone13leftBorderLine> 3.60854  -0.942979  3.60854  -1.29298</zone13leftBorderLine>
    <zone14leftBorderLine> 3.7565  -0.88353  6.1395  -1.09453</zone14leftBorderLine>
    <zone15leftBorderLine> 4.08346  0.004772  6.19546 0.000772</zone15leftBorderLine>
  </UltrasonicZones>
  <VehicleContour>
    <front0> 3.313 0.963</front0>
    <front1> 3.623 0.942</front1>
    <front2> 3.76  0.882</front2>
    <front3> 3.926 0.71</front3>
    <front4> 4.027  0.504</front4>
    <front5> 4.084  0.0</front5>

    <side0>  3.314 0.960</side0>
    <side1>  2.098 0.940</side1>
    <side2>  1.298 0.947</side2>
    <side3>  0.504 0.932</side3>
    <side4> -0.364 0.947</side4>

    <rear0> -0.362 0.949</rear0>
    <rear1> -0.614 0.912</rear1>
    <rear2> -0.92  0.844</rear2>
    <rear3> -1.029 0.737</rear3>
    <rear4> -1.113 0.474</rear4>
    <rear5> -1.146 0.160</rear5>

    <unfoldedMirror0> 0.0 0.0</unfoldedMirror0>
    <unfoldedMirror1> 0.0 0.0</unfoldedMirror1>
    <unfoldedMirror2> 0.0 0.0</unfoldedMirror2>
    <unfoldedMirror3> 0.0 0.0</unfoldedMirror3>

    <foldedMirror0> 0.0 0.0</foldedMirror0>
    <foldedMirror1> 0.0 0.0</foldedMirror1>
    <foldedMirror2> 0.0 0.0</foldedMirror2>
    <foldedMirror3> 0.0 0.0</foldedMirror3>
  </VehicleContour>
  <!-- Vehicle contour for DAI V223
  <VehicleContour>
    <front0> 3.540 0.960</front0>
    <front1> 3.722 0.920</front1>
    <front2> 3.922 0.802</front2>
    <front3> 4.018 0.584</front3>
    <front4> 4.082 0.340</front4>
    <front5> 4.110 0.096</front5>

    <side0>  3.540 0.960</side0>
    <side1>  2.904 0.977</side1>
    <side2>  2.269 0.977</side2>
    <side3> -0.073 0.977</side3>
    <side4> -0.378 0.960</side4>

    <rear0> -0.378 0.960</rear0>
    <rear1> -0.857 0.886</rear1>
    <rear2> -1.028 0.830</rear2>
    <rear3> -1.134 0.712</rear3>
    <rear4> -1.196 0.430</rear4>
    <rear5> -1.195 0.071</rear5>

    <unfoldedMirror0> 0.0 0.0</unfoldedMirror0>
    <unfoldedMirror1> 0.0 0.0</unfoldedMirror1>
    <unfoldedMirror2> 0.0 0.0</unfoldedMirror2>
    <unfoldedMirror3> 0.0 0.0</unfoldedMirror3>

    <foldedMirror0> 0.0 0.0</foldedMirror0>
    <foldedMirror1> 0.0 0.0</foldedMirror1>
    <foldedMirror2> 0.0 0.0</foldedMirror2>
    <foldedMirror3> 0.0 0.0</foldedMirror3>
  </VehicleContour> -->
  <!-- Outline for L663_90 is below: -->
  <VehicleOutline>
    <pointCount>19</pointCount>
    <frontBumperStartPointIndex>0</frontBumperStartPointIndex>
    <frontBumperEndPointIndex>5</frontBumperEndPointIndex>
    <mirrorPointIndex>9</mirrorPointIndex>
    <rearBumperStartPointIndex>13</rearBumperStartPointIndex>
    <rearBumperEndPointIndex>18</rearBumperEndPointIndex>
    <points>
      <p00> 3.43046 0.140141</p00>
      <p01> 3.41285 0.398509</p01>
      <p02> 3.27975 0.701896</p02>
      <p03> 3.09184 0.938733</p03>
      <p04> 2.92156 0.985709</p04>
      <p05> 2.75127 0.995496</p05>
      <p06> 2.38916 0.999410</p06>
      <p07> 1.90766 0.999410</p07>
      <p08> 1.56121 0.999410</p08>
      <p09> 1.41637 1.054220</p09>
      <p10> 1.39679 0.985709</p10>
      <p11> 0.84562 0.985709</p11>
      <p12> 0.26367 0.985709</p12>
      <p13>-0.29043 0.985709</p13>
      <p14>-0.43332 0.972008</p14>
      <p15>-0.71321 0.850653</p15>
      <p16>-0.94614 0.635346</p16>
      <p17>-1.14383 0.386765</p17>
      <p18>-1.15949 0.133176</p18>
    </points>
  </VehicleOutline>
  <!-- Outline for L663_110 is below: -->
  <!--
  <VehicleOutline>
    <pointCount>20</pointCount>
    <frontBumperStartPointIndex>0</frontBumperStartPointIndex>
    <frontBumperEndPointIndex>5</frontBumperEndPointIndex>
    <mirrorPointIndex>9</mirrorPointIndex>
    <rearBumperStartPointIndex>14</rearBumperStartPointIndex>
    <rearBumperEndPointIndex>19</rearBumperEndPointIndex>
    <points>
      <p00> 3.86833 0.140141</p00>
      <p01> 3.85071 0.398509</p01>
      <p02> 3.71761 0.701896</p02>
      <p03> 3.52971 0.938733</p03>
      <p04> 3.35942 0.985709</p04>
      <p05> 3.18913 0.995496</p05>
      <p06> 2.82702 0.999410</p06>
      <p07> 2.34552 0.999410</p07>
      <p08> 1.99907 0.999410</p08>
      <p09> 1.85423 1.054220</p09>
      <p10> 1.83466 0.985709</p10>
      <p11> 1.31886 0.985709</p11>
      <p12> 0.78830 0.985709</p12>
      <p13> 0.23182 0.985709</p13>
      <p14>-0.29043 0.985709</p14>
      <p15>-0.43332 0.972008</p15>
      <p16>-0.71321 0.850653</p16>
      <p17>-0.94614 0.635346</p17>
      <p18>-1.14383 0.386765</p18>
      <p19>-1.15949 0.133176</p19>
    </points>
  </VehicleOutline>
  -->
  <TileOverlay>
    <extractorOffset>-0.25</extractorOffset>
    <heightOverGround>0.01</heightOverGround>
    <shieldAlpha>1.0</shieldAlpha>
    <solidLine2DAlpha>1.0</solidLine2DAlpha>
    <shadow2DAlphaInner>0.9</shadow2DAlphaInner>
    <shadow2DAlpha>0.35</shadow2DAlpha>
    <shield3DAlphaRatioTop>0.05</shield3DAlphaRatioTop>
    <shield3DAlphaRatioDivide>0.3</shield3DAlphaRatioDivide>
    <shield3DAlphaRatioBottom>0.7</shield3DAlphaRatioBottom>
    <shield3DSideAlphaRatioTop>0.02</shield3DSideAlphaRatioTop>
    <shield3DSideAlphaRatioDivide>0.7</shield3DSideAlphaRatioDivide>
    <shield3DSideAlphaRatioBottom>0.7</shield3DSideAlphaRatioBottom>
    <shield3DBottomCoverAlphaRatio>1.0</shield3DBottomCoverAlphaRatio>
    <shieldHeight>1.0</shieldHeight>
    <shieldThickness>0.2</shieldThickness>
    <solidLineThicknessRatio>0.3</solidLineThicknessRatio>
    <shield3DThicknessRatio>0.1</shield3DThicknessRatio>
    <shieldOffset>0.03</shieldOffset>
    <shieldTopOffset>0.0</shieldTopOffset>
    <shieldRatioDivide>0.3</shieldRatioDivide>
    <shieldSideRatioDivide>0.5</shieldSideRatioDivide>
    <hairlineWidth>1.0</hairlineWidth>
    <hairlineAlpha>0.5</hairlineAlpha>
    <hairlineBlooming>0.6</hairlineBlooming>
    <hairlineAlphaRatioTop>0.1</hairlineAlphaRatioTop>
    <hairlineAlphaRatioDivide>0.5</hairlineAlphaRatioDivide>
    <hairlineAlphaRatioBottom>0.9</hairlineAlphaRatioBottom>
    <distanceDefault>1.25</distanceDefault>
    <distanceThreshL1>0.3</distanceThreshL1>
    <distanceThreshL2>0.7</distanceThreshL2>
    <distanceThreshL3>1.2</distanceThreshL3>
    <HysteresisDistanceThresh>0.05</HysteresisDistanceThresh>
    <MinDistanceThresh>0.30</MinDistanceThresh>
    <MinDistanceThreshForPosDisp>0.30</MinDistanceThreshForPosDisp>
    <distanceThreshCorner>0.60</distanceThreshCorner>
    <!--<SplineTypeForEachSegment>1</SplineTypeForEachSegment>-->
    <smoothingFrontCornerSplineType>0.36</smoothingFrontCornerSplineType>
    <smoothingRearCornerSplineType>0.72</smoothingRearCornerSplineType>
    <colors>
      <r2>241 29 30 255</r2>
      <r1>241 29 30 255</r1>
      <y2>244 179 0 255</y2>
      <y1>244 179 0 255</y1>
      <g2>0 205 69 255</g2>
      <g1>0 205 69 255</g1>
    </colors>
  </TileOverlay>
  <Trajectory>
    <length>3.0</length>
    <gradientWidth>0.007</gradientWidth>
    <refLineVisible>0</refLineVisible>
    <animDurRemainingDistance>1000.0</animDurRemainingDistance>
    <outermostLine_Width>0.04</outermostLine_Width>
    <outermostLine_Color_Manual>0.97 0.66 0.02 1.0</outermostLine_Color_Manual>
    <outermostLine_Color_Auto>0.97 0.66 0.02 1.0</outermostLine_Color_Auto>
    <!-- RED YELLOW GREEN COLOR
    <outermostLine_Colorful_Color_1>1.0 0.0 0.0 0.9</outermostLine_Colorful_Color_1>
    <outermostLine_Colorful_Color_2>0.97 0.66 0.02 1.0</outermostLine_Colorful_Color_2>
    <outermostLine_Colorful_Color_3>0.0 0.745 0.27 1.0</outermostLine_Colorful_Color_3>
    -->
    <outermostLine_Colorful_Color_1>0.97 0.66 0.02 1.0</outermostLine_Colorful_Color_1>
    <outermostLine_Colorful_Color_2>0.97 0.66 0.02 1.0</outermostLine_Colorful_Color_2>
    <outermostLine_Colorful_Color_3>0.97 0.66 0.02 1.0</outermostLine_Colorful_Color_3>
    <outermostLine_OL_WT_minGap>0.12</outermostLine_OL_WT_minGap>
    <outermostLineColoful_DI_MagicOffset>0.014</outermostLineColoful_DI_MagicOffset>
    <outermostLine_rear_point_offset>0.15</outermostLine_rear_point_offset>
    <outermostLine_front_point_offset>0.20</outermostLine_front_point_offset>
    <outermostLine_VehContour_Gap>0.0</outermostLine_VehContour_Gap>
    <wheelTrack_Width_Whole>0.26</wheelTrack_Width_Whole>
    <wheelTrack_Width_BorderLine>0.013</wheelTrack_Width_BorderLine>
    <wheelTrack_Color_Manual_Inside>0.97 0.66 0.02 0.23</wheelTrack_Color_Manual_Inside>
    <wheelTrack_Color_Manual_BorderLine>0.97 0.66 0.02 0.8</wheelTrack_Color_Manual_BorderLine>
    <wheelTrack_Color_Auto_Close_Inside>0.95 0.90 0.0 0.23</wheelTrack_Color_Auto_Close_Inside>
    <wheelTrack_Color_Auto_Close_BorderLine>0.95 0.90 0.0 0.8</wheelTrack_Color_Auto_Close_BorderLine>
    <wheelTrack_Color_Auto_Far_Inside>0.91 0.62 0.0 0.23</wheelTrack_Color_Auto_Far_Inside>
    <wheelTrack_Color_Auto_Far_BorderLine>0.91 0.62 0.0 0.8</wheelTrack_Color_Auto_Far_BorderLine>
    <actionPoint_Length>0.01</actionPoint_Length>
    <actionPoint_Color>0.95 0.90 0.0 0.8</actionPoint_Color>
    <renderOffset_Front>-5.0</renderOffset_Front>  <!-- disable action point -->
    <renderOffset_Rear>-5.0</renderOffset_Rear>    <!-- disable action point -->
    <DL1_Width>0.01</DL1_Width>
    <DL1_Offset_Front>0.30</DL1_Offset_Front>
    <DL1_Offset_Rear>0.30</DL1_Offset_Rear>
    <DL1_Color>1.0 0.0 0.0 0.9</DL1_Color>
    <DIs>
      <DIlength>0.08</DIlength>
      <DI1_Pos>0.5</DI1_Pos>
      <DI1_Thickness>0.062</DI1_Thickness>
      <DI2_Pos>1.3</DI2_Pos>
      <DI2_Thickness>0.076</DI2_Thickness>
      <DI3_Pos>2.5</DI3_Pos>
      <DI3_Thickness>0.07</DI3_Thickness>
      <DI4_Pos>2.0</DI4_Pos>
      <DI4_Thickness>0.07</DI4_Thickness>
      <DI5_Pos>4.0</DI5_Pos>
      <DI5_Thickness>0.07</DI5_Thickness>
    </DIs>
    <THTraj_StartPoint>-1.116 0.0 0.0</THTraj_StartPoint> <!-- Dyn70k, height over the ground, recomend to set Z to '0' -->
    <THTraj_Width>0.1</THTraj_Width>
    <THTraj_Length>1.2</THTraj_Length>
    <THTraj_Color>0.99 0.99 0.99 0.95</THTraj_Color>
    <THTrajBorder_Color>0.97 0.66 0.02 0.0</THTrajBorder_Color>
    <THTraj_ColorGradientPosRatio>0.6</THTraj_ColorGradientPosRatio>
    <DrivablePath_Color>1 1 1 0.4 </DrivablePath_Color>
    <DrivablePath_BorderWidth>0.1</DrivablePath_BorderWidth>
    <DrivablePath_BorderColor>1 1 1 1 </DrivablePath_BorderColor>
  </Trajectory>
  <StopLine>
    <stopLineColor>0.0 0.56 1.0 0.9</stopLineColor>
  </StopLine>
  <CalibOverlay>
    <calibSwitch>1</calibSwitch>
    <calibOverlayColor>1.0 0.0 0.0 1.0</calibOverlayColor>
  </CalibOverlay>
  <SplineOverlay>
    <colors>
      <nearColor>0.953 0.0 0.039 1.0</nearColor>
      <middleColor>1.0 0.494 0.0 1.0</middleColor>
      <farColor>0.0 0.761 0.216 1.0</farColor>
      <nearColor_OffCourse>0.68 0.22 0.2 1.0</nearColor_OffCourse>
      <middleColor_OffCourse>0.96 0.72 0.32 1.0</middleColor_OffCourse>
      <farColor_OffCourse>1.0 1.0 1.0 0.4</farColor_OffCourse>
      <nearColor_OffCourse_Shadow>0.8 0.0 0.039 0.2</nearColor_OffCourse_Shadow>
      <middleColor_OffCourse_Shadow>0.8 0.6 0.0 0.2</middleColor_OffCourse_Shadow>
      <farColor_OffCourse_Shadow>1.0 1.0 1.0 0.2</farColor_OffCourse_Shadow>
      <shadowColor>0.0 0.0 0.0 0.0</shadowColor>
    </colors>
    <zPos>0.01</zPos>
    <distanceNear>0.3</distanceNear>
    <distanceMiddle>0.6</distanceMiddle>
    <distanceFar>0.9</distanceFar>
    <shadowOuterContourDist>4.0</shadowOuterContourDist>
    <splineWidth>0.04</splineWidth>
    <splineWidthShadow>1.0</splineWidthShadow>
    <maxShowDistance>2.0</maxShowDistance>
    <endPointOffset>0.7</endPointOffset>
    <handlePointScale>0.35</handlePointScale>
    <handlePointScaleMid>0.55</handlePointScaleMid>
    <farHandlePointScale>0.8</farHandlePointScale>
    <fadingWidth>10</fadingWidth>
  </SplineOverlay>
  <PanoramaViewFront>
    <horizontalStart>0.0</horizontalStart>
    <horizontalEnd>9.0</horizontalEnd>
    <verticalStart>0.0</verticalStart>
    <verticalEnd>6.0</verticalEnd>
    <resolutionHorizontal>20</resolutionHorizontal>
    <resolutionVertical>40</resolutionVertical>
    <planeWidth>9.0</planeWidth>
    <planeHeight>6.0</planeHeight>
    <cylinder>
      <cylinderRad>4.5</cylinderRad>
      <cylinderHeight>6.0</cylinderHeight>
      <cylinderFovDeg>180.0</cylinderFovDeg>
      <offsetVertical>0.5</offsetVertical>
      <offsetHorizontal>0.0</offsetHorizontal>
      <x1Factor>23</x1Factor>
      <x2Factor>61</x2Factor>
      <x3Factor>193</x3Factor>
      <x4Factor>231</x4Factor>
      <c1>1.0</c1>
      <c2>1.0</c2>
      <point0>0.0</point0>
      <point1>0.0</point1>
      <point2>0.0</point2>
      <point3>0.0</point3>
      <point4>0.0</point4>
      <point5>0.0</point5>
      <smoothing>0</smoothing>
    </cylinder>
  </PanoramaViewFront>
  <PanoramaViewRear>
    <horizontalStart>0.0</horizontalStart>
    <horizontalEnd>9.0</horizontalEnd>
    <verticalStart>0.0</verticalStart>
    <verticalEnd>6.0</verticalEnd>
    <resolutionHorizontal>20</resolutionHorizontal>
    <resolutionVertical>40</resolutionVertical>
    <planeWidth>9.0</planeWidth>
    <planeHeight>6.0</planeHeight>
    <cylinder>
      <cylinderRad>4.5</cylinderRad>
      <cylinderHeight>6.0</cylinderHeight>
      <cylinderFovDeg>180.0</cylinderFovDeg>
      <offsetVertical>1.0</offsetVertical>
      <offsetHorizontal>0.0</offsetHorizontal>
      <x1Factor>23</x1Factor>
      <x2Factor>61</x2Factor>
      <x3Factor>190</x3Factor>
      <x4Factor>227</x4Factor>
      <c1>1.0</c1>
      <c2>1.0</c2>
      <point0>0.0</point0>
      <point1>0.0</point1>
      <point2>0.0</point2>
      <point3>0.0</point3>
      <point4>0.0</point4>
      <point5>0.0</point5>
      <smoothing>0</smoothing>
    </cylinder>
  </PanoramaViewRear>
  <VirtCam>
    <plan>
      <eye>1.3 0 15</eye>
      <center>1.3 0 7.8</center>
      <up>1 0 0</up>
      <fovy>45</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>0 0 0 0</stitchingLines>
      <stitchingBands>0.4 0.4 0.4 0.4</stitchingBands>
      <stitchingBandsFolded>0.1 0.1 0.1 0.1</stitchingBandsFolded>
    </plan>
    <front>
      <eye>4.2 0 1.27</eye>
      <center>5.05 0 0.75</center>
      <up>0 0 1</up>
      <fovy>90</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingMaskFolded>0</stitchingMaskFolded>
      <stitchingLines>315 225 135 45</stitchingLines>
      <stitchingLinesFolded>275 225 135 78</stitchingLinesFolded>
      <cullingMask>0</cullingMask>
    </front>
    <rear>
      <eye>-1.43 0 0.70</eye>
      <center>-2.34 0 0.29</center>
      <up>-0.41 0 0.91</up>
      <fovy>107</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </rear>
    <frontJunction>
      <eye>3.95 0 0.89</eye>
      <center>4.856 0 0.467</center>
      <up>0 0 1</up>
      <fovy>90</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </frontJunction>
    <rearJunction>
      <eye>3.95 0 0.89</eye>
      <center>4.856 0 0.467</center>
      <up>0 0 1</up>
      <fovy>90</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </rearJunction>
    <leftView>
      <eye>1.47 2.38 1.84</eye>
      <center>1.70 2.38 0.87</center>
      <up>1 0 0</up>
      <fovy>90</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </leftView>
    <rightView>
      <eye>1.47 -2.38 1.84</eye>
      <center>1.70 -2.38 0.87</center>
      <up>1 0 0</up>
      <fovy>90</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </rightView>
    <trailer>
      <eye>-0.78 0 0.92</eye>
      <center>-1.52 0 0.38</center>
      <up>0 0 1</up>
      <fovy>95</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </trailer>
    <trailerZoom>
      <eye>-0.78 0 0.92</eye>
      <center>-1.52 0 0.38</center>
      <up>0 0 1</up>
      <fovy>80</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </trailerZoom>
    <bonnet>
      <eye>-0.33 0 1.77</eye>
      <center>0.64 0 1.51</center>
      <up>0.26 0 0.97</up>
      <fovy>50</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>270 225 135 90</stitchingLines>
    </bonnet>
    <vertBonnet>
      <eye>-0.33 0 1.77</eye>
      <center>0.64 0 1.51</center>
      <up>0.0 -1 0.0</up>
      <fovy>50</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>270 225 135 90</stitchingLines>
    </vertBonnet>
    <driveAssistLeft>
      <eye>1.92655 1.02723 1.29545</eye>
      <center>2.71 1.24 0.71</center>
      <up>0 -0.15 1</up>
      <fovy>60</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </driveAssistLeft>
    <driveAssistMain>
      <eye>3.93 0 0.70506</eye>
      <center>4.84 0 0.29</center>
      <up>0 0 1</up>
      <fovy>100</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>270 225 135 90</stitchingLines>
    </driveAssistMain>
    <driveAssistRight>
      <eye>1.92655 -1.02723 1.29545</eye>
      <center>2.71 -1.24 0.71</center>
      <up>0 0.15 1</up>
      <fovy>60</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </driveAssistRight>
    <driveAssistDualFront>
      <eye>3.93 0 0.70506</eye>
      <center>4.84 0 0.29</center>
      <up>0 0 1</up>
      <fovy>100</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>270 225 135 90</stitchingLines>
    </driveAssistDualFront>
    <driveAssistDualRight>
      <eye>1.92655 -1.02723 1.29545</eye>
      <center>2.71 -1.24 0.71</center>
      <up>0 0.11 1</up>
      <fovy>60</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </driveAssistDualRight>
    <driveAssistDualLeft>
      <eye>1.92655 1.02723 1.29545</eye>
      <center>2.71 1.24 0.71</center>
      <up>0 -0.11 1</up>
      <fovy>60</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </driveAssistDualLeft>
    <frontLeft>
      <eye>4.83 3.87 3.56</eye>
      <center>4.28 3.24 3.01</center>
      <up>-0.37 -0.42 0.83</up>
      <fovy>50</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>315 225 175 1</stitchingLines>
    </frontLeft>
    <rearLeft>
      <eye>-2.41 3.45 3.56</eye>
      <center>-1.79 2.89 3.01</center>
      <up>-0.41 0.37 0.83</up>
      <fovy>50</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>315 225 175 1</stitchingLines>
    </rearLeft>
    <frontRight>
      <eye>4.83 -3.87 3.56</eye>
      <center>4.28 -3.24 3.01</center>
      <up>0.37 -0.42 0.83</up>
      <fovy>50</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>359 190 135 45</stitchingLines>
    </frontRight>
    <rearRight>
      <eye>-2.41 -3.45 3.56</eye>
      <center>-1.79 -2.89 3.01</center>
      <up>0.41 0.37 0.83</up>
      <fovy>50</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>359 190 135 45</stitchingLines>
    </rearRight>
    <persFront>
      <eye>6.72 0 3.44</eye>
      <center>5.9 0 2.86</center>
      <up>0 0 1</up>
      <fovy>50</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>359 225 120 45</stitchingLines>
    </persFront>
    <persRight>
      <eye>1.37 -4.47 3.84</eye>
      <center>1.37 -3.77 3.12</center>
      <up>0 -0.73 0.70</up>
      <fovy>50</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>359 225 120 45</stitchingLines>
    </persRight>
    <persRear>
      <eye>-4.38 0 2.68</eye>
      <center>-3.49 0 2.22</center>
      <up>0 0 1</up>
      <fovy>50</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>270 225 120 45</stitchingLines>
    </persRear>
    <persLeft>
      <eye>1.37 4.47 3.84</eye>
      <center>1.37 3.77 3.12</center>
      <up>0 -0.73 0.70</up>
      <fovy>50</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>359 180 120 45</stitchingLines>
    </persLeft>
    <kerbLeft>
      <eye>-1.6 1.51 1.25</eye>
      <center>-0.47 1.22 0.71</center>
      <up>0.35 -0.14 0.93</up>
      <fovy>50</fovy>
      <stitchingMask>15</stitchingMask>
      <stitchingLines>0 180 180 0</stitchingLines>
    </kerbLeft>
    <kerbRight>
      <eye>-1.6 -1.51 1.25</eye>
      <center>-0.47 -1.22 0.71</center>
      <up>0.35 0.14 0.93</up>
      <fovy>50</fovy>
      <stitchingMask>15</stitchingMask>
      <stitchingLines>359 180 180 0</stitchingLines>
    </kerbRight>
    <frontThreat>
      <eye>4.4 0 2.6</eye>
      <center>4.4 0 1.6</center>
      <up>1 0 0</up>
      <fovy>90</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>270 270 90 90</stitchingLines>
    </frontThreat>
    <rearThreat>
      <eye>-1.6 0 2</eye>
      <center>-1.6 0 1</center>
      <up>1 0 0</up>
      <fovy>90</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>270 270 90 90</stitchingLines>
    </rearThreat>
    <fullScreen>
      <eye>1.3 0 9</eye>
      <center>1.3 0 5.5</center>
      <up>0 -1 0</up>
      <fovy>35</fovy>
      <stitchingMask>15</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </fullScreen>
    <frontWheelLeft>
      <eye>1.75 1.0 1.34</eye>
      <center>2.43 1.01 0.61</center>
      <up>0.73 0.01 0.68</up>
      <fovy>60</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </frontWheelLeft>
    <frontWheelRight>
      <eye>1.75 -1.0 1.34</eye>
      <center>2.40 -1.01 0.58</center>
      <up>0.76 -0.01 0.65</up>
      <fovy>60</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </frontWheelRight>
    <bothWheelLeft>
      <eye>1.61 0.9 2.64</eye>
      <center>1.63 0.90 1.64</center>
      <up>1.00 0.00 0.00</up>
      <fovy>60</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </bothWheelLeft>
    <bothWheelRight>
      <eye>1.73 -1.0 2.24</eye>
      <center>1.74 -1.00 1.24</center>
      <up>1.06 0.00 0.00</up>
      <fovy>60</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </bothWheelRight>
    <rearWheelLeft>
      <eye>1.89 1.0 1.53</eye>
      <center>1.12 0.99 0.90</center>
      <up>-0.63 -0.01 0.77</up>
      <fovy>60</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </rearWheelLeft>
    <rearWheelRight>
      <eye>1.89 -1.0 1.53</eye>
      <center>1.12 -1.01 0.89</center>
      <up>-0.64 -0.01 0.77</up>
      <fovy>60</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </rearWheelRight>
    <overTheRoof>
      <eye>-7.5 0.0 8.25</eye>
      <center>1.4 0.0 0.0</center>
      <up>0.679819 0 0.73338</up>
      <fovy>45</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </overTheRoof>
    <parkView>
      <eye>-39.68 0.0 25.0</eye>
      <center>0 0.0 0</center>
      <up>0.43 0.00 0.90</up>
      <fovy>37.45</fovy>
      <stitchingMask>15</stitchingMask>
      <stitchingMaskFolded>6</stitchingMaskFolded>
      <stitchingLines>315 225 135 45</stitchingLines>
      <stitchingLinesFolded>275 225 135 78</stitchingLinesFolded>
      <cullingMask>0</cullingMask>
    </parkView>
    <virtualParkSearching>
      <eye>-30.0 0.0 13.0</eye>
      <center>1.3 0 0.12</center>
      <up>0 0 1</up>
      <fovy>45</fovy>
    </virtualParkSearching>
    <virtualParkConfirm>
      <eye>-0.1 0 32.0</eye>
      <center>1.3 0 0.12</center>
      <up>1 0 0</up>
      <fovy>45</fovy>
    </virtualParkConfirm>
    <virtualParkOutConfirm>
      <eye>-0.1 0 24.66</eye>
      <center>1.3 0 0.12</center>
      <up>1 0 0</up>
      <fovy>45</fovy>
    </virtualParkOutConfirm>
    <virtualParkGuidance>
      <eye>-10.0 0 25.0</eye>
      <center>1.3 0 0.12</center>
      <up>0 0 1</up>
      <fovy>45</fovy>
    </virtualParkGuidance>
    <virtualParkCompleteCross>
      <eye>-18 0 3.5</eye>
      <center>0 0 0.12</center>
      <up>0 0 1</up>
      <fovy>45</fovy>
    </virtualParkCompleteCross>
    <virtualParkCompletePara>
      <eye>2.30 -20.0 3.57</eye>
      <center>2.25 -19.0 3.38</center>
      <up>0 0 1</up>
      <fovy>45</fovy>
    </virtualParkCompletePara>
    <virtualParkCompleteDiagonal>
      <eye>-12.70 -11.22 4.35</eye>
      <center>-11.95 -10.61 4.13</center>
      <up>0 0 1</up>
      <fovy>45</fovy>
    </virtualParkCompleteDiagonal>
    <normal3D>
      <eye>-2.41 3.45 3.56</eye>
      <center>-1.79 2.89 3.01</center>
      <up>-0.41 0.37 0.83</up>
      <fovy>50</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>315 225 175 1</stitchingLines>
    </normal3D>
    <fullScreen3D>
      <eye>-3.30756283 0.0335036293 4.16841221</eye>
      <center>-2.54322624 0.0281674657 3.52361679</center>
      <up>0.644779742 -0.00450148527 0.764355183</up>
      <fovy>88.************843</fovy> <!-- Full/Half AR * 50 -->
      <stitchingMask>0</stitchingMask>
      <stitchingLines>270 225 120 45</stitchingLines>
    </fullScreen3D>
    <!-- Vertical mode -->
    <vertPlan>
      <eye>1.3 0 15</eye>
      <center>1.3 0 7.8</center>
      <up>0 -1 0</up>
      <fovy>45</fovy>
      <stitchingMask>15</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </vertPlan>
    <vertLeftView>
      <eye>1.47 2.38 1.84</eye>
      <center>1.70 2.38 0.87</center>
      <up>0 -1 0</up>
      <fovy>90</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </vertLeftView>
    <vertRightView>
      <eye>1.47 -2.38 1.84</eye>
      <center>1.70 -2.38 0.87</center>
      <up>0 -1 0</up>
      <fovy>90</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </vertRightView>
    <vertFrontWheelLeftView>
      <eye>1.75 1.0 1.34</eye>
      <center>1.75 1.0 0.34</center>
      <up>0 -1 0</up>
      <fovy>60</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </vertFrontWheelLeftView>
    <vertFrontWheelRightView>
      <eye>1.75 -1.0 1.34</eye>
      <center>1.75 -1.0 0.34</center>
      <up>0 -1 0</up>
      <fovy>60</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </vertFrontWheelRightView>
    <vertWheelLeftView>
      <eye>1.35 1.0 2.44</eye>
      <center>1.35 1.0 1.44</center>
      <up>0 -1 0</up>
      <fovy>60</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </vertWheelLeftView>
    <vertWheelRightView>
      <eye>1.35 -1.0 2.44</eye>
      <center>1.35 -1.0 1.44</center>
      <up>0 -1 0</up>
      <fovy>60</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </vertWheelRightView>
    <vertRearWheelLeftView>
      <eye>1.89 1.0 1.53</eye>
      <center>1.12 0.99 -0.90</center>
      <up>0 -1 0</up>
      <fovy>60</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </vertRearWheelLeftView>
    <vertRearWheelRightView>
      <eye>1.89 -1.0 1.53</eye>
      <center>1.12 -1.01 -0.89</center>
      <up>0 -1 0</up>
      <fovy>60</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </vertRearWheelRightView>
    <vertPersFront>
      <eye>6.54 0 4.92</eye>
      <center>5.81 0 4.24</center>
      <up>0 1 0</up>
      <fovy>53</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>359 225 120 45</stitchingLines>
    </vertPersFront>
    <vertPersRear>
      <eye>-3.75 0 4.92</eye>
      <center>-3.02 0 4.24</center>
      <up>0 -1 0</up>
      <fovy>53</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>270 225 120 45</stitchingLines>
    </vertPersRear>
    <vertRearLeft>
      <eye>-1.41 4.29 4.94</eye>
      <center>-1.01 3.68 4.25</center>
      <up>-0.84 -0.55 0</up>
      <fovy>53</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>315 225 175 1</stitchingLines>
    </vertRearLeft>
    <vertRearRight>
      <eye>-1.41 -4.29 4.94</eye>
      <center>-1.01 -3.68 4.25</center>
      <up>0.84 -0.55 0</up>
      <fovy>53</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>359 190 135 45</stitchingLines>
    </vertRearRight>
    <vertFrontLeft>
      <eye>4.03 4.41 4.94</eye>
      <center>3.65 3.78 4.25</center>
      <up>-0.86 0.51 0</up>
      <fovy>53</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>315 225 175 1</stitchingLines>
    </vertFrontLeft>
    <vertFrontRight>
      <eye>4.03 -4.41 4.94</eye>
      <center>3.65 -3.78 4.25</center>
      <up>0.86 0.51 0</up>
      <fovy>53</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>359 190 135 45</stitchingLines>
    </vertFrontRight>
    <!-- parking plan view -->
    <horiParkingPlan>
      <eye>1.3 0 15</eye>
      <center>1.3 0 7.8</center>
      <up>1 0 0</up>
      <fovy>45</fovy>
      <stitchingMask>15</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </horiParkingPlan>
    <frontBumper>
      <eye>4.18 0 6.04</eye>
      <center>4.31 0 5.04</center>
      <up>0.99 0 0.13</up>
      <fovy>45</fovy>
      <stitchingMask>15</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </frontBumper>
    <rearBumper>
      <eye>-1.39 0 6.04</eye>
      <center>-1.39 0 5.04</center>
      <up>1.0 0 0.0</up>
      <fovy>45</fovy>
      <stitchingMask>15</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </rearBumper>
    <ultraWide>
      <eye>-9.29 0 5.0</eye>
      <center>-8.33 0 4.73</center>
      <up>0.27 0 0 0.96</up>
      <fovy>45</fovy>
      <stitchingMask>15</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </ultraWide>
    <planetary>
      <eye>0.05 0.0 20.23</eye>
      <center>0.15 0.0 19.24</center>
      <up>1 0 0</up>
      <fovy>50</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>359 180 180 0</stitchingLines>
    </planetary>
    <vertParkingPlan>
      <eye>1.3 0 15</eye>
      <center>1.3 0 7.8</center>
      <up>0 -1 0</up>
      <fovy>45</fovy>
      <stitchingMask>15</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </vertParkingPlan>
    <vertParkView>
      <eye>-25.5 0.0 9.68</eye>
      <center>-24.59 0.0 9.26</center>
      <up>0 -1 0</up>
      <fovy>45</fovy>
      <stitchingMask>15</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </vertParkView>
    <vertVirtualParkSearching>
      <eye>-35.0 0.0 20.0</eye>
      <center>1.3 0 0.12</center>
      <up>0 -1 0</up>
      <fovy>45</fovy>
    </vertVirtualParkSearching>
    <vertVirtualParkConfirm>
      <eye>-0.1 0 28.0</eye>
      <center>0 0 0.12</center>
      <up>0 -1 0</up>
      <fovy>45</fovy>
    </vertVirtualParkConfirm>
    <vertVirtualParkGuidance>
      <eye>-18.0 0 28.0</eye>
      <center>0 0 0.12</center>
      <up>0 -1 0</up>
      <fovy>45</fovy>
    </vertVirtualParkGuidance>
    <vertVirtualParkCompleteCross>
      <eye>-18 0 3.5</eye>
      <center>0 0 0.12</center>
      <up>0 -1 0</up>
      <fovy>45</fovy>
    </vertVirtualParkCompleteCross>
    <vertVirtualParkCompletePara>
      <eye>2.30 -20.0 3.57</eye>
      <center>2.25 -19.0 3.38</center>
      <up>1 0 0</up>
      <fovy>45</fovy>
    </vertVirtualParkCompletePara>
    <vertVirtualParkCompleteDiagonal>
      <eye>-16.70 -14.52 5.35</eye>
      <center>-11.95 -10.61 4.13</center>
      <up>1 0 0</up>
      <fovy>45</fovy>
    </vertVirtualParkCompleteDiagonal>
  </VirtCam>
  <Views>
    <usableCanvasViewport>
      <origin>0 0</origin>
      <size>720 1920</size>
    </usableCanvasViewport>
    <planViewport>
      <origin>0 252</origin> <!-- 126+18， 90+18 -->
      <size>720 894</size>
    </planViewport>
    <remotePlanViewport>
      <origin>0 0</origin>
      <size>750 1080</size>
    </remotePlanViewport>
    <!-- need to consider plan view horizontal offset +4 -->
    <mainViewport>
      <origin>0 1149</origin>
      <size>720 639</size>
    </mainViewport>
    <remoteMainViewport>
      <origin>753 0</origin>
      <size>1167 1080</size>
    </remoteMainViewport>
    <!-- Viewports in APA -->
    <apaPlanViewport>
      <origin>0 90</origin>
      <size>1047 990</size> <!-- 1020+18+9, 954+18+18-->
    </apaPlanViewport>
    <fourWindowLeftViewport>
      <origin>822 108</origin>
      <size>531 369</size>
    </fourWindowLeftViewport>
    <fourWindowRightViewport>
      <origin>1371 108</origin>
      <size>531 369</size>
    </fourWindowRightViewport>
    <fourWindowCombinedViewport>
      <origin>1371 108</origin>
      <size>531 369</size>
    </fourWindowCombinedViewport>
    <apaParkingPlanViewport>
      <origin>975 108</origin>
      <size>927 954</size>
    </apaParkingPlanViewport>
    <apaAssistViewport>
      <origin>822 495</origin>
      <size>1080 567</size>
    </apaAssistViewport>
    <!-- End of viewports in APA -->
    <driveAssistMain>
      <origin>245 490</origin>
      <size>639 590</size>
    </driveAssistMain>
    <driveAssistLeft>
      <origin>640 0</origin>
      <size>540 720</size>
    </driveAssistLeft>
    <driveAssistRight>
      <origin>1180 0</origin>
      <size>540 720</size>
    </driveAssistRight>
    <dayNightDATripleLeft>
      <origin>884 490</origin>
      <size>4 590</size>
    </dayNightDATripleLeft>
    <dayNightDATripleRight>
      <origin>241 490</origin>
      <size>4 590</size>
    </dayNightDATripleRight>
    <driveAssistDualFrontViewportFL>
      <origin>385 490</origin>
      <size>744 590</size>
    </driveAssistDualFrontViewportFL>
    <driveAssistDualFrontViewportFR>
      <origin>0 490</origin>
      <size>744 590</size>
    </driveAssistDualFrontViewportFR>
    <driveAssistDualLeftViewport>
      <origin>0 490</origin>
      <size>381 590</size>
    </driveAssistDualLeftViewport>
    <driveAssistDualRightViewport>
      <origin>748 490</origin>
      <size>381 590</size>
    </driveAssistDualRightViewport>
    <wideViewport>
      <origin>190 0</origin>
      <size>1570 720</size>
    </wideViewport>
    <dayNightViewport>
      <origin>640 0</origin>
      <size>0 720</size>
    </dayNightViewport>
    <dayNightTADualViewport>
      <origin>1008 490</origin>
      <size>3 590</size>
    </dayNightTADualViewport>
    <dayNightTATripleLeftViewport>
      <origin>665 490</origin>
      <size>4 590</size>
    </dayNightTATripleLeftViewport>
    <dayNightTATripleRightViewport>
      <origin>1350 490</origin>
      <size>4 590</size>
    </dayNightTATripleRightViewport>
    <dayNightDADualLeftViewport>
      <origin>381 490</origin>
      <size>4 590</size>
    </dayNightDADualLeftViewport>
    <dayNightDADualRightViewport>
      <origin>744 490</origin>
      <size>4 590</size>
    </dayNightDADualRightViewport>
    <fusiView>
      <origin>0 0</origin>
      <size>40 10</size>
    </fusiView>
    <fisheyeViewport>
      <origin>495 0</origin>
      <size>1233 668</size>
    </fisheyeViewport>
    <fullScreenViewport>
      <origin>495 0</origin>
      <size>1233 668</size>
    </fullScreenViewport>
    <frontWheelLeft>
      <origin>0 1149</origin>
      <size>330 639</size>
    </frontWheelLeft>
    <frontWheelRight>
      <origin>390 1149</origin>
      <size>330 639</size>
    </frontWheelRight>
    <rearWheelLeft>
      <origin>0 1149</origin>
      <size>330 639</size>
    </rearWheelLeft>
    <rearWheelRight>
      <origin>390 1149</origin>
      <size>330 639</size>
    </rearWheelRight>
    <settingBarViewport>
      <origin>0 0</origin>
      <size>190 720</size>
    </settingBarViewport>
    <parkView>
      <origin>0 0</origin>
      <size>495 820</size>
    </parkView>
    <!-- Vertical mode -->
    <vertParkingPlanViewport>
      <origin>0 0</origin>
      <size>822 1080</size>
    </vertParkingPlanViewport>
    <vertFullScreenViewport>
      <origin>0 0</origin>
      <size>1622 1080</size>
    </vertFullScreenViewport>
    <vertMainViewport>
      <origin>0 0</origin>
      <size>761 1080</size>
    </vertMainViewport>
    <vertPlanViewport>
      <origin>761 0</origin>
      <size>861 540</size>
    </vertPlanViewport>
    <vertSideViewport>
      <origin>761 540</origin>
      <size>861 540</size>
    </vertSideViewport>
    <vertParkingUIViewport>
      <origin>834 0</origin>
      <size>1025 1080</size> <!-- 969 + 9 + 47, -->
    </vertParkingUIViewport>
    <vertWheelLeftViewport>
      <origin>0 0</origin>
      <size>761 480</size>
    </vertWheelLeftViewport>
    <vertWheelRightViewport>
      <origin>0 600</origin>
      <size>761 480</size>
    </vertWheelRightViewport>
    <vertWheelSeparator>
      <origin>0 480</origin>
      <size>761 120</size>
    </vertWheelSeparator>
    <!--Real view size for APA top view-->
    <realApaParkingPlanViewport>            <!-- origin is hu topleft corner at vehicle-->
      <origin>550 200</origin>
      <size>550 743</size>
    </realApaParkingPlanViewport>
    <realVertApaParkingPlanViewport>        <!-- origin is hu topleft corner at vehicle after rotate-->
      <origin>0 216</origin>
      <size>434 586</size>
    </realVertApaParkingPlanViewport>
    <!-- ST24 viewport setting, from HU resolution and also 0,0 origin should start from left top corner -->
    <!-- Real viewport for 2D and 3D viewport-->
    <realMainViewport>
      <origin>1096 24</origin>
      <size>1440 1272</size>
    </realMainViewport>
    <realMainViewportVert>
      <origin>24 24</origin>
      <size>1392 1000</size>
    </realMainViewportVert>
    <!-- Real virtual plan view-->
    <realVirtualPlanviewPort>
      <origin>0 87</origin>
      <size>550 856</size>
    </realVirtualPlanviewPort>
    <realVirtualPlanviewPortVert>
      <origin>0 802</origin>
      <size>1080 1014</size>
    </realVirtualPlanviewPortVert>
    <apaParkingPlanViewport>
      <origin>0 0</origin>
      <size>760 864</size>
    </apaParkingPlanViewport>
    <parkSingleViewport>
      <origin>0 0</origin>
      <size>864 488</size>
    </parkSingleViewport>
    <parkTop>
      <origin>0 0</origin>
      <size>536 770</size>
    </parkTop>
    <sideFrontRearViewport>
      <origin>0 870</origin>
      <size>858 972</size>
    </sideFrontRearViewport>
  </Views>
  <CustomRawFisheyeView>
    <viewport>
      <origin>0 490</origin>
      <size>747 590</size>
    </viewport>
  </CustomRawFisheyeView>
  <WheelAnimation>
    <rollSpeedLimit>16.0</rollSpeedLimit>
  </WheelAnimation>
  <OdometryConverter>
    <filter_x>0</filter_x>
    <filter_y>0</filter_y>
    <filter_yaw>0</filter_yaw>
    <yaw_in_degrees>1</yaw_in_degrees>
    <thresholdX>0.60</thresholdX>
    <thresholdY>0.30</thresholdY>
    <thresholdYaw>5.0</thresholdYaw>
  </OdometryConverter>
  <OverlayData>
    <ICE_stallTimeout>10</ICE_stallTimeout>
    <SATCAM_rear_tailgatePosition>0</SATCAM_rear_tailgatePosition>
  </OverlayData>
  <PlanView>
    <widthMeters>9.0</widthMeters>
    <widthMetersVeh2dDiff>0.6</widthMetersVeh2dDiff>
    <widthMetersParkingHori>13.5</widthMetersParkingHori>
    <widthMetersFullscreen>9.0</widthMetersFullscreen>
    <widthMetersVeh2dFullscreenDiff>0.0</widthMetersVeh2dFullscreenDiff>
    <widthMetersVert>9.0</widthMetersVert>
    <widthMetersVeh2dDiffVert>0.0</widthMetersVeh2dDiffVert>
    <widthMetersFullscreenVert>9.0</widthMetersFullscreenVert>
    <widthMetersVeh2dFullscreenDiffVert>0.0</widthMetersVeh2dFullscreenDiffVert>
    <widthMetersParkingVert>13.0</widthMetersParkingVert>
    <widthMetersBumper>6.5</widthMetersBumper>
  </PlanView>
  <!-- View-specific bowls -->
  <BonnetViewBowl>
    <numRadialSections>64</numRadialSections>
    <numHeightSections>16</numHeightSections>
    <bowlDefault>
        <semiaxis>7.3 4.8</semiaxis>
        <height>7</height>
    </bowlDefault>
  </BonnetViewBowl>
  <BonnetViewFloor>
    <size>10.0 10.0</size>
    <resolution>0.2 0.2</resolution>
  </BonnetViewFloor>
  <DayNightData>
    <dayColor>0.9333 0.9333 0.9333</dayColor>
    <nightColor>0.0824 0.0824 0.0824</nightColor>
  </DayNightData>
  <EngineeringView>
    <mode>2</mode>  <!-- Backchannel Screen ID 3-->
                    <!-- Tow assist eng screen 4-->
  </EngineeringView>
  <BowlShaperDefault> <!-- LARGE BOWL --> <!-- CVS_Bowl_Size_Tall in dcm -->
    <bowlDefault>
        <semiaxis>12.0 10.0</semiaxis>
        <height>7.0</height>
    </bowlDefault>
  </BowlShaperDefault>
  <BowlShaperMedium> <!-- MEDIUM BOWL, apply for horizontal view mode --> <!-- CVS_Bowl_Size_Mid in dcm -->
    <bowlDefault>
        <semiaxis>6.0 4.0</semiaxis>
        <height>4.0</height>
    </bowlDefault>
  </BowlShaperMedium>
  <BowlShaperSmall> <!-- SMALL BOWL, apply for verical view mode --> <!-- CVS_Bowl_Size_Small in dcm -->
    <bowlDefault>
        <semiaxis>6 3.8</semiaxis>
        <height>3.35</height>
        <n>2</n>
        <depth>5.0</depth>
        <slope>2.0</slope>
        <densityShift>1.4</densityShift>
    </bowlDefault>
  </BowlShaperSmall>
  <FloorPlateSm>
    <minDrivenDistX>6.0</minDrivenDistX>
    <minDrivenDistY>1.5</minDrivenDistY>
    <speedTrigVehTrans_lower_kph>0</speedTrigVehTrans_lower_kph>
    <speedTrigVehTrans_upper_kph>20</speedTrigVehTrans_upper_kph>
  </FloorPlateSm>
  <TranspSm>
    <transpDurnThreshold_ms>660</transpDurnThreshold_ms>
    <opaqueDurnThreshold_ms>2640</opaqueDurnThreshold_ms>
    <transition2opaqueDuration>3000.0</transition2opaqueDuration>
    <transition2transpDuration>3000.0</transition2transpDuration>
    <transpDurnThreshold_Parking_ms>660</transpDurnThreshold_Parking_ms>
    <opaqueDurnThreshold_Parking_ms>19800</opaqueDurnThreshold_Parking_ms>
    <transition2opaqueDuration_Parking>3000.0</transition2opaqueDuration_Parking>
    <transition2transpDuration_Parking>3000.0</transition2transpDuration_Parking>
    <minDrivenDistX>6.0</minDrivenDistX>
    <minDrivenDistY>1.5</minDrivenDistY>
    <vehicleOpacity>0.7</vehicleOpacity>  <!-- 0.0 is transparency -->  <!-- for vehicle 2D -->
    <wheelOpacity>1.0</wheelOpacity>  <!-- for vehicle 2D -->
  </TranspSm>
  <BlurSm>
    <deblurDurnThreshold_ms>660</deblurDurnThreshold_ms> <!-- duration from baseplate default texture to baseplate transparancy -->
    <blurDurnThreshold_ms>2640</blurDurnThreshold_ms>     <!-- duration from baseplate transparancy to baseplate default texture -->
    <anim2blurDuration>3000.0</anim2blurDuration>
    <anim2normalDuration>3000.0</anim2normalDuration>
    <blurredStateAlpha>0.0</blurredStateAlpha>
    <blurLevelDuringDriving>1.0</blurLevelDuringDriving>  <!-- 1.0 is transparency -->  <!-- for baseplate -->
    <movingTooFastThreshold_Kmh>60.0</movingTooFastThreshold_Kmh>
  </BlurSm>
  <GbcVehicle>
    <minDrivenDistX>6.0</minDrivenDistX>
    <minDrivenDistY>1.5</minDrivenDistY>
    <noSpeedTimeout>5.0</noSpeedTimeout>
    <transpAlpha>0.4</transpAlpha>
    <OpaqueToTransparentAnimationTime>2.0</OpaqueToTransparentAnimationTime>
    <TransparentToOpaqueAnimationTime>2.0</TransparentToOpaqueAnimationTime>
  </GbcVehicle>
  <GbcFloor>
    <minDrivenDistX>6.0</minDrivenDistX>
    <minDrivenDistY>1.5</minDrivenDistY>
    <noSpeedTimeout>5.0</noSpeedTimeout>
    <blurLevel>0.4</blurLevel>
    <BlurredToTexturedAnimationTime>2.0</BlurredToTexturedAnimationTime>
    <TexturedToBlurredAnimationTime>2.0</TexturedToBlurredAnimationTime>
  </GbcFloor>
  <HemisphereCamera>
    <minDistance>4.0</minDistance>
    <maxDistance>9.0</maxDistance>
    <minElevation>0.3</minElevation>     <!-- modify the minElevationHu in huhemidata as well -->
    <maxElevation>1.57</maxElevation>     <!-- modify the maxElevationHu in huhemidata as well -->
    <zoomDistanceInterpolationSpeed>30.0</zoomDistanceInterpolationSpeed>
    <sphericalInterpolationSpeed>200.0</sphericalInterpolationSpeed>
    <centerInterpolationSpeed>10.0</centerInterpolationSpeed>
  </HemisphereCamera>
  <huhemisdata>
    <zoomScale>8</zoomScale>
    <minElevationHu>0.3</minElevationHu>     <!-- keep the same as minElevation in HemisphereCamera -->
    <maxElevationHu>1.57</maxElevationHu>     <!-- keep the same as maxElevation in HemisphereCamera -->
    <zoomfactorHori>0.3</zoomfactorHori>      <!--0: HemisphereCamera maxDistance, 1: HemisphereCamera minDistance-->
    <zoomfactorVert>0.2</zoomfactorVert>      <!--0: HemisphereCamera maxDistance, 1: HemisphereCamera minDistance-->
    <fullscreenElevation>0.9</fullscreenElevation>
    <normalElevation>0.54</normalElevation>
    <zoomL1>0.5</zoomL1>
    <zoomL2>0.7</zoomL2>
    <zoomL3>0.85</zoomL3>
    <zoomL4>1.0</zoomL4>
    <zoomL5>1.1</zoomL5>
    <zoomL6>1.25</zoomL6>
    <zoomL7>1.5</zoomL7>
    <defaultDistanceFactor>0.5</defaultDistanceFactor>
  </huhemisdata>
  <CarCenter>
    <carCenterHori>0.0 0.0 0.12</carCenterHori>    <!-- x, y is difference w/ vehicle center -->
    <carCenterVert>0.0 0.0 0.02</carCenterVert>    <!-- x, y is difference w/ vehicle center -->
  </CarCenter>
  <PanAndZoom2D>
    <minDistance>4.0</minDistance>
    <maxDistance>15.0</maxDistance>
    <zoomDistanceInterpolationSpeed>30.0</zoomDistanceInterpolationSpeed>
    <positionInterpolationSpeed>200.0</positionInterpolationSpeed>
    <panningAreaMin>-3.0 -2.5</panningAreaMin>
    <panningAreaMax> 3.0  2.5</panningAreaMax>
  </PanAndZoom2D>
  <ParkingConfirmInterface>
    <settingParkAPAConfirm>
      <iconCenter>1160 340</iconCenter>
      <responseArea>148 148</responseArea>
    </settingParkAPAConfirm>
    <settingParkAPAParkOutConfirm>
      <iconCenter>960 360</iconCenter>
      <responseArea>148 148</responseArea>
    </settingParkAPAParkOutConfirm>
    <settingParkAPAParkOutConfirmText>  <!--text indicator -->
      <iconCenter>960 510</iconCenter>
    </settingParkAPAParkOutConfirmText>
    <settingParkAPAConfirmText>  <!--text indicator -->
      <iconCenter>1160 450</iconCenter>
    </settingParkAPAConfirmText>
    <settingParkRPAConfirm>
      <iconCenter>760 340</iconCenter>
      <responseArea>148 148</responseArea>
    </settingParkRPAConfirm>
    <settingParkRPAConfirmText>  <!--text indicator -->
      <iconCenter>760 450</iconCenter>
    </settingParkRPAConfirmText>
    <settingPARKAPADrvReqInd>
      <iconCenter>1160 510</iconCenter>
    </settingPARKAPADrvReqInd>
    <settingPARKRPASketch>
      <iconSize>474 252</iconSize>
      <iconCenter>1160 360</iconCenter>
    </settingPARKRPASketch>
    <settingPARKRPAChecklist>
      <iconSize>331 158</iconSize>
      <iconCenter>750 360</iconCenter>
    </settingPARKRPAChecklist>
    <settingPARKRPADrvReqInd>
      <iconCenter>960 360</iconCenter>
    </settingPARKRPADrvReqInd>
    <settingPARKConfirmTitle>
      <iconSize>232 27</iconSize>
      <iconCenter>960 180</iconCenter>
    </settingPARKConfirmTitle>
    <settingPARKConfirmExit>
      <iconCenter>960 550</iconCenter>
      <responseArea>100 100</responseArea>
    </settingPARKConfirmExit>
    <settingPARKConfirmSeparator>
      <iconCenter>960 520</iconCenter>
    </settingPARKConfirmSeparator>
    <settingPARKRPAConfirmSeparator>
      <iconCenter>960 210</iconCenter>
    </settingPARKRPAConfirmSeparator>
    <settingPARKPopOutBox>
      <iconSize>830 550</iconSize>
      <iconCenter>960 360</iconCenter>
    </settingPARKPopOutBox>
  </ParkingConfirmInterface>
  <ParkingSpotManager>
    <speedLimit>0.001</speedLimit>
    <parkOutCrossSpotYPos>2.5</parkOutCrossSpotYPos>
    <parkOutCrossFrontSpotXPos>6.2</parkOutCrossFrontSpotXPos>
    <parkOutCrossRearSpotXPos>-6.0</parkOutCrossRearSpotXPos>
    <parkOutParaSpotYPos>2.0</parkOutParaSpotYPos>
    <parkOutParaSpotXPos>7.5</parkOutParaSpotXPos>
    <parkingSpotHmiLength>5.2</parkingSpotHmiLength>
    <parkingSpotHmiWidth>2.4</parkingSpotHmiWidth>
    <spotOffsetX>5.0</spotOffsetX>
    <leftfreeParkSpotID>113</leftfreeParkSpotID>
    <rightfreeParkSpotID>114</rightfreeParkSpotID>
    <diagonalSpotLength>5.60</diagonalSpotLength>
    <diagonalSpotWidth>4.60</diagonalSpotWidth>
    <adjustmentValueOfOverlapDistance> 0.21 </adjustmentValueOfOverlapDistance>
    <adjustmentValueOfOverlapDistanceDiagonal> 0.5 </adjustmentValueOfOverlapDistanceDiagonal>
    <diagonalResponseAreaScaleFactor>0.75</diagonalResponseAreaScaleFactor>
    <spotDistanceBetweenLandRside> 4.0 </spotDistanceBetweenLandRside>
    <slotHalfWidth>1.2</slotHalfWidth> <!--Unit: in meter on UI planview-->
    <slotRear2RearAxelDistance>1.2</slotRear2RearAxelDistance> <!--Unit: in meter on UI planview-->
    <slotFront2RearAxelDistance>4.0</slotFront2RearAxelDistance>
  </ParkingSpotManager>
  <FreeparkingManager>
    <defaultPositionCross>-2.0 -4.47</defaultPositionCross>
    <defaultPositionParallel>-2.52 -2.85</defaultPositionParallel>
    <defaultPositionDiagonal>-2.52 -4.39</defaultPositionDiagonal>
    <defaultAngle>0.0</defaultAngle>
    <widthOffset>0.2</widthOffset> <!--default for parallel slot-->
    <lengthOffset>0.2</lengthOffset> <!--default for parallel slot-->
    <horizontalPad_default>1</horizontalPad_default>
    <freeparkingSlotWidth>2.2</freeparkingSlotWidth> <!-- actual width of freeparking slot in meter -->
    <rotateButtonRadius> 67 </rotateButtonRadius>
    <viewportBoundbackScale>1.05</viewportBoundbackScale>
    <rotateRange>10.0</rotateRange>
    <imageProperties>
      <slotWidthPixel>124.0</slotWidthPixel>
      <slotHeightPixel>296.0</slotHeightPixel>
      <!-- <rotateIconWidthPixel>37.0</rotateIconWidthPixel>
      <rotateIconHeightPixel>37.0</rotateIconHeightPixel> -->
      <rotateIconWidthExtendPixel>16.0</rotateIconWidthExtendPixel>
      <rotateIconHeightExtendPixel>15.0</rotateIconHeightExtendPixel>
      <!-- <imageWidthPixel>222.0</imageWidthPixel>
      <imageHeightPixel>444.0</imageHeightPixel> -->
    </imageProperties>
  </FreeparkingManager>
  <AugmentedViewTransition>
    <transitionWaveAnimationDuration>0.5</transitionWaveAnimationDuration>
    <transitionWaveMaxRadius>6.5</transitionWaveMaxRadius>
    <transitionWaveWidth>0.4</transitionWaveWidth>
    <scanWaveAnimationDuration>1.8</scanWaveAnimationDuration>
    <scanWaveMaxRadius>6.5</scanWaveMaxRadius>
    <scanWaveWidth>0.3</scanWaveWidth>
    <scanWaveBurstSize>1.0</scanWaveBurstSize>
    <scanWaveInterBurstTime>0.75</scanWaveInterBurstTime>
    <scanWaveIntraBurstTime>0.5</scanWaveIntraBurstTime>
    <scanWaveTravelStartingPoint>0.0</scanWaveTravelStartingPoint>  <!-- percentage of travel distance-->
    <scanWaveCenterXVert>2.0</scanWaveCenterXVert>
    <trueColor>0.1 0.36 0.51 1.0</trueColor>
    <position>-0.57 0.0 0.0</position>
  </AugmentedViewTransition>
  <UIElements>
    <cpcOverlaySwitchPressMR>
      <iconCenter>1820 890</iconCenter>
      <responseArea>200 200</responseArea>
    </cpcOverlaySwitchPressMR>
    <cpcOverlaySwitchPressST>
      <iconCenter>2360 1120</iconCenter>
      <responseArea>400 400</responseArea>
    </cpcOverlaySwitchPressST>
    <cpcOverlaySwitchPress_Hori> <!-- TODO: to be defined -->
      <iconCenter>1820 890</iconCenter>
      <responseArea>400 400</responseArea>
    </cpcOverlaySwitchPress_Hori>
    <cpcOverlaySwitchPress_Vert> <!-- TODO: to be defined -->
      <iconCenter>2360 1120</iconCenter>
      <responseArea>400 400</responseArea>
    </cpcOverlaySwitchPress_Vert>
    <swVersionShowSwitchBMR>
      <iconCenter>710 100</iconCenter>
      <responseArea>200 200</responseArea>
    </swVersionShowSwitchBMR>
    <swVersionShowSwitchCMR>
      <iconCenter>1820 100</iconCenter>
      <responseArea>200 200</responseArea>
    </swVersionShowSwitchCMR>
    <swVersionShowSwitchBST>
      <iconCenter>880 200</iconCenter>
      <responseArea>400 400</responseArea>
    </swVersionShowSwitchBST>
    <swVersionShowSwitchB_Hori> <!-- TODO: to be defined -->
      <iconCenter>880 200</iconCenter>
      <responseArea>400 400</responseArea>
    </swVersionShowSwitchB_Hori>
    <swVersionShowSwitchB_Vert> <!-- TODO: to be defined -->
      <iconCenter>880 200</iconCenter>
      <responseArea>400 400</responseArea>
    </swVersionShowSwitchB_Vert>
    <swVersionShowSwitchCST>
      <iconCenter>2360 200</iconCenter>
      <responseArea>400 400</responseArea>
    </swVersionShowSwitchCST>
    <swVersionShowSwitchC_Hori> <!-- TODO: to be defined -->
      <iconCenter>2360 200</iconCenter>
      <responseArea>400 400</responseArea>
    </swVersionShowSwitchC_Hori>
    <swVersionShowSwitchC_Vert> <!-- TODO: to be defined -->
      <iconCenter>2360 200</iconCenter>
      <responseArea>400 400</responseArea>
    </swVersionShowSwitchC_Vert>
    <settingQuit>
      <iconSize>53 27</iconSize>
      <iconCenter>79 68</iconCenter>
      <responseArea>80 80</responseArea>
    </settingQuit>
    <settingPPDIS>
      <iconSize>100 100</iconSize>
      <iconCenter>79 171</iconCenter>
      <responseArea>76 76</responseArea>
    </settingPPDIS>
    <settingPPON>
      <iconSize>100 100</iconSize>
      <iconCenter>79 171</iconCenter>
      <responseArea>76 76</responseArea>
    </settingPPON>
    <settingAutoCamActiv>
      <iconSize>76 76</iconSize>
      <iconCenter>79 288</iconCenter>
      <responseArea>76 76</responseArea>
    </settingAutoCamActiv>
    <!-- TODO -->
    <vehicleTransIcon>
      <iconSize>135 295</iconSize>
      <iconCenter>248 350</iconCenter>
    </vehicleTransIcon>
    <vehicleTransIconVert>
      <iconSize>235 110</iconSize>
      <iconCenter>741 540</iconCenter>
    </vehicleTransIconVert>
    <settingTextBoxSafeNotification>
      <iconSize>213 28</iconSize>
      <iconCenter>1103 627</iconCenter>
    </settingTextBoxSafeNotification>
    <settingTextBoxSafeNotification_vert>
      <iconSize>29 170</iconSize>
      <iconCenter>606 540</iconCenter>
    </settingTextBoxSafeNotification_vert>
    <settingParkingPlanIconLeft>
      <iconSize>76 64</iconSize>
      <iconCenter>588 1045</iconCenter>
    </settingParkingPlanIconLeft>
    <settingParkingPlanIconRight>
      <iconSize>76 64</iconSize>
      <iconCenter>876 1045</iconCenter>
    </settingParkingPlanIconRight>
    <settingParkingPlanIconLeft_vert>
      <iconSize>76 64</iconSize>
      <iconCenter>549 1030</iconCenter>
    </settingParkingPlanIconLeft_vert>
    <settingParkingPlanIconRight_vert>
      <iconSize>76 64</iconSize>
      <iconCenter>549 696</iconCenter>
    </settingParkingPlanIconRight_vert>
    <settingPARKIcon>
      <iconSize>148 148</iconSize>
      <iconCenter>840 80</iconCenter>
      <responseArea>148 148</responseArea>
    </settingPARKIcon>
    <settingPARKModeSelected>
      <iconSize>420 540</iconSize>
    </settingPARKModeSelected>
    <settingTextModeSelect>
      <iconSize>318 32</iconSize>
      <iconCenter>960 68</iconCenter>
    </settingTextModeSelect>
    <settingPARKAutoPic>
      <iconSize>121 264</iconSize>
      <iconCenter>248 608</iconCenter>
    </settingPARKAutoPic>
    <settingPARKAutoPic_Hori>
      <iconSize>121 264</iconSize>
      <iconCenter>248 284</iconCenter>
    </settingPARKAutoPic_Hori>
    <settingPARKAutoPic_Vert>
      <iconSize>121 264</iconSize>
      <iconCenter>400 540</iconCenter>
    </settingPARKAutoPic_Vert>
    <settingTextBoxSlotSearching_Hori>
      <iconSize>432 38</iconSize>
      <iconCenter>248 127</iconCenter>
    </settingTextBoxSlotSearching_Hori>
    <settingTextBoxSlotSearching_Vert>
      <iconCenter>107 360</iconCenter>
    </settingTextBoxSlotSearching_Vert>
    <settingParkingUIBackground_Hori>
      <iconSize>488 760</iconSize>
      <iconCenter>248 390</iconCenter>
    </settingParkingUIBackground_Hori>
    <settingParkingUIBackground_Vert>
      <iconSize>1080 1014</iconSize>
      <iconCenter>507 540</iconCenter>
    </settingParkingUIBackground_Vert>
    <settingSmallParkAutoPic>
      <iconSize>58 127</iconSize>
      <iconCenter>248 216</iconCenter>
    </settingSmallParkAutoPic>
    <settingCrossVehiclePic_Hori>
      <iconCenter>248 507</iconCenter>
    </settingCrossVehiclePic_Hori>
    <settingCrossVehiclePic_Vert>
      <iconCenter>584 540</iconCenter>
    </settingCrossVehiclePic_Vert>
    <settingSmallSlotAutoPic_Hori>
      <iconSize>58 127</iconSize>
      <iconCenter>248 247</iconCenter>
    </settingSmallSlotAutoPic_Hori>
    <settingSmallSlotAutoPic_Vert>
      <iconSize>58 127</iconSize>
      <iconCenter>211 360</iconCenter>
    </settingSmallSlotAutoPic_Vert>
    <settingSmallParkAutoPic_Hori>
      <iconSize>58 127</iconSize>
      <iconCenter>248 216</iconCenter>
    </settingSmallParkAutoPic_Hori>
    <settingSmallParkAutoPic_Vert>
      <iconCenter>183 360</iconCenter>
    </settingSmallParkAutoPic_Vert>
    <settingSmallAutoPicPara_Hori>
      <iconCenter>248 247</iconCenter>
    </settingSmallAutoPicPara_Hori>
    <settingSmallAutoPicPara_Vert>
      <iconCenter>248 365</iconCenter>
    </settingSmallAutoPicPara_Vert>
    <settingSmallAutoPicVertFrontInLeft_Hori>
      <iconCenter>220 483</iconCenter>
    </settingSmallAutoPicVertFrontInLeft_Hori>
    <settingSmallAutoPicVertFrontInLeft_Vert>
      <iconCenter>392 360</iconCenter>
    </settingSmallAutoPicVertFrontInLeft_Vert>
    <settingSmallAutoPicVertFrontInRight_Hori>
      <iconCenter>248 489</iconCenter>
    </settingSmallAutoPicVertFrontInRight_Hori>
    <settingSmallAutoPicVertFrontInRight_Vert>
      <iconCenter>392 360</iconCenter>
    </settingSmallAutoPicVertFrontInRight_Vert>
    <settingSmallAutoPicVertRearInLeft_Hori>
      <iconCenter>220 286</iconCenter>
    </settingSmallAutoPicVertRearInLeft_Hori>
    <settingSmallAutoPicVertRearInLeft_Vert>
      <iconCenter>210 360</iconCenter>
    </settingSmallAutoPicVertRearInLeft_Vert>
    <settingSmallAutoPicVertRearInRight_Hori>
      <iconCenter>248 290</iconCenter>
    </settingSmallAutoPicVertRearInRight_Hori>
    <settingSmallAutoPicVertRearInRight_Vert>
      <iconCenter>210 360</iconCenter>
    </settingSmallAutoPicVertRearInRight_Vert>
    <settingSmallParkAutoPicHoriCompletedParkOut>
      <iconSize>58 127</iconSize>
      <iconCenter>248 326</iconCenter>
    </settingSmallParkAutoPicHoriCompletedParkOut>
    <settingStarkParkButton_Hori>
      <iconSize>134 49</iconSize>
      <iconCenter>248 665</iconCenter>
      <responseArea>134 49</responseArea>
    </settingStarkParkButton_Hori>
    <settingGuidanceGearD>
      <iconSize>78 78</iconSize>
      <iconCenter>248 54</iconCenter>
    </settingGuidanceGearD>
    <settingSuspendQuitButton_Hori>
      <iconSize>134 49</iconSize>
      <iconCenter>301 656</iconCenter>
      <responseArea>134 49</responseArea>
    </settingSuspendQuitButton_Hori>
    <settingSuspendQuitButton_Vert>
      <iconSize>134 49</iconSize>
      <iconCenter>516 445</iconCenter>
      <responseArea>134 49</responseArea>
    </settingSuspendQuitButton_Vert>
    <settingGuideHorParallel>
      <iconSize>113 114</iconSize>
      <iconCenter>263 329</iconCenter>
    </settingGuideHorParallel>
    <settingSuspendContinueButton_Hori>
      <iconSize>134 49</iconSize>
      <iconCenter>131 656</iconCenter>
      <responseArea>134 49</responseArea>
    </settingSuspendContinueButton_Hori>
    <settingSuspendContinueButton_Vert>
      <iconSize>134 49</iconSize>
      <iconCenter>516 275</iconCenter>
      <responseArea>134 49</responseArea>
    </settingSuspendContinueButton_Vert>
    <settingPARKSearchingRoad>
      <iconSize>1255 850</iconSize>
      <iconCenter>960 360</iconCenter>
    </settingPARKSearchingRoad>
    <settingPARKSearchingWave1>
      <iconSize>402 284</iconSize>
      <iconCenter>960 520</iconCenter>
    </settingPARKSearchingWave1>
    <settingPARKSearchingWave2>
      <iconSize>278 177</iconSize>
      <iconCenter>960 520</iconCenter>
    </settingPARKSearchingWave2>
    <settingPARKParaSlotValid_Hori>
      <iconSize>92 186</iconSize>
      <responseArea>92 186</responseArea>
    </settingPARKParaSlotValid_Hori>
    <settingPARKVertSlotValid_Hori>
      <iconSize>186 92</iconSize>
      <responseArea>186 92</responseArea>
    </settingPARKVertSlotValid_Hori>
    <settingPARKDiagSlotValid_Hori>
      <iconSize>190 95</iconSize>
      <responseArea>190 95</responseArea>
    </settingPARKDiagSlotValid_Hori>
    <settingPARKParaSlotValid_Vert>
      <iconSize>83 168</iconSize>
      <responseArea>83 168</responseArea>
    </settingPARKParaSlotValid_Vert>
    <settingPARKVertSlotValid_Vert>
      <iconSize>168 83</iconSize>
      <responseArea>168 83 </responseArea>
    </settingPARKVertSlotValid_Vert>
    <settingPARKDiagSlotValid_Vert>
      <iconSize>171 86</iconSize>
      <responseArea>171 86</responseArea>
    </settingPARKDiagSlotValid_Vert>
    <!-- Vertical Slots UI-->
    <settingHoriSearchingVerticalSlot1L>
      <iconCenter>91 300</iconCenter>
    </settingHoriSearchingVerticalSlot1L>
    <settingHoriSearchingVerticalSlot2L>
      <iconCenter>91 384</iconCenter>
    </settingHoriSearchingVerticalSlot2L>
    <settingHoriSearchingVerticalSlot3L>
      <iconCenter>91 468</iconCenter>
    </settingHoriSearchingVerticalSlot3L>
    <settingHoriSearchingVerticalSlot4L>
      <iconCenter>91 552</iconCenter>
    </settingHoriSearchingVerticalSlot4L>
    <settingHoriSearchingVerticalSlot1R>
      <iconCenter>341 300</iconCenter>
    </settingHoriSearchingVerticalSlot1R>
    <settingHoriSearchingVerticalSlot2R>
      <iconCenter>341 384</iconCenter>
    </settingHoriSearchingVerticalSlot2R>
    <settingHoriSearchingVerticalSlot3R>
      <iconCenter>341 468</iconCenter>
    </settingHoriSearchingVerticalSlot3R>
    <settingHoriSearchingVerticalSlot4R>
      <iconCenter>341 552</iconCenter>
    </settingHoriSearchingVerticalSlot4R>
    <!-- Parallel Slots UI-->
    <settingHoriSearchingParallelSlot1L>
      <iconCenter>64 253</iconCenter>
    </settingHoriSearchingParallelSlot1L>
    <settingHoriSearchingParallelSlot2L>
      <iconCenter>64 399</iconCenter>
    </settingHoriSearchingParallelSlot2L>
    <settingHoriSearchingParallelSlot3L>
      <iconCenter>64 545</iconCenter>
    </settingHoriSearchingParallelSlot3L>
    <settingHoriSearchingParallelSlot1R>
      <iconCenter>368 253</iconCenter>
    </settingHoriSearchingParallelSlot1R>
    <settingHoriSearchingParallelSlot2R>
      <iconCenter>368 399</iconCenter>
    </settingHoriSearchingParallelSlot2R>
    <settingHoriSearchingParallelSlot3R>
      <iconCenter>368 545</iconCenter>
    </settingHoriSearchingParallelSlot3R>
    <settingHoriSelectedSlotVertRightRearIn_Hori>
      <iconCenter>336 469</iconCenter>
    </settingHoriSelectedSlotVertRightRearIn_Hori>
    <settingHoriSelectedSlotVertRightFrontIn_Hori>
      <iconCenter>336 301</iconCenter>
    </settingHoriSelectedSlotVertRightFrontIn_Hori>
    <settingHoriSelectedSlotVertLeftRearIn_Hori>
      <iconCenter>96 469</iconCenter>
    </settingHoriSelectedSlotVertLeftRearIn_Hori>
    <settingHoriSelectedSlotVertLeftFrontIn_Hori>
      <iconCenter>96 301</iconCenter>
    </settingHoriSelectedSlotVertLeftFrontIn_Hori>
    <settingHoriSelectedSlotParaRight_Hori>
      <iconCenter>360 400</iconCenter>
    </settingHoriSelectedSlotParaRight_Hori>
    <settingHoriSelectedSlotParaLeft_Hori>
      <iconCenter>72 400</iconCenter>
    </settingHoriSelectedSlotParaLeft_Hori>
    <settingHoriSelectedSlotVertRightRearIn_Vert>
      <iconCenter>372 471</iconCenter>
    </settingHoriSelectedSlotVertRightRearIn_Vert>
    <settingHoriSelectedSlotVertRightFrontIn_Vert>
      <iconCenter>231 471</iconCenter>
    </settingHoriSelectedSlotVertRightFrontIn_Vert>
    <settingHoriSelectedSlotVertLeftRearIn_Vert>
      <iconCenter>372 248</iconCenter>
    </settingHoriSelectedSlotVertLeftRearIn_Vert>
    <settingHoriSelectedSlotVertLeftFrontIn_Vert>
      <iconCenter>231 248</iconCenter>
    </settingHoriSelectedSlotVertLeftFrontIn_Vert>
    <settingHoriSelectedSlotParaRight_Vert>
      <iconCenter>377 490</iconCenter>
    </settingHoriSelectedSlotParaRight_Vert>
    <settingHoriSelectedSlotParaLeft_Vert>
      <iconCenter>377 230</iconCenter>
    </settingHoriSelectedSlotParaLeft_Vert>
    <settingHoriCombinationDiagRight_Hori>
      <iconCenter>302 336</iconCenter>
    </settingHoriCombinationDiagRight_Hori>
    <settingHoriCombinationDiagLeft_Hori>
      <iconCenter>130 336</iconCenter>
    </settingHoriCombinationDiagLeft_Hori>
    <settingHoriCombinationDiagRight_Vert>
      <iconCenter>292 438</iconCenter>
    </settingHoriCombinationDiagRight_Vert>
    <settingHoriCombinationDiagLeft_Vert>
      <iconCenter>292 282</iconCenter>
    </settingHoriCombinationDiagLeft_Vert>
    <settingHoriCompleteCombinationDiagRight_Hori>
      <iconCenter>334 383</iconCenter>
    </settingHoriCompleteCombinationDiagRight_Hori>
    <settingHoriCompleteCombinationDiagLeft_Hori>
      <iconCenter>98 383</iconCenter>
    </settingHoriCompleteCombinationDiagLeft_Hori>
    <settingHoriCompleteCombinationDiagRight_Vert>
      <iconCenter>345 478</iconCenter>
    </settingHoriCompleteCombinationDiagRight_Vert>
    <settingHoriCompleteCombinationDiagLeft_Vert>
      <iconCenter>345 242</iconCenter>
    </settingHoriCompleteCombinationDiagLeft_Vert>
    <settingParkOutSmallAuto_Hori>
      <iconCenter>248 339</iconCenter>
    </settingParkOutSmallAuto_Hori>
    <settingParkOutSmallAuto_Vert>
      <iconCenter>302 360</iconCenter>
    </settingParkOutSmallAuto_Vert>
    <settingParkOutGuidanceCombinationParaRight_Hori>
      <iconCenter>248 405</iconCenter>
    </settingParkOutGuidanceCombinationParaRight_Hori>
    <settingParkOutGuidanceCombinationParaRight_Vert>
      <iconCenter>564 540</iconCenter>
    </settingParkOutGuidanceCombinationParaRight_Vert>
    <settingParkOutGuidanceCombinationParaLeft_Hori>
      <iconCenter>248 405</iconCenter>
    </settingParkOutGuidanceCombinationParaLeft_Hori>
    <settingParkOutGuidanceCombinationParaLeft_Vert>
      <iconCenter>564 540</iconCenter>
    </settingParkOutGuidanceCombinationParaLeft_Vert>
    <settingHoriParkOutSlotParaRight_Hori>
      <iconCenter>248 458</iconCenter>
    </settingHoriParkOutSlotParaRight_Hori>
    <settingHoriParkOutSlotParaRight_Vert>
      <iconCenter>564 540</iconCenter>
    </settingHoriParkOutSlotParaRight_Vert>
    <settingParkOutSlotParaLeft_Hori>
      <iconCenter>248 458</iconCenter>
    </settingParkOutSlotParaLeft_Hori>
    <settingParkOutSlotParaLeft_Vert>
      <iconCenter>564 540</iconCenter>
    </settingParkOutSlotParaLeft_Vert>
    <settingHoriGuidelineVertRightRearIn_Hori>
      <iconCenter>248 416</iconCenter>
    </settingHoriGuidelineVertRightRearIn_Hori>
    <settingHoriGuidelineVertRightFrontIn_Hori>
      <iconCenter>236 358</iconCenter>
    </settingHoriGuidelineVertRightFrontIn_Hori>
    <settingHoriGuidelineVertLeftRearIn_Hori>
      <iconCenter>196 411</iconCenter>
    </settingHoriGuidelineVertLeftRearIn_Hori>
    <settingHoriGuidelineVertLeftFrontIn_Hori>
      <iconCenter>196 358</iconCenter>
    </settingHoriGuidelineVertLeftFrontIn_Hori>
    <settingHoriGuidelineParaRight_Hori>
      <iconCenter>267 334</iconCenter>
    </settingHoriGuidelineParaRight_Hori>
    <settingHoriGuidelineParaLeft_Hori>
      <iconCenter>165 334</iconCenter>
    </settingHoriGuidelineParaLeft_Hori>
    <settingHoriGuidelineVertRightRearIn_Vert>
      <iconCenter>564 540</iconCenter>
    </settingHoriGuidelineVertRightRearIn_Vert>
    <settingHoriGuidelineVertRightFrontIn_Vert>
      <iconCenter>280 381</iconCenter>
    </settingHoriGuidelineVertRightFrontIn_Vert>
    <settingHoriGuidelineVertLeftRearIn_Vert>
      <iconCenter>322 338</iconCenter>
    </settingHoriGuidelineVertLeftRearIn_Vert>
    <settingHoriGuidelineVertLeftFrontIn_Vert>
      <iconCenter>280 338</iconCenter>
    </settingHoriGuidelineVertLeftFrontIn_Vert>
    <settingHoriGuidelineParaRight_Vert>
      <iconCenter>322 405</iconCenter>
    </settingHoriGuidelineParaRight_Vert>
    <settingHoriGuidelineParaLeft_Vert>
      <iconCenter>322 314</iconCenter>
    </settingHoriGuidelineParaLeft_Vert>
    <settingPARKParaSlotPos1L>
      <iconCenter>315 342</iconCenter>
    </settingPARKParaSlotPos1L>
    <settingPARKParaSlotPos2L>
      <iconCenter>315 539</iconCenter>
    </settingPARKParaSlotPos2L>
    <settingPARKParaSlotPos3L>
      <iconCenter>315 738</iconCenter>
    </settingPARKParaSlotPos3L>
    <settingPARKParaSlotPos1R>
      <iconCenter>724 342</iconCenter>
    </settingPARKParaSlotPos1R>
    <settingPARKParaSlotPos2R>
      <iconCenter>724 539</iconCenter>
    </settingPARKParaSlotPos2R>
    <settingPARKParaSlotPos3R>
      <iconCenter>724 738</iconCenter>
    </settingPARKParaSlotPos3R>
    <settingPARKSlotPos1L>
      <iconCenter>351 406</iconCenter>
    </settingPARKSlotPos1L>
    <settingPARKSlotPos1R>
      <iconCenter>686 406</iconCenter>
    </settingPARKSlotPos1R>
    <settingPARKSlotPos2L>
      <iconCenter>351 519</iconCenter>
    </settingPARKSlotPos2L>
    <settingPARKSlotPos2R>
      <iconCenter>686 519</iconCenter>
    </settingPARKSlotPos2R>
    <settingPARKSlotPos3L>
      <iconCenter>351 632</iconCenter>
    </settingPARKSlotPos3L>
    <settingPARKSlotPos3R>
      <iconCenter>686 632</iconCenter>
    </settingPARKSlotPos3R>
    <settingPARKSlotPos4L>
      <iconCenter>351 745</iconCenter>
    </settingPARKSlotPos4L>
    <settingPARKSlotPos4R>
      <iconCenter>686 745</iconCenter>
    </settingPARKSlotPos4R>
    <settingPARKBackground>
      <iconSize>1920 720</iconSize>
      <iconCenter>960 360</iconCenter>
    </settingPARKBackground>
    <!-- <settingPARKRPAGuidance>
      <iconCenter>960 360</iconCenter>
    </settingPARKRPAGuidance> -->
    <settingParkFrontIsClearSteeringWheel>
			<iconSize>274 218</iconSize>
			<iconCenter>248 307</iconCenter>
    </settingParkFrontIsClearSteeringWheel>
    <settingParkFrontIsClearVehicleAvailable>
			<iconCenter>248 533</iconCenter>
    </settingParkFrontIsClearVehicleAvailable>
    <settingPARKFinished>
      <iconSize>659 506</iconSize>
      <iconCenter>1220 500</iconCenter>
    </settingPARKFinished>
    <settingTextBox>
      <iconSize>680 115</iconSize>
      <iconCenter>1220 80</iconCenter>
    </settingTextBox>
    <settingParkingUICenterIcon_Hori>
      <iconCenter>248 408</iconCenter>
    </settingParkingUICenterIcon_Hori>
    <settingParkingUICenterIcon_Vert>
      <iconCenter>487 540</iconCenter>
    </settingParkingUICenterIcon_Vert>
    <settingParkingUIGearIcon_Hori>
      <iconCenter>137 135</iconCenter>
    </settingParkingUIGearIcon_Hori>
    <settingParkingUIGearIcon_Vert>
      <iconCenter>182 415</iconCenter>
    </settingParkingUIGearIcon_Vert>
    <settingParkingUISeachingArrowIcon_Hori>
      <iconCenter>248 363</iconCenter>
    </settingParkingUISeachingArrowIcon_Hori>
    <settingParkingUISeachingArrowIcon_Vert>
      <iconCenter>315 360</iconCenter>
    </settingParkingUISeachingArrowIcon_Vert>
    <settingParkingUISeachingWaveIcon_Hori>
      <iconCenter>248 365</iconCenter>
    </settingParkingUISeachingWaveIcon_Hori>
    <settingParkingUISeachingWaveIcon_Vert>
      <iconCenter>326 360</iconCenter>
    </settingParkingUISeachingWaveIcon_Vert>
    <settingParkingUIContinueDrivingText_Hori>
      <iconCenter>282 135</iconCenter>
    </settingParkingUIContinueDrivingText_Hori>
    <settingParkingUIContinueDrivingText_Vert>
      <iconCenter>182 579</iconCenter>
    </settingParkingUIContinueDrivingText_Vert>
    <settingParkingUIContinueDrivingDistance>
      <iconCenter>273 113</iconCenter>
    </settingParkingUIContinueDrivingDistance>
    <settingParkingUISeachingVehicleWithDoors>
      <iconCenter>248 370</iconCenter>
    </settingParkingUISeachingVehicleWithDoors>
    <settingParkingUISeachingAutoIcon_Hori>
      <iconCenter>248 451</iconCenter>
    </settingParkingUISeachingAutoIcon_Hori>
    <settingParkingUISeachingAutoIcon_Vert>
      <iconCenter>395 360</iconCenter>
    </settingParkingUISeachingAutoIcon_Vert>
    <settingParkingUITopTextWhite_Hori>
      <iconCenter>248 132</iconCenter>
    </settingParkingUITopTextWhite_Hori>
    <settingParkingUITopTextWhite_Vert>
      <iconCenter>178 540</iconCenter>
    </settingParkingUITopTextWhite_Vert>
    <settingParkingUITopTextBlue_Hori>
      <iconCenter>248 186</iconCenter>
    </settingParkingUITopTextBlue_Hori>
    <settingParkingUITopTextBlue_Vert>
      <iconCenter>246 540</iconCenter>
    </settingParkingUITopTextBlue_Vert>
    <settingQuitIn30SecondText_Hori>
      <iconCenter>248 604</iconCenter>
    </settingQuitIn30SecondText_Hori>
    <settingQuitIn30SecondText_Vert>
      <iconCenter>921 540</iconCenter>
    </settingQuitIn30SecondText_Vert>
    <settingParkingUIFunctionButton_Hori>
      <iconCenter>248 30</iconCenter>
    </settingParkingUIFunctionButton_Hori>
    <settingParkingUIFunctionButton_Vert>
      <iconCenter>92 540</iconCenter>
    </settingParkingUIFunctionButton_Vert>
    <settingParkingStartPauseConfirmButton_Hori>
      <iconCenter>248 742</iconCenter>
    </settingParkingStartPauseConfirmButton_Hori>
    <settingParkingStartPauseConfirmButton_Vert>
      <iconCenter>873 540</iconCenter>
    </settingParkingStartPauseConfirmButton_Vert>
    <settingParkinContinueButton_Hori>
      <iconCenter>138 742</iconCenter>
    </settingParkinContinueButton_Hori>
    <settingParkinContinueButton_Vert>
      <iconCenter>873 410</iconCenter>
    </settingParkinContinueButton_Vert>
    <settingParkinQuitButton_Hori>
      <iconCenter>355 742</iconCenter>
    </settingParkinQuitButton_Hori>
    <settingParkinQuitButton_Vert>
      <iconCenter>873 649</iconCenter>
    </settingParkinQuitButton_Vert>
    <settingParkinQuitButtonMiddle_Hori>
      <iconCenter>249 743</iconCenter>
    </settingParkinQuitButtonMiddle_Hori>
    <settingParkinQuitButtonMiddle_Vert>
      <iconCenter>888 540</iconCenter>
    </settingParkinQuitButtonMiddle_Vert>
    <settingParkingUIParkOutSideLeftButton_Hori>
			<iconCenter>108 529</iconCenter>
    </settingParkingUIParkOutSideLeftButton_Hori>
    <settingParkingUIParkOutSideLeftButton_Vert>
      <iconCenter>626 365</iconCenter>
    </settingParkingUIParkOutSideLeftButton_Vert>
    <settingParkingUIParkOutSideRightButton_Hori>
			<iconCenter>389 529</iconCenter>
    </settingParkingUIParkOutSideRightButton_Hori>
    <settingParkingUIParkOutSideRightButton_Vert>
      <iconCenter>626 717</iconCenter>
    </settingParkingUIParkOutSideRightButton_Vert>
    <settingParkingUIParkOutSideVehicle_Hori>
			<iconCenter>248 458</iconCenter>
    </settingParkingUIParkOutSideVehicle_Hori>
    <settingParkingUIParkOutSideVehicle_Vert>
      <iconCenter>586 540</iconCenter>
    </settingParkingUIParkOutSideVehicle_Vert>
    <settingParkingUIParkOutSideSteeringWheel_Hori>
			<iconCenter>248 589</iconCenter>
    </settingParkingUIParkOutSideSteeringWheel_Hori>
    <settingParkingUIParkOutSideSteeringWheel_Vert>
      <iconCenter>934 540</iconCenter>
    </settingParkingUIParkOutSideSteeringWheel_Vert>
    <settingParkingUIParkOutSideTextBottom_Hori>
			<iconCenter>248 186</iconCenter>
    </settingParkingUIParkOutSideTextBottom_Hori>
    <settingParkingUIParkOutSideTextBottom_Vert>
      <iconCenter>239 540</iconCenter>
    </settingParkingUIParkOutSideTextBottom_Vert>
    <settingParkingUIRPAMobilePhoneIcon_Hori>
      <iconCenter>248 398</iconCenter>
    </settingParkingUIRPAMobilePhoneIcon_Hori>
    <settingParkingUIRPAMobilePhoneIcon_Vert>
      <iconCenter>571 540</iconCenter>
    </settingParkingUIRPAMobilePhoneIcon_Vert>
    <settingParkingUIRPATextPrompt_Hori>
      <iconCenter>222 570</iconCenter>
    </settingParkingUIRPATextPrompt_Hori>
    <settingParkingUIRPATextPrompt_Vert>
      <iconCenter>925 540</iconCenter>
    </settingParkingUIRPATextPrompt_Vert>
    <settingParkingUIRPAPleaseLeaveTheCar_Hori>
      <iconCenter>248 159</iconCenter>
    </settingParkingUIRPAPleaseLeaveTheCar_Hori>
    <settingParkingUIRPAPleaseLeaveTheCar_Vert>
      <iconCenter>925 540</iconCenter>
    </settingParkingUIRPAPleaseLeaveTheCar_Vert>
    <settingParkingUIFreeParkingTextDescription_Hori>
      <iconCenter>248 182</iconCenter>
    </settingParkingUIFreeParkingTextDescription_Hori>
    <settingParkingUIFreeParkingTextDescription_Vert>
      <iconCenter>273 540</iconCenter>
    </settingParkingUIFreeParkingTextDescription_Vert>
    <settingParkingUIFreeParkingInstructions_Hori>
      <iconCenter>248 370</iconCenter>
    </settingParkingUIFreeParkingInstructions_Hori>
    <settingParkingUIFreeParkingInstructions_Vert>
      <iconCenter>459 540</iconCenter>
    </settingParkingUIFreeParkingInstructions_Vert>
    <settingParkingUIFreeParkingOperationGuide_Hori>
      <iconCenter>248 583</iconCenter>
    </settingParkingUIFreeParkingOperationGuide_Hori>
    <settingParkingUIFreeParkingOperationGuide_Vert>
      <iconCenter>695 540</iconCenter>
    </settingParkingUIFreeParkingOperationGuide_Vert>
    <!-- Hot zone setting -->
    <settingFunctionSelectionButtonParkinPress_Hori>
      <iconSize>156 66</iconSize>
      <iconCenter>111 120</iconCenter>
      <responseArea>156 66</responseArea>
    </settingFunctionSelectionButtonParkinPress_Hori>
    <settingFunctionSelectionButtonParkoutPress_Hori>
      <iconSize>156 66</iconSize>
      <iconCenter>276 120</iconCenter>
      <responseArea>156 66</responseArea>
    </settingFunctionSelectionButtonParkoutPress_Hori>
    <settingFunctionSelectionButtonFreeParkingPress_Hori>
      <iconSize>156 66</iconSize>
      <iconCenter>441 120</iconCenter>
      <responseArea>156 66</responseArea>
    </settingFunctionSelectionButtonFreeParkingPress_Hori>
    <settingParkTypeButtonAPAPress_Hori>
      <iconSize>207 72</iconSize>
      <iconCenter>416 72</iconCenter>
      <responseArea>207 72</responseArea>
    </settingParkTypeButtonAPAPress_Hori>
    <settingParkTypeButtonRPAPress_Hori>
      <iconSize>207 72</iconSize>
      <iconCenter>622 72</iconCenter>
      <responseArea>207 72</responseArea>
    </settingParkTypeButtonRPAPress_Hori>
    <settingParkOutSideLeftButtonSelectedPress_Hori>
      <iconSize>100 100</iconSize>
      <iconCenter>120 675</iconCenter>
      <responseArea>100 100</responseArea>
    </settingParkOutSideLeftButtonSelectedPress_Hori>
    <settingParkOutSideRightButtonSelectedPress_Hori>
      <iconSize>100 100</iconSize>
      <iconCenter>432 675</iconCenter>
      <responseArea>100 100</responseArea>
    </settingParkOutSideRightButtonSelectedPress_Hori>
    <settingParkingStartPauseConfirmButtonPress_Hori>
      <iconSize>216 60</iconSize>
      <iconCenter>276 912</iconCenter>
      <responseArea>216 60</responseArea>
    </settingParkingStartPauseConfirmButtonPress_Hori>
    <settingParkinContinueButtonPress_Hori>
      <iconSize>216 60</iconSize>
      <iconCenter>155 912</iconCenter>
      <responseArea>216 60</responseArea>
    </settingParkinContinueButtonPress_Hori>
    <settingParkinQuitButtonPress_Hori>
      <iconSize>216 60</iconSize>
      <iconCenter>394 912</iconCenter>
      <responseArea>216 60</responseArea>
    </settingParkinQuitButtonPress_Hori>
    <settingSearchingFrontInButtonPress_Hori>
      <iconSize>180 66</iconSize>
      <iconCenter>632 885</iconCenter>
      <responseArea>180 66</responseArea>
    </settingSearchingFrontInButtonPress_Hori>
    <settingSearchingRearInButtonPress_Hori>
      <iconSize>180 66</iconSize>
      <iconCenter>405 885</iconCenter>
      <responseArea>180 66</responseArea>
    </settingSearchingRearInButtonPress_Hori>
    <settingFreeParkingSpaceTypeCrossButtonPress_Hori>
      <iconSize>120 210</iconSize>
      <iconCenter>276 498</iconCenter>
      <responseArea>120 210</responseArea>
    </settingFreeParkingSpaceTypeCrossButtonPress_Hori>
    <settingFreeParkingSpaceTypeParallelButtonPress_Hori>
      <iconSize>120 210</iconSize>
      <iconCenter>108 498</iconCenter>
      <responseArea>120 210</responseArea>
    </settingFreeParkingSpaceTypeParallelButtonPress_Hori>
    <settingFreeParkingSpaceTypeDiagonalButtonPress_Hori>
      <iconSize>120 210</iconSize>
      <iconCenter>444 498</iconCenter>
      <responseArea>120 210</responseArea>
    </settingFreeParkingSpaceTypeDiagonalButtonPress_Hori>
    <settingFunctionSelectionButtonParkinPress_Vert>
      <iconSize>156 66</iconSize>
      <iconCenter>375 893</iconCenter>
      <responseArea>156 66</responseArea>
    </settingFunctionSelectionButtonParkinPress_Vert>
    <settingFunctionSelectionButtonParkoutPress_Vert>
      <iconSize>156 66</iconSize>
      <iconCenter>540 893</iconCenter>
      <responseArea>156 66</responseArea>
    </settingFunctionSelectionButtonParkoutPress_Vert>
    <settingFunctionSelectionButtonFreeParkingPress_Vert>
      <iconSize>156 66</iconSize>
      <iconCenter>705 893</iconCenter>
      <responseArea>156 66</responseArea>
    </settingFunctionSelectionButtonFreeParkingPress_Vert>
    <settingParkTypeButtonAPAPress_Vert>
      <iconSize>207 72</iconSize>
      <iconCenter>437 759</iconCenter>
      <responseArea>207 72</responseArea>
    </settingParkTypeButtonAPAPress_Vert>
    <settingParkTypeButtonRPAPress_Vert>
      <iconSize>207 72</iconSize>
      <iconCenter>643 759</iconCenter>
      <responseArea>207 72</responseArea>
    </settingParkTypeButtonRPAPress_Vert>
    <settingParkOutSideLeftButtonSelectedPress_Vert>
      <iconSize>100 100</iconSize>
      <iconCenter>365 1427</iconCenter>
      <responseArea>100 100</responseArea>
    </settingParkOutSideLeftButtonSelectedPress_Vert>
    <settingParkOutSideRightButtonSelectedPress_Vert>
      <iconSize>100 100</iconSize>
      <iconCenter>717 1427</iconCenter>
      <responseArea>100 100</responseArea>
    </settingParkOutSideRightButtonSelectedPress_Vert>
    <settingParkingStartPauseConfirmButtonPress_Vert>
      <iconSize>180 66</iconSize>
      <iconCenter>540 1470</iconCenter>
      <responseArea>180 66</responseArea>
    </settingParkingStartPauseConfirmButtonPress_Vert>
    <settingParkinContinueButtonPress_Vert>
      <iconSize>180 66</iconSize>
      <iconCenter>426 1470</iconCenter>
      <responseArea>180 66</responseArea>
    </settingParkinContinueButtonPress_Vert>
    <settingParkinQuitButtonPress_Vert>
      <iconSize>192 60</iconSize>
      <iconCenter>649 1674</iconCenter>
      <responseArea>192 60</responseArea>
    </settingParkinQuitButtonPress_Vert>
    <settingSearchingFrontInButtonPress_Vert>
      <iconSize>180 66</iconSize>
      <iconCenter>654 1470</iconCenter>
      <responseArea>180 66</responseArea>
    </settingSearchingFrontInButtonPress_Vert>
    <settingSearchingRearInButtonPress_Vert>
      <iconSize>180 66</iconSize>
      <iconCenter>426 1470</iconCenter>
      <responseArea>180 66</responseArea>
    </settingSearchingRearInButtonPress_Vert>
    <settingFreeParkingSpaceTypeCrossButtonPress_Vert>
      <iconSize>120 210</iconSize>
      <iconCenter>540 1260</iconCenter>
      <responseArea>120 210</responseArea>
    </settingFreeParkingSpaceTypeCrossButtonPress_Vert>
    <settingFreeParkingSpaceTypeParallelButtonPress_Vert>
      <iconSize>120 210</iconSize>
      <iconCenter>372 1260</iconCenter>
      <responseArea>120 210</responseArea>
    </settingFreeParkingSpaceTypeParallelButtonPress_Vert>
    <settingFreeParkingSpaceTypeDiagonalButtonPress_Vert>
      <iconSize>120 210</iconSize>
      <iconCenter>708 1260</iconCenter>
      <responseArea>120 210</responseArea>
    </settingFreeParkingSpaceTypeDiagonalButtonPress_Vert>
    <settingWheelSeparatorHorizontal>
      <iconSize>120 954</iconSize>
      <iconCenter>1302 108</iconCenter>
    </settingWheelSeparatorHorizontal>
    <settingWheelSeparatorVertical>
      <iconSize>761 120</iconSize>
      <iconCenter>380 540</iconCenter>
    </settingWheelSeparatorVertical>
  <!-- Ending with D: For DENZA project; Ending with M: For MR project; -->
    <settingParkinQuitButtonPress_DENZA>
      <iconCenter>872 1216</iconCenter>
      <responseArea>288 80</responseArea>
    </settingParkinQuitButtonPress_DENZA>
    <settingParkinQuitButtonPress_MR>
      <iconCenter>654 912</iconCenter>
      <responseArea>244 60</responseArea>
    </settingParkinQuitButtonPress_MR>
    <settingSuspendContinueButtonPress_DENZA>
      <iconCenter>504 1216</iconCenter>
      <responseArea>288 80</responseArea>
    </settingSuspendContinueButtonPress_DENZA>
    <settingSuspendContinueButtonPress_MR>
      <iconCenter>378 912</iconCenter>
      <responseArea>244 60</responseArea>
    </settingSuspendContinueButtonPress_MR>
    <settingSuspendContinueButtonPress_Hori>
      <iconCenter>154 912</iconCenter>
      <responseArea>216 60</responseArea>
    </settingSuspendContinueButtonPress_Hori>
    <settingSuspendContinueButtonPress_Vert>
      <iconCenter>410 1674</iconCenter>
      <responseArea>192 60</responseArea>
    </settingSuspendContinueButtonPress_Vert>
    <settingParkingStartButtonPress_DENZA>
      <iconCenter>692 1216</iconCenter>
      <responseArea>288 80</responseArea>
    </settingParkingStartButtonPress_DENZA>
    <settingParkingStartButtonPress_MR>
      <iconCenter>519 912</iconCenter>
      <responseArea>244 60</responseArea>
    </settingParkingStartButtonPress_MR>
    <settingParkingStartButtonPress_Hori>
      <iconCenter>274 912</iconCenter>
      <responseArea>216 60</responseArea>
    </settingParkingStartButtonPress_Hori>
    <settingParkingStartButtonPress_Vert>
      <iconCenter>540 1674</iconCenter>
      <responseArea>192 60</responseArea>
    </settingParkingStartButtonPress_Vert>

  </UIElements>
  <ParkingSpace>
    <isEnabled>1</isEnabled>  <!-- 0: not enabled; 1: enabled; -->
    <isHoriFlip>1</isHoriFlip>  <!-- 0: not enabled; 1: enabled; -->
    <isVertFlip>1</isVertFlip>  <!-- 0: not enabled; 1: enabled; -->
    <enablePSMode>7</enablePSMode>  <!-- show parkspace or not.
                                        bit 0: scanning, bit 1: selected, bit 2: guidance at parkin, bit 3: guidance at parkout.
                                        Eg. 1111B=15D means show all the time.
                                        Eg. 0111B=7D means not show at guidance at parkout-->
    <iconSize>159 335</iconSize>  <!-- the size is tuning based on planwidth is 11.0m -->
    <iconSizeVert>305 140</iconSizeVert>  <!-- the size is tuning based on planwidth is 8.0m -->
    <texturePathSelectableCrossSlot>cc/resources/icons/parkslot_selectable_cross.png</texturePathSelectableCrossSlot>
    <texturePathSelectableParaSlot>cc/resources/icons/parkslot_selectable_para.png</texturePathSelectableParaSlot>
    <texturePathNextCrossSlot>cc/resources/icons/parkslot_selectable_cross.png</texturePathNextCrossSlot>
    <texturePathNextParaSlot>cc/resources/icons/parkslot_selectable_para.png</texturePathNextParaSlot>
    <texturePathSelectedCrossSlot>cc/resources/icons/parkslot_selected_cross.png</texturePathSelectedCrossSlot>
    <texturePathSelectedParaSlot>cc/resources/icons/parkslot_selected_para.png</texturePathSelectedParaSlot>
    <texturePathTargetCrossSlot>cc/resources/icons/parkslot_target_cross.png</texturePathTargetCrossSlot>
    <texturePathTargetParaSlot>cc/resources/icons/parkslot_target_para.png</texturePathTargetParaSlot>
    <slotCrossX>2.6</slotCrossX>
    <slotParallelX>5.5</slotParallelX>
    <slotOffset>1.2 0</slotOffset>
    <seachingSpacewidth_cm>238</seachingSpacewidth_cm>  <!-- widthWithMirrors: 2.175,  base on displayed ratio: 15*168/1060 = 2,3773 m -->
    <seachingSpaceDepthOffset_cm>30</seachingSpaceDepthOffset_cm>  <!-- we dont consider value < 5 cm -->
    <manoeuverShowDelay>0.2</manoeuverShowDelay>  <!-- in second -->
    <parkingDiagSlotAngleLowerLimit>0.401</parkingDiagSlotAngleLowerLimit>  <!-- keep the same as in ParkingSlot -->
    <parkingDiagSlotAngleUpperLimit>1.309</parkingDiagSlotAngleUpperLimit>  <!-- keep the same as in ParkingSlot -->
    <pldDelayDuration>0.06</pldDelayDuration>
  </ParkingSpace>
  <ParkingSlot>
    <parkingDiagSlotAngleLowerLimit>0.401</parkingDiagSlotAngleLowerLimit>  <!-- lower and upper limits of diag slot,
                                 \                                             unit: radian--> <!-- keep the same as in ParkingSpace -->
    <parkingDiagSlotAngleUpperLimit>1.309</parkingDiagSlotAngleUpperLimit>  <!-- keep the same as in ParkingSpace -->
    <parkingSlotflashDeplay>5</parkingSlotflashDeplay>
    <parkingCrossSlotConversionCycles>60</parkingCrossSlotConversionCycles>
  </ParkingSlot>
  <ParkingView>
    <planViewResHori>852.0 720.0</planViewResHori>
    <planViewResVert>1014.0 1080.0</planViewResVert>
    <noticeRollingPeriodInMs>500.0</noticeRollingPeriodInMs>
  </ParkingView>
  <DistanceDigitalDisplay>
    <offset_front>1.5 0.0 0.0</offset_front>
    <offset_front_parking>1.0 0.0 0.0</offset_front_parking>
    <offset_rear>1.4 0.0 0.0</offset_rear>
    <offset_rear_parking>1.3 0.0 0.0</offset_rear_parking>
    <fixedFrontPosition>5.8</fixedFrontPosition> <!-- default front fixed position -->
    <fixedRearPosition>-2.8</fixedRearPosition> <!-- default rear fixed position -->
    <fixedFrontPosition_vert_parking>6.0</fixedFrontPosition_vert_parking> <!-- default front fixed position -->
    <fixedRearPosition_vert_parking>-2.8</fixedRearPosition_vert_parking> <!-- default rear fixed position -->
    <stopTextFrontPosition>5.576 -0.64</stopTextFrontPosition>
    <stopTextRearPosition>-3.1 -0.64</stopTextRearPosition>
    <stopScaleFactor_Hori>150.0</stopScaleFactor_Hori>
    <stopTextFrontPosition_vert_parking>5.6 -0.64</stopTextFrontPosition_vert_parking>
    <stopTextRearPosition_vert_parking>-3.0 -0.64</stopTextRearPosition_vert_parking>
    <textOffsetPercentageFront>0.6</textOffsetPercentageFront>
    <textOffsetPercentageRear>0.4</textOffsetPercentageRear>
    <heightOverGround>3.0</heightOverGround> <!--to make it stays above vehicle 2D-->
    <fontSizeNormal>30.0</fontSizeNormal>
    <fontSizeFullscreen>37.0</fontSizeFullscreen>
    <fontSizeFullscreenEnlarge>45.0</fontSizeFullscreenEnlarge>
  </DistanceDigitalDisplay>
  <DynamicGear>
    <positionHori>1012 -411 0</positionHori> <!--Unit: in meter on UI planview-->
    <positionVert>432 -894 0</positionVert> <!--Unit: in meter on UI planview-->
    <color>0.2 0.584 1.0 0.9</color>
    <outerRadius>28.0</outerRadius> <!--Unit: in meter on parking planview-->
    <innerRadius>25.0</innerRadius> <!--Unit: in meter on parking planview-->
    <horiToVertScaling>1.0</horiToVertScaling>
    <clockwise>1</clockwise>
  </DynamicGear>
  <CustomVehicleColorSettings>
    <TimeGray> <!-- TIME_GRAY --> <!-- only used by HC -->
      <diffuseColor>     75  78  82 255   </diffuseColor>
      <specColor1>       170 170 170  0   </specColor1>
      <specShininess1>   200              </specShininess1>
      <specColor2>       170 170 170 255  </specColor2>
      <specShininess2>   10               </specShininess2>
      <reflectionPower>  1.0              </reflectionPower>
      <fresnel>          1.5              </fresnel>
      <lightPos>         0.0  1.0  1.0    </lightPos>
      <brightness>       0.7              </brightness>  <!-- brightness is the last way to modify. better set to 1.0 -->
      <chromeDiffuseColor>  127 127 127 255 </chromeDiffuseColor>  <!-- logo baseplate -->
      <chromeSpecColor>     127 127 127 255 </chromeSpecColor>
      <chromeSpecShininess> 100             </chromeSpecShininess>
      <chromeBrightness>    1.0             </chromeBrightness>
      <chrome2Brightness>   0.5             </chrome2Brightness>  <!-- front logo -->
      <chrome3Brightness>   0.5             </chrome3Brightness>  <!-- rear logo -->
      <veh2dBrightness>     0.55            </veh2dBrightness>  <!-- vehicle2D -->
    </TimeGray>
    <MountainAsh> <!-- MOUNTAIN_ASH --> <!-- only used by HC -->
      <diffuseColor>     30  39  55 255   </diffuseColor>
      <specColor1>       30 20 40  0   </specColor1>
      <specShininess1>   200              </specShininess1>
      <specColor2>       170 170 170 255  </specColor2>
      <specShininess2>   10               </specShininess2>
      <reflectionPower>  1.0              </reflectionPower>
      <fresnel>          1.5              </fresnel>
      <lightPos>         0.0  1.0  1.0    </lightPos>
      <brightness>       0.6              </brightness>  <!-- brightness is the last way to modify. better set to 1.0 -->
      <chromeDiffuseColor>  127 127 127 255 </chromeDiffuseColor>  <!-- logo baseplate -->
      <chromeSpecColor>     127 127 127 255 </chromeSpecColor>
      <chromeSpecShininess> 100             </chromeSpecShininess>
      <chromeBrightness>    1.0             </chromeBrightness>
      <chrome2Brightness>   0.5             </chrome2Brightness>  <!-- front logo -->
      <chrome3Brightness>   0.5             </chrome3Brightness>  <!-- rear logo -->
      <veh2dBrightness>     0.55            </veh2dBrightness>  <!-- vehicle2D -->
    </MountainAsh>
    <JunWareGray> <!-- JunWareGray -->
      <diffuseColor>     30  39  55 255   </diffuseColor>
      <specColor1>       30 20 40  0   </specColor1>
      <specShininess1>   200              </specShininess1>
      <specColor2>       170 170 170 255  </specColor2>
      <specShininess2>   10               </specShininess2>
      <reflectionPower>  1.0              </reflectionPower>
      <fresnel>          1.5              </fresnel>
      <lightPos>         0.0  1.0  1.0    </lightPos>
      <brightness>       0.6              </brightness>
      <chromeDiffuseColor>    0   0   0 255 </chromeDiffuseColor>
      <chromeSpecColor>      51  51  51 255 </chromeSpecColor>
      <chromeSpecShininess>  20             </chromeSpecShininess>
      <chromeBrightness>    1.0             </chromeBrightness>
      <chrome2Brightness>   1.5             </chrome2Brightness>
      <chrome3Brightness>   1.5             </chrome3Brightness>
      <veh2dBrightness>     0.8             </veh2dBrightness>
    </JunWareGray>
    <RedEmperor> <!-- RED_EMPEROR -->
      <diffuseColor>     100  21  22 255  </diffuseColor>
      <specColor1>       100   0   5  0   </specColor1>
      <specShininess1>   200              </specShininess1>
      <specColor2>       100   0   5 255  </specColor2>
      <specShininess2>   10               </specShininess2>
      <reflectionPower>  1.0              </reflectionPower>
      <fresnel>         1.5               </fresnel>
      <lightPos>         0.0  1.0  1.0    </lightPos>
      <brightness>       1.0              </brightness>
      <chromeDiffuseColor>  127 127 127 255 </chromeDiffuseColor>
      <chromeSpecColor>     127 127 127 255 </chromeSpecColor>
      <chromeSpecShininess> 100             </chromeSpecShininess>
      <chromeBrightness>    1.0             </chromeBrightness>
      <chrome2Brightness>   0.5             </chrome2Brightness>
      <chrome3Brightness>   1.5             </chrome3Brightness>
      <veh2dBrightness>     1.1             </veh2dBrightness>
    </RedEmperor>
    <SnowyWhite> <!-- SNOWY_WHITE -->
      <diffuseColor>     240 240 235 255  </diffuseColor>
      <specColor1>       191 191 191  0   </specColor1>
      <specShininess1>   150              </specShininess1>
      <specColor2>       255 255 255 255  </specColor2>
      <specShininess2>   10               </specShininess2>
      <reflectionPower>  2.2              </reflectionPower>
      <fresnel>          1.6              </fresnel>
      <lightPos>         0.2  0.9  1.0    </lightPos>
      <brightness>       0.7             </brightness>
      <chromeDiffuseColor>  127 127 127 255 </chromeDiffuseColor>
      <chromeSpecColor>     127 127 127 255 </chromeSpecColor>
      <chromeSpecShininess> 50             </chromeSpecShininess>
      <chromeBrightness>    1.0             </chromeBrightness>
      <chrome2Brightness>   0.5             </chrome2Brightness>
      <chrome3Brightness>   0.5             </chrome3Brightness>
      <veh2dBrightness>     0.6             </veh2dBrightness>
    </SnowyWhite>
    <SilverGlazeWhite> <!-- SilverGlazeWhite -->
      <diffuseColor>     240 240 235 255  </diffuseColor>
      <specColor1>       191 191 191  0   </specColor1>
      <specShininess1>   150              </specShininess1>
      <specColor2>       255 255 255 255  </specColor2>
      <specShininess2>   10               </specShininess2>
      <reflectionPower>  2.2              </reflectionPower>
      <fresnel>          1.6              </fresnel>
      <lightPos>         0.2  0.9  1.0    </lightPos>
      <brightness>       0.7             </brightness>
      <chromeDiffuseColor>  127 127 127 255 </chromeDiffuseColor>
      <chromeSpecColor>     127 127 127 255 </chromeSpecColor>
      <chromeSpecShininess> 50             </chromeSpecShininess>
      <chromeBrightness>    1.0             </chromeBrightness>
      <chrome2Brightness>   0.5             </chrome2Brightness>
      <chrome3Brightness>   0.5             </chrome3Brightness>
      <veh2dBrightness>     0.6             </veh2dBrightness>
    </SilverGlazeWhite>
    <DuDuWhite> <!-- DuDuWhite -->
      <diffuseColor>     240 240 235 255  </diffuseColor>
      <specColor1>       191 191 191  0   </specColor1>
      <specShininess1>   150              </specShininess1>
      <specColor2>       255 255 255 255  </specColor2>
      <specShininess2>   10               </specShininess2>
      <reflectionPower>  2.2              </reflectionPower>
      <fresnel>          1.6              </fresnel>
      <lightPos>         0.2  0.9  1.0    </lightPos>
      <brightness>       0.7             </brightness>
      <chromeDiffuseColor>  127 127 127 255 </chromeDiffuseColor>
      <chromeSpecColor>     127 127 127 255 </chromeSpecColor>
      <chromeSpecShininess> 50             </chromeSpecShininess>
      <chromeBrightness>    1.0             </chromeBrightness>
      <chrome2Brightness>   0.5             </chrome2Brightness>
      <chrome3Brightness>   0.5             </chrome3Brightness>
      <veh2dBrightness>     0.6             </veh2dBrightness>
    </DuDuWhite>
    <SilverSandBlack>  <!-- SILVERSAND_BLACK, XUANKONG_BLACK -->
      <diffuseColor>     0   0   0 255    </diffuseColor>
      <specColor1>       86  86  86  0    </specColor1>
      <specShininess1>   300              </specShininess1>
      <specColor2>       86  86  86 255   </specColor2>
      <specShininess2>   20               </specShininess2>
      <reflectionPower>  2.0              </reflectionPower>
      <fresnel>          2.0              </fresnel>
      <lightPos>         1.0  3.0  1.1    </lightPos>
      <brightness>       0.6              </brightness>
      <chromeDiffuseColor>  80 80 80 255 </chromeDiffuseColor>
      <chromeSpecColor>     100 100 100 255 </chromeSpecColor>
      <chromeSpecShininess> 100             </chromeSpecShininess>
      <chromeBrightness>    1.0             </chromeBrightness>
      <chrome2Brightness>   0.5             </chrome2Brightness>
      <chrome3Brightness>   1.5             </chrome3Brightness>
      <veh2dBrightness>     0.7             </veh2dBrightness>
    </SilverSandBlack>
    <WisdomBlue>   <!-- WISDOM_BLUE -->
      <diffuseColor>     2  32  100 255    </diffuseColor>
      <specColor1>       0 106 160  0     </specColor1>
      <specShininess1>   200              </specShininess1>
      <specColor2>       0 106 160 255    </specColor2>
      <specShininess2>   10               </specShininess2>
      <reflectionPower>  1.0              </reflectionPower>
      <fresnel>          1.5              </fresnel>
      <lightPos>         0.5  1.0  1.3    </lightPos>
      <brightness>       0.8              </brightness>
      <chromeDiffuseColor>  127 127 127 255 </chromeDiffuseColor>
      <chromeSpecColor>     127 127 127 255 </chromeSpecColor>
      <chromeSpecShininess> 100             </chromeSpecShininess>
      <chromeBrightness>    1.0             </chromeBrightness>
      <chrome2Brightness>   0.5             </chrome2Brightness>
      <chrome3Brightness>   1.5             </chrome3Brightness>
      <veh2dBrightness>     0.8             </veh2dBrightness>
    </WisdomBlue>
    <QianshanCui>   <!-- QIANSHAN_CUI -->
      <diffuseColor>    15  17  15 255   </diffuseColor>
      <specColor1>       5  60  50  0     </specColor1>
      <specShininess1>   200              </specShininess1>
      <specColor2>       5  60  50 255    </specColor2>
      <specShininess2>   10               </specShininess2>
      <reflectionPower>  1.0              </reflectionPower>
      <fresnel>          1.5              </fresnel>
      <lightPos>         0.0  0.6  1.0    </lightPos>
      <brightness>       1.6              </brightness>
      <chromeDiffuseColor>    0   0   0 255 </chromeDiffuseColor>
      <chromeSpecColor>      51  51  51 255 </chromeSpecColor>
      <chromeSpecShininess>  20             </chromeSpecShininess>
      <chromeBrightness>    1.0             </chromeBrightness>
      <chrome2Brightness>   1.5             </chrome2Brightness>
      <chrome3Brightness>   1.5             </chrome3Brightness>
      <veh2dBrightness>     0.8             </veh2dBrightness>
    </QianshanCui>
    <Azure>   <!-- AZURE -->
      <diffuseColor>    125 146 162 255   </diffuseColor>
      <specColor1>       50  60  70  0     </specColor1>
      <specShininess1>   200              </specShininess1>
      <specColor2>       50  60  70 255    </specColor2>
      <specShininess2>   10               </specShininess2>
      <reflectionPower>  1.0              </reflectionPower>
      <fresnel>          1.5              </fresnel>
      <lightPos>         0.0  0.6  1.0    </lightPos>
      <brightness>       1.0              </brightness>
      <chromeDiffuseColor>  127 127 127 255 </chromeDiffuseColor>
      <chromeSpecColor>     127 127 127 255 </chromeSpecColor>
      <chromeSpecShininess> 100             </chromeSpecShininess>
      <chromeBrightness>    1.0             </chromeBrightness>
      <chrome2Brightness>   0.5             </chrome2Brightness>
      <chrome3Brightness>   1.5             </chrome3Brightness>
      <veh2dBrightness>     0.8             </veh2dBrightness>
    </Azure>
    <MushanPink>   <!-- MUSHAN_PINK -->
      <diffuseColor>    140  140  170 255   </diffuseColor>
      <specColor1>       50  60  60  0     </specColor1>
      <specShininess1>   200              </specShininess1>
      <specColor2>       50  60  60 255    </specColor2>
      <specShininess2>   10               </specShininess2>
      <reflectionPower>  1.0              </reflectionPower>
      <fresnel>          1.5              </fresnel>
      <lightPos>         0.0  0.6  1.0    </lightPos>
      <brightness>       1.0              </brightness>
      <chromeDiffuseColor>    0   0   0 255 </chromeDiffuseColor>
      <chromeSpecColor>      51  51  51 255 </chromeSpecColor>
      <chromeSpecShininess>  20             </chromeSpecShininess>
      <chromeBrightness>    1.0             </chromeBrightness>
      <chrome2Brightness>   1.5             </chrome2Brightness>
      <chrome3Brightness>   1.5             </chrome3Brightness>
      <veh2dBrightness>     0.8             </veh2dBrightness>
    </MushanPink>
    <Golden>   <!-- MUSHAN_PINK -->
      <diffuseColor>    140  140  170 255   </diffuseColor>
      <specColor1>       50  60  60  0     </specColor1>
      <specShininess1>   200              </specShininess1>
      <specColor2>       50  60  60 255    </specColor2>
      <specShininess2>   10               </specShininess2>
      <reflectionPower>  1.0              </reflectionPower>
      <fresnel>          1.5              </fresnel>
      <lightPos>         0.0  0.6  1.0    </lightPos>
      <brightness>       1.0              </brightness>
      <chromeDiffuseColor>    0   0   0 255 </chromeDiffuseColor>
      <chromeSpecColor>      51  51  51 255 </chromeSpecColor>
      <chromeSpecShininess>  20             </chromeSpecShininess>
      <chromeBrightness>    1.0             </chromeBrightness>
      <chrome2Brightness>   1.5             </chrome2Brightness>
      <chrome3Brightness>   1.5             </chrome3Brightness>
      <veh2dBrightness>     0.8             </veh2dBrightness>
    </Golden>
  </CustomVehicleColorSettings>
  <Vehicle2DSettings>
    <transparent>
      <frontLeftDoorOpen>cc/vehicle_model/vehicle2d/transparent_front_left_door_open_warning.png</frontLeftDoorOpen>
      <frontRightDoorOpen>cc/vehicle_model/vehicle2d/transparent_front_right_door_open_warning.png</frontRightDoorOpen>
      <hoodOpen>cc/vehicle_model/vehicle2d/transparent_hood_open_warning.png</hoodOpen>
      <rearLeftDoorOpen>cc/vehicle_model/vehicle2d/transparent_rear_left_door_open_warning.png</rearLeftDoorOpen>
      <rearRightDoorOpen>cc/vehicle_model/vehicle2d/transparent_rear_right_door_open_warning.png</rearRightDoorOpen>
      <trunkOpen>cc/vehicle_model/vehicle2d/transparent_trunk_open_warning.png</trunkOpen>
      <vehicleBodyOpen>cc/vehicle_model/vehicle2d/transparent_vehicle_body_body_without_doors.png</vehicleBodyOpen>
      <frontLeftDoorClose>cc/vehicle_model/vehicle2d/transparent_front_left_door_close_normal.png</frontLeftDoorClose>
      <frontRightDoorClose>cc/vehicle_model/vehicle2d/transparent_front_right_door_close_normal.png</frontRightDoorClose>
      <hoodClose>cc/vehicle_model/vehicle2d/transparent_hood_close.png</hoodClose>
      <rearLeftDoorClose>cc/vehicle_model/vehicle2d/transparent_rear_left_door_close_normal.png</rearLeftDoorClose>
      <rearRightDoorClose>cc/vehicle_model/vehicle2d/transparent_rear_right_door_close_normal.png</rearRightDoorClose>
      <trunkClose>cc/vehicle_model/vehicle2d/transparent_trunk_close.png</trunkClose>
    </transparent>
    <azure>
      <frontLeftDoorOpen>cc/vehicle_model/vehicle2d/azure_front_left_door_open_warning.png</frontLeftDoorOpen>
      <frontRightDoorOpen>cc/vehicle_model/vehicle2d/azure_front_right_door_open_warning.png</frontRightDoorOpen>
      <hoodOpen>cc/vehicle_model/vehicle2d/azure_hood_open_warning.png</hoodOpen>
      <rearLeftDoorOpen>cc/vehicle_model/vehicle2d/azure_rear_left_door_open_warning.png</rearLeftDoorOpen>
      <rearRightDoorOpen>cc/vehicle_model/vehicle2d/azure_rear_right_door_open_warning.png</rearRightDoorOpen>
      <trunkOpen>cc/vehicle_model/vehicle2d/azure_trunk_open_warning.png</trunkOpen>
      <vehicleBodyOpen>cc/vehicle_model/vehicle2d/azure_vehicle_body_body_without_doors.png</vehicleBodyOpen>
      <frontLeftDoorClose>cc/vehicle_model/vehicle2d/azure_front_left_door_close_normal.png</frontLeftDoorClose>
      <frontRightDoorClose>cc/vehicle_model/vehicle2d/azure_front_right_door_close_normal.png</frontRightDoorClose>
      <hoodClose>cc/vehicle_model/vehicle2d/azure_hood_close.png</hoodClose>
      <rearLeftDoorClose>cc/vehicle_model/vehicle2d/azure_rear_left_door_close_normal.png</rearLeftDoorClose>
      <rearRightDoorClose>cc/vehicle_model/vehicle2d/azure_rear_right_door_close_normal.png</rearRightDoorClose>
      <trunkClose>cc/vehicle_model/vehicle2d/azure_trunk_close.png</trunkClose>
    </azure>
	  <black>
      <frontLeftDoorOpen>cc/vehicle_model/vehicle2d/black_front_left_door_open_warning.png</frontLeftDoorOpen>
      <frontRightDoorOpen>cc/vehicle_model/vehicle2d/black_front_right_door_open_warning.png</frontRightDoorOpen>
      <hoodOpen>cc/vehicle_model/vehicle2d/black_hood_open_warning.png</hoodOpen>
      <rearLeftDoorOpen>cc/vehicle_model/vehicle2d/black_rear_left_door_open_warning.png</rearLeftDoorOpen>
      <rearRightDoorOpen>cc/vehicle_model/vehicle2d/black_rear_right_door_open_warning.png</rearRightDoorOpen>
      <trunkOpen>cc/vehicle_model/vehicle2d/black_trunk_open_warning.png</trunkOpen>
      <vehicleBodyOpen>cc/vehicle_model/vehicle2d/black_vehicle_body_body_without_doors.png</vehicleBodyOpen>
      <frontLeftDoorClose>cc/vehicle_model/vehicle2d/black_front_left_door_close_normal.png</frontLeftDoorClose>
      <frontRightDoorClose>cc/vehicle_model/vehicle2d/black_front_right_door_close_normal.png</frontRightDoorClose>
      <hoodClose>cc/vehicle_model/vehicle2d/black_hood_close.png</hoodClose>
      <rearLeftDoorClose>cc/vehicle_model/vehicle2d/black_rear_left_door_close_normal.png</rearLeftDoorClose>
      <rearRightDoorClose>cc/vehicle_model/vehicle2d/black_rear_right_door_close_normal.png</rearRightDoorClose>
      <trunkClose>cc/vehicle_model/vehicle2d/black_trunk_close.png</trunkClose>
    </black>
    <blue>
      <frontLeftDoorOpen>cc/vehicle_model/vehicle2d/blue_front_left_door_open_warning.png</frontLeftDoorOpen>
      <frontRightDoorOpen>cc/vehicle_model/vehicle2d/blue_front_right_door_open_warning.png</frontRightDoorOpen>
      <hoodOpen>cc/vehicle_model/vehicle2d/blue_hood_open_warning.png</hoodOpen>
      <rearLeftDoorOpen>cc/vehicle_model/vehicle2d/blue_rear_left_door_open_warning.png</rearLeftDoorOpen>
      <rearRightDoorOpen>cc/vehicle_model/vehicle2d/blue_rear_right_door_open_warning.png</rearRightDoorOpen>
      <trunkOpen>cc/vehicle_model/vehicle2d/blue_trunk_open_warning.png</trunkOpen>
      <vehicleBodyOpen>cc/vehicle_model/vehicle2d/blue_vehicle_body_body_without_doors.png</vehicleBodyOpen>
      <frontLeftDoorClose>cc/vehicle_model/vehicle2d/blue_front_left_door_close_normal.png</frontLeftDoorClose>
      <frontRightDoorClose>cc/vehicle_model/vehicle2d/blue_front_right_door_close_normal.png</frontRightDoorClose>
      <hoodClose>cc/vehicle_model/vehicle2d/blue_hood_close.png</hoodClose>
      <rearLeftDoorClose>cc/vehicle_model/vehicle2d/blue_rear_left_door_close_normal.png</rearLeftDoorClose>
      <rearRightDoorClose>cc/vehicle_model/vehicle2d/blue_rear_right_door_close_normal.png</rearRightDoorClose>
      <trunkClose>cc/vehicle_model/vehicle2d/blue_trunk_close.png</trunkClose>
    </blue>
    <gray>
      <frontLeftDoorOpen>cc/vehicle_model/vehicle2d/gray_front_left_door_open_warning.png</frontLeftDoorOpen>
      <frontRightDoorOpen>cc/vehicle_model/vehicle2d/gray_front_right_door_open_warning.png</frontRightDoorOpen>
      <hoodOpen>cc/vehicle_model/vehicle2d/gray_hood_open_warning.png</hoodOpen>
      <rearLeftDoorOpen>cc/vehicle_model/vehicle2d/gray_rear_left_door_open_warning.png</rearLeftDoorOpen>
      <rearRightDoorOpen>cc/vehicle_model/vehicle2d/gray_rear_right_door_open_warning.png</rearRightDoorOpen>
      <trunkOpen>cc/vehicle_model/vehicle2d/gray_trunk_open_warning.png</trunkOpen>
      <vehicleBodyOpen>cc/vehicle_model/vehicle2d/gray_vehicle_body_body_without_doors.png</vehicleBodyOpen>
      <frontLeftDoorClose>cc/vehicle_model/vehicle2d/gray_front_left_door_close_normal.png</frontLeftDoorClose>
      <frontRightDoorClose>cc/vehicle_model/vehicle2d/gray_front_right_door_close_normal.png</frontRightDoorClose>
      <hoodClose>cc/vehicle_model/vehicle2d/gray_hood_close.png</hoodClose>
      <rearLeftDoorClose>cc/vehicle_model/vehicle2d/gray_rear_left_door_close_normal.png</rearLeftDoorClose>
      <rearRightDoorClose>cc/vehicle_model/vehicle2d/gray_rear_right_door_close_normal.png</rearRightDoorClose>
      <trunkClose>cc/vehicle_model/vehicle2d/gray_trunk_close.png</trunkClose>
    </gray>
    <mattegray>
      <frontLeftDoorOpen>cc/vehicle_model/vehicle2d/matte_gray_front_left_door_open_warning.png</frontLeftDoorOpen>
      <frontRightDoorOpen>cc/vehicle_model/vehicle2d/matte_gray_front_right_door_open_warning.png</frontRightDoorOpen>
      <hoodOpen>cc/vehicle_model/vehicle2d/matte_gray_hood_open_warning.png</hoodOpen>
      <rearLeftDoorOpen>cc/vehicle_model/vehicle2d/matte_gray_rear_left_door_open_warning.png</rearLeftDoorOpen>
      <rearRightDoorOpen>cc/vehicle_model/vehicle2d/matte_gray_rear_right_door_open_warning.png</rearRightDoorOpen>
      <trunkOpen>cc/vehicle_model/vehicle2d/matte_gray_trunk_open_warning.png</trunkOpen>
      <vehicleBodyOpen>cc/vehicle_model/vehicle2d/matte_gray_vehicle_body_body_without_doors.png</vehicleBodyOpen>
      <frontLeftDoorClose>cc/vehicle_model/vehicle2d/matte_gray_front_left_door_close_normal.png</frontLeftDoorClose>
      <frontRightDoorClose>cc/vehicle_model/vehicle2d/matte_gray_front_right_door_close_normal.png</frontRightDoorClose>
      <hoodClose>cc/vehicle_model/vehicle2d/matte_gray_hood_close.png</hoodClose>
      <rearLeftDoorClose>cc/vehicle_model/vehicle2d/matte_gray_rear_left_door_close_normal.png</rearLeftDoorClose>
      <rearRightDoorClose>cc/vehicle_model/vehicle2d/matte_gray_rear_right_door_close_normal.png</rearRightDoorClose>
      <trunkClose>cc/vehicle_model/vehicle2d/matte_gray_trunk_close.png</trunkClose>
    </mattegray>
    <mushanpink>
      <frontLeftDoorOpen>cc/vehicle_model/vehicle2d/mushanpink_front_left_door_open_warning.png</frontLeftDoorOpen>
      <frontRightDoorOpen>cc/vehicle_model/vehicle2d/mushanpink_front_right_door_open_warning.png</frontRightDoorOpen>
      <hoodOpen>cc/vehicle_model/vehicle2d/mushanpink_hood_open_warning.png</hoodOpen>
      <rearLeftDoorOpen>cc/vehicle_model/vehicle2d/mushanpink_rear_left_door_open_warning.png</rearLeftDoorOpen>
      <rearRightDoorOpen>cc/vehicle_model/vehicle2d/mushanpink_rear_right_door_open_warning.png</rearRightDoorOpen>
      <trunkOpen>cc/vehicle_model/vehicle2d/mushanpink_trunk_open_warning.png</trunkOpen>
      <vehicleBodyOpen>cc/vehicle_model/vehicle2d/mushanpink_vehicle_body_body_without_doors.png</vehicleBodyOpen>
      <frontLeftDoorClose>cc/vehicle_model/vehicle2d/mushanpink_front_left_door_close_normal.png</frontLeftDoorClose>
      <frontRightDoorClose>cc/vehicle_model/vehicle2d/mushanpink_front_right_door_close_normal.png</frontRightDoorClose>
      <hoodClose>cc/vehicle_model/vehicle2d/mushanpink_hood_close.png</hoodClose>
      <rearLeftDoorClose>cc/vehicle_model/vehicle2d/mushanpink_rear_left_door_close_normal.png</rearLeftDoorClose>
      <rearRightDoorClose>cc/vehicle_model/vehicle2d/mushanpink_rear_right_door_close_normal.png</rearRightDoorClose>
      <trunkClose>cc/vehicle_model/vehicle2d/mushanpink_trunk_close.png</trunkClose>
    </mushanpink>
    <golden>
      <frontLeftDoorOpen>cc/vehicle_model/vehicle2d/golden_front_left_door_open_warning.png</frontLeftDoorOpen>
      <frontRightDoorOpen>cc/vehicle_model/vehicle2d/golden_front_right_door_open_warning.png</frontRightDoorOpen>
      <hoodOpen>cc/vehicle_model/vehicle2d/golden_hood_open_warning.png</hoodOpen>
      <rearLeftDoorOpen>cc/vehicle_model/vehicle2d/golden_rear_left_door_open_warning.png</rearLeftDoorOpen>
      <rearRightDoorOpen>cc/vehicle_model/vehicle2d/golden_rear_right_door_open_warning.png</rearRightDoorOpen>
      <trunkOpen>cc/vehicle_model/vehicle2d/golden_trunk_open_warning.png</trunkOpen>
      <vehicleBodyOpen>cc/vehicle_model/vehicle2d/golden_vehicle_body_body_without_doors.png</vehicleBodyOpen>
      <frontLeftDoorClose>cc/vehicle_model/vehicle2d/golden_front_left_door_close_normal.png</frontLeftDoorClose>
      <frontRightDoorClose>cc/vehicle_model/vehicle2d/golden_front_right_door_close_normal.png</frontRightDoorClose>
      <hoodClose>cc/vehicle_model/vehicle2d/golden_hood_close.png</hoodClose>
      <rearLeftDoorClose>cc/vehicle_model/vehicle2d/golden_rear_left_door_close_normal.png</rearLeftDoorClose>
      <rearRightDoorClose>cc/vehicle_model/vehicle2d/golden_rear_right_door_close_normal.png</rearRightDoorClose>
    </golden>
	  <qianshancui>
      <frontLeftDoorOpen>cc/vehicle_model/vehicle2d/qianshancui_front_left_door_open_warning.png</frontLeftDoorOpen>
      <frontRightDoorOpen>cc/vehicle_model/vehicle2d/qianshancui_front_right_door_open_warning.png</frontRightDoorOpen>
      <hoodOpen>cc/vehicle_model/vehicle2d/qianshancui_hood_open_warning.png</hoodOpen>
      <rearLeftDoorOpen>cc/vehicle_model/vehicle2d/qianshancui_rear_left_door_open_warning.png</rearLeftDoorOpen>
      <rearRightDoorOpen>cc/vehicle_model/vehicle2d/qianshancui_rear_right_door_open_warning.png</rearRightDoorOpen>
      <trunkOpen>cc/vehicle_model/vehicle2d/qianshancui_trunk_open_warning.png</trunkOpen>
      <vehicleBodyOpen>cc/vehicle_model/vehicle2d/qianshancui_vehicle_body_body_without_doors.png</vehicleBodyOpen>
      <frontLeftDoorClose>cc/vehicle_model/vehicle2d/qianshancui_front_left_door_close_normal.png</frontLeftDoorClose>
      <frontRightDoorClose>cc/vehicle_model/vehicle2d/qianshancui_front_right_door_close_normal.png</frontRightDoorClose>
      <hoodClose>cc/vehicle_model/vehicle2d/qianshancui_hood_close.png</hoodClose>
      <rearLeftDoorClose>cc/vehicle_model/vehicle2d/qianshancui_rear_left_door_close_normal.png</rearLeftDoorClose>
      <rearRightDoorClose>cc/vehicle_model/vehicle2d/qianshancui_rear_right_door_close_normal.png</rearRightDoorClose>
      <trunkClose>cc/vehicle_model/vehicle2d/qianshancui_trunk_close.png</trunkClose>
    </qianshancui>
    <red>
      <frontLeftDoorOpen>cc/vehicle_model/vehicle2d/red_front_left_door_open_warning.png</frontLeftDoorOpen>
      <frontRightDoorOpen>cc/vehicle_model/vehicle2d/red_front_right_door_open_warning.png</frontRightDoorOpen>
      <hoodOpen>cc/vehicle_model/vehicle2d/red_hood_open_warning.png</hoodOpen>
      <rearLeftDoorOpen>cc/vehicle_model/vehicle2d/red_rear_left_door_open_warning.png</rearLeftDoorOpen>
      <rearRightDoorOpen>cc/vehicle_model/vehicle2d/red_rear_right_door_open_warning.png</rearRightDoorOpen>
      <trunkOpen>cc/vehicle_model/vehicle2d/red_trunk_open_warning.png</trunkOpen>
      <vehicleBodyOpen>cc/vehicle_model/vehicle2d/red_vehicle_body_body_without_doors.png</vehicleBodyOpen>
      <frontLeftDoorClose>cc/vehicle_model/vehicle2d/red_front_left_door_close_normal.png</frontLeftDoorClose>
      <frontRightDoorClose>cc/vehicle_model/vehicle2d/red_front_right_door_close_normal.png</frontRightDoorClose>
      <hoodClose>cc/vehicle_model/vehicle2d/red_hood_close.png</hoodClose>
      <rearLeftDoorClose>cc/vehicle_model/vehicle2d/red_rear_left_door_close_normal.png</rearLeftDoorClose>
      <rearRightDoorClose>cc/vehicle_model/vehicle2d/red_rear_right_door_close_normal.png</rearRightDoorClose>
      <trunkClose>cc/vehicle_model/vehicle2d/red_trunk_close.png</trunkClose>
    </red>
	  <white>
      <frontLeftDoorOpen>cc/vehicle_model/vehicle2d/white_front_left_door_open_warning.png</frontLeftDoorOpen>
      <frontRightDoorOpen>cc/vehicle_model/vehicle2d/white_front_right_door_open_warning.png</frontRightDoorOpen>
      <hoodOpen>cc/vehicle_model/vehicle2d/white_hood_open_warning.png</hoodOpen>
      <rearLeftDoorOpen>cc/vehicle_model/vehicle2d/white_rear_left_door_open_warning.png</rearLeftDoorOpen>
      <rearRightDoorOpen>cc/vehicle_model/vehicle2d/white_rear_right_door_open_warning.png</rearRightDoorOpen>
      <trunkOpen>cc/vehicle_model/vehicle2d/white_trunk_open_warning.png</trunkOpen>
      <vehicleBodyOpen>cc/vehicle_model/vehicle2d/white_vehicle_body_body_without_doors.png</vehicleBodyOpen>
      <frontLeftDoorClose>cc/vehicle_model/vehicle2d/white_front_left_door_close_normal.png</frontLeftDoorClose>
      <frontRightDoorClose>cc/vehicle_model/vehicle2d/white_front_right_door_close_normal.png</frontRightDoorClose>
      <hoodClose>cc/vehicle_model/vehicle2d/white_hood_close.png</hoodClose>
      <rearLeftDoorClose>cc/vehicle_model/vehicle2d/white_rear_left_door_close_normal.png</rearLeftDoorClose>
      <rearRightDoorClose>cc/vehicle_model/vehicle2d/white_rear_right_door_close_normal.png</rearRightDoorClose>
      <trunkClose>cc/vehicle_model/vehicle2d/white_trunk_close.png</trunkClose>
    </white>
    <silverglazewhite>
      <frontLeftDoorOpen>cc/vehicle_model/vehicle2d/silver_glaze_white_front_left_door_open_warning.png</frontLeftDoorOpen>
      <frontRightDoorOpen>cc/vehicle_model/vehicle2d/silver_glaze_white_front_right_door_open_warning.png</frontRightDoorOpen>
      <hoodOpen>cc/vehicle_model/vehicle2d/silver_glaze_white_hood_open_warning.png</hoodOpen>
      <rearLeftDoorOpen>cc/vehicle_model/vehicle2d/silver_glaze_white_rear_left_door_open_warning.png</rearLeftDoorOpen>
      <rearRightDoorOpen>cc/vehicle_model/vehicle2d/silver_glaze_white_rear_right_door_open_warning.png</rearRightDoorOpen>
      <trunkOpen>cc/vehicle_model/vehicle2d/silver_glaze_white_trunk_open_warning.png</trunkOpen>
      <vehicleBodyOpen>cc/vehicle_model/vehicle2d/silver_glaze_white_vehicle_body_body_without_doors.png</vehicleBodyOpen>
      <frontLeftDoorClose>cc/vehicle_model/vehicle2d/silver_glaze_white_front_left_door_close_normal.png</frontLeftDoorClose>
      <frontRightDoorClose>cc/vehicle_model/vehicle2d/silver_glaze_white_front_right_door_close_normal.png</frontRightDoorClose>
      <hoodClose>cc/vehicle_model/vehicle2d/silver_glaze_white_hood_close.png</hoodClose>
      <rearLeftDoorClose>cc/vehicle_model/vehicle2d/silver_glaze_white_rear_left_door_close_normal.png</rearLeftDoorClose>
      <rearRightDoorClose>cc/vehicle_model/vehicle2d/silver_glaze_white_rear_right_door_close_normal.png</rearRightDoorClose>
      <trunkClose>cc/vehicle_model/vehicle2d/silver_glaze_white_trunk_close.png</trunkClose>
    </silverglazewhite>
    <duduwhite>
      <frontLeftDoorOpen>cc/vehicle_model/vehicle2d/ddwhite_front_left_door_open_warning.png</frontLeftDoorOpen>
      <frontRightDoorOpen>cc/vehicle_model/vehicle2d/ddwhite_front_right_door_open_warning.png</frontRightDoorOpen>
      <hoodOpen>cc/vehicle_model/vehicle2d/ddwhite_hood_open_warning.png</hoodOpen>
      <rearLeftDoorOpen>cc/vehicle_model/vehicle2d/ddwhite_rear_left_door_open_warning.png</rearLeftDoorOpen>
      <rearRightDoorOpen>cc/vehicle_model/vehicle2d/ddwhite_rear_right_door_open_warning.png</rearRightDoorOpen>
      <trunkOpen>cc/vehicle_model/vehicle2d/ddwhite_trunk_open_warning.png</trunkOpen>
      <vehicleBodyOpen>cc/vehicle_model/vehicle2d/ddwhite_vehicle_body_body_without_doors.png</vehicleBodyOpen>
      <frontLeftDoorClose>cc/vehicle_model/vehicle2d/ddwhite_front_left_door_close_normal.png</frontLeftDoorClose>
      <frontRightDoorClose>cc/vehicle_model/vehicle2d/ddwhite_front_right_door_close_normal.png</frontRightDoorClose>
      <hoodClose>cc/vehicle_model/vehicle2d/ddwhite_hood_close.png</hoodClose>
      <rearLeftDoorClose>cc/vehicle_model/vehicle2d/ddwhite_rear_left_door_close_normal.png</rearLeftDoorClose>
      <rearRightDoorClose>cc/vehicle_model/vehicle2d/ddwhite_rear_right_door_close_normal.png</rearRightDoorClose>
      <trunkClose>cc/vehicle_model/vehicle2d/silver_glaze_white_trunk_close.png</trunkClose>
    </duduwhite>
    <junWareGray>
      <frontLeftDoorOpen>cc/vehicle_model/vehicle2d/junWareGray_front_left_door_open_warning.png</frontLeftDoorOpen>
      <frontRightDoorOpen>cc/vehicle_model/vehicle2d/junWareGray_front_right_door_open_warning.png</frontRightDoorOpen>
      <hoodOpen>cc/vehicle_model/vehicle2d/junWareGray_hood_open_warning.png</hoodOpen>
      <rearLeftDoorOpen>cc/vehicle_model/vehicle2d/junWareGray_rear_left_door_open_warning.png</rearLeftDoorOpen>
      <rearRightDoorOpen>cc/vehicle_model/vehicle2d/junWareGray_rear_right_door_open_warning.png</rearRightDoorOpen>
      <trunkOpen>cc/vehicle_model/vehicle2d/junWareGray_trunk_open_warning.png</trunkOpen>
      <vehicleBodyOpen>cc/vehicle_model/vehicle2d/junWareGray_vehicle_body_body_without_doors.png</vehicleBodyOpen>
      <frontLeftDoorClose>cc/vehicle_model/vehicle2d/junWareGray_front_left_door_close_normal.png</frontLeftDoorClose>
      <frontRightDoorClose>cc/vehicle_model/vehicle2d/junWareGray_front_right_door_close_normal.png</frontRightDoorClose>
      <hoodClose>cc/vehicle_model/vehicle2d/junWareGray_hood_close.png</hoodClose>
      <rearLeftDoorClose>cc/vehicle_model/vehicle2d/junWareGray_rear_left_door_close_normal.png</rearLeftDoorClose>
      <rearRightDoorClose>cc/vehicle_model/vehicle2d/junWareGray_rear_right_door_close_normal.png</rearRightDoorClose>
      <trunkClose>cc/vehicle_model/vehicle2d/silver_glaze_white_trunk_close.png</trunkClose>
    </junWareGray>
    <gearStatusDrive>cc/resources/ui/gear_D.png</gearStatusDrive>
    <gearStatusReverse>cc/resources/ui/gear_R.png</gearStatusReverse>
    <gearStatusPark>cc/resources/ui/gear_P.png</gearStatusPark>
    <gearStatusNeutral>cc/resources/ui/gear_N.png</gearStatusNeutral>
    <proportion>516.0 516.0</proportion> <!-- measured manually in vehicle 2d picture in pixel -->
    <aspectRatioOfVehicle>0.721</aspectRatioOfVehicle> <!-- For HCST23 only for vehicle TransIcon ratio-->
    <planViewPos>742.5 738.0</planViewPos> <!-- aligned with param in VehicleTransIconSettings-->
    <planViewPosVert>288.0 863.0</planViewPosVert> <!-- aligned with param in VehicleTransIconSettings-->
    <parkingPlanViewPos>742.5 738.0</parkingPlanViewPos> <!-- aligned with param in VehicleTransIconSettings, For HC23, the position is 742.5, we choose to set 742-->
    <avmBackgroundPos>248.0 385.5</avmBackgroundPos> <!-- Position for setting avm background icons-->
    <planViewPosRemote>400.0 540.0</planViewPosRemote> <!-- Position for setting avm background icons-->
    <!-- OFFSET -->
    <planViewFLPosOffset>         -0.3387096774193548 -0.08527131782945736     </planViewFLPosOffset>
    <planViewFRPosOffset>         0.3387096774193548 -0.08527131782945736    </planViewFRPosOffset>
    <planViewRLPosOffset>        -0.3387096774193548 0.14728682170542637     </planViewRLPosOffset>
    <planViewRRPosOffset>         0.3387096774193548 0.14728682170542637     </planViewRRPosOffset>
    <planViewHoodPosOffset>        0.0 0.0           </planViewHoodPosOffset>
    <planViewTrunkPosOffset>       0.0 0.0          </planViewTrunkPosOffset>
    <!-- SCALE -->
    <planViewDoorScale>     0.3225806451612903 0.29069767441860467    </planViewDoorScale>
    <planViewHoodPosScale>  1.0 0.31007751937984496  </planViewHoodPosScale>
    <planViewTrunkPosScale> 1.0 0.1937984496124031  </planViewTrunkPosScale>
    <!-- MASK -->
    <horiParkingLeftMaskOffset>37.0 0.0</horiParkingLeftMaskOffset>
    <horiParkingRightMaskOffset>-37.0 0.0</horiParkingRightMaskOffset>
    <horiParkingMaskScaleFactor>1.11</horiParkingMaskScaleFactor>
    <vertParkingLeftMaskOffset>0.0 -48.0</vertParkingLeftMaskOffset>
    <vertParkingRightMaskOffset>0.0 48.0</vertParkingRightMaskOffset>
    <vertParkingMaskScaleFactor>1.11</vertParkingMaskScaleFactor>
    <normalMaskScaleFactor>0.824 1.712</normalMaskScaleFactor>
    <fullscreenMaskScaleFactor>0.606 1.259</fullscreenMaskScaleFactor>
    <imageInImageScaleFactor>0.749 1.560</imageInImageScaleFactor>
    <gearSizePercentage>0.25</gearSizePercentage>
    <gearOffsetPercentage>0.0</gearOffsetPercentage>
  </Vehicle2DSettings>
  <VehicleTransIconSettings>
    <isEnabled>1</isEnabled>
  </VehicleTransIconSettings>
  <StreetOverlay>
    <gridFadeOutBegin>9.0</gridFadeOutBegin>
    <gridFadeOutEnd>16.0</gridFadeOutEnd>
    <gridXOffset>5.0</gridXOffset>
    <gridYOffset>3.7</gridYOffset>
    <gridWidth>4.0</gridWidth>
    <gridHeight>-0.01</gridHeight>
    <gridTileSize>1.0 2.0</gridTileSize>
    <gridTileOffset>0.5 0.0</gridTileOffset>
    <gridTexMinFilterMode>2</gridTexMinFilterMode>
    <roadBegin>4.5</roadBegin>
    <roadEnd>-10.0</roadEnd>
    <roadHeight>-0.02</roadHeight>
    <roadWidth>-0.8</roadWidth>
    <roadFadeOutFront>20.0</roadFadeOutFront>
    <roadFadeOutRear>5.0</roadFadeOutRear>
    <roadTexture>cc/vehicle_model/ui/park_road.png</roadTexture>
    <roadTexMinFilterMode>0</roadTexMinFilterMode>
    <roadTexCoordExp>3.0</roadTexCoordExp>
  </StreetOverlay>
  <SWInfoOverlay>
    <offset_SwVersion>2.6 0.0 0.0</offset_SwVersion>
    <offset_HwVersion>3.1 0.0 0.0</offset_HwVersion>
  </SWInfoOverlay>
  <STBSettings>
    <height>0.05</height>
    <shadowHeight>0.01</shadowHeight>
    <color>1.0 1.0 1.0 0.9</color>
    <shadowColor>0.0 0.0 0.0 0.9</shadowColor>
    <wheelRadius>0.36425</wheelRadius>
    <wheelAlpha>1.0</wheelAlpha>     <!-- transparency of wheels. 0.0 is full transparent. 1.0 is not transparent-->
    <lightPosition>0.0 1.0 1.0</lightPosition>
    <lineWidth>0.008</lineWidth>
    <lineGradientWidth>0.010</lineGradientWidth>
    <shadowWidth>0.04</shadowWidth>
    <shadowGradientWidth>0.03</shadowGradientWidth>
    <wheelinePositionX>0.5</wheelinePositionX>
    <wheelinePositionY>0.05</wheelinePositionY>
    <wheelinePositionZ>0.5</wheelinePositionZ>
    <wheelineWidth>0.04</wheelineWidth>  <!-- plus to the distance between two sides of the wheel width-->
    <wheelineLength>0.05</wheelineLength>  <!-- plus to the wheel diameter-->
  </STBSettings>
  <SpeedOverlay>
    <offset_Speed>-4.4 0.0 0.0</offset_Speed>
    <position_hori>6.3 0.0 0.0 </position_hori>
    <position_vert>1.3 4.7 0.0 </position_vert>
    <fontSize>35</fontSize>
  </SpeedOverlay>
  <DynamicDistance>
    <positionHori>1011 -605 0</positionHori> <!--Unit: in meter on UI planview-->
    <positionVert>629 -894 0</positionVert> <!--Unit: in meter on UI planview-->
    <fontType>cc/resources/HYQiHei_55S.ttf</fontType>
    <color>1.0 1.0 1.0 1.0</color>
    <charSizeHori>40.0</charSizeHori>
    <FontResolutionWidthHori>100.0</FontResolutionWidthHori>
    <FontResolutionHeightHori>115.0</FontResolutionHeightHori>
    <horiToVertScaling>1.0</horiToVertScaling>
  </DynamicDistance>
  <LowpolyVehicle>
    <vehicleModelFilename>cc/vehicle_model/vehicle_simplified.osgb</vehicleModelFilename>
  </LowpolyVehicle>
  <TargetParkingSpot>
    <slotHalfWidth>1.2</slotHalfWidth> <!--Unit: in meter on UI planview-->
    <slotRear2RearAxelDistance>1.2</slotRear2RearAxelDistance> <!--Unit: in meter on UI planview-->
    <slotFront2RearAxelDistance>4.0</slotFront2RearAxelDistance>
    <selectedFilename>cc/vehicle_model/ui/night/181_dynamic_ps_selected_cross_left.png</selectedFilename>
    <selectedCrossRFilename>cc/vehicle_model/ui/night/180_parking_space_selected_cross_right_t.png</selectedCrossRFilename>
    <selectedParaLFilename>cc/vehicle_model/ui/night/189_parking_space_selected_parallel_left_t.png</selectedParaLFilename>
    <selectedParaRFilename>cc/vehicle_model/ui/night/188_parking_space_selected_parallel_right_t.png</selectedParaRFilename>
    <selectableFilename>cc/vehicle_model/ui/night/targetSlot_selectable.png</selectableFilename>
    <selectedFilenameDay>cc/vehicle_model/ui/day/181_dynamic_ps_selected_cross_left.png</selectedFilenameDay>
    <selectedCrossRFilenameDay>cc/vehicle_model/ui/day/180_parking_space_selected_cross_right_t.png</selectedCrossRFilenameDay>
    <selectedParaLFilenameDay>cc/vehicle_model/ui/day/189_parking_space_selected_parallel_left_t.png</selectedParaLFilenameDay>
    <selectedParaRFilenameDay>cc/vehicle_model/ui/day/188_parking_space_selected_parallel_right_t.png</selectedParaRFilenameDay>
    <selectableFilenameDay>cc/vehicle_model/ui/day/targetSlot_selectable.png</selectableFilenameDay>
  </TargetParkingSpot>
  <apaCornerIcon>
    <topLeftPosition>505.0f 659.0f</topLeftPosition>
    <topBottomPosition>505.0f 9.0f</topBottomPosition>
  </apaCornerIcon>
  <DrivablePath>
    <isTesting>0</isTesting>
    <path_1>-918 2251 4788 -2902</path_1>
    <path_2>-958 2451 4788 -3304</path_2>
    <path_3>-965 2441 4788 -3022</path_3>
    <path_4>-965 2241 4788  -3022</path_4>
    <path_5>-965 2241 4788  -3022</path_5>
    <path_6>-965 2241 4788  -3022</path_6>
    <path_7>-1318 1544 5126 -3986</path_7>
    <path_8>-972 2423 4788  -3041</path_8>
    <path_9>-972 2423 4788  -3041</path_9>
    <path_10>-972 2423 4788 -3041</path_10>
    <path_11>-972 2423 4788 -3041</path_11>
    <path_12>-972 2423 4788 -3041</path_12>
    <path_13>-972 2423 4788 -3041</path_13>
  </DrivablePath>
  <PlanViewEnlargeSettings>
    <planViewEnlargeSettings>
      <enlargeFactor>0.72</enlargeFactor>
      <enlargeFrontOffset> 0.0   -0.21  </enlargeFrontOffset> <!-- percentage of planview size, negative means vehicle go up, camera down -->
      <enlargeRearOffset>  0.0    0.21  </enlargeRearOffset>  <!-- percentage of planview size, negative means vehicle go up, camera down -->
    </planViewEnlargeSettings>
    <planViewFullscreenEnlargeSettings>
      <enlargeFactor>0.72</enlargeFactor>
      <enlargeFrontOffset> 0.0   -0.21  </enlargeFrontOffset> <!-- percentage of planview size, negative means vehicle go up, camera down -->
      <enlargeRearOffset>  0.0    0.21  </enlargeRearOffset>  <!-- percentage of planview size, negative means vehicle go up, camera down -->
    </planViewFullscreenEnlargeSettings>
    <planViewFloatscreenEnlargeSettings>
      <enlargeFactor>0.72</enlargeFactor>
      <enlargeFrontOffset> 0.0   -0.21  </enlargeFrontOffset>
      <enlargeRearOffset>  0.0    0.21  </enlargeRearOffset>
    </planViewFloatscreenEnlargeSettings>
    <vertPlanViewEnlargeSettings>
      <enlargeFactor>0.72</enlargeFactor>
      <enlargeFrontOffset>  0.22    0.0  </enlargeFrontOffset> <!-- percentage of planview size, negative means vehicle go up, camera down -->
      <enlargeRearOffset>  -0.22    0.0 </enlargeRearOffset>   <!-- percentage of planview size, negative means vehicle go up, camera down -->
    </vertPlanViewEnlargeSettings>
    <vertPlanViewFullscreenEnlargeSettings>
      <enlargeFactor>0.72</enlargeFactor>
      <enlargeFrontOffset>  0.25    0.0  </enlargeFrontOffset> <!-- percentage of planview size, negative means vehicle go up, camera down -->
      <enlargeRearOffset>  -0.25    0.0 </enlargeRearOffset>   <!-- percentage of planview size, negative means vehicle go up, camera down -->
    </vertPlanViewFullscreenEnlargeSettings>
    <enableAnimation>1</enableAnimation>
  </PlanViewEnlargeSettings>
  <RimLine>
    <filenameTexture>cc/resources/icons/rim_protection.png</filenameTexture>
    <bothWheelview_topLeftLine>
      <bottomRight> 1.6  1.0</bottomRight>
      <topLeft>     3.0  1.3</topLeft>
    </bothWheelview_topLeftLine>
    <bothWheelview_bottomLeftLine>
      <bottomRight> 0.6  1.0</bottomRight>
      <topLeft>     1.2  1.3</topLeft>
    </bothWheelview_bottomLeftLine>
    <frontWheelview_topLeftLine>
      <bottomRight> 1.7  1.0</bottomRight>
      <topLeft>     2.8  1.3</topLeft>
    </frontWheelview_topLeftLine>
    <rearWheelview_bottomLeftLine>
      <bottomRight> -0.5  1.0</bottomRight>
      <topLeft>      1.2  1.3</topLeft>
    </rearWheelview_bottomLeftLine>
  </RimLine>
  <ArrowTrajectory>
    <ArrowLength>0.75</ArrowLength>
    <ArrowWidth>0.34</ArrowWidth>
    <StartDistanceFromFrontBumper>1.25</StartDistanceFromFrontBumper>
    <StartDistanceFromRearBumper>0.75</StartDistanceFromRearBumper>
    <GapDistance>0.6</GapDistance>
    <AnimationSpeed>6</AnimationSpeed>
    <groundLevel>0.01</groundLevel>
  </ArrowTrajectory>
  <FreeparkingRotateButton>
    <parkablePressableTexture>cc/vehicle_model/ui/night/freeparking/rotatebutton_blue.png</parkablePressableTexture>
    <unparkablePressableTexture>cc/vehicle_model/ui/night/freeparking/rotatebutton_white.png</unparkablePressableTexture>
    <parkablePressedTexture>cc/vehicle_model/ui/night/freeparking/rotatebutton_blue_pressed.png</parkablePressedTexture>
    <unparkablePressedTexture>cc/vehicle_model/ui/night/freeparking/rotatebutton_white_pressed.png</unparkablePressedTexture>
    <groundLevel>0.1</groundLevel>
    <mipmapBias>-0.2</mipmapBias>
    <size>1.4 1.4</size>
    <arrowSize>1.27 2.8</arrowSize>
    <offsetPercentage>0.3</offsetPercentage>
    <step>5</step>
  </FreeparkingRotateButton>
  <IQ_Chamaeleon>
    <enableChamaeleon>1</enableChamaeleon>

    <roisData>
        <stitchingLineFactor>1.4</stitchingLineFactor>
        <roiSizeWidth>1.8</roiSizeWidth>
        <roiSizeHight>1.8</roiSizeHight>
        <useOrthoLine>0</useOrthoLine>
        <useFilterRois>1</useFilterRois>
        <filterDeltaE>20</filterDeltaE>
    </roisData>
    <estimatorData>
        <rampFac>0.8</rampFac>
        <thresholdLowerDeltaL>30</thresholdLowerDeltaL>
        <thresholdUpperDeltaL>32</thresholdUpperDeltaL>
        <thresholdLowerDeltaAB>25</thresholdLowerDeltaAB>
        <thresholdUpperDeltaAB>27</thresholdUpperDeltaAB>
        <minOutputGain>0.6</minOutputGain>
        <maxOutputGain>1.4</maxOutputGain>
    </estimatorData>
    <visu>
        <visuRoiData>
            <enable>0</enable>
            <viewport>
                <visuViewportX>800</visuViewportX>
                <visuViewportY>400</visuViewportY>
                <visuViewportWidth>150</visuViewportWidth>
                <visuViewportHeight>150</visuViewportHeight>
            </viewport>
        </visuRoiData>
    </visu>
    <baseSignalData>
        <ignoreCarDoorState>0</ignoreCarDoorState> <!-- 0: not ignore, keep current deg logic; 1: ignore, no degradation when door open -->
        <ignoreMirrorState>0</ignoreMirrorState>
        <ignoreDegradationState>0</ignoreDegradationState>
        <ignoreCalibState>0</ignoreCalibState> <!-- 1: ignore the calibration status, no degegration when no calibration -->
    </baseSignalData>
  </IQ_Chamaeleon>
  <IQ_SharpnessHarmonization>
    <defaultView>
        <enable>0</enable>
        <sharpeningFactorDay>2.0</sharpeningFactorDay>
        <sharpeningFactorNight>2.0</sharpeningFactorNight>
        <smoothingFactorDay>1.0</smoothingFactorDay>
        <smoothingFactorNight>1.0</smoothingFactorNight>
        <luminanceDayToNight>100</luminanceDayToNight>
        <luminanceNightToDay>100</luminanceNightToDay>
        <maxSharpening>1.8</maxSharpening>
        <debug>0</debug>
    </defaultView>
  </IQ_SharpnessHarmonization>
  <IQ_TemporalNoiseFilter>
    <filteredImageWeight>0.55</filteredImageWeight>
    <motionDetectionThreshold>11</motionDetectionThreshold>
    <setToPassThrough>0</setToPassThrough>
    <outputMotionMap>0</outputMotionMap>
    <enable>0</enable>
  </IQ_TemporalNoiseFilter>
  <CameraImageCallback>
    <brightFactorNight>1.3</brightFactorNight>
  </CameraImageCallback>
  <VirtualRealityObject>
    <slotHalfWidth>1.25</slotHalfWidth>
    <slotRear2RearAxelDistance>1.4</slotRear2RearAxelDistance>
    <slotFront2RearAxelDistance>4.1</slotFront2RearAxelDistance>
    <vehicleModelScaleFactor>1.0 1.0 1.0</vehicleModelScaleFactor>
  </VirtualRealityObject>
  <LowpolyVehicle>
    <vehicleModelFilename>cc/vehicle_model/virualReality/large_trunk/Mesh_large_trunk.osg</vehicleModelFilename>
  </LowpolyVehicle>
  <VirtualEgoVehicle>
    <vehicleModelFilename>cc/vehicle_model/virualReality/car/Mesh_Car.osg</vehicleModelFilename>
  </VirtualEgoVehicle>
  <VirtualParkSlot>
    <occupiedFilename>cc/vehicle_model/virualReality/parkslot_occupied.png</occupiedFilename>
    <notSelectableFilename>cc/vehicle_model/virualReality/parkslot_occupied.png</notSelectableFilename>
    <selectableFilename>cc/vehicle_model/virualReality/parkslot_selectable.png</selectableFilename>
    <selectedFilename>cc/vehicle_model/virualReality/parkslot_selected.png</selectedFilename>
  </VirtualParkSlot>
  <LowpolyPedestrian>
    <pedestrianModelFilename>cc/vehicle_model/virualReality/walking_man/Mesh_man.osg</pedestrianModelFilename>
    <colorNormal>0.50 0.71 0.89 1.0</colorNormal>
    <colorCritical>0.89 0.25 0.29 1.0</colorCritical>
    <heightOverGround>0.0</heightOverGround>
  </LowpolyPedestrian>
  <VirtualDataHandler>
    <objectConfidenceThreshold>80.0</objectConfidenceThreshold>
    <pedestrianConfidenceThreshold>90.0</pedestrianConfidenceThreshold>
    <objectPositionCovXThreshold>1.0</objectPositionCovXThreshold>
    <objectPositionCovYThreshold>1.0</objectPositionCovYThreshold>
    <objectVelocityCovXThreshold>2.0</objectVelocityCovXThreshold>
    <objectVelocityCovYThreshold>1.0</objectVelocityCovYThreshold>
    <objectPositionXFarThreshold>3.5</objectPositionXFarThreshold>
    <objectPositionXNearThreshold>3.0</objectPositionXNearThreshold>
    <objectPositionYFarThreshold>3.0</objectPositionYFarThreshold>
    <objectPositionYNearThreshold>2.5</objectPositionYNearThreshold>
    <pedestrianSwitchPress>
      <iconCenter>810 100</iconCenter>  <!-- top left corner of C area -->
      <responseArea>200 200</responseArea>
    </pedestrianSwitchPress>
  </VirtualDataHandler>
  <VirtualCamParam>
    <minElevation>0.1</minElevation>
    <zoomStep>0.03</zoomStep>
  </VirtualCamParam>
  <DistanceOverlay>
    <characterSize>0.35</characterSize>
    <offsetDistance>0.6</offsetDistance>
  </DistanceOverlay>
  <VirtualRealityManager>
    <ScaleFactorNoInformation>1.0</ScaleFactorNoInformation>
    <ScaleFactorCar>3.5</ScaleFactorCar>
    <ScaleFactorTruck>10.0</ScaleFactorTruck>
    <ScaleFactorBus>1.0</ScaleFactorBus>
    <ScaleFactorPedestrian>1.0</ScaleFactorPedestrian>
    <ScaleFactorBicyclistWithMan>2.0</ScaleFactorBicyclistWithMan>
    <ScaleFactorTricyclistWithMan>1.0</ScaleFactorTricyclistWithMan>
    <ScaleFactorTrafficBarrierCone>1.0</ScaleFactorTrafficBarrierCone>
    <ScaleFactorSpeedBumps>0.01</ScaleFactorSpeedBumps>
    <ScaleFactorTrafficBarrierParkingLock>1.0</ScaleFactorTrafficBarrierParkingLock>
    <ScaleFactorLimitedBlock>0.02</ScaleFactorLimitedBlock>
    <ScaleFactorWaterHorse>1.0</ScaleFactorWaterHorse>
    <ScaleFactorShoppingTrolleyWithMan>1.0</ScaleFactorShoppingTrolleyWithMan>
    <ScaleFactorParmWithMan>1.0</ScaleFactorParmWithMan>
    <ScaleFactorTrafficBarrierWarnningTriangle>1.0</ScaleFactorTrafficBarrierWarnningTriangle>
    <ScaleFactorTrafficBarrierBollardSleeve>1.0</ScaleFactorTrafficBarrierBollardSleeve>
    <ScaleFactorMotorcycleWithMan>1.0</ScaleFactorMotorcycleWithMan>
    <ScaleFactorPedestrianChild>1.0</ScaleFactorPedestrianChild>
    <ScaleFactorFireHydrant>1.0</ScaleFactorFireHydrant>
    <ScaleFactorFireBox>1.0</ScaleFactorFireBox>
    <ScaleFactorTrashCan>1.0</ScaleFactorTrashCan>
    <ScaleFactorSmallSweeper>1.0</ScaleFactorSmallSweeper>
    <ScaleFactorThinRoundPile>1.0</ScaleFactorThinRoundPile>
    <ScaleFactorSphericalStonePier>1.0</ScaleFactorSphericalStonePier>
    <ScaleFactorPillarStonePier>1.0</ScaleFactorPillarStonePier>
    <ScaleFactorSquareStonePier>1.0</ScaleFactorSquareStonePier>
    <ScaleFactorTrees>1.0</ScaleFactorTrees>
    <ScaleFactorCat>1.0</ScaleFactorCat>
    <ScaleFactorDogs>1.0</ScaleFactorDogs>
    <ScaleFactorGroundMarchingArrowReticle>1.0</ScaleFactorGroundMarchingArrowReticle>
    <ScaleFactorGridlines>1.0</ScaleFactorGridlines>
    <ScaleFactorPillars>0.01</ScaleFactorPillars>
    <ScaleFactorCrosswalks>1.0</ScaleFactorCrosswalks>
    <ScaleFactorChargingPile>1.0</ScaleFactorChargingPile>
    <ScaleFactorBasementArea>1.0</ScaleFactorBasementArea>
    <ScaleFactorSUV>0.01</ScaleFactorSUV>
    <ScaleFactorBucket>1.0</ScaleFactorBucket>
    <ScaleFactorWall>1.0</ScaleFactorWall>
    <ScaleFactorBicyclistWithoutMan>1.0</ScaleFactorBicyclistWithoutMan>
    <ScaleFactorMotorcycleWithoutMan>1.0</ScaleFactorMotorcycleWithoutMan>
    <ScaleFactorTricyclistWithoutMan>1.0</ScaleFactorTricyclistWithoutMan>
    <ScaleFactorTrafficBarrierWarningTrianglesForMotorVehicle>1.0</ScaleFactorTrafficBarrierWarningTrianglesForMotorVehicle>
    <ScaleFactorFence>1.0</ScaleFactorFence>
    <ScaleFactorBasementGate>1.0</ScaleFactorBasementGate>
    <ScaleFactorEntranceExitOfBasement>1.0</ScaleFactorEntranceExitOfBasement>
    <ScaleFactorShoppingTrolleyWithoutMan>1.0</ScaleFactorShoppingTrolleyWithoutMan>
    <ScaleFactorParmWithoutMan>1.0</ScaleFactorParmWithoutMan>
    <ScaleFactorBasementGateOpen>1.0</ScaleFactorBasementGateOpen>
    <ScaleFactorGreenBelt>1.0</ScaleFactorGreenBelt>
  </VirtualRealityManager>
  <CameraSteeringFront>
      <minSteeringAngle>0.0</minSteeringAngle>
      <maxSteeringAngle>4.0</maxSteeringAngle>
      <maxCamRotation>4.0</maxCamRotation>
      <angleHysteresis>3.0</angleHysteresis>
      <imageTurnPercentage>1.0</imageTurnPercentage>
  </CameraSteeringFront>
  <ParkingSlotCornersManager>
    <occupiedFilenameCross>cc/vehicle_model/ui/night/slot/back_unselectable_cross.png</occupiedFilenameCross>
    <notSelectableFilenameCross>cc/vehicle_model/ui/night/slot/back_unselectable_cross.png</notSelectableFilenameCross>
    <selectableFilenameCross>cc/vehicle_model/ui/night/slot/back_selectable_cross.png</selectableFilenameCross>
    <selectedFilenameCross>cc/vehicle_model/ui/night/slot/back_selected_cross.png</selectedFilenameCross>
    <occupiedFilenameParallel>cc/vehicle_model/ui/night/slot/back_unselectable_parallel.png</occupiedFilenameParallel>
    <notSelectableFilenameParallel>cc/vehicle_model/ui/night/slot/back_unselectable_parallel.png</notSelectableFilenameParallel>
    <selectableFilenameParallel>cc/vehicle_model/ui/night/slot/back_selectable_parallel.png</selectableFilenameParallel>
    <selectedFilenameParallel>cc/vehicle_model/ui/night/slot/back_selected_parallel.png</selectedFilenameParallel>
  </ParkingSlotCornersManager>
  <PtsOverlay>
    <extractorMinDistance>0.035</extractorMinDistance>
    <colors>
        <r2>255   2   0 255</r2> <!--rgba(255,   2,   0, 255)-->
        <r1>252  60   0 255</r1> <!--rgba(252,  60,   0, 255)-->
        <o2>253 107  13 255</o2> <!--rgba(253, 107,  13, 255)-->
        <o1>253 158  60 255</o1> <!--rgba(253, 158,  60, 255)-->
        <y2>242 252 100 255</y2> <!--rgba(242, 252, 100, 255)-->
        <y1>176 253  61 255</y1> <!--rgba(176, 253,  61, 255)-->
    </colors>
    <distances>
        <r2>0.3f</r2>
        <r1>0.4f</r1>
        <o2>0.5f</o2>
        <o1>0.6f</o1>
        <y2>0.7f</y2>
        <y1>0.9f</y1>
    </distances>
  </PtsOverlay>
  <CombinedViewCallback>
    <moveSpeed>1.2f</moveSpeed>
    <middleSkipSpeed>500</middleSkipSpeed>
    <gapSize>6</gapSize>
  </CombinedViewCallback>
</CodingParameters>
