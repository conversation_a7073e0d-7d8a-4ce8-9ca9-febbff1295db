//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: WM APA
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CXH5SZH Chen Xiangqin (CC-DA/EAV2-CN)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS WM
/// @file  CustomWarpFisheyeView.cpp
/// @brief
//=============================================================================

#include "cc/views/customwarpfisheyeview/inc/CustomWarpFisheyeView.h"
#include "CustomSystemConf.h"
#include "cc/imgui/inc/imgui_manager.h"
#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h"
#include "pc/svs/core/inc/Framework.h"
#include "pc/svs/core/inc/ShaderManager.h"
#include "pc/svs/core/inc/VideoTexture.h"
#include "pc/svs/factory/inc/SV3DAlgorithm.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h"
#include "pc/svs/virtcam/inc/VirtualCam.h"
#include "pc/svs/util/math/inc/FloatComp.h"

using pc::util::logging::g_AppContext;

namespace cc
{
namespace views
{
namespace warpfisheye
{

pc::util::coding::Item<SteeringFovData> g_steeringFov("SteeringFov");
//!
//! CustomWarpFisheyeView
//!

CustomWarpFisheyeView::CustomWarpFisheyeView(
    const std::string&                                 f_name,
    const pc::core::Viewport&                          f_viewport,
    pc::core::Framework*                               f_framework,
    pc::core::sysconf::Cameras                         f_camId,
    pc::views::warpfisheye::FisheyeModel*              f_pModel,
    const pc::views::warpfisheye::FisheyeViewSettings* f_settings,
    const CustomWarpFisheyeViewCropSettings*           f_cropBounds,
    rbp::vis::imp::sh::ESharpnessView                  f_settingSharpnessHarmonization,
    rbp::vis::imp::tnf::ETnfView                       f_settingTemporalNoiseFilter)
    : pc::views::warpfisheye::WarpFisheyeView(
          f_name,
          f_viewport,
          f_framework,
          f_camId,
          f_pModel,
          f_settings,
          f_cropBounds->m_cropBounds, // PRQA S 4050
          f_settingSharpnessHarmonization,
          f_settingTemporalNoiseFilter)
    , m_framework(f_framework)
    , m_camId(f_camId)
    , m_initialized(false)
    , m_aspectRatio(static_cast<vfc::float32_t>(getViewport()->aspectRatio()))
    , m_offGeode{}
    , m_greyGeode{}
    , m_cropParam(f_cropBounds)
    , m_viewportSize(osg::Vec2f(static_cast<vfc::float32_t>(getViewport()->width()), static_cast<vfc::float32_t>(getViewport()->height())))
    , m_steerAngle(0.0f)
    , m_isSteerChanged(false)
{
    customCreateView();
    m_initialized = true;

    // if (f_camId == pc::core::sysconf::Cameras::REAR_CAMERA)
    // {
    //     this->setProjectionResizePolicy(osg::Camera::ProjectionResizePolicy::FIXED);
    // }
}

CustomWarpFisheyeView::~CustomWarpFisheyeView() = default;

void CustomWarpFisheyeView::traverse(osg::NodeVisitor& f_nv)
{
    if ((osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType()))
    {
        if (!m_initialized)
        {
            customCreateView();
            m_initialized = true;
        }

        // Conditions for update checked inside
        customUpdate();
    }

    pc::views::warpfisheye::WarpFisheyeView::traverse(f_nv);
}

void CustomWarpFisheyeView::customCreateView()
{
    // Place the ortho camera at position (-1,0,0), looking to (0,0,0) in D70K reference
    setViewMatrixAsLookAt(osg::Vec3f(-1.0f, 0.0f, 0.0f), osg::Vec3f(0.0f, 0.0f, 0.0f), osg::Vec3f(0.0f, 0.0f, 1.0f));
    setCullingMode(osg::CullSettings::NO_CULLING);
    osg::StateSet* const l_stateSet = m_projObj->getOrCreateStateSet();
    l_stateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF);

    // osg::Vec4f l_cropBounds = osg::Vec4f( -1.0f, 1.0f, -1.0f, 1.0f);
    osg::Vec4f l_cropBounds = m_cropParam->m_cropBounds;

    if (l_cropBounds != osg::Vec4f())
    {
        setProjectionMatrixAsOrtho2D(
            l_cropBounds[0u], l_cropBounds[1u], l_cropBounds[2u] / m_aspectRatio, l_cropBounds[3u] / m_aspectRatio);
    }
    else
    {
        // Set default limits.
        // Horizontal limits to ortho projection matrix are aligned to geometry points (-1, 1)
        // Vertical limits are dependent on viewport aspect ratio
        setProjectionMatrixAsOrtho2D(
            -1.0,                                              // left
            1.0,                                               // right
            -1.0 / static_cast<vfc::float64_t>(m_aspectRatio), // bottom
            1.0 / static_cast<vfc::float64_t>(m_aspectRatio)); // top
    }

    // geode for grey Texture
    m_offGeode = new osg::Geode();
    m_offGeode->setName("Off Texture Geode");
    osg::Geometry* const l_quadOff = osg::createTexturedQuadGeometry(
        osg::Vec3f(0.0f, 1.0f, 1.0f / m_aspectRatio),
        osg::Vec3f(0.0f, -2.0f, 0.0f),                  // with fixed to 2
        osg::Vec3f(0.0f, 0.0f, -2.0f / m_aspectRatio)); // height

    osg::Vec4Array* const coloursOff = new osg::Vec4Array(1u);
    (*coloursOff)[0u].set(0.6f, 0.6f, 0.6f, 1.0f);
    l_quadOff->setColorArray(coloursOff, osg::Array::BIND_OVERALL);
    l_quadOff->setUseDisplayList(false);
    l_quadOff->setUseVertexBufferObjects(true);

    m_offGeode->addDrawable(l_quadOff); // PRQA S 3803

    osg::StateSet*                           const l_pStateSetOff = m_offGeode->getOrCreateStateSet();
    pc::core::TextureShaderProgramDescriptor l_basicTexShaderOff("basicColor");
    l_basicTexShaderOff.apply(l_pStateSetOff);                        // PRQA S 3803
    l_pStateSetOff->setMode(GL_BLEND, osg::StateAttribute::ON);       // PRQA S 3143
    l_pStateSetOff->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF); // PRQA S 3143

    // geode for grey Texture
    m_greyGeode = new osg::Geode();
    m_greyGeode->setName("Grey Texture Geode");
    osg::Geometry* const l_quad = osg::createTexturedQuadGeometry(
        osg::Vec3f(0.0f, 1.0f, 1.0f / m_aspectRatio),
        osg::Vec3f(0.0f, -2.0f, 0.0f),                  // with fixed to 2
        osg::Vec3f(0.0f, 0.0f, -2.0f / m_aspectRatio)); // height

    osg::Vec4Array* const colours = new osg::Vec4Array(1u);
    // (*colours)[0u].set(0.227f,0.227f,0.227f,1.0f); // Hex 3a3a3a
    (*colours)[0u].set(0.05f, 0.05f, 0.05f, 1.0f); // Hex 3a3a3a
    l_quad->setColorArray(colours, osg::Array::BIND_OVERALL);
    l_quad->setUseDisplayList(false);
    l_quad->setUseVertexBufferObjects(true);

    m_greyGeode->addDrawable(l_quad); // PRQA S 3803

    osg::StateSet*                           const l_pStateSet = m_greyGeode->getOrCreateStateSet();
    pc::core::TextureShaderProgramDescriptor l_basicTexShader("basicColor");
    l_basicTexShader.apply(l_pStateSet);                           // PRQA S 3803
    l_pStateSet->setMode(GL_BLEND, osg::StateAttribute::ON);       // PRQA S 3143
    l_pStateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF); // PRQA S 3143

    osg::Group::addChild(m_offGeode);  // PRQA S 3803
    osg::Group::addChild(m_greyGeode); // PRQA S 3803
    m_offGeode->setNodeMask(0u);
    m_greyGeode->setNodeMask(0u);

    // Update model and compute initial texture coordinates
    customUpdate();
}

void CustomWarpFisheyeView::customUpdate()
{
    // read degradation and deactivation mask
    vfc::uint32_t                                l_degradationMaskTarget = 0u;
    const pc::daddy::CameraDegradationMaskDaddy* const l_degradationMaskDaddy =
        m_framework->m_degradationMaskReceiver.getData();
    if (l_degradationMaskDaddy != nullptr)
    {
        l_degradationMaskTarget = l_degradationMaskDaddy->m_Data;
    }
    vfc::uint32_t                                 l_deactivationMaskTarget = 0u;
    const pc::daddy::CameraDeactivationMaskDaddy* const l_deactivationMaskDaddy =
        m_framework->m_deactivationMaskReceiver.getData();
    if (l_deactivationMaskDaddy != nullptr)
    {
        l_deactivationMaskTarget = l_deactivationMaskDaddy->m_Data;
    }
    const vfc::uint32_t l_camId_u = static_cast<vfc::uint32_t>(m_camId);
    // activate and deactivate the panoramer view geode according to the masks
    if (static_cast<bool>(l_degradationMaskTarget & (static_cast<vfc::uint32_t>(1u) << l_camId_u)))
    {
        // SHADER_CAM_OFF
        pc::views::warpfisheye::WarpFisheyeView::m_projObj->setNodeMask(0u);
        m_greyGeode->setNodeMask(~0u);
        m_offGeode->setNodeMask(0u);
    }
    else
    {
        if (static_cast<bool>(l_deactivationMaskTarget & (static_cast<vfc::uint32_t>(1u) << l_camId_u)))
        {
            // SHADER_CAM_DISABLED
            pc::views::warpfisheye::WarpFisheyeView::m_projObj->setNodeMask(~0u);
            m_greyGeode->setNodeMask(0u);
            // m_offGeode->setNodeMask(~0u);
        }
        else
        {
            // SHADER_CAM_ON
            pc::views::warpfisheye::WarpFisheyeView::m_projObj->setNodeMask(~0u);
            m_greyGeode->setNodeMask(0u);
            m_offGeode->setNodeMask(0u);
        }
    }

    if (m_framework->m_steeringAngleFrontReceiver.hasData() && m_steeringEnabled)
    {
        const auto                      steeringContainer = m_framework->m_steeringAngleFrontReceiver.getData();
        vfc::CSI::si_radian_f32_t l_angleInRadians  = steeringContainer->m_Data; // automatic conversion by VFC
        vfc::float32_t            l_angle           = l_angleInRadians.value();
        if (vfc::isPositive(l_angle))
        {
            l_angle = std::min(l_angle, osg::DegreesToRadians(g_steeringFov->m_enableFovRange));
        }
        else
        {
            l_angle = std::max(l_angle, osg::DegreesToRadians(-g_steeringFov->m_enableFovRange));
        }
        if (m_camId == pc::core::sysconf::Cameras::REAR_CAMERA)
        {
            l_angle = -l_angle;
        }

        if (isNotEqual(l_angle, m_steerAngle))
        {
            m_isSteerChanged = true;
            m_steerAngle = l_angle;
        }
    }

    bool l_isViewPortChanged = false;
    const auto viewport = this->getViewport();
    if (viewport != nullptr)
    {
        osg::Vec2f newViewportSize = {static_cast<vfc::float32_t>(viewport->width()), static_cast<vfc::float32_t>(viewport->height())};
        l_isViewPortChanged = ((m_viewportSize.x() != newViewportSize.x()) || (m_viewportSize.y() != newViewportSize.y()));
        if (l_isViewPortChanged)
        {
            setAspectRatio(static_cast<vfc::float32_t>(this->getViewport()->aspectRatio()));
            m_viewportSize = newViewportSize;
        }
    }

    if (l_isViewPortChanged || m_isSteerChanged)
    {
        m_isSteerChanged = false;
        osg::Vec4f l_cropBounds = m_cropParam->m_cropBounds;
        if (l_cropBounds != osg::Vec4f())
        {
            updateViewportBounds(
                l_cropBounds[0u] - m_steerAngle, l_cropBounds[1u] - m_steerAngle, l_cropBounds[2u], l_cropBounds[3u]);
        }
        else
        {
            setProjectionMatrixAsOrtho2D(-1.0 - m_steerAngle, 1.0, -m_steerAngle - 1.0, 1.0);
        }
        if (l_isViewPortChanged && (m_camId == pc::core::sysconf::Cameras::REAR_CAMERA))
        {
            osg::Matrixf l_mirrorTheWorld;
            l_mirrorTheWorld(0, 0) = -1.f;
            setProjectionMatrix(getProjectionMatrix() * l_mirrorTheWorld);
        }
        computeTextureCoordinates();
    }
}

} // namespace warpfisheye
} // namespace views
} // namespace cc
