#include "cc/vhm/inc/cusm.hpp"
#include "cc/vhm/inc/usm_signaltimer10ms.hpp"
#include "cc/target/common/inc/linux_svs_interface.hpp"
#include <cmath>
#include <stdio.h>
#include "thread"
#include "valin/inc/rbp_vsm_valin_conversion_api.hpp"
#include "valin/inc/valin_com2usm.hpp"
#include <signal.h>

#include "cc/vhm/inc/usm_user.hpp"

#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h"
#include "cc/vhm/inc/vhm.hpp"
#include "cc/daddy/inc/CustomDaddyPorts.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"
#include "cc/assets/customfloorplategenerator/inc/CustomFloorPlateGenerator.h"
#include "pc/svs/util/math/inc/FloatComp.h"

#define RBP_COMPONENT_VHM
#define ODO_COMPENSATE
using pc::util::logging::g_AppContext;

extern "C"{
#include "rbp_par_header_gen.h"  // PRQA S 1060
#include "rbp_global_include_api.h"
#include "rbp_par_swcpma_api.h"
#include "rbp_flt_api.h"
#include "rbp_flt_swcpma_api.h"
#include "rbp_vhm_swcpma_api.h"
#include "rbp_vhm_common_data_api.h"
#include "rbp_vhm_monitor.h"
#include "rbp_vhm_common_def.h"
#include "rbp_vhm_bas.h"
#include "cc/assets/pmasip/pma_pf/SRV_VHM/SRC/OUTPUT_CHECK/rbp_vhm_output_check.h"
}

extern rbp_Type_vhm_Parameters_st rbp_vhm_Parameters_st;
extern rbp_Type_par_RAMData_st rbp_par_RAMData_st;

namespace usm

{
usm::Cusm usm_run = usm::Cusm();

void setParam()
{
    (((rbp_Type_par_VehPara_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VehPara_du16]))->Wheelbase_u16) = static_cast<vfc::uint16_t>((pc::vehicle::g_mechanicalData->m_wheelbase)*1000.f);
    (((rbp_Type_par_VHM_Vehicle_Common_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_Common_du16]))->WICLength_u16) = static_cast<vfc::uint16_t>(cc::vhm::g_vhmConfig->m_WICLength_u16);
    (((rbp_Type_par_VHM_Vehicle_Common_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_Common_du16]))->MaxWICValue_u16) = static_cast<vfc::uint16_t>(cc::vhm::g_vhmConfig->m_MaxWICValue);
    (((rbp_Type_par_VHM_Vehicle_Common_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_Common_du16]))->SWAMax_u16) = static_cast<vfc::uint16_t>(cc::vhm::g_vhmConfig->m_SWAMax);
    (((rbp_Type_par_VHM_Vehicle_Common_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_Common_du16]))->SteeringRatio_u16) = static_cast<vfc::uint16_t>(cc::vhm::g_vhmConfig->m_SteeringRatio);
    (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->AxleBaseRear_u16) = static_cast<vfc::uint16_t>((pc::vehicle::g_mechanicalData->m_trackRear)*1000.f);
    (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->WICTeethCount_u16) = static_cast<vfc::uint16_t>(cc::vhm::g_vhmConfig->m_WICTeethCount);
    (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->WIC_CycleTime_MS_u16) = static_cast<vfc::uint16_t>(cc::vhm::g_vhmConfig->m_WICCycleTime_MS);
    (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->MultMonomA_ForwSWA2SA_s16) = static_cast<vfc::int16_t>(cc::vhm::g_vhmConfig->m_MultMonomA_ForwSWA2SA);
    (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->MultMonomB_ForwSWA2SA_s16) = static_cast<vfc::int16_t>(cc::vhm::g_vhmConfig->m_MultMonomB_ForwSWA2SA);
    (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->CoeffC_ForwSWA2SA_s32) = static_cast<vfc::int32_t>(cc::vhm::g_vhmConfig->m_CoeffC_ForwSWA2SA);
    (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->CoeffD_ForwSWA2SA_s32) = static_cast<vfc::int32_t>(cc::vhm::g_vhmConfig->m_CoeffD_ForwSWA2SA);
    (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->MultMonomA_BackSWA2SA_s16) = static_cast<vfc::int16_t>(cc::vhm::g_vhmConfig->m_MultMonomA_BackSWA2SA);
    (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->MultMonomB_BackSWA2SA_s16) = static_cast<vfc::int16_t>(cc::vhm::g_vhmConfig->m_MultMonomB_BackSWA2SA);
    (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->CoeffC_BackSWA2SA_s32) = static_cast<vfc::int32_t>(cc::vhm::g_vhmConfig->m_CoeffC_BackSWA2SA);
    (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->CoeffD_BackSWA2SA_s32) = static_cast<vfc::int32_t>(cc::vhm::g_vhmConfig->m_CoeffD_BackSWA2SA);
    (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->ScalingMonomA_SWA2SA_u8) = static_cast<vfc::uint8_t>(cc::vhm::g_vhmConfig->m_ScalingMonomA_SWA2SA);
    (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->ScalingMonomB_SWA2SA_u8) = static_cast<vfc::uint8_t>(cc::vhm::g_vhmConfig->m_ScalingMonomB_SWA2SA);
    (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->ScalingMonomCD_SWA2SA_u8) = static_cast<vfc::uint8_t>(cc::vhm::g_vhmConfig->m_ScalingMonomCD_SWA2SA);
    (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->ClippingAngle1_s16) = static_cast<vfc::int16_t>(cc::vhm::g_vhmConfig->m_ClippingAngle1_s16);
    (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->ClippingAngle2_s16) = static_cast<vfc::int16_t>(cc::vhm::g_vhmConfig->m_ClippingAngle2_s16);
    (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->CoeffC_BackSA2SWAC_s32) = static_cast<vfc::int32_t>(cc::vhm::g_vhmConfig->m_CoeffC_BackSA2SWAC_s32);
    (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->CoeffC_BackSA2SWAL_s32) = static_cast<vfc::int32_t>(cc::vhm::g_vhmConfig->m_CoeffC_BackSA2SWAL_s32);
    (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->CoeffC_BackSA2SWAR_s32) = static_cast<vfc::int32_t>(cc::vhm::g_vhmConfig->m_CoeffC_BackSA2SWAR_s32);
    (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->CoeffC_ForwSA2SWAC_s32) = static_cast<vfc::int32_t>(cc::vhm::g_vhmConfig->m_CoeffC_ForwSA2SWAC_s32);
    (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->CoeffC_ForwSA2SWAL_s32) = static_cast<vfc::int32_t>(cc::vhm::g_vhmConfig->m_CoeffC_ForwSA2SWAL_s32);
    (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->CoeffC_ForwSA2SWAR_s32) = static_cast<vfc::int32_t>(cc::vhm::g_vhmConfig->m_CoeffC_ForwSA2SWAR_s32);
    (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->CoeffD_BackSA2SWAC_s32) = static_cast<vfc::int32_t>(cc::vhm::g_vhmConfig->m_CoeffD_BackSA2SWAC_s32);
    (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->CoeffD_BackSA2SWAL_s32) = static_cast<vfc::int32_t>(cc::vhm::g_vhmConfig->m_CoeffD_BackSA2SWAL_s32);
    (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->CoeffD_BackSA2SWAR_s32) = static_cast<vfc::int32_t>(cc::vhm::g_vhmConfig->m_CoeffD_BackSA2SWAR_s32);
    (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->CoeffD_ForwSA2SWAC_s32) = static_cast<vfc::int32_t>(cc::vhm::g_vhmConfig->m_CoeffD_ForwSA2SWAC_s32);
    (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->CoeffD_ForwSA2SWAL_s32) = static_cast<vfc::int32_t>(cc::vhm::g_vhmConfig->m_CoeffD_ForwSA2SWAL_s32);
    (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->CoeffD_ForwSA2SWAR_s32) = static_cast<vfc::int32_t>(cc::vhm::g_vhmConfig->m_CoeffD_ForwSA2SWAR_s32);
    {
        (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->DifFrontWheelRadiiTable_pu16[0]) = static_cast<vfc::uint16_t>(cc::vhm::g_vhmConfig->m_DifFrontWheelRadiiTable_pu16.m_FrontWheelRadii0);
        (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->DifFrontWheelRadiiTable_pu16[1]) = static_cast<vfc::uint16_t>(cc::vhm::g_vhmConfig->m_DifFrontWheelRadiiTable_pu16.m_FrontWheelRadii1);
        (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->DifFrontWheelRadiiTable_pu16[2]) = static_cast<vfc::uint16_t>(cc::vhm::g_vhmConfig->m_DifFrontWheelRadiiTable_pu16.m_FrontWheelRadii2);
        (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->DifFrontWheelRadiiTable_pu16[3]) = static_cast<vfc::uint16_t>(cc::vhm::g_vhmConfig->m_DifFrontWheelRadiiTable_pu16.m_FrontWheelRadii3);
        (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->DifFrontWheelRadiiTable_pu16[4]) = static_cast<vfc::uint16_t>(cc::vhm::g_vhmConfig->m_DifFrontWheelRadiiTable_pu16.m_FrontWheelRadii4);
        (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->DifFrontWheelRadiiTable_pu16[5]) = static_cast<vfc::uint16_t>(cc::vhm::g_vhmConfig->m_DifFrontWheelRadiiTable_pu16.m_FrontWheelRadii5);
        (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->DifFrontWheelRadiiTable_pu16[6]) = static_cast<vfc::uint16_t>(cc::vhm::g_vhmConfig->m_DifFrontWheelRadiiTable_pu16.m_FrontWheelRadii6);
        (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->DifFrontWheelRadiiTable_pu16[7]) = static_cast<vfc::uint16_t>(cc::vhm::g_vhmConfig->m_DifFrontWheelRadiiTable_pu16.m_FrontWheelRadii7);
        (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->DifFrontWheelRadiiTable_pu16[8]) = static_cast<vfc::uint16_t>(cc::vhm::g_vhmConfig->m_DifFrontWheelRadiiTable_pu16.m_FrontWheelRadii8);
        (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->DifFrontWheelRadiiTable_pu16[9]) = static_cast<vfc::uint16_t>(cc::vhm::g_vhmConfig->m_DifFrontWheelRadiiTable_pu16.m_FrontWheelRadii9);
        (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->DifFrontWheelRadiiTable_pu16[10]) = static_cast<vfc::uint16_t>(cc::vhm::g_vhmConfig->m_DifFrontWheelRadiiTable_pu16.m_FrontWheelRadii10);
        (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->DifFrontWheelRadiiTable_pu16[11]) = static_cast<vfc::uint16_t>(cc::vhm::g_vhmConfig->m_DifFrontWheelRadiiTable_pu16.m_FrontWheelRadii11);
        (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->DifFrontWheelRadiiTable_pu16[12]) = static_cast<vfc::uint16_t>(cc::vhm::g_vhmConfig->m_DifFrontWheelRadiiTable_pu16.m_FrontWheelRadii12);
        (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->DifFrontWheelRadiiTable_pu16[13]) = static_cast<vfc::uint16_t>(cc::vhm::g_vhmConfig->m_DifFrontWheelRadiiTable_pu16.m_FrontWheelRadii13);
        (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->DifFrontWheelRadiiTable_pu16[14]) = static_cast<vfc::uint16_t>(cc::vhm::g_vhmConfig->m_DifFrontWheelRadiiTable_pu16.m_FrontWheelRadii14);
        (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->DifFrontWheelRadiiTable_pu16[15]) = static_cast<vfc::uint16_t>(cc::vhm::g_vhmConfig->m_DifFrontWheelRadiiTable_pu16.m_FrontWheelRadii15);
        (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->DifFrontWheelRadiiTable_pu16[16]) = static_cast<vfc::uint16_t>(cc::vhm::g_vhmConfig->m_DifFrontWheelRadiiTable_pu16.m_FrontWheelRadii16);
        (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->DifFrontWheelRadiiTable_pu16[17]) = static_cast<vfc::uint16_t>(cc::vhm::g_vhmConfig->m_DifFrontWheelRadiiTable_pu16.m_FrontWheelRadii17);
        (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->DifFrontWheelRadiiTable_pu16[18]) = static_cast<vfc::uint16_t>(cc::vhm::g_vhmConfig->m_DifFrontWheelRadiiTable_pu16.m_FrontWheelRadii18);
        (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->DifFrontWheelRadiiTable_pu16[19]) = static_cast<vfc::uint16_t>(cc::vhm::g_vhmConfig->m_DifFrontWheelRadiiTable_pu16.m_FrontWheelRadii19);
        (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->DifFrontWheelRadiiTable_pu16[20]) = static_cast<vfc::uint16_t>(cc::vhm::g_vhmConfig->m_DifFrontWheelRadiiTable_pu16.m_FrontWheelRadii20);
        (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->DifFrontWheelRadiiTable_pu16[21]) = static_cast<vfc::uint16_t>(cc::vhm::g_vhmConfig->m_DifFrontWheelRadiiTable_pu16.m_FrontWheelRadii21);
        (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->DifFrontWheelRadiiTable_pu16[22]) = static_cast<vfc::uint16_t>(cc::vhm::g_vhmConfig->m_DifFrontWheelRadiiTable_pu16.m_FrontWheelRadii22);
        (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->DifFrontWheelRadiiTable_pu16[23]) = static_cast<vfc::uint16_t>(cc::vhm::g_vhmConfig->m_DifFrontWheelRadiiTable_pu16.m_FrontWheelRadii23);
        (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->DifFrontWheelRadiiTable_pu16[24]) = static_cast<vfc::uint16_t>(cc::vhm::g_vhmConfig->m_DifFrontWheelRadiiTable_pu16.m_FrontWheelRadii24);
        (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->DifFrontWheelRadiiTable_pu16[25]) = static_cast<vfc::uint16_t>(cc::vhm::g_vhmConfig->m_DifFrontWheelRadiiTable_pu16.m_FrontWheelRadii25);
        (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->DifFrontWheelRadiiTable_pu16[26]) = static_cast<vfc::uint16_t>(cc::vhm::g_vhmConfig->m_DifFrontWheelRadiiTable_pu16.m_FrontWheelRadii26);
        (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->DifFrontWheelRadiiTable_pu16[27]) = static_cast<vfc::uint16_t>(cc::vhm::g_vhmConfig->m_DifFrontWheelRadiiTable_pu16.m_FrontWheelRadii27);
        (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->DifFrontWheelRadiiTable_pu16[28]) = static_cast<vfc::uint16_t>(cc::vhm::g_vhmConfig->m_DifFrontWheelRadiiTable_pu16.m_FrontWheelRadii28);
        (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->DifFrontWheelRadiiTable_pu16[29]) = static_cast<vfc::uint16_t>(cc::vhm::g_vhmConfig->m_DifFrontWheelRadiiTable_pu16.m_FrontWheelRadii29);
        (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->DifFrontWheelRadiiTable_pu16[30]) = static_cast<vfc::uint16_t>(cc::vhm::g_vhmConfig->m_DifFrontWheelRadiiTable_pu16.m_FrontWheelRadii30);
        (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->DifFrontWheelRadiiTable_pu16[31]) = static_cast<vfc::uint16_t>(cc::vhm::g_vhmConfig->m_DifFrontWheelRadiiTable_pu16.m_FrontWheelRadii31);
        (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->DifFrontWheelRadiiTable_pu16[32]) = static_cast<vfc::uint16_t>(cc::vhm::g_vhmConfig->m_DifFrontWheelRadiiTable_pu16.m_FrontWheelRadii32);
    }
    (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->DrivenAxle_en) = static_cast<rbp_Tag_vhm_DrivenAxle_en>(cc::vhm::g_vhmConfig->m_DrivenAxle_en);
    (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->MultMonomA_BackSA2SWAC_s16) = static_cast<vfc::int16_t>(cc::vhm::g_vhmConfig->m_MultMonomA_BackSA2SWAC_s16);
    (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->MultMonomA_BackSA2SWAL_s16) = static_cast<vfc::int16_t>(cc::vhm::g_vhmConfig->m_MultMonomA_BackSA2SWAL_s16);
    (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->MultMonomA_BackSA2SWAR_s16) = static_cast<vfc::int16_t>(cc::vhm::g_vhmConfig->m_MultMonomA_BackSA2SWAR_s16);
    (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->MultMonomA_ForwSA2SWAC_s16) = static_cast<vfc::int16_t>(cc::vhm::g_vhmConfig->m_MultMonomA_ForwSA2SWAC_s16);
    (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->MultMonomA_ForwSA2SWAL_s16) = static_cast<vfc::int16_t>(cc::vhm::g_vhmConfig->m_MultMonomA_ForwSA2SWAL_s16);
    (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->MultMonomA_ForwSA2SWAR_s16) = static_cast<vfc::int16_t>(cc::vhm::g_vhmConfig->m_MultMonomA_ForwSA2SWAR_s16);
    (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->MultMonomB_BackSA2SWAC_s16) = static_cast<vfc::int16_t>(cc::vhm::g_vhmConfig->m_MultMonomB_BackSA2SWAC_s16);
    (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->MultMonomB_BackSA2SWAL_s16) = static_cast<vfc::int16_t>(cc::vhm::g_vhmConfig->m_MultMonomB_BackSA2SWAL_s16);
    (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->MultMonomB_BackSA2SWAR_s16) = static_cast<vfc::int16_t>(cc::vhm::g_vhmConfig->m_MultMonomB_BackSA2SWAR_s16);
    (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->MultMonomB_ForwSA2SWAC_s16) = static_cast<vfc::int16_t>(cc::vhm::g_vhmConfig->m_MultMonomB_ForwSA2SWAC_s16);
    (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->MultMonomB_ForwSA2SWAL_s16) = static_cast<vfc::int16_t>(cc::vhm::g_vhmConfig->m_MultMonomB_ForwSA2SWAL_s16);
    (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->MultMonomB_ForwSA2SWAR_s16) = static_cast<vfc::int16_t>(cc::vhm::g_vhmConfig->m_MultMonomB_ForwSA2SWAR_s16);
    (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->ScalingMonomA_SA2SWAC_u8) = static_cast<vfc::uint8_t>(cc::vhm::g_vhmConfig->m_ScalingMonomA_SA2SWAC_u8);
    (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->ScalingMonomA_SA2SWAL_u8) = static_cast<vfc::uint8_t>(cc::vhm::g_vhmConfig->m_ScalingMonomA_SA2SWAL_u8);
    (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->ScalingMonomA_SA2SWAR_u8) = static_cast<vfc::uint8_t>(cc::vhm::g_vhmConfig->m_ScalingMonomA_SA2SWAR_u8);
    (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->ScalingMonomB_SA2SWAC_u8) = static_cast<vfc::uint8_t>(cc::vhm::g_vhmConfig->m_ScalingMonomB_SA2SWAC_u8);
    (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->ScalingMonomB_SA2SWAL_u8) = static_cast<vfc::uint8_t>(cc::vhm::g_vhmConfig->m_ScalingMonomB_SA2SWAL_u8);
    (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->ScalingMonomB_SA2SWAR_u8) = static_cast<vfc::uint8_t>(cc::vhm::g_vhmConfig->m_ScalingMonomB_SA2SWAR_u8);
    (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->ScalingMonomCD_SA2SWAC_u8) = static_cast<vfc::uint8_t>(cc::vhm::g_vhmConfig->m_ScalingMonomCD_SA2SWAC_u8);
    (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->ScalingMonomCD_SA2SWAL_u8) = static_cast<vfc::uint8_t>(cc::vhm::g_vhmConfig->m_ScalingMonomCD_SA2SWAL_u8);
    (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->ScalingMonomCD_SA2SWAR_u8) = static_cast<vfc::uint8_t>(cc::vhm::g_vhmConfig->m_ScalingMonomCD_SA2SWAR_u8);
}

void usm_init()
{
    printf("Process \"usm\" started!\n");
    rbp_par_init_vd();
    rbp_flt_init_vd();
    setParam();
    rbp_vhm_common_init_vd();
}

void usm_deinit()
{
    usm::usm_run.deInit();
}

void usm_can_recvMainfunction()
{
}


void inputVhmSignals()
{
  //   XLOG_INFO(g_AppContext, "[svs]: Wheelbase_u16 !!" <<
  //                     (((rbp_Type_par_VehPara_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VehPara_du16]))->Wheelbase_u16));

  // XLOG_INFO(g_AppContext, "[svs]: WICLength_u16 !!" <<
  //                     (((rbp_Type_par_VHM_Vehicle_Common_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_Common_du16]))->WICLength_u16));

  // XLOG_INFO(g_AppContext, "[svs]: MaxWICValue_u16 !!" <<
  //                     (((rbp_Type_par_VHM_Vehicle_Common_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_Common_du16]))->MaxWICValue_u16));

  // XLOG_INFO(g_AppContext, "[svs]: SWAMax_u16  !!" <<
  //                     (((rbp_Type_par_VHM_Vehicle_Common_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_Common_du16]))->SWAMax_u16));

  // XLOG_INFO(g_AppContext, "[svs]: SteeringRatio_u16  !!" <<
  //                     (((rbp_Type_par_VHM_Vehicle_Common_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_Common_du16]))->SteeringRatio_u16));

  // XLOG_INFO(g_AppContext, "[svs]: (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->AxleBaseRear_u16) !!" <<
  //                     (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->AxleBaseRear_u16));

  // XLOG_INFO(g_AppContext, "[svs]: (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->WICTeethCount_u16)  !!" <<
  //                     (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->WICTeethCount_u16));

  // XLOG_INFO(g_AppContext, "[svs]: WIC_CycleTime_MS_u16 !!" <<
  //                     (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->WIC_CycleTime_MS_u16));

  // XLOG_INFO(g_AppContext, "[svs]: MultMonomA_ForwSWA2SA_s16  !!" <<
  //                     (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->MultMonomA_ForwSWA2SA_s16));

  // XLOG_INFO(g_AppContext, "[svs]: MultMonomB_ForwSWA2SA_s16 !!" <<
  //                     (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->MultMonomB_ForwSWA2SA_s16));

  // XLOG_INFO(g_AppContext, "[svs]: CoeffC_ForwSWA2SA_s32  !!" <<
  //                     (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->CoeffC_ForwSWA2SA_s32));

  // XLOG_INFO(g_AppContext, "[svs]: CoeffD_ForwSWA2SA_s32  !!" <<
  //                     (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->CoeffD_ForwSWA2SA_s32));

  // XLOG_INFO(g_AppContext, "[svs]: MultMonomA_BackSWA2SA_s16  !!" <<
  //                     (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->MultMonomA_BackSWA2SA_s16));

  // XLOG_INFO(g_AppContext, "[svs]: MultMonomB_BackSWA2SA_s16  !!" <<
  //                     (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->MultMonomB_BackSWA2SA_s16));

  // XLOG_INFO(g_AppContext, "[svs]: CoeffC_BackSWA2SA_s32  !!" <<
  //                     (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->CoeffC_BackSWA2SA_s32));

  // XLOG_INFO(g_AppContext, "[svs]: CoeffD_BackSWA2SA_s32  !!" <<
  //                     (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->CoeffD_BackSWA2SA_s32));

  // XLOG_INFO(g_AppContext, "[svs]: ScalingMonomA_SWA2SA_u8  !!" <<
  //                     static_cast<vfc::int32_t>(((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->ScalingMonomA_SWA2SA_u8));

  // XLOG_INFO(g_AppContext, "[svs]: ScalingMonomB_SWA2SA_u8  !!" <<
  //                     static_cast<vfc::int32_t>(((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->ScalingMonomB_SWA2SA_u8));

  // XLOG_INFO(g_AppContext, "[svs]: ScalingMonomCD_SWA2SA_u8  !!" <<
  //                     static_cast<vfc::int32_t>(((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->ScalingMonomCD_SWA2SA_u8));

  // XLOG_INFO(g_AppContext, "[svs]: ClippingAngle1_s16 !!" <<
  //                     (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->ClippingAngle1_s16));

  // XLOG_INFO(g_AppContext, "[svs]: ClippingAngle2_s16 !!" <<
  //   (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->ClippingAngle2_s16));

  // XLOG_INFO(g_AppContext, "[svs]: CoeffC_BackSA2SWAC_s32 !!" <<
  //   (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->CoeffC_BackSA2SWAC_s32));

  // XLOG_INFO(g_AppContext, "[svs]: CoeffC_BackSA2SWAL_s32  !!" <<
  //   (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->CoeffC_BackSA2SWAL_s32));

  // XLOG_INFO(g_AppContext, "[svs]: CoeffC_BackSA2SWAR_s32  !!" <<
  //   (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->CoeffC_BackSA2SWAR_s32));

  // XLOG_INFO(g_AppContext, "[svs]: CoeffC_ForwSA2SWAC_s32 !!" <<
  //   (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->CoeffC_ForwSA2SWAC_s32) );

  // XLOG_INFO(g_AppContext, "[svs]: CoeffC_ForwSA2SWAL_s32  !!" <<
  //   (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->CoeffC_ForwSA2SWAL_s32));

  // XLOG_INFO(g_AppContext, "[svs]: CoeffC_ForwSA2SWAR_s32 !!" <<
  //   (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->CoeffC_ForwSA2SWAR_s32));

  // XLOG_INFO(g_AppContext, "[svs]: CoeffD_BackSA2SWAC_s32  !!" <<
  //   (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->CoeffD_BackSA2SWAC_s32));

  // XLOG_INFO(g_AppContext, "[svs]: CoeffD_BackSA2SWAL_s32 !!" <<
  //   (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->CoeffD_BackSA2SWAL_s32) );

  // XLOG_INFO(g_AppContext, "[svs]: CoeffD_BackSA2SWAR_s32  !!" <<
  //   (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->CoeffD_BackSA2SWAR_s32));

  // XLOG_INFO(g_AppContext, "[svs]: CoeffD_ForwSA2SWAC_s32  !!" <<
  //   (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->CoeffD_ForwSA2SWAC_s32));

  // XLOG_INFO(g_AppContext, "[svs]: CoeffD_ForwSA2SWAL_s32  !!" <<
  //   (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->CoeffD_ForwSA2SWAL_s32));

  // XLOG_INFO(g_AppContext, "[svs]: CoeffD_ForwSA2SWAR_s32  !!" <<
  //   (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->CoeffD_ForwSA2SWAR_s32));

  // for (int i = 0; i < 33; i++)
  // {
  //   XLOG_INFO(g_AppContext, "[svs]: DifFrontWheelRadiiTable_pu16[" << i << "] !!" <<
  //       (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->DifFrontWheelRadiiTable_pu16[i]));
  // }
  // XLOG_INFO(g_AppContext, "[svs]: DrivenAxle_en  !!" <<
  //   static_cast<vfc::int32_t>(((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->DrivenAxle_en));

  // XLOG_INFO(g_AppContext, "[svs]: MultMonomA_BackSA2SWAC_s16  !!" <<
  //   (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->MultMonomA_BackSA2SWAC_s16));

  // XLOG_INFO(g_AppContext, "[svs]: MultMonomA_BackSA2SWAL_s16  !!" <<
  //   (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->MultMonomA_BackSA2SWAL_s16) );

  // XLOG_INFO(g_AppContext, "[svs]: MultMonomA_BackSA2SWAR_s16 !!" <<
  //   (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->MultMonomA_BackSA2SWAR_s16));

  // XLOG_INFO(g_AppContext, "[svs]: MultMonomA_ForwSA2SWAC_s16 !!" <<
  //   (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->MultMonomA_ForwSA2SWAC_s16));

  // XLOG_INFO(g_AppContext, "[svs]: MultMonomA_ForwSA2SWAL_s16 !!" <<
  //   (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->MultMonomA_ForwSA2SWAL_s16));

  // XLOG_INFO(g_AppContext, "[svs]: MultMonomA_ForwSA2SWAR_s16  !!" <<
  //   (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->MultMonomA_ForwSA2SWAR_s16));

  // XLOG_INFO(g_AppContext, "[svs]: MultMonomB_BackSA2SWAC_s16  !!" <<
  //   (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->MultMonomB_BackSA2SWAC_s16));

  // XLOG_INFO(g_AppContext, "[svs]: MultMonomB_BackSA2SWAL_s16!!" <<
  //   (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->MultMonomB_BackSA2SWAL_s16));

  // XLOG_INFO(g_AppContext, "[svs]: MultMonomB_BackSA2SWAR_s16  !!" <<
  //   (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->MultMonomB_BackSA2SWAR_s16));

  // XLOG_INFO(g_AppContext, "[svs]: MultMonomB_ForwSA2SWAC_s16 !!" <<
  //   (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->MultMonomB_ForwSA2SWAC_s16));

  // XLOG_INFO(g_AppContext, "[svs]: MultMonomB_ForwSA2SWAL_s16  !!" <<
  //   (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->MultMonomB_ForwSA2SWAL_s16) );

  // XLOG_INFO(g_AppContext, "[svs]: MultMonomB_ForwSA2SWAR_s16 !!" <<
  //   (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->MultMonomB_ForwSA2SWAR_s16));

  // XLOG_INFO(g_AppContext, "[svs]: ScalingMonomA_SA2SWAC_u8  !!" <<
  //   static_cast<vfc::int32_t>(((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->ScalingMonomA_SA2SWAC_u8) );

  // XLOG_INFO(g_AppContext, "[svs]: ScalingMonomA_SA2SWAL_u8  !!" <<
  //   static_cast<vfc::int32_t>(((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->ScalingMonomA_SA2SWAL_u8));

  // XLOG_INFO(g_AppContext, "[svs]: ScalingMonomA_SA2SWAR_u8  !!" <<
  //   static_cast<vfc::int32_t>(((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->ScalingMonomA_SA2SWAR_u8));

  // XLOG_INFO(g_AppContext, "[svs]: ScalingMonomB_SA2SWAC_u8  !!" <<
  //   static_cast<vfc::int32_t>(((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->ScalingMonomB_SA2SWAC_u8));

  // XLOG_INFO(g_AppContext, "[svs]: ScalingMonomB_SA2SWAL_u8  !!" <<
  //   static_cast<vfc::int32_t>(((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->ScalingMonomB_SA2SWAL_u8));

  // XLOG_INFO(g_AppContext, "[svs]: ScalingMonomB_SA2SWAR_u8  !!" <<
  //   static_cast<vfc::int32_t>(((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->ScalingMonomB_SA2SWAR_u8));

  // XLOG_INFO(g_AppContext, "[svs]: ScalingMonomCD_SA2SWAC_u8  !!" <<
  //   static_cast<vfc::int32_t>(((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->ScalingMonomCD_SA2SWAC_u8));

  // XLOG_INFO(g_AppContext, "[svs]: ScalingMonomCD_SA2SWAL_u8  !!" <<
  //   static_cast<vfc::int32_t>(((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->ScalingMonomCD_SA2SWAL_u8));

  // XLOG_INFO(g_AppContext, "[svs]: ScalingMonomCD_SA2SWAR_u8  !!" <<
  //   static_cast<vfc::int32_t>(((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->ScalingMonomCD_SA2SWAR_u8));
  //   XLOG_INFO_OS(g_AppContext) <<"[!!!!!!!!!!!!!!!!!]: inputVhmSignals !!" << XLOG_ENDL;
    static int l_count_valin = 0;

    valin::pf_data.m_pfGear.m_gearStatus=static_cast<valin::EGearStatus>(g_dataContainerToSvs.m_StrippedPfValData.m_gearStatus);

    valin::pf_data.m_pfOdometry.m_wheelDrvDirRR = g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelDrvDirRR;
    valin::pf_data.m_pfOdometry.m_wheelDrvDirRL = g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelDrvDirRL;
    valin::pf_data.m_pfOdometry.m_wheelDrvDirFR = g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelDrvDirFR;
    valin::pf_data.m_pfOdometry.m_wheelDrvDirFL = g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelDrvDirFL;

    valin::pf_data.m_pfPmaFixedPoint.m_vehSpeed = static_cast<vfc::uint16_t >(g_dataContainerToSvs.m_StrippedPfValData.m_vehicleVelocity.value() *3.6f*10.f); //m_vehSpeed unit km/h

    valin::pf_data.m_pfPmaFixedPoint.m_wheelRotationFL = (uint16_t)(g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelRotationFL*2);
    valin::pf_data.m_pfPmaFixedPoint.m_wheelRotationFR = (uint16_t)(g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelRotationFR*2);
    valin::pf_data.m_pfPmaFixedPoint.m_wheelRotationRL = (uint16_t)(g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelRotationRL*2);
    valin::pf_data.m_pfPmaFixedPoint.m_wheelRotationRR = (uint16_t)(g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelRotationRR*2);

    valin::pf_data.m_pfOdometry.m_wheelImpCtrFR = g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelImpCtrFR;
    valin::pf_data.m_pfOdometry.m_wheelImpCtrFL = g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelImpCtrFL;
    valin::pf_data.m_pfOdometry.m_wheelImpCtrRL = g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelImpCtrRL;
    valin::pf_data.m_pfOdometry.m_wheelImpCtrRR = g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelImpCtrRR;

    valin::pf_data.m_pfPmaFixedPoint.m_outsideTemp = 26.0f;
    valin::pf_data.m_pfTrailer.m_trailerState=false;
    valin::pf_data.m_pfTrailer.m_trailerHitchPresent=false;

    // valin::pf_data.m_pfTirePress.m_tirePressFL=static_cast<vfc::TSIUnitType<vfc::float32_t>::si_pascal_t>(com_megaIPC_user::g_inputSignalList.m_TirePressFL.load(std::memory_order_relaxed)*1000);
    // valin::pf_data.m_pfTirePress.m_tirePressFR=static_cast<vfc::TSIUnitType<vfc::float32_t>::si_pascal_t>(com_megaIPC_user::g_inputSignalList.m_TirePressFR.load(std::memory_order_relaxed)*1000);
    // valin::pf_data.m_pfTirePress.m_tirePressRL=static_cast<vfc::TSIUnitType<vfc::float32_t>::si_pascal_t>(com_megaIPC_user::g_inputSignalList.m_TirePressRL.load(std::memory_order_relaxed)*1000);
    // valin::pf_data.m_pfTirePress.m_tirePressRR=static_cast<vfc::TSIUnitType<vfc::float32_t>::si_pascal_t>(com_megaIPC_user::g_inputSignalList.m_TirePressRR.load(std::memory_order_relaxed)*1000);

    // rbp_Time_Type l_impCtrTimeStamp=static_cast<rbp_Time_Type>(com_megaIPC_user::g_inputSignalList.m_WheelSpeedPulse_Time.load(std::memory_order_relaxed));

    rbp_Time_Type l_ECUtime_ms_pu32;
    bool l_isOk = rbp_getCurrentECUTime_1ms_b(&l_ECUtime_ms_pu32);
    rbp_Time_Type l_systemTime = l_ECUtime_ms_pu32;

  valin::pf_data.m_pfOdometry.m_wheelImpCtrFRTimestamp = tic::CGlobalTimestamp(g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelImpCtrFRTimestamp);
  valin::pf_data.m_pfOdometry.m_wheelImpCtrRRTimestamp = tic::CGlobalTimestamp(g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelImpCtrRRTimestamp);
  valin::pf_data.m_pfOdometry.m_wheelImpCtrFLTimestamp = tic::CGlobalTimestamp(g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelImpCtrFLTimestamp);
  valin::pf_data.m_pfOdometry.m_wheelImpCtrRLTimestamp = tic::CGlobalTimestamp(g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelImpCtrRLTimestamp);

    // XLOG_INFO_OS(g_AppContext) <<"[Inside svs]: l_systemTime !!!!!" <<
    //                         l_systemTime << XLOG_ENDL;

    float l_steeringWheelAngle= g_dataContainerToSvs.m_StrippedPfValData.m_driverSteeringWheelAngle.value();

    l_steeringWheelAngle=abs(l_steeringWheelAngle)>800?0:l_steeringWheelAngle;
    valin::pf_data.m_pfSteering.m_steeringWheelAngle       = static_cast<vfc::CSI::si_degree_f32_t>(l_steeringWheelAngle);

    // rbp_Time_Type l_angleTimeStamp=static_cast<rbp_Time_Type>(com_megaIPC_user::g_inputSignalList.m_Steering_Time.load(std::memory_order_relaxed));
    valin::pf_data.m_pfSteering.m_frontWheelAngleTimestamp = tic::CGlobalTimestamp(l_systemTime);   // bus signal cycle time is 10ms

    //Using for vhm auto calib
    valin::pf_data.m_pfOdometry.m_yawRate                  = static_cast<vfc::CSI::si_degree_per_second_f32_t>(0.0);

    valin::pf_data.m_pfOdometry.m_longitudinalAcceleration = static_cast<vfc::CSI::si_metre_per_square_second_f32_t>(0.0f);
    valin::pf_data.m_pfOdometry.m_lateralAcceleration      = static_cast<vfc::CSI::si_metre_per_square_second_f32_t>(0.0f);

    // valin::pf_data.m_pfSteering.m_rearAxleSteeringAngle    = vfc::CSI::si_degree_f32_t(0.0f);
    valin::pf_data.m_pfSteering.m_rearAxleSteeringAngle    = g_dataContainerToSvs.m_StrippedPfValData.m_rearWheelAngle;
    valin::pf_data.m_pfSteering.m_epsStatus                = valin::EEpsStatus::EPS_INACTIVE;
    valin::pf_data.m_pfSteering.m_epsStatusRas             = valin::EPS_RAS_NOT_PRESENT;
    // XLOG_INFO(g_AppContext, "[svs]: g_dataContainerToSvs.m_StrippedPfValData.m_rearWheelAngle  !!" <<
    //                  static_cast<float>(g_dataContainerToSvs.m_StrippedPfValData.m_rearWheelAngle.value()));
    // vfc::uint8_t l_mirror=com_megaIPC_user::g_inputSignalList.m_RightBodyLoda_1_ExtRMirFldSts_R_MirrorFoldUnfoldSt.load(std::memory_order_relaxed);

    // if(l_mirror==0){
    //     l_mirror=1;//MIRROR_FOLDED
    // }
    // else{
    //     l_mirror=0;//valin::EStateMirror::MIRROR_UNFOLDED;
    // }

    valin::pf_data.m_pfDoorAndMirror.m_stateExteriorMirrorLeft  = valin::EStateMirror::MIRROR_UNFOLDED;
    valin::pf_data.m_pfDoorAndMirror.m_stateExteriorMirrorRight = valin::EStateMirror::MIRROR_UNFOLDED;

    // //status check
    // vfc::uint8_t l_temp=com_megaIPC_user::g_inputSignalList.m_YRS_0x246_YRS_LgtAcce_St.load(std::memory_order_relaxed);
    // if(com_megaIPC_user::g_inputSignalList.m_YRS_0x246_YRS_LgtSnsrSts.load(std::memory_order_relaxed)!=1){
    //     l_temp=15;
    // }
    // valin::pf_data.m_pfOdometry.m_longitudinalAccelerationQualifier = static_cast<valin::ELongAccQualifier>(l_temp);

    //         l_temp=com_megaIPC_user::g_inputSignalList.m_YRS_0x242_YRS_LatAcce_St.load(std::memory_order_relaxed);
    // if(com_megaIPC_user::g_inputSignalList.m_YRS_0x242_YRS_LatSnsrSts.load(std::memory_order_relaxed)!=1){
    //     l_temp=15;
    // }
    // valin::pf_data.m_pfOdometry.m_lateralAccelerationQualifier      = static_cast<valin::ELateralAccQualifier>(l_temp);

    //         l_temp=com_megaIPC_user::g_inputSignalList.m_YRS_0x242_YRS_YawRateSt.load(std::memory_order_relaxed);
    // if(com_megaIPC_user::g_inputSignalList.m_YRS_0x242_YRS_YawRateSnsrSts.load(std::memory_order_relaxed)!=1||com_megaIPC_user::g_inputSignalList.m_YRS_0x242_YRS_YawRateCalSts.load(std::memory_order_relaxed)!=1){
    //     l_temp=15;
    // }
    // valin::pf_data.m_pfOdometry.m_qualifierYawVelocityVehicle       = static_cast<valin::EQualifierYawVelocityVehicle>(l_temp);

    // XLOG_INFO(g_AppContext, "[svs]: Wheelbase_u16 !!" <<
    //                   (((rbp_Type_par_VehPara_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VehPara_du16]))->Wheelbase_u16));

    // XLOG_INFO(g_AppContext, "[svs]: WICLength_u16 !!" <<
    //                     (((rbp_Type_par_VHM_Vehicle_Common_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_Common_du16]))->WICLength_u16));

    // XLOG_INFO(g_AppContext, "[svs]: MaxWICValue_u16 !!" <<
    //                     (((rbp_Type_par_VHM_Vehicle_Common_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_Common_du16]))->MaxWICValue_u16));

    // XLOG_INFO(g_AppContext, "[svs]: SWAMax_u16  !!" <<
    //                     (((rbp_Type_par_VHM_Vehicle_Common_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_Common_du16]))->SWAMax_u16));

    // XLOG_INFO(g_AppContext, "[svs]: SteeringRatio_u16  !!" <<
    //                     (((rbp_Type_par_VHM_Vehicle_Common_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_Common_du16]))->SteeringRatio_u16));

    // XLOG_INFO(g_AppContext, "[svs]: (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->AxleBaseRear_u16) !!" <<
    //                     (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->AxleBaseRear_u16));

    // XLOG_INFO(g_AppContext, "[svs]: (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->WICTeethCount_u16)  !!" <<
    //                     (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->WICTeethCount_u16));

    // XLOG_INFO(g_AppContext, "[svs]: WIC_CycleTime_MS_u16 !!" <<
    //                     (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->WIC_CycleTime_MS_u16));

    // XLOG_INFO(g_AppContext, "[svs]: MultMonomA_ForwSWA2SA_s16  !!" <<
    //                     (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->MultMonomA_ForwSWA2SA_s16));

    // XLOG_INFO(g_AppContext, "[svs]: MultMonomB_ForwSWA2SA_s16 !!" <<
    //                     (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->MultMonomB_ForwSWA2SA_s16));

    // XLOG_INFO(g_AppContext, "[svs]: CoeffC_ForwSWA2SA_s32  !!" <<
    //                     (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->CoeffC_ForwSWA2SA_s32));

    // XLOG_INFO(g_AppContext, "[svs]: CoeffD_ForwSWA2SA_s32  !!" <<
    //                     (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->CoeffD_ForwSWA2SA_s32));

    // XLOG_INFO(g_AppContext, "[svs]: MultMonomA_BackSWA2SA_s16  !!" <<
    //                     (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->MultMonomA_BackSWA2SA_s16));

    // XLOG_INFO(g_AppContext, "[svs]: MultMonomB_BackSWA2SA_s16  !!" <<
    //                     (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->MultMonomB_BackSWA2SA_s16));

    // XLOG_INFO(g_AppContext, "[svs]: CoeffC_BackSWA2SA_s32  !!" <<
    //                     (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->CoeffC_BackSWA2SA_s32));

    // XLOG_INFO(g_AppContext, "[svs]: CoeffD_BackSWA2SA_s32  !!" <<
    //                     (((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->CoeffD_BackSWA2SA_s32));

    // XLOG_INFO(g_AppContext, "[svs]: ScalingMonomA_SWA2SA_u8  !!" <<
    //                     static_cast<vfc::int32_t>(((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->ScalingMonomA_SWA2SA_u8));

    // XLOG_INFO(g_AppContext, "[svs]: ScalingMonomB_SWA2SA_u8  !!" <<
    //                     static_cast<vfc::int32_t>(((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->ScalingMonomB_SWA2SA_u8));

    // XLOG_INFO(g_AppContext, "[svs]: ScalingMonomCD_SWA2SA_u8  !!" <<
    //                     static_cast<vfc::int32_t>(((rbp_Type_par_VHM_Vehicle_st *)(rbp_par_RAMData_st.ClusterPointer_ppvd[rbp_Idx_VHM_Vehicle_du16]))->ScalingMonomCD_SWA2SA_u8));

    valin::pf_data.m_pfSteering.m_rearWheelAngleStatus              = valin::REAR_STEERING_ANGLE_VALID;
    valin::pf_data.m_pfSteering.m_frontWheelAngleStatus             = valin::EFrontWheelAngleStatus::FWA_LOW_PRECISION;

    valin::pf_data.m_pfOdometry.m_wheelRotationFRQualifier          = static_cast<valin::EWheelRotationQualifier>(g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelRotationFRQualifier);
    valin::pf_data.m_pfOdometry.m_wheelRotationFLQualifier          = static_cast<valin::EWheelRotationQualifier>(g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelRotationFLQualifier);
    valin::pf_data.m_pfOdometry.m_wheelRotationRRQualifier          = static_cast<valin::EWheelRotationQualifier>(g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelRotationRRQualifier);
    valin::pf_data.m_pfOdometry.m_wheelRotationRLQualifier          = static_cast<valin::EWheelRotationQualifier>(g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelRotationRLQualifier);

    valin::pf_data.m_pfBrakes.m_espOpMode                           = valin::EEspOpMode::ABS_ASR_ESP_CTRL_INACTIVE;

    //Using for VHM2
    // if(com_megaIPC_user::g_inputSignalList.m_Driving_IGNStatus.load(std::memory_order_relaxed)==8&&com_megaIPC_user::g_inputSignalList.m_Driving_ACCStatus.load(std::memory_order_relaxed)==8){
    //     valin::pf_data.m_pfVehicleInfo.m_ignSwState                 =valin::EIgnSwState::IGN_SW_ST_OFF;
    // }
    // else if(com_megaIPC_user::g_inputSignalList.m_Driving_IGNStatus.load(std::memory_order_relaxed)==1){
    //     valin::pf_data.m_pfVehicleInfo.m_ignSwState                 =valin::EIgnSwState::IGN_SW_ST_ON;
    // }
    // else if(com_megaIPC_user::g_inputSignalList.m_Driving_ACCStatus.load(std::memory_order_relaxed)==1){
    //     valin::pf_data.m_pfVehicleInfo.m_ignSwState                 =valin::EIgnSwState::IGN_SW_ST_ACC;
    // }
    // else{
    //     valin::pf_data.m_pfVehicleInfo.m_ignSwState                 =valin::EIgnSwState::IGN_SW_ST_OFF;
    // }

    // valin::pf_data.m_pfVehicleInfo.m_uBatt=com_megaIPC_user::g_inputSignalList.m_FrontRoomLoad_1_LowBatVFb.load(std::memory_order_relaxed)*10;

    // valin::cpj_data.m_VehBlocked   = false;
}

void usm_Mainfunction()
{
    inputVhmSignals();
    rbp_vsm_valinAllConversion_vd();
    rbp_vhm_common_run10ms_vd();
    getVhmOutput();
    getDegradation();
}


void getDegradation()
{
  static rbp_Type_flt_EventResult_en s_MaxUnknownDistUndef_en = rbp_FLT_EVENT_NOT_PRESENT_enm;
  static rbp_Type_flt_EventResult_en s_MaxUnknownDistDiscarded_en = rbp_FLT_EVENT_NOT_PRESENT_enm;
  static rbp_Type_flt_EventResult_en s_WicFailure_en = rbp_FLT_EVENT_NOT_PRESENT_enm;
  static rbp_Type_flt_EventResult_en s_OutputCheckFailed_en = rbp_FLT_EVENT_NOT_PRESENT_enm;
  static rbp_Type_flt_EventResult_en s_EspIntervention_en = rbp_FLT_EVENT_NOT_PRESENT_enm;

  rbp_Type_flt_EventResult_en l_MaxUnknownDistUndef_en = rbp_flt_GetEventResult_en(rbp_EventVhmMaxUnknownDistUndef_enm);
  rbp_Type_flt_EventResult_en l_MaxUnknownDistDiscarded_en = rbp_flt_GetEventResult_en(rbp_EventVhmMaxUnknownDistDiscarded_enm);
  rbp_Type_flt_EventResult_en l_WicFailure_en = rbp_flt_GetEventResult_en(rbp_EventVhmWicFailure_enm);
  rbp_Type_flt_EventResult_en l_OutputCheckFailed_en = rbp_flt_GetEventResult_en(rbp_EventVhmOutputCheckFailed_enm);
  rbp_Type_flt_EventResult_en l_EspIntervention_en = rbp_flt_GetEventResult_en(rbp_EventVhmEspIntervention_enm);

  if (s_MaxUnknownDistUndef_en != l_MaxUnknownDistUndef_en)
  {
    XLOG_INFO(g_AppContext, "vhm event rbp_EventVhmMaxUnknownDistUndef_enm:"
              << static_cast<vfc::int32_t>(l_MaxUnknownDistUndef_en));
  }
  if (s_MaxUnknownDistDiscarded_en != l_MaxUnknownDistDiscarded_en)
  {
    XLOG_INFO(g_AppContext, "vhm event rbp_EventVhmMaxUnknownDistDiscarded_enm:"
              << static_cast<vfc::int32_t>(l_MaxUnknownDistDiscarded_en));
  }
  if (s_WicFailure_en != l_WicFailure_en)
  {
    XLOG_INFO(g_AppContext, "vhm event rbp_EventVhmWicFailure_enm:"
              << static_cast<vfc::int32_t>(l_WicFailure_en));
  }
  if (s_OutputCheckFailed_en != l_OutputCheckFailed_en)
  {
    XLOG_INFO(g_AppContext, "vhm event rbp_EventVhmOutputCheckFailed_enm:"
              << static_cast<vfc::int32_t>(l_OutputCheckFailed_en));
  }
  if (s_EspIntervention_en != l_EspIntervention_en)
  {
    XLOG_INFO(g_AppContext, "vhm event rbp_EventVhmEspIntervention_enm:"
              << static_cast<vfc::int32_t>(l_EspIntervention_en));
  }

  s_MaxUnknownDistUndef_en = l_MaxUnknownDistUndef_en;
  s_MaxUnknownDistDiscarded_en = l_MaxUnknownDistDiscarded_en;
  s_WicFailure_en = l_WicFailure_en;
  s_OutputCheckFailed_en = l_OutputCheckFailed_en;
  s_EspIntervention_en = l_EspIntervention_en;

  static rbp_Type_vhm_outputCheck_st s_vhm_outputCheck_pst = {
      .MovingDirectionValid_bf1 = 1,
      .WheelAngleRangeValid_bf1 = 1,
      .CurvatureRangeValid_bf1 = 1,
      .VelocityCalculationValid_bf1 = 1,
      .VelocityRangeValid_bf1 = 1,
      .TractionKept_bf1 = 1,
      .SteeringWheelAngleOffsetValid_bf1 = 1,
      .TrackWidthValid_bf1 = 1,
      .SlopeRangeValid_bf1 = 1,
      .TractionMonitoringActive_bf1 = 1,
      .TrackWidthMonitoringActive_bf1 = 1,
      .SlopeRangeMonitoringActive_bf1 = 1,
      .WaitingForInput_bf1 = 1,
      .DataValid_bf1 = 1
  };
  const rbp_Type_vhm_outputCheck_st* l_vhm_outputCheck_pst = rbp_vhm_getOutputCheck_cpcst();

  if (s_vhm_outputCheck_pst.MovingDirectionValid_bf1 != l_vhm_outputCheck_pst->MovingDirectionValid_bf1)
  {
    s_vhm_outputCheck_pst.MovingDirectionValid_bf1 = l_vhm_outputCheck_pst->MovingDirectionValid_bf1;
    XLOG_INFO(g_AppContext, "vhm data valid MovingDirectionValid_bf1:"
              << static_cast<vfc::int32_t>(s_vhm_outputCheck_pst.MovingDirectionValid_bf1));
  }

  if (s_vhm_outputCheck_pst.TractionKept_bf1 != l_vhm_outputCheck_pst->TractionKept_bf1)
  {
    s_vhm_outputCheck_pst.TractionKept_bf1 = l_vhm_outputCheck_pst->TractionKept_bf1;
    XLOG_INFO(g_AppContext, "vhm data valid TractionKept_bf1:"
              << static_cast<vfc::int32_t>(s_vhm_outputCheck_pst.TractionKept_bf1));
  }

  if (s_vhm_outputCheck_pst.SteeringWheelAngleOffsetValid_bf1 != l_vhm_outputCheck_pst->SteeringWheelAngleOffsetValid_bf1)
  {
    s_vhm_outputCheck_pst.SteeringWheelAngleOffsetValid_bf1 = l_vhm_outputCheck_pst->SteeringWheelAngleOffsetValid_bf1;
    XLOG_INFO(g_AppContext, "vhm data valid SteeringWheelAngleOffsetValid_bf1:"
              << static_cast<vfc::int32_t>(s_vhm_outputCheck_pst.SteeringWheelAngleOffsetValid_bf1));
  }

  if (s_vhm_outputCheck_pst.TrackWidthValid_bf1 != l_vhm_outputCheck_pst->TrackWidthValid_bf1)
  {
    s_vhm_outputCheck_pst.TrackWidthValid_bf1 = l_vhm_outputCheck_pst->TrackWidthValid_bf1;
    XLOG_INFO(g_AppContext, "vhm data valid TrackWidthValid_bf1:"
              << static_cast<vfc::int32_t>(s_vhm_outputCheck_pst.TrackWidthValid_bf1));
  }

  if (s_vhm_outputCheck_pst.SlopeRangeValid_bf1 != l_vhm_outputCheck_pst->SlopeRangeValid_bf1)
  {
    s_vhm_outputCheck_pst.SlopeRangeValid_bf1 = l_vhm_outputCheck_pst->SlopeRangeValid_bf1;
    XLOG_INFO(g_AppContext, "vhm data valid SlopeRangeValid_bf1:"
              << static_cast<vfc::int32_t>(s_vhm_outputCheck_pst.SlopeRangeValid_bf1));
  }

  if (s_vhm_outputCheck_pst.TractionMonitoringActive_bf1 != l_vhm_outputCheck_pst->TractionMonitoringActive_bf1)
  {
    s_vhm_outputCheck_pst.TractionMonitoringActive_bf1 = l_vhm_outputCheck_pst->TractionMonitoringActive_bf1;
    XLOG_INFO(g_AppContext, "vhm data valid TractionMonitoringActive_bf1:"
              << static_cast<vfc::int32_t>(s_vhm_outputCheck_pst.TractionMonitoringActive_bf1));
  }

  if (s_vhm_outputCheck_pst.TrackWidthMonitoringActive_bf1 != l_vhm_outputCheck_pst->TrackWidthMonitoringActive_bf1)
  {
    s_vhm_outputCheck_pst.TrackWidthMonitoringActive_bf1 = l_vhm_outputCheck_pst->TrackWidthMonitoringActive_bf1;
    XLOG_INFO(g_AppContext, "vhm data valid TrackWidthMonitoringActive_bf1:"
              << static_cast<vfc::int32_t>(s_vhm_outputCheck_pst.TrackWidthMonitoringActive_bf1));
  }

  if (s_vhm_outputCheck_pst.SlopeRangeMonitoringActive_bf1 != l_vhm_outputCheck_pst->SlopeRangeMonitoringActive_bf1)
  {
    s_vhm_outputCheck_pst.SlopeRangeMonitoringActive_bf1 = l_vhm_outputCheck_pst->SlopeRangeMonitoringActive_bf1;
    XLOG_INFO(g_AppContext, "vhm data valid SlopeRangeMonitoringActive_bf1:"
              << static_cast<vfc::int32_t>(s_vhm_outputCheck_pst.SlopeRangeMonitoringActive_bf1));
  }

  if (s_vhm_outputCheck_pst.WaitingForInput_bf1 != l_vhm_outputCheck_pst->WaitingForInput_bf1)
  {
    s_vhm_outputCheck_pst.WaitingForInput_bf1 = l_vhm_outputCheck_pst->WaitingForInput_bf1;
    XLOG_INFO(g_AppContext, "vhm data valid WaitingForInput_bf1:"
              << static_cast<vfc::int32_t>(s_vhm_outputCheck_pst.WaitingForInput_bf1));
  }

  if (s_vhm_outputCheck_pst.DataValid_bf1 != l_vhm_outputCheck_pst->DataValid_bf1)
  {
    s_vhm_outputCheck_pst.DataValid_bf1 = l_vhm_outputCheck_pst->DataValid_bf1;
    XLOG_INFO(g_AppContext, "vhm data valid DataValid_bf1:"
              << static_cast<vfc::int32_t>(s_vhm_outputCheck_pst.DataValid_bf1));
  }

  if (s_vhm_outputCheck_pst.CurvatureRangeValid_bf1 != l_vhm_outputCheck_pst->CurvatureRangeValid_bf1)
  {
    s_vhm_outputCheck_pst.CurvatureRangeValid_bf1 = l_vhm_outputCheck_pst->CurvatureRangeValid_bf1;
    XLOG_INFO(g_AppContext, "vhm data valid CurvatureRangeValid_bf1:"
              << static_cast<vfc::int32_t>(s_vhm_outputCheck_pst.CurvatureRangeValid_bf1));
  }
  if (s_vhm_outputCheck_pst.WheelAngleRangeValid_bf1 != l_vhm_outputCheck_pst->WheelAngleRangeValid_bf1)
  {
    s_vhm_outputCheck_pst.WheelAngleRangeValid_bf1 = l_vhm_outputCheck_pst->WheelAngleRangeValid_bf1;
    XLOG_INFO(g_AppContext, "vhm data valid WheelAngleRangeValid_bf1:"
              << static_cast<vfc::int32_t>(s_vhm_outputCheck_pst.WheelAngleRangeValid_bf1));
  }
  if (s_vhm_outputCheck_pst.VelocityRangeValid_bf1 != l_vhm_outputCheck_pst->VelocityRangeValid_bf1)
  {
    s_vhm_outputCheck_pst.VelocityRangeValid_bf1 = l_vhm_outputCheck_pst->VelocityRangeValid_bf1;
    XLOG_INFO(g_AppContext, "vhm data valid VelocityRangeValid_bf1:"
              << static_cast<vfc::int32_t>(s_vhm_outputCheck_pst.VelocityRangeValid_bf1));
  }
  if (s_vhm_outputCheck_pst.VelocityCalculationValid_bf1 != l_vhm_outputCheck_pst->VelocityCalculationValid_bf1)
  {
    s_vhm_outputCheck_pst.VelocityCalculationValid_bf1 = l_vhm_outputCheck_pst->VelocityCalculationValid_bf1;
    XLOG_INFO(g_AppContext, "vhm data valid VelocityCalculationValid_bf1:"
              << static_cast<vfc::int32_t>(s_vhm_outputCheck_pst.VelocityCalculationValid_bf1));
  }

}

void compensateOdo(pc::daddy::OdometryDataDaddy& f_container)
{
  static vfc::float32_t l_speed_last_frame = 0.f;
  static vfc::float32_t l_yaw_last_frame   = 0.f;
  static vfc::float32_t l_deltaX_sum_x     = 0.f;
  static vfc::float32_t l_deltaX_sum_y     = 0.f;

  vfc::float32_t l_currentSpeed = f_container.m_Data.m_velocity.value();
  vfc::float32_t l_currentYaw   = f_container.m_Data.m_yawAngle.value();

  vfc::float32_t l_deltaSpeed   = l_currentSpeed - l_speed_last_frame;
  vfc::float32_t l_deltaYaw     = l_currentYaw   - l_yaw_last_frame;

  // Linear compensate
  vfc::float32_t l_compensate_k = 0.f;
  vfc::float32_t l_compensate_b = 0.f;

  //Not enable tuning Odo in a constant speed driving
  if ( isGreater(std::abs(l_deltaSpeed), cc::assets::floorplate::g_customFloorPlateData->m_odoTuningThreshold_Velocity) &&
       isLess(l_deltaYaw, cc::assets::floorplate::g_customFloorPlateData->m_odoTuningThreshold_Yaw))
  {
    if (pc::daddy::FORWARD == f_container.m_Data.m_vehMoveDir)
    {
      if (isGreaterEqual(l_deltaSpeed, 0.f)) //For accelerate
      {
        l_compensate_k = cc::assets::floorplate::g_customFloorPlateData->m_odoCompensate_Forward_Accelerate_k;
        l_compensate_b = cc::assets::floorplate::g_customFloorPlateData->m_odoCompensate_Forward_Accelerate_b;
      }
      else
      {
        l_compensate_k = cc::assets::floorplate::g_customFloorPlateData->m_odoCompensate_Forward_Decelerate_k;
        l_compensate_b = cc::assets::floorplate::g_customFloorPlateData->m_odoCompensate_Forward_Decelerate_b;
      }
    }
    else if (pc::daddy::BACKWARD == f_container.m_Data.m_vehMoveDir )
    {
      if (isGreaterEqual(l_deltaSpeed, 0.f)) //For accelerate
      {
        l_compensate_k = cc::assets::floorplate::g_customFloorPlateData->m_odoCompensate_Backward_Accelerate_k;
        l_compensate_b = cc::assets::floorplate::g_customFloorPlateData->m_odoCompensate_Backward_Accelerate_b;
      }
      else
      {
        l_compensate_k = cc::assets::floorplate::g_customFloorPlateData->m_odoCompensate_Backward_Decelerate_k;
        l_compensate_b = cc::assets::floorplate::g_customFloorPlateData->m_odoCompensate_Backward_Decelerate_b;
      }
    }
    else
    {
      // do nothing
    }

    vfc::float32_t l_deltaX = 0.f;

    if (isLess(l_deltaSpeed,cc::assets::floorplate::g_customFloorPlateData->m_odoTuningThreshold_Acceleration))
    {
      l_deltaX = l_deltaSpeed * l_compensate_k + l_compensate_b ;
    }

    l_deltaX_sum_x = l_deltaX_sum_x + l_deltaX * cos(l_currentYaw); // PRQA S 3012  #code looks fine
    l_deltaX_sum_y = l_deltaX_sum_y + l_deltaX * sin(l_currentYaw); // PRQA S 3012  #code looks fine
  }

  f_container.m_Data.m_xPos.value() = f_container.m_Data.m_xPos.value() + l_deltaX_sum_x; //uint meter
  f_container.m_Data.m_yPos.value() = f_container.m_Data.m_yPos.value() + l_deltaX_sum_y;

  l_speed_last_frame = l_currentSpeed;
  l_yaw_last_frame   = l_currentYaw;

}

void getVhmOutput()
{
    rbp_Type_vhm_common_outputData_st    l_vhm_output_new;
    rbp_vhm_common_getVHMOutputInterfaceData_b(&l_vhm_output_new);

    vfc::uint32_t  l_timeStamp = l_vhm_output_new.VHMData_st.TimeStamp_ms_ux;

    g_dataContainerToSvs.m_VHMData.m_curOdometry_X          = \
                static_cast<vfc::CSI::si_metre_f32_t>(
                l_vhm_output_new.VHMData_st.ActualVehicleState_st.Position_mm_st.X_s32*0.001f); //mm -> m

    g_dataContainerToSvs.m_VHMData.m_curOdometry_Y          = \
                static_cast<vfc::CSI::si_metre_f32_t>(
                l_vhm_output_new.VHMData_st.ActualVehicleState_st.Position_mm_st.Y_s32*0.001f); //mm -> m

    g_dataContainerToSvs.m_VHMData.m_curOdometry_YawAngle   = \
                static_cast<vfc::CSI::si_radian_f32_t>(
                l_vhm_output_new.VHMData_st.ActualVehicleState_st.YawAngle_rad_f32);

    g_dataContainerToSvs.m_VHMData.m_velocity               = \
                static_cast<vfc::CSI::si_metre_per_second_f32_t>(
                std::sqrt(l_vhm_output_new.VHMData_st.MotionState_st.Velocity_mps_st.X_f32 * \
                        l_vhm_output_new.VHMData_st.MotionState_st.Velocity_mps_st.X_f32 + \
                        l_vhm_output_new.VHMData_st.MotionState_st.Velocity_mps_st.Y_f32 * \
                        l_vhm_output_new.VHMData_st.MotionState_st.Velocity_mps_st.Y_f32 ));  //m/s

    g_dataContainerToSvs.m_VHMData.m_posXRaw                = \
                l_vhm_output_new.VHMData_st.ActualVehicleState_st.Position_mm_st.X_s32*0.001f; // Not use in svs

    g_dataContainerToSvs.m_VHMData.m_posYRaw                = \
                l_vhm_output_new.VHMData_st.ActualVehicleState_st.Position_mm_st.Y_s32*0.001f; // Not use in svs

    g_dataContainerToSvs.m_VHMData.m_yawAngleRaw            = \
                static_cast<vfc::CSI::si_radian_f32_t>(
                l_vhm_output_new.VHMData_st.ActualVehicleState_st.YawAngle_rad_f32); // Not use in svs

    vfc::int32_t l_vhm_X = l_vhm_output_new.VHMData_st.ActualVehicleState_st.Position_mm_st.X_s32;

    // XLOG_INFO_OS(g_AppContext ) << "VHM output: l_vhm_X  l_vhm_output_new.VHMData_st.ActualVehicleState_st.Position_mm_st.X_s32 value is: !!!!!!!!!!!!"<< l_vhm_X << XLOG_ENDL;
    // XLOG_INFO_OS(g_AppContext ) << "VHM output: l_vhm_output_new.VHMData_st.ActualVehicleState_st.Position_mm_st.X_s32 value is: "<< l_vhm_output_new.VHMData_st.ActualVehicleState_st.Position_mm_st.Y_s32 << XLOG_ENDL;
    // XLOG_INFO_OS(g_AppContext ) << "VHM output: l_vhm_YawAngle l_vhm_output_new.VHMData_st.ActualVehicleState_st.YawAngle_rad_f32   value is: "<< static_cast <float_t>(l_vhm_output_new.VHMData_st.ActualVehicleState_st.YawAngle_rad_f32)   << XLOG_ENDL;

    static rbp_Type_vhm_VehMoveDir_en l_vehMoveDir_en = rbp_vhm_MoveDirUnknown_enm;
    rbp_vhm_getVehMoveDir_b(&l_vehMoveDir_en);

    vfc::float32_t l_ConvertSwa2Sa_f32 = 0.0f;
    vfc::float32_t l_steeringWheelAngle = g_dataContainerToSvs.m_StrippedPfValData.m_driverSteeringWheelAngle.value();
        // XLOG_INFO_OS(g_AppContext ) << "VHM output: l_vehMoveDir_en is: "<< static_cast <int>(l_vehMoveDir_en)   << XLOG_ENDL;

    cc::target::common::EGearStatus l_gearStatus = g_dataContainerToSvs.m_StrippedPfValData.m_gearStatus;
    if ((rbp_vhm_MoveDirForward_enm == l_vehMoveDir_en) || (rbp_vhm_MoveDirBackward_enm == l_vehMoveDir_en))
    {
        l_ConvertSwa2Sa_f32 = rbp_vhm_ConvertSWA2SA_s16((SInt16)(l_steeringWheelAngle * 25.0f),(l_vehMoveDir_en == rbp_vhm_MoveDirForward_enm))/4096.f;
    }
    else if((cc::target::common::EGearStatus::GEAR_DRIVE == l_gearStatus) || (cc::target::common::EGearStatus::GEAR_NEUTRAL == l_gearStatus))
    {
        l_ConvertSwa2Sa_f32 = rbp_vhm_ConvertSWA2SA_s16((SInt16)(l_steeringWheelAngle * 25.0f),true)/4096.f;
    }
    else
    {
        l_ConvertSwa2Sa_f32 = rbp_vhm_ConvertSWA2SA_s16((SInt16)(l_steeringWheelAngle * 25.0f),false)/4096.f;
    }

    g_dataContainerToSvs.m_StrippedPfValData.m_frontWheelAngle = static_cast<vfc::CSI::si_degree_f32_t>(l_ConvertSwa2Sa_f32 * 57.29578f);
    // XLOG_INFO_OS(g_AppContext ) << "The m_frontWheelAngle value is: "<< g_dataContainerToSvs.m_StrippedPfValData.m_frontWheelAngle.value()  << XLOG_ENDL;
    // g_dataContainerToSvs.m_StrippedPfValData.m_frontWheelAngle = static_cast<vfc::CSI::si_degree_f32_t>(0.06f * l_steeringWheelAngle);  // temp solution
    // g_dataContainerToSvs.m_StrippedPfValData.m_frontWheelAngle = static_cast<vfc::CSI::si_degree_f32_t>(l_vhm_output_new.VHMData_st.Steering_st.FrontWheelAngle_rad_f32);  // tested, not work. furthur study.

    // daddy deliver
    if (pc::daddy::BaseDaddyPorts::sm_OdometryDataDaddySenderPort.isConnected())
    {
        pc::daddy::OdometryDataDaddy& l_container = pc::daddy::BaseDaddyPorts::sm_OdometryDataDaddySenderPort.reserve();
        l_container.m_Data.m_xPos     = g_dataContainerToSvs.m_VHMData.m_curOdometry_X;
        l_container.m_Data.m_yPos     = g_dataContainerToSvs.m_VHMData.m_curOdometry_Y;
        l_container.m_Data.m_yawAngle = g_dataContainerToSvs.m_VHMData.m_curOdometry_YawAngle;
        int32_t l_x = static_cast<int32_t>(l_container.m_Data.m_xPos.value()*100);
        // slogf(240322, _SLOG_INFO, "The vhm_x in Daddy is (cm): %d \n", l_x);
        l_container.m_Data.m_velocity = static_cast<vfc::CSI::si_metre_per_second_f32_t>(std::abs(g_dataContainerToSvs.m_VHMData.m_velocity.value()));

        switch(l_vehMoveDir_en)
        {
            case rbp_vhm_MoveDirForward_enm:
                l_container.m_Data.m_vehMoveDir = pc::daddy::FORWARD;
            break;
            case rbp_vhm_MoveDirBackward_enm:
                l_container.m_Data.m_vehMoveDir = pc::daddy::BACKWARD;
            break;
            default:
                l_container.m_Data.m_vehMoveDir = pc::daddy::STANDSTILL;
            break;
        }

#ifdef ODO_COMPENSATE
        if (g_dataContainerToSvs.m_vehicleInfo.vehicleType == "sghc")
        {
            compensateOdo(l_container);
        }
#endif

        pc::daddy::BaseDaddyPorts::sm_OdometryDataDaddySenderPort.deliver();
    }

    // for VHM raw data
    if (cc::daddy::CustomDaddyPorts::sm_customVhmAbstRawDataDaddy_SenderPort.isConnected())
    {
        cc::daddy::CustomVhmAbstRaw_t& l_container = cc::daddy::CustomDaddyPorts::sm_customVhmAbstRawDataDaddy_SenderPort.reserve();
        l_container.m_Data.m_posXRaw  = g_dataContainerToSvs.m_VHMData.m_posXRaw;
        l_container.m_Data.m_posYRaw  = g_dataContainerToSvs.m_VHMData.m_posYRaw;
        l_container.m_Data.m_yawAngleRaw = g_dataContainerToSvs.m_VHMData.m_yawAngleRaw;
        cc::daddy::CustomDaddyPorts::sm_customVhmAbstRawDataDaddy_SenderPort.deliver();
    }

    //! Vehicle Steering Angle ***************************************************************************************************
    if ( pc::daddy::BaseDaddyPorts::sm_SteeringAngleFrontDaddySenderPort.isConnected() )
    {
        pc::daddy::SteeringAngleDaddy& l_container = pc::daddy::BaseDaddyPorts::sm_SteeringAngleFrontDaddySenderPort.reserve();
        l_container.m_Data = g_dataContainerToSvs.m_StrippedPfValData.m_frontWheelAngle ;
        // XLOG_INFO_OS(g_AppContext) <<"[Inside svs]: m_frontWheelAngle is " <<f_data_in.m_frontWheelAngle.value()<< XLOG_ENDL;
        pc::daddy::BaseDaddyPorts::sm_SteeringAngleFrontDaddySenderPort.deliver();
    }
}

}
