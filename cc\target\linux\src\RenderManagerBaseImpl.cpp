

// #ifndef OSG_GLES3_AVAILABLE
//     #define OSG_GLES3_AVAILABLE
// #endif

#include "cc/target/common/inc/commonInterface.h"
#include "cc/target/linux/inc/RenderManagerBaseImpl.h"
#include <iostream>


#include <cassert>
#include <iomanip> //std::setprecision
#include <unistd.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <malloc.h>
#include <fcntl.h>
#include <sys/inotify.h>

#ifndef EGL_EGLEXT_PROTOTYPES
#define EGL_EGLEXT_PROTOTYPES
#endif // EGL_EGLEXT_PROTOTYPES
#ifndef GL_GLEXT_PROTOTYPES
#define GL_GLEXT_PROTOTYPES
#endif // GL_GLEXT_PROTOTYPES
#include <EGL/egl.h>
#include <EGL/eglext.h>
//#include <EGL/eglextQCOM.h>
#include <GLES2/gl2.h>
#include <GLES2/gl2ext.h>

#include "cc/daddy/inc/CustomDaddyPorts.h"
#include "cc/util/logging/inc/LoggingContexts.h"
#include "cc/target/linux/inc/CustomInterfaceSync.h"
#include "cc/target/linux/inc/com_linux_user.hpp"
#include "cc/vhm/inc/vhm.hpp"


using pc::util::logging::g_AppContext;
using pc::util::logging::g_StartupContext;
using pc::util::logging::g_EngineContext;
using pc::util::logging::g_COMSocketContext;


USE_OSGPLUGIN(freetype)
USE_OSGPLUGIN(glsl)
USE_OSGPLUGIN(jpeg)
USE_OSGPLUGIN(ktx)
USE_OSGPLUGIN(osg)
USE_OSGPLUGIN(osg2)
USE_OSGPLUGIN(png)

USE_DOTOSGWRAPPER_LIBRARY(osg)
USE_DOTOSGWRAPPER_LIBRARY(osgText)
USE_DOTOSGWRAPPER_LIBRARY(osgViewer)

USE_SERIALIZER_WRAPPER_LIBRARY(osg)
USE_SERIALIZER_WRAPPER_LIBRARY(osgAnimation)
USE_SERIALIZER_WRAPPER_LIBRARY(osgText)

USE_COMPRESSOR_WRAPPER(ZLibCompressor)

#define EVENT_SIZE  (sizeof(struct inotify_event))
// #define EVENT_BUF_LEN     (1024 * (EVENT_SIZE + 16))
constexpr std::size_t EVENT_BUF_LEN = 1024u * (EVENT_SIZE + 16u);

std::string g_prefixPath = "";

int g_keepRunning = 1;
static pc::target::qualcomm::QcEngine *g_engine = nullptr;


extern "C" {

ServiceBase* createComLinuxUserInstance()
{
  return new svs::com_linux_user;
}
}


extern "C"
{
    RenderManagerBase * createRenderManager()
    {
        static RenderManagerBaseImpl mRenderManagerBaseImpl;
        return &mRenderManagerBaseImpl;
    }

    int releaseRenderManager(RenderManagerBase * /*f_RenderManager*/)
    {
        return 0;
    }
}

std::string CONCAT_PATH(const std::string& path) {
  return g_dataContainerToSvs.m_vehicleInfo.commonPath +"/" + path;
}

std::string CONCAT_PATH_VEHICLE_MODEL(const std::string& path) {
  return g_dataContainerToSvs.m_vehicleInfo.configPath +"/" + path;
}


static void runMain()
{
  //! set log level
  //cc::util::logging::updateLogLevelAll(pc::util::logging::LOG_INFO);

  const int64_t l_startTime = chrono_ms();
  XLOG_INFO(g_StartupContext, "SV3D Start Time [ms]: " << l_startTime);

  //! INIT ************************************************************************************************************
  osg::setNotifyHandler(new pc::util::logging::SV3DNotifyHandler); //! wrap osg logging into our logging interface
  osgDB::Registry::instance()->setReadFileCallback(new pc::util::logging::ReadFileLoggerCallback);

  //! Daddy senders
  cc::daddy::CustomDaddyPorts::connectMemPools();


#if 1
  //! IPC
  XLOG_INFO(g_StartupContext, "IPC Start Time [ms]: " << chrono_ms()); // PRQA S 4060

  //! start it at early stage to be able to fetch the coding and parameters
  //! that are necessary for the application
  cc::target::linux::CustomInterfaceSync l_customInterface;
  //low layer driver for IPC
  //starts in wait mode until the coding and parameters have been loaded
  pc::core::Thread l_customInterfaceThread("IPC sync", &l_customInterface);
  if( !l_customInterfaceThread.start() )
  {
    XLOG_FATAL( g_EngineContext , "IPC failed to start!"); // PRQA S 4060
    // return(EXIT_FAILURE);
  }
#endif

    //! Coding parameters
  const std::string l_cpcExtrinsicPath = g_dataContainerToSvs.m_vehicleInfo.externalParamPath+"/"+\
                                     cc::target::sysconf::g_EXTRINSIC_CALIBRATION_LATEST_XML_FILENAME;
  bool l_calibLatest = false;
  {
    pc::util::coding::CodingManager* const l_codingManager = pc::util::coding::getCodingManager();
    //cc::core::readParam(l_codingManager);

    const std::string l_CODING_XML                        = CONCAT_PATH(cc::target::sysconf::g_CODING_XML);
    const std::string l_CODING_XML_VEH                    = CONCAT_PATH_VEHICLE_MODEL(cc::target::sysconf::g_CODING_XML_VEH);
    const std::string l_RESOURCE_FOLDER                   = CONCAT_PATH(cc::target::sysconf::g_RESOURCE_FOLDER);
    const std::string l_EXTRINSIC_CALIBRATION_XML         = CONCAT_PATH_VEHICLE_MODEL(cc::target::sysconf::g_EXTRINSIC_CALIBRATION_XML);
    const std::string l_DEFAULT_EXTRINSIC_CALIBRATION_XML = CONCAT_PATH(pc::core::sysconf::g_DEFAULT_EXTRINSIC_CALIBRATION_XML);

    XLOG_WARN(g_EngineContext, "[SVS] l_DEFAULT_EXTRINSIC_CALIBRATION_XML: "<<l_DEFAULT_EXTRINSIC_CALIBRATION_XML);

    if (false == osgDB::fileExists(l_EXTRINSIC_CALIBRATION_XML))
    {
      //read from fallback file location
      pc::util::coding::XmlFileReader(l_DEFAULT_EXTRINSIC_CALIBRATION_XML).run(l_codingManager);
      XLOG_WARN(g_EngineContext, "Extrinsics not found in the second partition, use local file");
    }
    else
    {
      pc::util::coding::XmlFileReader(l_EXTRINSIC_CALIBRATION_XML).run(l_codingManager);
      XLOG_WARN(g_EngineContext, "Extrinsic: "<< l_EXTRINSIC_CALIBRATION_XML);
      XLOG_WARN(g_EngineContext, "Using vehicle model extrinsic");
    }

    //Cpc result
    if (true == osgDB::fileExists(l_cpcExtrinsicPath))
    {
      XLOG_INFO(g_StartupContext, "Using CPC extrinsic... : "<< l_cpcExtrinsicPath);
      pc::util::coding::XmlFileReader(l_cpcExtrinsicPath).run(l_codingManager);
      l_calibLatest = true;
    }
    else
    {
      XLOG_WARN(g_EngineContext, "no Extrinsic in cpc path: "<< l_cpcExtrinsicPath);
      XLOG_WARN(g_EngineContext, "will not using cpc extrinsic...");
    }

    //! Intrinsic load
    const std::string l_intrinsicLatestPath = g_dataContainerToSvs.m_vehicleInfo.externalParamPath+"/"+\
                                        cc::target::sysconf::g_INTRINSIC_CALIBRATION_LATEST_YAML_FOLDER;

    XLOG_INFO(g_StartupContext, "The latest intrinsic path... : "<< l_intrinsicLatestPath);

    if (true == osgDB::fileExists(l_intrinsicLatestPath+"/nrcs_front.yaml")&& \
        true == osgDB::fileExists(l_intrinsicLatestPath+"/nrcs_right.yaml")&& \
        true == osgDB::fileExists(l_intrinsicLatestPath+"/nrcs_rear.yaml") && \
        true == osgDB::fileExists(l_intrinsicLatestPath+"/nrcs_left.yaml") && \
        true == pc::c2w::IntrinsicYamlReader(l_intrinsicLatestPath).checkIntrinsicVaild(pc::core::sysconf::EXT_FRONT_CAMERA) && \
        true == pc::c2w::IntrinsicYamlReader(l_intrinsicLatestPath).checkIntrinsicVaild(pc::core::sysconf::EXT_RIGHT_CAMERA) && \
        true == pc::c2w::IntrinsicYamlReader(l_intrinsicLatestPath).checkIntrinsicVaild(pc::core::sysconf::EXT_REAR_CAMERA)  && \
        true == pc::c2w::IntrinsicYamlReader(l_intrinsicLatestPath).checkIntrinsicVaild(pc::core::sysconf::EXT_LEFT_CAMERA))
    {
      XLOG_INFO(g_StartupContext, "Using latest intrinsic path... : "<< l_intrinsicLatestPath);
      pc::c2w::IntrinsicYamlReader(l_intrinsicLatestPath).run();
    }
    else{
      XLOG_INFO(g_StartupContext, "Using default intrinsic path... : "<< CONCAT_PATH_VEHICLE_MODEL(cc::target::sysconf::g_DEFAULT_INTRINSIC_CALIBRATION_YAML));
      pc::c2w::IntrinsicYamlReader(CONCAT_PATH_VEHICLE_MODEL(cc::target::sysconf::g_DEFAULT_INTRINSIC_CALIBRATION_YAML)).run();
    }

    XLOG_INFO(g_StartupContext, "Read XML Start Time [ms]: " << chrono_ms());
    pc::util::coding::XmlFileReader(l_CODING_XML).run(l_codingManager);
    XLOG_INFO(g_StartupContext, "Read XML End Time [ms]: " << chrono_ms());

    XLOG_INFO(g_StartupContext, "Read XML From VehicleModel Start Time [ms]: " << chrono_ms());
    pc::util::coding::XmlFileReader(l_CODING_XML_VEH).run(l_codingManager);

  }


    //! Scene, Framework, Engine
  cc::core::CustomScene* const l_pScene = new cc::core::CustomScene;// PRQA S 4756

  cc::core::CustomFramework* const l_pFramework = new cc::core::CustomFramework(l_pScene);// PRQA S 4756
  if(nullptr == l_pFramework)
  {
    XLOG_ERROR(g_StartupContext, "fail to create cc::core::CustomFramework(l_pScene) ");
  }

  //! init daddy ports after construction of framework which will do the connection step of the daddy receivers
  cc::daddy::CustomDaddyPorts::initPorts();

  //! deliver calibration status daddy
  if ( true == pc::daddy::BaseDaddyPorts::sm_calibrationStsSenderPort.isConnected() )
  {
    auto& l_rData = pc::daddy::BaseDaddyPorts::sm_calibrationStsSenderPort.reserve();
    l_rData.m_Data = l_calibLatest;
    pc::daddy::BaseDaddyPorts::sm_calibrationStsSenderPort.deliver();
  }

  //! View Mode State machine start
  ViewModeStateMachine l_viewModeStateMachine(l_pFramework);
  pc::core::Thread l_viewModeThread("View Mode Thread", &l_viewModeStateMachine);
  if ( !l_viewModeThread.start() )
  {
    XLOG_FATAL(cc::util::logging::g_viewModeSMContext, "ViewMode Thread failed to start!");
    //assert(!"ViewMode Thread failed to start!" );
    exit(EXIT_FAILURE);
  }
  else
  {
    XLOG_INFO(cc::util::logging::g_viewModeSMContext, "ViewMode Thread succeed to start!");
  }

  // //! Worker
  cc::worker::core::CustomTaskManager l_taskManager;
  pc::core::Thread l_workerThread("Worker Thread", &l_taskManager);
  if (!l_workerThread.start())
  {
    XLOG_FATAL(g_EngineContext, "worker thread failed to start!");
    assert(("Worker thread failed to start!", false));
    exit(EXIT_FAILURE);
  }
  else
  {
    XLOG_INFO(g_EngineContext, "worker thread succeed to start!");
  }

  // cpc
  cc::cpc::CpcRunnable l_cpcRunnable(l_pFramework);
  // l_taskManager.setCpcRunnable(&l_cpcRunnable);
  pc::core::Thread l_CPCThread("CPC Thread", &l_cpcRunnable);
  if (!l_CPCThread.start())
  {
    XLOG_FATAL(g_EngineContext, "CPC failed to start!"); // PRQA S 4060

    assert(!(static_cast<bool>("CPC Thread failed to start!")));
    exit(EXIT_FAILURE);
  }

  //vhm
  cc::vhm::VhmManager l_vhmManager;
  pc::core::Thread l_vhmThread("VHM Thread", &l_vhmManager);
  if (!l_vhmThread.start())
  {
    XLOG_FATAL(g_StartupContext, "Error==============Vhm thread failed to start!");
    //assert(!"Vhm thread failed to start!");
    //exit(EXIT_FAILURE);
  }


  g_engine = new pc::target::qualcomm::QcEngine(l_pFramework, 300u);
  if(nullptr != g_engine)
  {
    XLOG_INFO(g_EngineContext, "runMain() -------------- succeed to create engine");
  }
  else
  {
    XLOG_INFO(g_EngineContext, "runMain() -------------- fail to create engine");
  }

  const std::string l_cpcExtrinsicFolder = g_dataContainerToSvs.m_vehicleInfo.externalParamPath;
  int length = 0;
  int fd = 0;
  int wd = 0;
  unsigned char buffer[EVENT_BUF_LEN] = {0};
  fd = inotify_init();
  if (fd < 0)
  {
    XLOG_FATAL(g_StartupContext, "CPC Extrinsic File inotify_init error");
    exit(EXIT_FAILURE);
  }
  // watch if cpc extrinsic file created
  wd = inotify_add_watch(fd, l_cpcExtrinsicFolder.c_str(), IN_CREATE | IN_DELETE);
  if (wd == -1)
  {
    close(fd);
    XLOG_FATAL(g_StartupContext, "CPC Extrinsic File inotify_add_watch error");
    exit(EXIT_FAILURE);
  }

  static int loopIndex = 0;
  while(0 != g_keepRunning)
  {
    loopIndex++;
    XLOG_INFO(g_EngineContext, "runMain---thread------------loop:" << loopIndex);

    length = read(fd, static_cast<void*>(buffer), EVENT_BUF_LEN);
    if (length >= 0)
    {
      unsigned char *ptr = buffer;
      while (ptr < buffer + length)
      {
        struct inotify_event *event = (struct inotify_event *)ptr;
        if (event->wd == wd)
        {
            if (true == pc::daddy::BaseDaddyPorts::sm_calibrationStsSenderPort.isConnected())
            {
              auto& l_rData = pc::daddy::BaseDaddyPorts::sm_calibrationStsSenderPort.reserve();
              if ((event->mask & IN_CREATE) != 0)
              {
                if (osgDB::fileExists(l_cpcExtrinsicPath))
                {
                  l_rData.m_Data = true;
                  XLOG_INFO(g_EngineContext, "CPC Extrinsic File creation event detected, " << loopIndex);
                }
              }
              else if ((event->mask & IN_DELETE) != 0)
              {
                if (!osgDB::fileExists(l_cpcExtrinsicPath))
                {
                  l_rData.m_Data = false;
                  XLOG_INFO(g_EngineContext, "CPC Extrinsic File deleted event detected, " << loopIndex);
                }
              }
              else
              {
                // do nothing
              }
              pc::daddy::BaseDaddyPorts::sm_calibrationStsSenderPort.deliver();
            }
        }
        ptr += sizeof(struct inotify_event) + event->len;
      }
    }

    usleep(10*1000*1000);
  }
}


RenderManagerBaseImpl::RenderManagerBaseImpl()
    : initialized(false)
    , m_textureIds{}
{

}

RenderManagerBaseImpl::~RenderManagerBaseImpl()
{
    deInit();
}

int RenderManagerBaseImpl::init(std::shared_ptr<RenderConfig> renderConfig)
{
    XLOG_INFO_OS(g_EngineContext) << "-----------------RenderManagerBaseImpl::init" << XLOG_ENDL;

    g_prefixPath = renderConfig->mResourcePath;

    // debug ----- just for debug
    // g_dataContainerToSvs.m_vehicleInfo.commonPath = "/data/avmsv3d/";
    // g_dataContainerToSvs.m_vehicleInfo.configPath = "/data/avmsv3d/";


    // XLOG_INFO_OS(g_EngineContext) << "-----------------set g_prefixPath = " << g_prefixPath <<XLOG_ENDL;

    initialized = true;

    mEngThread = new std::thread(runMain);

    static int waitIndex = 0;
    while(g_engine == nullptr)
    {
        waitIndex++;
        XLOG_INFO_OS(g_EngineContext) << "-----------------wait-engine-to-be-created 100msX" << waitIndex << XLOG_ENDL;
        usleep(100*1000);
    }

    if(nullptr != g_engine)
    {
        XLOG_INFO_OS(g_EngineContext) << "-----------------engine do init" << XLOG_ENDL;
        g_engine->OnInit();
    }
    else
    {
        XLOG_ERROR_OS(g_EngineContext) << "-----------------fail to create engine, can not do engine init" << XLOG_ENDL;
    }

    XLOG_INFO_OS(g_EngineContext) << "-----------------RenderManagerBaseImpl::init leave" << XLOG_ENDL;

    return 0;
}

int RenderManagerBaseImpl::deInit()
{
    if (!initialized)
    {
        return -1;
    }

    return 0;
}

int RenderManagerBaseImpl::setFrameBuffer(unsigned int fbo)
{
  if(nullptr != g_engine)
  {
    g_engine->setDefaulFBOId(fbo);
  }
  return 0;
}

int RenderManagerBaseImpl::drawCameraTexture(int cameraIdentifies[], int size)
{
    static uint64_t l_noFrameNum = 0;
    if(size > 0)
    {
      g_engine->OnIterate();
    }
    else
    {
      l_noFrameNum++;
      if(l_noFrameNum < 100 || l_noFrameNum%100 == 0)
      {
        XLOG_INFO(g_EngineContext, "RenderManagerBaseImpl::drawCameraTexture------no camera frame, total num:" << l_noFrameNum);
      }
    }

    return 0;
}

int RenderManagerBaseImpl::drawWindow(int /*x*/, int /*y*/, int /*width*/, int /*height*/)
{
    return 0;
}

int RenderManagerBaseImpl::getCameraTextureId(int /*identify*/)
{
    //none
    return 0;
}

int RenderManagerBaseImpl::setCameraTextureId(int textureId[], int size)
{

    g_engine->setOESTextureIds(size, textureId);
    return 0;
}

#if 0
void RenderManagerBaseImpl::setTouchEvent(MotionEvent event)
{


  int touch_type = 0;

  if (event.action == MotionEvent::ACTION::ACTION_DOWN)
  {
    touch_type= 1;
  }
  else if (event.action == MotionEvent::ACTION::ACTION_MOVE)
  {
    touch_type = 3;
  }
  else if (event.action == MotionEvent::ACTION::ACTION_UP)
  {
    touch_type = 2;
  }
  else
  {
    touch_type = 0;
  }

  XLOG_INFO(g_EngineContext, "setTouchEvent ---------type="<<(int)event.action<<"internal-type="<< touch_type <<" x="<<(unsigned int)event.x <<" y="<<(unsigned int)event.y);

  if ( true == cc::daddy::CustomDaddyPorts::sm_HmiData_SenderPort.isConnected() )
  {
    auto& l_rData = cc::daddy::CustomDaddyPorts::sm_HmiData_SenderPort.reserve();
    l_rData.m_Data.m_huX = (unsigned int)event.x;
    l_rData.m_Data.m_huY = (unsigned int)event.y;
    cc::daddy::CustomDaddyPorts::sm_HmiData_SenderPort.deliver();
  }
  if ( true == cc::daddy::CustomDaddyPorts::sm_HUtouchEvenTypeDaddy_SenderPort.isConnected() )
  {
    auto& l_rData = cc::daddy::CustomDaddyPorts::sm_HUtouchEvenTypeDaddy_SenderPort.reserve();
    l_rData.m_Data = touch_type;
    cc::daddy::CustomDaddyPorts::sm_HUtouchEvenTypeDaddy_SenderPort.deliver();
  }
}
#endif
