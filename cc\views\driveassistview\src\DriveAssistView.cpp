//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: ESJ1LR Esparza Jose (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  DriveAssistView.cpp
/// @brief
//=============================================================================

#include "pc/svs/core/inc/Framework.h"
#include "pc/svs/virtcam/inc/VirtualCam.h"

#include "cc/views/driveassistview/inc/DriveAssistView.h"
#include "cc/views/driveassistview/inc/DriveAssistViewUpdateCallback.h"



namespace cc
{
namespace views
{
namespace driveassistview
{

static pc::util::coding::Item<DriveAssistViewSettings> g_driveAssistViewSettings("DriveAssistView");

//!
//! DriveAssistView
//!
DriveAssistView::DriveAssistView(
      pc::core::Framework* f_framework,
      const std::string& f_name,
      const pc::core::Viewport& f_viewport,
      pc::core::sysconf::Cameras f_cam )
  : pc::core::View(f_name, f_viewport) // PRQA S 2323
  , m_pFramework(f_framework) // PRQA S 2323
  , m_cam(f_cam) // PRQA S 2323
{
  if (nullptr != f_framework)
  {
    // set the camera position to match the left SatCam
    pc::virtcam::LookAt l_lookat(osg::Vec3f(0.0f,0.0f,0.0f), osg::Vec3f(0.0f,0.0f,0.0f), osg::Vec3f(0.0f,0.0f,1.0f));

    SingleDriveAssistView l_settings = g_driveAssistViewSettings->m_LeftCam;
    if ( pc::core::sysconf::RIGHT_CAMERA == m_cam)
    {
        l_settings = g_driveAssistViewSettings->m_RightCam;
    }


    if (m_pFramework->m_cameraCalibrationReceiver.isConnected())
    {
        const pc::daddy::CameraCalibrationDaddy* const l_pCalibDaddy = m_pFramework->m_cameraCalibrationReceiver.getData();
        if (nullptr != l_pCalibDaddy)
        {
        const pc::c2w::SatCam& l_satCam = l_pCalibDaddy->m_Data[static_cast<vfc::uint32_t>(m_cam)];

        l_lookat.m_eye     = l_satCam.getExtrinsicCalibration().getTranslation();
        l_lookat.m_center  = l_lookat.m_eye + l_settings.m_deltaCenter; // meaning of center is different for fixed virtual cams!
        l_lookat.m_up      = l_settings.m_up;
        }
    }
    else
    {
        // use the values as per coding
        l_lookat.m_eye     = l_settings.m_eye;
        l_lookat.m_center  = l_lookat.m_eye + l_settings.m_deltaCenter; // meaning of center is different for fixed virtual cams!
        l_lookat.m_up      = l_settings.m_up;
    }

    setProjectionMatrixAsPerspective(l_settings.m_fovy,
        static_cast<vfc::float64_t> (f_viewport.m_size.x()) / static_cast<vfc::float64_t> (f_viewport.m_size.y()),
        0.05,
        20.0);

    setViewMatrix(osg::Matrixd::lookAt(l_lookat.m_eye, l_lookat.m_center, l_lookat.m_up));

    addUpdateCallback(new DriveAssistViewUpdateCallback(m_pFramework, m_cam, l_settings.m_deltaCenter, l_settings.m_up));
  }

}

DriveAssistView::~DriveAssistView() = default;


} // namespace driveassistview
} // namespace assets
} // namespace cc
