//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: LIA1LR David Liepelt (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  HeadUnitHemisphereCameraUpdater.cpp
/// @brief
//=============================================================================

#include "cc/virtcam/inc/HeadUnitHemisphereCameraUpdater.h"

#include "pc/generic/util/cli/inc/CommandCallback.h"
#include "pc/generic/util/cli/inc/CommandRegistry.h"
#include "cc/core/inc/CustomFramework.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h"
#include "pc/svs/util/math/inc/CommonMath.h"
#include "cc/util/logging/inc/LoggingContexts.h"
#include "cc/sm/viewmode/inc/ViewModeStateMachine.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/assets/corner/inc/ViewportCorner.h"

#include "cc/imgui/inc/imgui_manager.h"
#include "cc/target/common/inc/linux_svs_interface.hpp"

using pc::util::logging::g_AppContext;

#define GET_PORT_DATA(dataDaddy, port, allPortHaveDataFlag) \
    auto (dataDaddy) = port.getData(); \
    (allPortHaveDataFlag) = (allPortHaveDataFlag && ((dataDaddy) != nullptr)); \
    if ((dataDaddy) == nullptr) { std::cout << #port << " doesn't have data!\n"; }

using cc::util::logging::g_viewModeSMContext;

namespace cc
{
namespace virtcam
{

pc::util::coding::Item<HeadUnitHemisphereCameraData> g_huhemisPara("huhemisdata");

//!
//! HeadUnitHemisphereCameraUpdater::ReturnChannel
//!
void HeadUnitHemisphereCameraUpdater::ReturnChannel::operator()(osg::Node *f_node, osg::NodeVisitor *f_nv)
{
    if (f_node == nullptr || f_nv == nullptr)
    {
        return;
    }
    osg::ref_ptr<const HeadUnitHemisphereCameraUpdater> l_updater{};
    osg::Camera* const                            l_camera = f_node->asCamera();

    l_updater = nullptr;

    // The update callback is attached to the scene root and the tracked camera.
    // Depending on which node we are visiting, we can either send default or tracked values
    // This is a somwhat clumsy way to keep sending default values even if the tracked camera is disabled in the
    // scenegraph
    const bool maySendDefaultValues = (f_node == m_sceneNode.get());
    const bool maySendTrackedValues = (l_camera != nullptr) && (f_node != m_sceneNode.get());

    // Want to send default values, but visiting the wrong node? Want to send tracked values, but visiting the wrong
    // node?
    if ((m_returnDefaultValues && !maySendDefaultValues) || (!m_returnDefaultValues && !maySendTrackedValues))
    {
        // We're outta here...
        traverse(f_node, f_nv);
        return;
    }

    vfc::int32_t l_axis1 = 0, l_axis2 = 0, l_zoom = 0; // PRQA S 4107
    l_axis1 = m_defaultAxis1;
    l_axis2 = m_defaultAxis2;
    l_zoom  = m_defaultZoom;

    if (!m_returnDefaultValues && m_hemisphereUpdater.lock(l_updater))
    {
        osg::Vec3f     l_center;
        vfc::float32_t l_elevation = 0.0f, l_azimuth = 0.0f; // PRQA S 4107
        vfc::float32_t l_distance = 0.0f;

        // at this point it also should always use the SVS center
        l_updater->projectCameraToParametricDomain(l_camera, l_center, l_elevation, l_azimuth, l_distance, true); // PRQA S 2759
        l_updater->normalizeAndClampParameters(l_elevation, l_azimuth, l_distance);

        l_updater->quantizeParameters(l_elevation, l_azimuth, l_distance, l_axis1, l_axis2, l_zoom);
    }

    // Deliver data to viewmode state machine
    cc::daddy::HemisphereParameterData_Daddy& l_parameterDaddy = cc::daddy::CustomDaddyPorts::sm_HemisphereParameterData_SenderPort.reserve();
    l_parameterDaddy.m_Data.m_camPosAxis1Rq = static_cast<vfc::uint32_t>(l_axis1);
    l_parameterDaddy.m_Data.m_camPosAxis2Rq = static_cast<vfc::uint32_t>(l_axis2);
    l_parameterDaddy.m_Data.m_zoomFactorRq = static_cast<vfc::uint32_t>(l_zoom);
    cc::daddy::CustomDaddyPorts::sm_HemisphereParameterData_SenderPort.deliver();

    traverse(f_node, f_nv);
}


//!
//! HeadUnitHemisphereCameraUpdater
//!
HeadUnitHemisphereCameraUpdater::HeadUnitHemisphereCameraUpdater(pc::core::Framework* f_framwork)
  : cc::virtcam::HemisphereCameraUpdater() // PRQA S 4050
  , m_returnChannelUpdateCallback(nullptr) // PRQA S 2323
  , m_framework(f_framwork)
  , m_suspended(true)
  , m_updateOnce(false)
  , m_camPosAxis1Rq(0u)
  , m_camPosAxis2Rq(0u)
  , m_camPosAxis1RqPre(0u)
  , m_camPosAxis2RqPre(0u)
  , m_zoomFactorRq(static_cast<uint32_t>(cc::target::common::E3DZoomLevel::LEVEL3))
  , m_zoomFactorIpc(static_cast<uint32_t>(cc::target::common::E3DZoomLevel::LEVEL3))
  , m_functionM(0.0f)
  , m_functionA(0.0f)
  , m_zoomFactor(0u)
  , m_axis1Range(450u)
  , m_axis2Range(1800u)
  , m_touchWidth(0.0f)
  , m_touchHeight(0.0f)
  , m_movingStartX(0u)
  , m_movingStartY(0u)
  , m_moving(false)
  , m_dragStartCamPosX(50)
  , m_dragStartCamPosY(50)
  , m_curviewid(EScreenID_PERSPECTIVE_PRE)
  , m_touchEvent(0u)
  , m_freeviewSt(false)
  , m_isFreeModeSt(false)
  , m_isFreeModeStPre(false)
  , m_horizontalPad(true)
  , m_horiViewport(cc::core::g_views->m_mainViewport)
  , m_vertViewport(cc::core::g_views->m_vertMainViewport)
  , m_SurroundViewRotateAngle_ReceiverPort()
  , m_HUfreemodeAngleAzimuthReceiver()
  , m_HUfreemodeAngleElevationReceiver()
{
  const vfc::float32_t l_asix1Offset = (g_huhemisPara->m_minElevationHu / g_huhemisPara->m_maxElevationHu) * (static_cast<vfc::float32_t>(m_axis1Range) - 1.0f);
  m_asix1Offset = static_cast<vfc::uint32_t> (l_asix1Offset);  //PRQA S 3016
  m_returnChannelUpdateCallback = new ReturnChannel(this);
  calculateFunctionParams();

  cc::daddy::CustomDaddyPorts::sm_SurroundViewRotateAngle_SenderPort.connect(m_SurroundViewRotateAngle_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_HUfreemodeAngleAzimuthDaddy_SenderPort. connect(m_HUfreemodeAngleAzimuthReceiver);
  cc::daddy::CustomDaddyPorts::sm_HUfreemodeAngleElevationDaddy_SenderPort. connect(m_HUfreemodeAngleElevationReceiver);
}

HeadUnitHemisphereCameraUpdater::~HeadUnitHemisphereCameraUpdater() noexcept
{
    cc::daddy::CustomDaddyPorts::sm_SurroundViewRotateAngle_SenderPort.disconnect(m_SurroundViewRotateAngle_ReceiverPort);
    cc::daddy::CustomDaddyPorts::sm_HUfreemodeAngleAzimuthDaddy_SenderPort. disconnect(m_HUfreemodeAngleAzimuthReceiver);
    cc::daddy::CustomDaddyPorts::sm_HUfreemodeAngleElevationDaddy_SenderPort. disconnect(m_HUfreemodeAngleElevationReceiver);
}


void HeadUnitHemisphereCameraUpdater::calculateFunctionParams()
{
  // assumed the funtion maps from HU the values [0,...,450] to the new PARK values [minElevation(PI/18),...,maxElevation(PI/2)]
  // function approch is x_PARK = x_HU * a + m
  // first m can be calculated by minEle = 0 * a + m
  vfc::float32_t l_minEle = 0.0f, l_maxEle = 0.0f;
  getElevationRange(l_minEle, l_maxEle);
  m_functionM = l_minEle;
  // second calculate with that the factor a = (maxEle - m) / 450
  m_functionA = (l_maxEle - m_functionM)/450.0f;
}


void HeadUnitHemisphereCameraUpdater::quantizeAxisParameters(vfc::float32_t f_elevation, vfc::float32_t f_azimuth, vfc::int32_t& f_axis1, vfc::int32_t& f_axis2) const
{
  // Clamp elevation between l_minEle and l_maxEle
  vfc::float32_t l_minEle = 0.0f, l_maxEle = 0.0f;
  getElevationRange(l_minEle, l_maxEle);
  f_elevation = osg::clampTo<vfc::float32_t>(f_elevation, l_minEle, l_maxEle);
  // Bring azimuth into range [0..2PI]
  f_azimuth = std::fmod(f_azimuth, static_cast<vfc::float32_t>(osg::PI*2.0));
  while (f_azimuth <= 0.0f)
  {
    f_azimuth+= static_cast<vfc::float32_t>(osg::PI*2.0);
  }
  f_axis1 = (static_cast<vfc::int32_t>(floor((f_elevation - m_functionM)/m_functionA + 0.5)));
  f_axis2 = static_cast<vfc::int32_t>(floor((0.5 * static_cast<vfc::float64_t>(f_azimuth) / osg::PI) * static_cast<vfc::float64_t>(AXIS2_RANGE) + 0.5)) % AXIS2_RANGE;
}


void HeadUnitHemisphereCameraUpdater::dequantizeAxisParameters(vfc::int32_t f_axis1, vfc::int32_t f_axis2, vfc::float32_t& f_elevation, vfc::float32_t& f_azimuth) const
{
    f_axis1 = f_axis1 % AXIS1_RANGE;
    f_axis2 = f_axis2 % AXIS2_RANGE;
    if (f_axis1 < 0)
    {
        f_axis1 += AXIS1_RANGE;
    }
    if (f_axis2 < 0)
    {
        f_axis2 += AXIS1_RANGE;
    }

    vfc::float32_t l_minEle = 0.0f, l_maxEle = 0.0f;
    getElevationRange(l_minEle, l_maxEle);
    f_elevation = m_functionA * static_cast<vfc::float32_t>(f_axis1) + m_functionM;
    f_elevation = osg::clampTo<vfc::float32_t>(f_elevation, l_minEle, l_maxEle);
    f_azimuth   = 2.0f * static_cast<vfc::float32_t>(osg::PI) * static_cast<vfc::float32_t>(f_axis2) / static_cast<vfc::float32_t>(AXIS2_RANGE);
}


void HeadUnitHemisphereCameraUpdater::quantizeZoomParameter(vfc::float32_t f_contzoom, vfc::int32_t& f_zoom) const
{
    constexpr vfc::int32_t         l_zoom = ZOOM_RANGE - 2;
    constexpr vfc::float32_t d      = 1.0f / static_cast<vfc::float32_t>(l_zoom);
    if (f_contzoom <= 0.25f * d)
    {
        f_zoom = 0;
    }
    else if (f_contzoom >= 1.f - 0.25f * d)
    {
        f_zoom = ZOOM_RANGE - 1;
    }
    else
    {
        const vfc::float32_t l_zoom_f = // PRQA S 2880
            1.0f + (std::max(0.0f, (f_contzoom * (static_cast<vfc::float32_t>(ZOOM_RANGE) - 2.0f))));
        f_zoom = static_cast<vfc::int32_t>(l_zoom_f); // PRQA S 3016
    }
}


void HeadUnitHemisphereCameraUpdater::enable()
{
  m_updateOnce = true;
  setEnabled(true);
}

void HeadUnitHemisphereCameraUpdater::retrieveZoomReferenceDistance(const osg::Camera* f_camera)
{
    using namespace cc::target::common;
    osg::Vec3f     l_center{};
    vfc::float32_t l_elevation = 0.0f;
    vfc::float32_t l_azimuth = 0.0f;
    vfc::float32_t l_distance = 0.0f;
    projectCameraToParametricDomain(f_camera, l_center, l_elevation, l_azimuth, l_distance);
    vfc::float32_t l_minDistance = 0.0f;
	vfc::float32_t l_maxDistance = 0.0f;
    getZoomRange(l_minDistance, l_maxDistance);
    m_referenceDistance = g_huhemisPara->m_defaultDistanceFactor * (l_minDistance + l_maxDistance);
    std::array<vfc::float32_t, static_cast<int>(E3DZoomLevel::LEVEL6) + 1> l_zoomDistances;
    l_zoomDistances.at(static_cast<int>(E3DZoomLevel::LEVEL0)) = m_referenceDistance / g_huhemisPara->m_zoomL0;
    l_zoomDistances.at(static_cast<int>(E3DZoomLevel::LEVEL1)) = m_referenceDistance / g_huhemisPara->m_zoomL1;
    l_zoomDistances.at(static_cast<int>(E3DZoomLevel::LEVEL2)) = m_referenceDistance / g_huhemisPara->m_zoomL2;
    l_zoomDistances.at(static_cast<int>(E3DZoomLevel::LEVEL3)) = m_referenceDistance / g_huhemisPara->m_zoomL3;
    l_zoomDistances.at(static_cast<int>(E3DZoomLevel::LEVEL4)) = m_referenceDistance / g_huhemisPara->m_zoomL4;
    l_zoomDistances.at(static_cast<int>(E3DZoomLevel::LEVEL5)) = m_referenceDistance / g_huhemisPara->m_zoomL5;
    l_zoomDistances.at(static_cast<int>(E3DZoomLevel::LEVEL6)) = m_referenceDistance / g_huhemisPara->m_zoomL6;
    vfc::float32_t l_closestDistance = std::numeric_limits<vfc::float32_t>::max();
    vfc::int32_t l_closestDistanceIndex = static_cast<int>(E3DZoomLevel::LEVEL3);
    for (int i = 0; i < l_zoomDistances.size(); i++)
    {
      const vfc::float32_t l_deltaDistance = std::abs(l_distance - l_zoomDistances[i]);
      if (l_deltaDistance < l_closestDistance)
      {
        l_closestDistanceIndex = i;
        l_closestDistance = l_deltaDistance;
      }
    }
    if (m_zoomFactorIpc != l_closestDistanceIndex)
    {
        m_zoomFactorIpc = l_closestDistanceIndex;
    }
}


void HeadUnitHemisphereCameraUpdater::updateCamera(osg::Camera* f_camera, const osg::FrameStamp* f_frameStamp)
{

  if ( f_camera == nullptr || f_frameStamp == nullptr)
  {
  	return;
  }
  const bool previousSuspend = m_suspended;
  m_SurroundViewRotateAngle_ReceiverPort.update();
  m_HUfreemodeAngleAzimuthReceiver.update();
  m_HUfreemodeAngleElevationReceiver.update();
  const auto viewport = f_camera->getViewport();
  if (nullptr != viewport)
  {
    m_horiViewport.m_origin = {static_cast<int>(viewport->x()), static_cast<int>(viewport->y())};
    m_horiViewport.m_size   = {static_cast<int>(viewport->width()), static_cast<int>(viewport->height())};
  }

#ifdef TARGET_STANDALONE
    addDebugLogSIL(f_camera);
#endif

  const vfc::float32_t l_currentDistancePrevious = getCurrentDistance();

  if (m_SurroundViewRotateAngle_ReceiverPort.hasNewData())
  {
      XLOG_INFO(g_AppContext, "HuDebug - updateCamera(): m_SurroundViewRotateAngle_ReceiverPort.hasNewData()");
      m_setSurroundAngle = (m_SurroundViewRotateAngle_ReceiverPort.getData()->m_Data);
      m_rotateLeft = m_setSurroundAngle >= 0;
      m_setSurroundAngle = static_cast<std::int32_t>(static_cast<float>(std::abs(m_setSurroundAngle) / 360.0) * AXIS2_RANGE); // qac fixed
      m_rotateProgress = 0.0f;
      m_angleRotate = 0;
      m_rotateMode = true;
      m_cancelRotateModeRq = false;
      setToCamera(f_camera);
  }

  if (m_rotateMode)
  {
      if (m_cancelRotateModeRq)
      {
          m_cancelRotateModeRq = false;
          m_rotateProgress = 1.0f;
          m_rotateMode = false;
      }
      else
      {
          if (m_suspended)
          {
              // m_suspended = false;
              cancelDeriveParameter();
          }
          onRotateMode(f_camera, f_frameStamp);
      }
  }
  else if (updateDaddyInputs() || (!m_suspended && isAnimating()))
  {
      // ...dispatch to base class
      cc::virtcam::HemisphereCameraUpdater::updateCamera(f_camera, f_frameStamp);
      m_suspended = false;
  }
  else
  {
  }

  if (m_suspended)
  {
    setToCamera(f_camera);
  }
  retrieveZoomReferenceDistance(f_camera);

  //! Send Zoom Level
  if (cc::daddy::CustomDaddyPorts::sm_ZoomLevelIPC_SenderPort.isConnected())
  {
      auto& l_container = cc::daddy::CustomDaddyPorts::sm_ZoomLevelIPC_SenderPort.reserve();
      l_container.m_Data = static_cast<cc::target::common::E3DZoomLevel>(m_zoomFactorIpc);
      cc::daddy::CustomDaddyPorts::sm_ZoomLevelIPC_SenderPort.deliver();
  }
  m_SurroundViewRotateAngle_ReceiverPort.cleanup();
  m_HUfreemodeAngleAzimuthReceiver.cleanup();
  m_HUfreemodeAngleElevationReceiver.cleanup();

  if (getCurrentDistance() != l_currentDistancePrevious)
  {
      XLOG_INFO(g_AppContext, "HuDebug - updateCamera(): getCurrentDistance() != l_currentDistancePrevious: distance changed from " << l_currentDistancePrevious << " to " << getCurrentDistance());
  }
}


void HeadUnitHemisphereCameraUpdater::onRotateMode(osg::Camera* f_camera, const osg::FrameStamp* f_frameStamp)
{
    m_rotateProgress += g_huhemisPara->m_rotateSpeed;
    int tmp = static_cast<int>(pc::util::smootherstep(0.0f, 1.0f, m_rotateProgress) * static_cast<float>(m_setSurroundAngle));
    const float totalRotateAngle = std::min(tmp, m_setSurroundAngle);
    const auto deltaAngle = totalRotateAngle - m_angleRotate;
    m_camPosAxis2Rq += (m_rotateLeft) ? -deltaAngle : +deltaAngle;
    vfc::float32_t l_elevation = 0.0f;
    vfc::float32_t l_azimuth   = 0.0f;

    m_camPosAxis2Rq = m_camPosAxis2Rq % AXIS2_RANGE;
    if (m_camPosAxis2Rq < 0)
    {
        m_camPosAxis2Rq += AXIS2_RANGE;
    }
    dequantizeAxisParameters( static_cast<vfc::int32_t>(m_camPosAxis1Rq), static_cast<vfc::int32_t>(m_camPosAxis2Rq),
        l_elevation, l_azimuth);
    rotateTo(l_elevation, l_azimuth);
    if (isAnimating())
    {
        cc::virtcam::HemisphereCameraUpdater::updateCamera(f_camera, f_frameStamp);
    }
    m_angleRotate = totalRotateAngle;
    if (m_angleRotate >= m_setSurroundAngle)
    {
        m_rotateMode = false;
    }
    XLOG_INFO(g_AppContext, "HuDebug - onRotateMode(): "
        << "m_angleRotate: " << m_angleRotate
        << ", m_setSurroundAngle: " << m_setSurroundAngle
        << ", m_rotateProgress: " << m_rotateProgress
        << ", m_camPosAxis2Rq: " << m_camPosAxis2Rq
        << ", m_rotateLeft: " << m_rotateLeft);
}


void HeadUnitHemisphereCameraUpdater::enableReturnChannelDefaultValue(osg::Matrix f_viewMatrix)
{
  osg::Vec3f l_center;
  vfc::float32_t l_elevation = 0.0f, l_azimuth = 0.0f;
  vfc::float32_t l_distance =0.0f;
  // at this point it always should use the SVS cam center cc::virtcam::g_positions->m_overTheRoof.m_center
  projectCameraToParametricDomain(f_viewMatrix, l_center, l_elevation, l_azimuth, l_distance, true);
  normalizeAndClampParameters(l_elevation, l_azimuth, l_distance);

  vfc::int32_t l_axis1 = 0, l_axis2 = 0, l_zoom = 0;
  quantizeParameters(l_elevation, l_azimuth, l_distance, l_axis1, l_axis2, l_zoom);
  enableReturnChannelDefaultValue(l_axis1, l_axis2, l_zoom);
}


void HeadUnitHemisphereCameraUpdater::attachReturnChannelUpdateCallback(osg::Camera* f_camera, osg::Node* f_scene) const
{
  if (f_camera == nullptr || f_scene == nullptr)
  {
    return;
  }
  // Attach return channel update callback to both the tracked camera and the scene root.
  // This is a somwhat clumsy way to keep sending default values even if the tracked camera is disabled in the scenegraph
  m_returnChannelUpdateCallback->setScene(f_scene);
  f_camera->addUpdateCallback(m_returnChannelUpdateCallback);
  f_scene->addUpdateCallback(m_returnChannelUpdateCallback);
}


void HeadUnitHemisphereCameraUpdater::reset()
{
  m_suspended = true;
  cc::virtcam::HemisphereCameraUpdater::reset();
}


osg::Matrix HeadUnitHemisphereCameraUpdater::getViewMatrix(vfc::float32_t f_elevation, vfc::float32_t f_azimuth, vfc::float32_t f_distance, const osg::Vec3f& f_hemisphereCenter)
{
  const osg::Vec3f currentDirection = cc::virtcam::HemisphereCameraUpdater::transformSphericalToWorld(f_elevation, f_azimuth, 1.0f);
  osg::Vec3f eye, up;
  eye = osg::Vec3f(f_hemisphereCenter + currentDirection*f_distance);
  if (m_horizontalPad && (f_elevation < osg::PI))
  {
    up = osg::Vec3f(0.0f, 0.0f, 1.0f);
  }
  if (m_horizontalPad && (f_elevation >= osg::PI))
  {
    up = osg::Vec3f(std::cos(f_azimuth), -std::sin(f_azimuth), 0.0f);
  }
  if (!m_horizontalPad)
  {
    up = osg::Vec3f(-std::sin(f_azimuth), -std::cos(f_azimuth), 0.0f);
  }
  return osg::Matrix::lookAt(eye, f_hemisphereCenter, up);
}


void HeadUnitHemisphereCameraUpdater::updateHorizontalVerticalCoordinates(vfc::uint32_t& f_huX, vfc::uint32_t& f_huY)
{
  if (m_horizontalPad) // ! Horizontal mode
  {
    f_huY = cc::core::g_views->m_usableCanvasViewport.m_size.y() - f_huY;  // convert to bottom left
    const vfc::int32_t l_huX_delta = (static_cast<vfc::int32_t>(f_huX) - m_horiViewport.m_origin.x());
    const vfc::int32_t l_huy_delta = (static_cast<vfc::int32_t>(f_huY) - m_horiViewport.m_origin.y());
    f_huX = (l_huX_delta > 0) ? static_cast<vfc::uint32_t>(l_huX_delta) : 0u;
    f_huY = (l_huy_delta > 0 && l_huy_delta < m_horiViewport.m_size.y()) ? static_cast<vfc::uint32_t>(l_huy_delta) : 0u;
    m_touchWidth  = static_cast<vfc::float32_t>(m_horiViewport.m_size.x());
    m_touchHeight = static_cast<vfc::float32_t>(m_horiViewport.m_size.y());

  }
  else // ! Vertical mode
  {
    const vfc::int32_t l_huX_delta = (static_cast<vfc::int32_t>(f_huX) - m_vertViewport.m_origin.y());
    const vfc::int32_t l_huy_delta = (static_cast<vfc::int32_t>(f_huY) - m_vertViewport.m_origin.x());
    f_huX = (l_huX_delta > 0 && l_huX_delta < m_vertViewport.m_size.y()) ? static_cast<vfc::uint32_t>(l_huX_delta) : 0u;
    f_huY = (l_huy_delta > 0 && l_huy_delta < m_vertViewport.m_size.x()) ? static_cast<vfc::uint32_t>(l_huy_delta) : 0u;
    m_touchWidth  = static_cast<vfc::float32_t>(m_vertViewport.m_size.y());
    m_touchHeight = static_cast<vfc::float32_t>(m_vertViewport.m_size.x());
  }
}

void HeadUnitHemisphereCameraUpdater::setToCamera(const osg::Camera* f_cam)
{
  osg::Vec3f     l_center = {0.0f, 0.0f, 0.0f};
  vfc::float32_t l_elevation = 0.0f;
  vfc::float32_t l_azimuth = 0.0f;
  vfc::float32_t l_distance = 0.0f;

  projectCameraToParametricDomain(f_cam, l_center, l_elevation, l_azimuth, l_distance);
  setHemisphereCenter(l_center);
  normalizeAndClampParameters(l_elevation, l_azimuth, l_distance);
  // zoomTo(l_distance);
  rotateTo(l_elevation, l_azimuth);
  zoomTo(l_distance);

  vfc::int32_t l_axis1 = 0;
  vfc::int32_t l_axis2 = 0;

//   retrieveZoomReferenceDistance(f_cam);

  quantizeAxisParameters(l_elevation, l_azimuth, l_axis1, l_axis2);
  m_camPosAxis1Rq = l_axis1;
  m_camPosAxis2Rq = l_axis2;
}

bool HeadUnitHemisphereCameraUpdater::updateDaddyInputs()  // PRQA S 6041  // PRQA S 6043  // PRQA S 6040
{
  bool dirty = false;
  const cc::core::CustomFramework* l_framework = m_framework->asCustomFramework();

  bool allPortsHaveData = true;
  GET_PORT_DATA(touchStatusContainer, l_framework->m_HUTouchTypeReceiver, allPortsHaveData);
  GET_PORT_DATA(displayedViewContainer, l_framework->m_displayedView_ReceiverPort, allPortsHaveData);
  GET_PORT_DATA(freeModeStsContainer, l_framework->m_freeModeSt_ReceiverPort, allPortsHaveData);
  GET_PORT_DATA(huRotateStatusContainer, l_framework->m_SVSRotateStatusDaddy_Receiver, allPortsHaveData);
  GET_PORT_DATA(hmiDataContainer, l_framework->m_hmiDataReceiver, allPortsHaveData);
  GET_PORT_DATA(zoomFactorContainer, l_framework->m_ZoomLevel_ReceiverPort, allPortsHaveData);

  if (!allPortsHaveData)
  {
    return false;
  }

  m_touchEvent   = static_cast<vfc::uint32_t>(touchStatusContainer->m_Data);
  const vfc::uint32_t l_prevviewid = m_curviewid;
  m_curviewid    = displayedViewContainer->m_Data;
  m_isFreeModeSt = freeModeStsContainer->m_Data;
  if (3u != m_touchEvent) { m_moving = false; }

  if (l_prevviewid != m_curviewid)
  {
    XLOG_INFO(g_AppContext, "HuDebug - updateCamera(): view changed from " << l_prevviewid << " to " << m_curviewid);
  }

  if (static_cast<vfc::uint8_t>(cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI) == huRotateStatusContainer->m_Data)
  {
    if ((EScreenID_PERSPECTIVE_RL  == m_curviewid) || (EScreenID_PERSPECTIVE_FL  == m_curviewid) || (EScreenID_PERSPECTIVE_PFR == m_curviewid) ||
        (EScreenID_PERSPECTIVE_FR  == m_curviewid) || (EScreenID_PERSPECTIVE_RR  == m_curviewid) || (EScreenID_PERSPECTIVE_PRE == m_curviewid))
    {
      m_horizontalPad = true;
    }
  }
  if (static_cast<vfc::uint8_t>(cc::target::common::EThemeTypeHU::ETHEME_TYPE_VERT) == huRotateStatusContainer->m_Data)
  {
    if ((EScreenID_VERT_PERSPECTIVE_PFR  == m_curviewid) || (EScreenID_VERT_PERSPECTIVE_PRE  == m_curviewid) || (EScreenID_VERT_PERSPECTIVE_RL   == m_curviewid) || (EScreenID_VERT_PERSPECTIVE_RR   == m_curviewid) ||
        (EScreenID_VERT_PERSPECTIVE_FL   == m_curviewid) || (EScreenID_VERT_PERSPECTIVE_FR   == m_curviewid) || (EScreenID_VERT_PERSPECTIVE_FL_R == m_curviewid) || (EScreenID_VERT_PERSPECTIVE_FL_L == m_curviewid) ||
        (EScreenID_VERT_PERSPECTIVE_FR_R == m_curviewid) || (EScreenID_VERT_PERSPECTIVE_FR_L == m_curviewid) || (EScreenID_VERT_PERSPECTIVE_RL_R == m_curviewid) || (EScreenID_VERT_PERSPECTIVE_RL_L == m_curviewid) ||
        (EScreenID_VERT_PERSPECTIVE_RR_R == m_curviewid) || (EScreenID_VERT_PERSPECTIVE_RR_L == m_curviewid))
    {
      m_horizontalPad = false;
    }
  }

  if (!m_isFreeModeStPre && m_isFreeModeSt)
  {
    //getPreviewidToAxis(m_curviewid);
    m_dragStartCamPosY = static_cast<vfc::int32_t>(m_camPosAxis1Rq);
    m_dragStartCamPosX = static_cast<vfc::int32_t>(m_camPosAxis2Rq);
    // zoomTo(m_horizontalPad ? g_huhemisPara->m_zoomfactorHori : g_huhemisPara->m_zoomfactorVert);
    XLOG_INFO(g_AppContext, "HuDebug - updateCamera(): !m_isFreeModeStPre && m_isFreeModeSt, zoom to default settings");
  }
  m_isFreeModeStPre = m_isFreeModeSt;

  vfc::uint32_t l_huX = static_cast<vfc::uint32_t>(hmiDataContainer->m_Data.m_huX);
  vfc::uint32_t l_huY = static_cast<vfc::uint32_t>(hmiDataContainer->m_Data.m_huY);
  updateHorizontalVerticalCoordinates(l_huX, l_huY);

  const vfc::uint32_t l_zoomFactorRq = static_cast<int>(zoomFactorContainer->m_Data);
  bool l_zoomChanged = false;
  if (l_framework->m_ZoomLevel_ReceiverPort.hasNewData()
    && (m_curviewid != EScreenID_PLANETARY_VIEW && m_curviewid != EScreenID_FRONT_BUMPER && m_curviewid != EScreenID_REAR_BUMPER && m_curviewid != EScreenID_ULTRA_WIDE_SURROUND_VIEW))
  {
    XLOG_INFO_OS(g_AppContext) << "HeadUnitHemisphereCameraUpdater: new zoomLevel: " << l_zoomFactorRq << XLOG_ENDL;
    XLOG_INFO(g_AppContext, "HuDebug - updateCamera(): l_framework->m_ZoomLevel_ReceiverPort.hasNewData(), zoom to " << l_zoomFactorRq);
    l_zoomChanged = update(m_zoomFactorRq, l_zoomFactorRq);
  }

  //! define the initial view state of rotation and zoom
  if ( 3u == m_touchEvent || l_zoomChanged)
  {
    m_freeviewSt = false;
    XLOG_INFO(g_AppContext, "HuDebug - updateCamera():  3u == m_touchEvent || l_zoomChanged, m_freeviewSt = false");
  }

  //! rotation handling
  vfc::int32_t l_camPosAxis1 = m_camPosAxis1Rq;
  vfc::int32_t l_camPosAxis2 = m_camPosAxis2Rq;

  const bool newAzimuthData = m_HUfreemodeAngleAzimuthReceiver.hasNewData();
  const bool newElevationData = m_HUfreemodeAngleElevationReceiver.hasNewData();

  if (newAzimuthData || newElevationData)
  {
    const cc::daddy::HUFreemodeAngleDaddy_t* l_cameraAngle1 = m_HUfreemodeAngleElevationReceiver.getData();
    const cc::daddy::HUFreemodeAngleDaddy_t* l_cameraAngle2 = m_HUfreemodeAngleAzimuthReceiver.getData();
    vfc::float32_t l_elevation = 0.0f;
    vfc::float32_t l_azimuth = 0.0f;
    if (newElevationData)
    {
        const vfc::float32_t tmp = l_cameraAngle1->m_Data * 5;
        l_camPosAxis1 = static_cast<vfc::int32_t>(tmp);
    }
    if (newAzimuthData)
    {
        const vfc::float32_t tmp = l_cameraAngle2->m_Data * 5 + 900;
        l_camPosAxis2 = static_cast<vfc::int32_t>(tmp);
    }

    dequantizeAxisParameters(static_cast<vfc::int32_t>(l_camPosAxis1), static_cast<vfc::int32_t>(l_camPosAxis2), l_elevation, l_azimuth);

    const bool l_isFullscreen = (m_curviewid == EscreenID_FULL_SCREEN_3D);
    const vfc::float32_t l_minElevation = l_isFullscreen ? cc::virtcam::g_huhemisPara->m_fullscreenElevation
                                                         : cc::virtcam::g_huhemisPara->m_normalElevation;


    bool l_axis1Changed = false;

    if (l_elevation < l_minElevation){
      l_elevation = l_minElevation;
    }
    else{
      l_axis1Changed = update(m_camPosAxis1Rq, l_camPosAxis1);
    }

    const bool l_axis2Changed = update(m_camPosAxis2Rq, l_camPosAxis2);

    if (l_axis1Changed || l_axis2Changed || m_updateOnce)
    {
      rotateTo(l_elevation, l_azimuth);
      dirty = true;
    }
    XLOG_INFO(g_AppContext, "HuDebug - updateCamera():  newAzimuthData || newElevationData, m_camPosAxis1Rq: " << m_camPosAxis1Rq
        << ", m_camPosAxis2Rq: " << m_camPosAxis2Rq
        << ", l_camPosAxis1: " << l_camPosAxis1
        << ", l_camPosAxis2: " << l_camPosAxis2);
  }

  if (3u == m_touchEvent && isInsideTouchArea(l_huX, l_huY) &&
    isPenMovingOnScreen(static_cast<vfc::uint16_t>(l_huX), static_cast<vfc::uint16_t>(l_huY), l_camPosAxis1, l_camPosAxis2) &&
    (m_curviewid != EScreenID_PLANETARY_VIEW && m_curviewid != EScreenID_FRONT_BUMPER && m_curviewid != EScreenID_REAR_BUMPER && m_curviewid != EScreenID_ULTRA_WIDE_SURROUND_VIEW))
  {
    bool l_axis1Changed = update(m_camPosAxis1Rq, l_camPosAxis1);
    bool l_axis2Changed = update(m_camPosAxis2Rq, l_camPosAxis2);
    if (l_axis1Changed || l_axis2Changed || m_updateOnce)
    {
      vfc::float32_t l_elevation = 0.0f;
      vfc::float32_t l_azimuth   = 0.0f;
      dequantizeAxisParameters(
        static_cast<vfc::int32_t>(m_camPosAxis1Rq), static_cast<vfc::int32_t>(m_camPosAxis2Rq),
        l_elevation, l_azimuth);
      rotateTo(l_elevation, l_azimuth);
      dirty = true;
      m_freeviewSt = true;

    }
  }

  if (((m_camPosAxis2Rq != m_camPosAxis2RqPre) || (m_camPosAxis1Rq != m_camPosAxis1RqPre))&&
      ((m_curviewid != EScreenID_PLANETARY_VIEW) &&
       (m_curviewid != EScreenID_ULTRA_WIDE_SURROUND_VIEW) &&
       (m_curviewid != EScreenID_FRONT_BUMPER) &&
       (m_curviewid != EScreenID_REAR_BUMPER) ) )
  {
    if (cc::daddy::CustomDaddyPorts::sm_camPosAxis2Rq_SenderPort.isConnected())
    {
      auto& l_container = cc::daddy::CustomDaddyPorts::sm_camPosAxis2Rq_SenderPort.reserve();
      l_container.m_Data.m_Hori = static_cast<vfc::int32_t>((m_camPosAxis2Rq/5+180)%360);
      l_container.m_Data.m_Vert = static_cast<vfc::int32_t>((m_camPosAxis1Rq/5)); // 0-449:0-90
      XLOG_INFO_OS( g_viewModeSMContext ) <<  "l_container.m_Data.m_Vert : " << l_container.m_Data.m_Vert <<XLOG_ENDL;
      cc::daddy::CustomDaddyPorts::sm_camPosAxis2Rq_SenderPort.deliver();
    }
  }

  if (m_camPosAxis1RqPre != m_camPosAxis1Rq)
  {
    XLOG_INFO(g_AppContext, "HuDebug - updateCamera(): (m_camPosAxis1RqPre != m_camPosAxis1Rq), m_camPosAxis1Rq: " << m_camPosAxis1Rq);
  }
  if (m_camPosAxis2RqPre != m_camPosAxis2Rq)
  {
    XLOG_INFO(g_AppContext, "HuDebug - updateCamera(): (m_camPosAxis2RqPre != m_camPosAxis2Rq), m_camPosAxis2Rq: " << m_camPosAxis2Rq);
  }

  m_camPosAxis1RqPre = m_camPosAxis1Rq;
  m_camPosAxis2RqPre = m_camPosAxis2Rq;

  //! zoom change handling
  if (l_zoomChanged)
  {
    // m_zoomLevel = (g_huhemisPara->m_zoomScale) * m_zoomFactorRq;
    m_zoomFactorIpc = m_zoomFactorRq;
    using namespace cc::target::common;
    switch (static_cast<E3DZoomLevel>(m_zoomFactorIpc))
    {
    case E3DZoomLevel::LEVEL0:
    {
        m_zoomLevel = m_referenceDistance / g_huhemisPara->m_zoomL0; break;// Zoom out level 3 50%, please set directly to 3D wide-angle view, do not use this value
    }
    case E3DZoomLevel::LEVEL1:
    {
        m_zoomLevel = m_referenceDistance / g_huhemisPara->m_zoomL1; break;// Zoom out level 2 70%
    }
    case E3DZoomLevel::LEVEL2:
    {
        m_zoomLevel = m_referenceDistance / g_huhemisPara->m_zoomL2; break;// Zoom out level 1 85%
    }
    case E3DZoomLevel::LEVEL3:
    {
        m_zoomLevel = m_referenceDistance / g_huhemisPara->m_zoomL3; break;// No zoom
    }
    case E3DZoomLevel::LEVEL4:
    {
        m_zoomLevel = m_referenceDistance / g_huhemisPara->m_zoomL4; break;// Zoom in level 1 110%
    }
    case E3DZoomLevel::LEVEL5:
    {
        m_zoomLevel = m_referenceDistance / g_huhemisPara->m_zoomL5; break;// Zoom in level 2 125%
    }
    case E3DZoomLevel::LEVEL6:
    {
        m_zoomLevel = m_referenceDistance / g_huhemisPara->m_zoomL6; break;// Zoom in level 3 150%
    }
    default:
    {
        m_zoomLevel = m_referenceDistance; break;// No zoom
    }
    }

    if ( false == m_freeviewSt )
    {
      vfc::float32_t l_elevation = 0.0f;
      vfc::float32_t l_azimuth   = 0.0f;
      dequantizeAxisParameters(static_cast<vfc::int32_t>(m_camPosAxis1Rq), static_cast<vfc::int32_t>(m_camPosAxis2Rq), l_elevation, l_azimuth);
      rotateTo(l_elevation, l_azimuth);
      XLOG_INFO(g_AppContext, "HuDebug - updateCamera(): l_zoomChanged && !m_freeviewSt, rotateTo(l_elevation, l_azimuth) , m_camPosAxis1Rq: " << m_camPosAxis1Rq
            << ", m_camPosAxis2Rq: " << m_camPosAxis2Rq);
    }

    vfc::float32_t l_elevation = 0.0f;
    vfc::float32_t l_azimuth   = 0.0f;
    normalizeAndClampParameters(l_elevation, l_azimuth, m_zoomLevel);
    zoomTo(m_zoomLevel);
    dirty = true;
    m_updateOnce = false;
    m_freeviewSt = true;
  }

  //! only relevant for SIL at the moment
  if (l_framework->m_HUCameraCommandDaddyReceiver.hasNewData())
  {
    const cc::daddy::HUCameraCommandsDaddy* cameraCommandsData = l_framework->m_HUCameraCommandDaddyReceiver.getData();
    if (cameraCommandsData->m_Data.hemisphere3DSet)
    {
      XLOG_INFO_OS( g_viewModeSMContext ) <<  "Incoming Position change command: " << cameraCommandsData->m_Data.hemisphere3D.x() << " AXIS_2: " << cameraCommandsData->m_Data.hemisphere3D.y() << XLOG_ENDL;//PRQA S 4060
      vfc::float32_t l_elevation = 0.0f;
      vfc::float32_t l_azimuth = 0.0f;
      m_camPosAxis1Rq = static_cast<vfc::uint32_t>(cameraCommandsData->m_Data.hemisphere3D.x());
      m_camPosAxis2Rq = static_cast<vfc::uint32_t>(cameraCommandsData->m_Data.hemisphere3D.y());
      dequantizeAxisParameters(static_cast<vfc::int32_t>(m_camPosAxis1Rq), static_cast<vfc::int32_t>(m_camPosAxis2Rq), l_elevation, l_azimuth);
      rotateTo(l_elevation, l_azimuth);
      dirty = true;
    }
    if (cameraCommandsData->m_Data.zoom3DSet)
    {
      zoomTo(static_cast<vfc::float32_t>(cameraCommandsData->m_Data.zoom3D));
      vfc::float32_t l_zoom = 0.0f;
      m_zoomFactor = cameraCommandsData->m_Data.zoom3D;
      dequantizeZoomParameter(static_cast<vfc::int32_t>(cameraCommandsData->m_Data.zoom3D), l_zoom);
      zoomTo(l_zoom);
      dirty = true;
    }
  }


  return (dirty);
}


bool HeadUnitHemisphereCameraUpdater::isPenMovingOnScreen(
  vfc::uint16_t f_huX,
  vfc::uint16_t f_huY,
  vfc::int32_t& f_camPosAxis1,
  vfc::int32_t& f_camPosAxis2)
{
  if (m_moving)
  {
    getHUCoordinatesToAxis(f_huX, f_huY, f_camPosAxis1, f_camPosAxis2);
    return true;
  }
  else
  {
    m_movingStartX = f_huX;
    m_movingStartY = f_huY;
    m_dragStartCamPosY = (m_camPosAxis1Rq);
    m_dragStartCamPosX = (m_camPosAxis2Rq);
    m_moving = true;
    return true;
  }
}


void HeadUnitHemisphereCameraUpdater::getHUCoordinatesToAxis(
  const vfc::uint32_t f_huX,
  const vfc::uint32_t f_huY,
  vfc::int32_t& f_camPosAxis1,
  vfc::int32_t& f_camPosAxis2)
{
  // Axis2: In Y direction, Axis1: In X direction
  const vfc::float32_t dx = (static_cast<vfc::float32_t>(f_huX) - static_cast<vfc::float32_t>(m_movingStartX))/(m_touchWidth);
  const vfc::float32_t dy = -(static_cast<vfc::float32_t>(f_huY) - static_cast<vfc::float32_t>(m_movingStartY))/(m_touchHeight);

  const vfc::float32_t l_dragStartCamPosY = dy*(static_cast<vfc::float32_t>(m_axis1Range) - 1.0f);
  const vfc::float32_t l_dragStartCamPosX = dx*(static_cast<vfc::float32_t>(m_axis2Range) - 1.0f);
  vfc::int32_t l_camPosTmp1 = m_dragStartCamPosY + static_cast<vfc::int32_t>(l_dragStartCamPosY);  //PRQA S 3016
  vfc::int32_t l_camPosTmp2 = m_dragStartCamPosX + static_cast<vfc::int32_t>(l_dragStartCamPosX);  //PRQA S 3016

  l_camPosTmp1 = std::max(static_cast<vfc::int32_t>(m_asix1Offset) , std::min(l_camPosTmp1, static_cast<vfc::int32_t>(m_axis1Range) - 1));

  l_camPosTmp2 = l_camPosTmp2 % static_cast<vfc::int32_t>(m_axis2Range);
  if (l_camPosTmp2 < 0)
  {
    l_camPosTmp2 += static_cast<vfc::int32_t>(m_axis2Range);
  }

  f_camPosAxis1 = static_cast<vfc::uint32_t>(l_camPosTmp1);
  f_camPosAxis2 = static_cast<vfc::uint32_t>(l_camPosTmp2);
}


bool HeadUnitHemisphereCameraUpdater::isInsideTouchArea(const vfc::uint32_t& f_huX, const vfc::uint32_t& f_huY)
{
  return ((f_huX > 0u) && (static_cast<vfc::float32_t>(f_huX) < m_touchWidth) && (f_huY > 0u) && (static_cast<vfc::float32_t>(f_huY) < m_touchHeight));
}


// void HeadUnitHemisphereCameraUpdater::getPreviewidToAxis(const EScreenID f_previewid)
// {
//   switch (f_previewid)
//   {
//     case EScreenID_PERSPECTIVE_RL:  m_camPosAxis1Rq = m_asix1Offset; m_camPosAxis2Rq = 225u;  break;
//     case EScreenID_PERSPECTIVE_PLE: m_camPosAxis1Rq = m_asix1Offset; m_camPosAxis2Rq = 450u;  break;
//     case EScreenID_PERSPECTIVE_FL:  m_camPosAxis1Rq = m_asix1Offset; m_camPosAxis2Rq = 675u;  break;
//     case EScreenID_PERSPECTIVE_PFR: m_camPosAxis1Rq = m_asix1Offset; m_camPosAxis2Rq = 900u;  break;
//     case EScreenID_PERSPECTIVE_FR:  m_camPosAxis1Rq = m_asix1Offset; m_camPosAxis2Rq = 1125u; break;
//     case EScreenID_PERSPECTIVE_PRI: m_camPosAxis1Rq = m_asix1Offset; m_camPosAxis2Rq = 1350u; break;
//     case EScreenID_PERSPECTIVE_RR:  m_camPosAxis1Rq = m_asix1Offset; m_camPosAxis2Rq = 1575u; break;
//     case EScreenID_PERSPECTIVE_PRE: m_camPosAxis1Rq = m_asix1Offset; m_camPosAxis2Rq = 0u;    break;
//     case EscreenID_FULL_SCREEN_3D:  m_camPosAxis1Rq = m_asix1Offset; m_camPosAxis2Rq = 450u;  break;
//     default :
//     {
//       m_camPosAxis1Rq = m_asix1Offset;
//       m_camPosAxis2Rq = 0u;
//       break;
//     }
//   }
// }


void HeadUnitHemisphereCameraUpdater::addDebugLogSIL(osg::Camera* f_camera)
{
  vfc::float32_t l_distanceMin = 0.0f;
  vfc::float32_t l_distanceMax = 0.0f;
  vfc::float32_t l_elevationMin = 0.0f;
  vfc::float32_t l_elevationMax = 0.0f;
  osg::Vec3f     l_center{0.0f, 0.0f, 0.0f};
  vfc::float32_t l_elevation = 0.0f;
  vfc::float32_t l_azimuth = 0.0f;
  vfc::float32_t l_distance = 0.0f;
  pc::virtcam::VirtualCamera l_virtcam;
  // clang-format off
  std::string l_zoomLevel;
  using namespace cc::target::common;
  switch (static_cast<E3DZoomLevel>(m_zoomFactorIpc))
  {
  case E3DZoomLevel::LEVEL0:
  {
      l_zoomLevel = "LEVEL0"; break;
  }
  case E3DZoomLevel::LEVEL1:
  {
      l_zoomLevel = "LEVEL1"; break;
  }
  case E3DZoomLevel::LEVEL2:
  {
      l_zoomLevel = "LEVEL2"; break;
  }
  case E3DZoomLevel::LEVEL3:
  {
      l_zoomLevel = "LEVEL3"; break;
  }
  case E3DZoomLevel::LEVEL4:
  {
      l_zoomLevel = "LEVEL4"; break;
  }
  case E3DZoomLevel::LEVEL5:
  {
      l_zoomLevel = "LEVEL5"; break;
  }
  case E3DZoomLevel::LEVEL6:
  {
      l_zoomLevel = "LEVEL6"; break;
  }
  default:
  {
      l_zoomLevel = "INVALID";
      break;
  }
  }
  // clang-format on


  getZoomRange(l_distanceMin, l_distanceMax);
  getElevationRange(l_elevationMin, l_elevationMax);
  projectCameraToParametricDomain(f_camera, l_center, l_elevation, l_azimuth, l_distance, false);
  auto l_viewMatrix = getViewMatrix(l_elevation, l_azimuth, getTargetDistance(), l_center);
  l_viewMatrix.getLookAt(l_virtcam.m_eye, l_virtcam.m_center, l_virtcam.m_up, 1.0f);
  // clang-format off
  IMGUI_LOG("HeadUnitCameraUpdater", "m_suspended", m_suspended);
  IMGUI_LOG("HeadUnitCameraUpdater", "l_center", std::to_string(l_center.x()) + " " + std::to_string(l_center.y()) + " " + std::to_string(l_center.z()));
  // IMGUI_LOG("HeadUnitCameraUpdater", "l_referenceElevation", l_referenceElevation);
  IMGUI_LOG("HeadUnitCameraUpdater", "l_azimuth", osg::RadiansToDegrees(l_azimuth));
  IMGUI_LOG("HeadUnitCameraUpdater", "l_elevation", osg::RadiansToDegrees((l_elevation)));
  IMGUI_LOG("HeadUnitCameraUpdater", "l_distance", l_distance);
  // IMGUI_LOG("HeadUnitCameraUpdater", "l_referenceAzimuth", l_referenceAzimuth);
  // IMGUI_LOG("HeadUnitCameraUpdater", "l_referenceZoom", l_referenceZoom);
  IMGUI_LOG("HeadUnitCameraUpdater", "l_distanceMin", l_distanceMin);
  IMGUI_LOG("HeadUnitCameraUpdater", "l_distanceMax", l_distanceMax);
  IMGUI_LOG("HeadUnitCameraUpdater", "l_elevationMin", l_elevationMin);
  IMGUI_LOG("HeadUnitCameraUpdater", "l_elevationMax", l_elevationMax);
  IMGUI_LOG("HeadUnitCameraUpdater", "currentZoomLevel", getCurrentZoomLevel());
  IMGUI_LOG("HeadUnitCameraUpdater", "l_virtcam.m_eye", std::to_string(l_virtcam.m_eye.x()) + " " + std::to_string(l_virtcam.m_eye.y()) + " " + std::to_string(l_virtcam.m_eye.z()));
  IMGUI_LOG("HeadUnitCameraUpdater", "l_virtcam.m_center", std::to_string(l_virtcam.m_center.x()) + " " + std::to_string(l_virtcam.m_center.y()) + " " + std::to_string(l_virtcam.m_center.z()));
  IMGUI_LOG("HeadUnitCameraUpdater", "l_virtcam.m_up", std::to_string(l_virtcam.m_up.x()) + " " + std::to_string(l_virtcam.m_up.y()) + " " + std::to_string(l_virtcam.m_up.z()));
  IMGUI_LOG("HeadUnitCameraUpdater", "l_targetDistance", getTargetDistance());
  IMGUI_LOG("HeadUnitCameraUpdater", "l_currentDistance", getCurrentDistance());
  IMGUI_LOG("HeadUnitCameraUpdater", "m_referenceDistance", m_referenceDistance);
  IMGUI_LOG("HeadUnitCameraUpdater", "zoomLevel_IPC", l_zoomLevel);
}

} // namespace virtcam
} // namespace cc
