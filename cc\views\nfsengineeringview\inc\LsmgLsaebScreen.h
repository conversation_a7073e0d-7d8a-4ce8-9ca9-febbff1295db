//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: MADCSZH MAO Donny (CC-DA/EAV2-CN)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  LsmgLsaebScreen.h
/// @brief
//=============================================================================

#ifndef CC_VIEWS_ENGINEERINGVIEW_LSMGLSAEBSCREEN_H
#define CC_VIEWS_ENGINEERINGVIEW_LSMGLSAEBSCREEN_H

#include "pc/svs/views/engineeringview/inc/EngineeringView.h"
#include "cc/core/inc/CustomUltrasonic.h"
#include "cc/daddy/inc/CustomDaddyTypes.h"
#include "cc/daddy/inc/CustomDaddyPorts.h"
#include "cc/views/planview/inc/PlanView.h"

#include <osgText/Text>
#include <osg/Matrixf>

namespace cc
{
namespace views
{
namespace nfsengineeringview
{

//======================================================
// EngSectorData
//------------------------------------------------------
/// Managing the borders.
/// The distances are measured around the car along the
/// sectors.
/// <AUTHOR>
//======================================================
class EngSectorData
{
public:
  EngSectorData();
  // ~EngSectorData();

  osg::Vec2f m_leftBorderRefPoint;
  osg::Vec2f m_leftBorderRefPointEnd;
  osg::Vec2f m_leftBorderDir;
  vfc::float32_t      m_currentDistance;

};

//======================================================
// LsmgLsaebScreen
//------------------------------------------------------
/// Responsible for the lsmg/lsaeb engineer screen.
/// Creates the lsmg/lsaeb enginer screen and draw
/// different engineers sectors.
/// Shows lsaeb signals.
/// <AUTHOR>
//======================================================
class LsmgLsaebScreen : public pc::views::engineeringview::EngineeringScreen
{
public:

  LsmgLsaebScreen(cc::views::planview::PlanViewCullCallback* f_cullcallback);

  virtual bool update(pc::core::Framework* f_framework);

protected:

  virtual ~LsmgLsaebScreen();

private:

  //! Copy constructor is not permitted.
  LsmgLsaebScreen (const LsmgLsaebScreen& other); // = delete
  //! Copy assignment operator is not permitted.
  LsmgLsaebScreen& operator=(const LsmgLsaebScreen& other); // = delete

  void defineEngViewSectorData();

  void drawEngViewSectors(const osg::Matrixf& f_mat);

  void setSectorDistTextPos();

  static std::string getAebOpModeString(cc::daddy::EAebVmcOpMode f_aebMode);

  vfc::uint32_t                                              m_counterCurrentDaddy;
  vfc::uint32_t                                              m_counterCurrentAeb;
  vfc::uint32_t                                              m_counterCurrentDist;
  osg::ref_ptr<cc::core::CustomZoneLayout>                  m_engZoneLayout;
  std::vector<EngSectorData>                                m_engSectors;
  std::vector<EngSectorData>                                m_screenSectors;
  std::vector<pc::views::engineeringview::KeyValueTextBox*> m_pTextBoxDist;
  cc::views::planview::PlanViewCullCallback*                m_planViewCullCall;
  bool                                                      m_sectorDrawn_b;
  vfc::uint32_t                                             m_ussZoneNum;
};



} // namespace nfsengineeringview
} // namespace views
} // namespace cc

#endif // CC_VIEWS_ENGINEERINGVIEW_LSMGLSAEBSCREEN_H