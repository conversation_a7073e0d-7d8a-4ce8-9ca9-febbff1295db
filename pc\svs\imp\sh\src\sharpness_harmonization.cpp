/// @copyright (C) 2023 Robert <PERSON>.
/// The reproduction, distribution and utilization of this file as well as the
/// communication of its contents to others without express authorization is
/// prohibited. Offenders will be held liable for the payment of damages. All
/// rights reserved in the event of the grant of a patent, utility model or
/// design.
/// @file

#include "pc/svs/imp/sh/inc/sharpness_harmonization.hpp"
#include "pc/svs/imp/common/inc/interpolation.hpp"

namespace rbp
{
namespace vis
{
namespace imp
{
namespace sh
{

pc::util::coding::Item<SharpnessHarmonizationData> g_sharpnessHarmonizationData("IQ_SharpnessHarmonization");

void SharpnessHarmonizationData::setEnable(bool f_enable)
{
    m_defaultView.m_enable = f_enable;
    // turn debug off when Sharpness Harmonization is on
    if (f_enable)
    {
        m_defaultView.m_debug = false;
    }
}

void SharpnessHarmonizationData::setEnableDebug(bool f_enable)
{
    // turn Sharpness Harmonization on as a prerequisite for the debug mode
    if (f_enable)
    {
        m_defaultView.m_enable = true;
    }
    m_defaultView.m_debug = f_enable;
}

SharpnessHarmonization::SharpnessHarmonization(pc::core::Framework* f_pFramework)
    : osg::Group{}
    , m_framework(f_pFramework)
    , m_luminanceValues()
    , m_sharpeningFactor()
    , m_smoothingFactor()
    , m_initialized(false)
    , m_ispDataReceived(false)
{
    setNumChildrenRequiringUpdateTraversal(getNumChildrenRequiringUpdateTraversal() + 1U);
    init();
}

void SharpnessHarmonization::init()
{
    vfc::contiguous_fill_n(m_luminanceValues.begin(), common::usize(m_luminanceValues), g_daylightLuminance);
    // use daylight values as default
    vfc::contiguous_fill_n(
        m_sharpeningFactor.begin(),
        common::usize(m_sharpeningFactor),
        g_sharpnessHarmonizationData->m_defaultView.m_sharpeningFactorDay);
    vfc::contiguous_fill_n(
        m_smoothingFactor.begin(),
        common::usize(m_smoothingFactor),
        g_sharpnessHarmonizationData->m_defaultView.m_smoothingFactorDay);

    m_initialized = true;
}

void SharpnessHarmonization::receiveLuminanceValues()
{
    const pc::daddy::ISPDataDaddy* l_ispDataReceiverData = m_framework->m_luminanceReceiver.getData();

    // if no data is received on Daddy port use the default value which is the daylight setting (see init()-method)
    if (nullptr != l_ispDataReceiverData)
    {
        for (const auto camera : common::CCameraIndex::ALL)
        {
            m_luminanceValues[camera]  = l_ispDataReceiverData->m_Data.m_ispData[camera].m_luminance;
            m_sharpeningFactor[camera] = common::calcLinearTransitionValue(
                m_luminanceValues[camera],
                g_sharpnessHarmonizationData->m_defaultView.m_luminanceDayToNight,
                g_sharpnessHarmonizationData->m_defaultView.m_luminanceNightToDay,
                g_sharpnessHarmonizationData->m_defaultView.m_sharpeningFactorNight,
                g_sharpnessHarmonizationData->m_defaultView.m_sharpeningFactorDay);
            m_smoothingFactor[camera] = common::calcLinearTransitionValue(
                m_luminanceValues[camera],
                g_sharpnessHarmonizationData->m_defaultView.m_luminanceDayToNight,
                g_sharpnessHarmonizationData->m_defaultView.m_luminanceNightToDay,
                g_sharpnessHarmonizationData->m_defaultView.m_smoothingFactorNight,
                g_sharpnessHarmonizationData->m_defaultView.m_smoothingFactorDay);
        }
        m_ispDataReceived = true;
    }
}

void SharpnessHarmonization::traverse(osg::NodeVisitor& f_nv)
{
    if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
    {
        receiveLuminanceValues();
        deliverOutput();
    }
    osg::Group::traverse(f_nv);
}

//--------------------------------------------------------------------------------------------------------------
/// @brief Copy view specific settings to daddy related container.
///
/// @param[in]  f_sharpnessHarmonizationDataView  Reference to sharpness harmonization data view container
/// @param[out] f_viewSetting                     Reference to daddy related container
/// @param[in]  f_sharpeningFactor                Sharpening factor input
/// @param[in]  f_smoothingFactor                 Smoothing factor input
//--------------------------------------------------------------------------------------------------------------
static void setOutputSettings(
    const bool f_calibState,
    const SharpnessHarmonizationDataView& f_sharpnessHarmonizationDataView,
    sh::CSharpnessSettingsView&           f_viewSetting,
    vfc::float32_t                        f_sharpeningFactor,
    vfc::float32_t                        f_smoothingFactor)
{
    f_viewSetting.m_enable           = f_sharpnessHarmonizationDataView.m_enable && f_calibState;
    f_viewSetting.m_sharpeningFactor = f_sharpeningFactor;
    f_viewSetting.m_smoothingFactor  = f_smoothingFactor;
    f_viewSetting.m_maxSharpening    = f_sharpnessHarmonizationDataView.m_maxSharpening;
    f_viewSetting.m_debug            = f_sharpnessHarmonizationDataView.m_debug;
}

void SharpnessHarmonization::deliverOutput()
{
    bool l_calibState = true;
    const pc::daddy::CalibrationStsDaddy* l_calibStsReceiverData = m_framework->m_calibStsReceiver.getData();
    if (nullptr != l_calibStsReceiverData)
    {
        l_calibState = l_calibStsReceiverData->m_Data;
    }

    if (pc::daddy::BaseDaddyPorts::sm_sharpnessSettingsDaddySenderPort.isConnected())
    {
        pc::daddy::SharpnessSettingsDaddy& l_data =
            pc::daddy::BaseDaddyPorts::sm_sharpnessSettingsDaddySenderPort.reserve();

        setOutputSettings(
            l_calibState,
            g_sharpnessHarmonizationData->m_defaultView,
            l_data.m_Data.m_viewSettings[ESharpnessView::FIXED],
            // by default use daylight settings
            g_sharpnessHarmonizationData->m_defaultView.m_sharpeningFactorDay,
            g_sharpnessHarmonizationData->m_defaultView.m_smoothingFactorDay);

        setOutputSettings(
            l_calibState,
            g_sharpnessHarmonizationData->m_pxlDensityView,
            l_data.m_Data.m_viewSettings[ESharpnessView::PXLDENSITY],
            g_sharpnessHarmonizationData->m_pxlDensityView.m_sharpeningFactorDay,
            g_sharpnessHarmonizationData->m_pxlDensityView.m_smoothingFactorDay);

        for (const auto camera : common::CCameraIndex::ALL)
        {
            setOutputSettings(
                l_calibState,
                g_sharpnessHarmonizationData->m_defaultView,
                l_data.m_Data.m_viewSettings[getSharpnessViewFromCameraId(camera.asEnum())],
                m_sharpeningFactor[camera],
                m_smoothingFactor[camera]);
        }

        // average luminance dependent setting for all 4 cameras
        vfc::float32_t l_sum = 0.F;
        for (const auto camera : common::CCameraIndex::ALL)
        {
            l_sum += m_luminanceValues[camera];
        }
        const vfc::float32_t l_averageLuminance = l_sum / 4.F;
        const vfc::float32_t l_sharpeningFactor = common::calcLinearTransitionValue(
            l_averageLuminance,
            g_sharpnessHarmonizationData->m_defaultView.m_luminanceDayToNight,
            g_sharpnessHarmonizationData->m_defaultView.m_luminanceNightToDay,
            g_sharpnessHarmonizationData->m_defaultView.m_sharpeningFactorNight,
            g_sharpnessHarmonizationData->m_defaultView.m_sharpeningFactorDay);
        const vfc::float32_t l_smoothingFactor = common::calcLinearTransitionValue(
            l_averageLuminance,
            g_sharpnessHarmonizationData->m_defaultView.m_luminanceDayToNight,
            g_sharpnessHarmonizationData->m_defaultView.m_luminanceNightToDay,
            g_sharpnessHarmonizationData->m_defaultView.m_smoothingFactorNight,
            g_sharpnessHarmonizationData->m_defaultView.m_smoothingFactorDay);

        setOutputSettings(      
            l_calibState,
            g_sharpnessHarmonizationData->m_defaultView,
            l_data.m_Data.m_viewSettings[ESharpnessView::MULTIPLE_LUM],
            l_sharpeningFactor,
            l_smoothingFactor);

        pc::daddy::BaseDaddyPorts::sm_sharpnessSettingsDaddySenderPort.deliver();
    }
}

} // namespace sh
} // namespace imp
} // namespace vis
} // namespace rbp
