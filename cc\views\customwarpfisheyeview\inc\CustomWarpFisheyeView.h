//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: WM APA
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CXH5SZH Chen Xiangqin (CC-DA/EAV2-CN)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS WM
/// @file  CustomWarpFisheyeView.h
/// @brief
//=============================================================================

#ifndef CC_VIEWS_CUSTOMWARPFISHEYEVIEW_H
#define CC_VIEWS_CUSTOMWARPFISHEYEVIEW_H

#include "pc/svs/core/inc/Framework.h"
#include "pc/svs/views/warpfisheyeview/inc/WarpFisheyeView.h"

namespace cc
{
namespace views
{
namespace warpfisheye
{

//!
//! CustomWarpFisheyeViewCropSettings
//!

class CustomWarpFisheyeViewCropSettings : public pc::util::coding::ISerializable
{
public:
    CustomWarpFisheyeViewCropSettings()
        : m_cropBounds()
    {
    }

    SERIALIZABLE(CustomWarpFisheyeViewCropSettings)
    {
        ADD_MEMBER(osg::Vec4f, cropBounds);
    }

    osg::Vec4f m_cropBounds;
};

//!
//! SteeringFovData
//!
class SteeringFovData : public pc::util::coding::ISerializable
{
public:

  SteeringFovData()
    : m_enableFovRange(20.f)
  {
  }

  SERIALIZABLE(SteeringFovData)
  {
    ADD_FLOAT_MEMBER(enableFovRange);
  }

  vfc::float32_t m_enableFovRange;
};

extern pc::util::coding::Item<SteeringFovData> g_steeringFov;

//======================================================
// CustomWarpFisheyeView
//------------------------------------------------------
/// Warp view of the input images.
/// Debug views, the images won't be distorted.
/// <AUTHOR>
//======================================================
class CustomWarpFisheyeView : public pc::views::warpfisheye::WarpFisheyeView
{
public:
    CustomWarpFisheyeView(
        const std::string&                                 f_name,
        const pc::core::Viewport&                          f_viewport,
        pc::core::Framework*                               f_framework,
        pc::core::sysconf::Cameras                         f_camId,
        pc::views::warpfisheye::FisheyeModel*              f_pModel,
        const pc::views::warpfisheye::FisheyeViewSettings* f_settings,
        const CustomWarpFisheyeViewCropSettings*           f_cropBounds,
        rbp::vis::imp::sh::ESharpnessView f_settingSharpnessHarmonization = rbp::vis::imp::sh::ESharpnessView::FIXED,
        rbp::vis::imp::tnf::ETnfView f_settingTemporalNoiseFilter = rbp::vis::imp::tnf::ETnfView::DEFAULT);

    virtual void traverse(osg::NodeVisitor& f_nv) override;

    void customCreateView();
    void customUpdate();

    void setEnabledSteering(bool f_enable) { m_steeringEnabled = f_enable; }

protected:
    //! Copy constructor is not permitted.
    CustomWarpFisheyeView(const CustomWarpFisheyeView& other) = delete;
    //! Copy assignment operator is not permitted.
    CustomWarpFisheyeView& operator=(const CustomWarpFisheyeView& other) = delete;

    virtual ~CustomWarpFisheyeView();

private:
    pc::core::Framework*                     m_framework;
    pc::core::sysconf::Cameras               m_camId;
    bool                                     m_initialized; //!< Initialization only on first update traversal
    vfc::float32_t                           m_aspectRatio;
    osg::ref_ptr<osg::Geode>                 m_offGeode;
    osg::ref_ptr<osg::Geode>                 m_greyGeode;
    const CustomWarpFisheyeViewCropSettings* m_cropParam;
    osg::Vec2f                               m_viewportSize{};
    vfc::float32_t                           m_steerAngle;
    bool                                     m_isSteerChanged;
    bool                                     m_steeringEnabled = false;
};

} // namespace warpfisheye
} // namespace views
} // namespace cc

#endif // CC_VIEWS_CUSTOMWARPFISHEYEVIEW_H