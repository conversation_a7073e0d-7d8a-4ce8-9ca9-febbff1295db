/// @copyright (C) 2025 Robert <PERSON>.
/// The reproduction, distribution and utilization of this file as well as the
/// communication of its contents to others without express authorization is
/// prohibited. Offenders will be held liable for the payment of damages. All
/// rights reserved in the event of the grant of a patent, utility model or
/// design.
/// @file

#ifndef RBP_VIS_IMP_TNF_DATA_HPP
#define RBP_VIS_IMP_TNF_DATA_HPP

#include "pc/svs/core/inc/SystemConf.h"

#include "pc/svs/imp/tnf/inc/temporal_noise_filter_tabs.hpp"
#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/coding/inc/ISerializable.h"

#include "vfc/core/vfc_types.hpp"

namespace rbp
{
namespace vis
{
namespace imp
{
namespace tnf
{

class TemporalNoiseFilterData : public pc::util::coding::ISerializable
{
public:
    TemporalNoiseFilterData();

    TemporalNoiseFilterData(
        vfc::float32_t f_filteredImageWeight,
        vfc::uint32_t  f_motionDetectionThreshold,
        bool           f_combineMotionMap,
        bool           f_setToPassThrough,
        bool           f_outputMotionMap,
        bool           f_enable);

    SERIALIZABLE(TemporalNoiseFilterData)
    {
        ADD_MEMBER(vfc::float32_t, filteredImageWeight);
        ADD_MEMBER(vfc::uint32_t, motionDetectionThreshold);
        ADD_MEMBER(bool, combineMotionMap);
        ADD_MEMBER(bool, setToPassThrough);
        ADD_MEMBER(bool, outputMotionMap);
        ADD_MEMBER(bool, enable);
    }

    vfc::float32_t m_filteredImageWeight;
    vfc::uint32_t  m_motionDetectionThreshold;
    bool           m_combineMotionMap;
    bool           m_setToPassThrough;
    bool           m_outputMotionMap;
    bool           m_enable;
};

extern pc::util::coding::Item<TemporalNoiseFilterData> g_temporalNoiseFilterData;

extern vfc::TCArray<std::size_t, pc::core::sysconf::E_SYSTEM_CONFIG_NUMBER_OF_CAMERAS> g_tnfBufferIndex;

} // namespace tnf
} // namespace imp
} // namespace vis
} // namespace rbp

#endif // RBP_VIS_IMP_TNF_DATA_HPP
