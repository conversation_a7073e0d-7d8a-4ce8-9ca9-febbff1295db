//-------------------------------------------------------------------------------
// Copyright (c) 2018 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include "cc/views/parkview/inc/ParkView.h"
#include "cc/assets/augmentedview/inc/AugmentedViewTransition.h"
#include "cc/views/parkview/inc/ParkViewAnimationHandler.h"
// #include "cc/assets/parkingspots/inc/ParkingSpotManager.h"
// #include "cc/assets/impostor/inc/FixedImpostor.h"
#include "cc/assets/streetoverlay/inc/StreetOverlay.h"
#include "cc/core/inc/CustomScene.h"
// #include "cc/assets/handsonicon/inc/HandsOnIcon.h"
// #include "cc/assets/warnsymbols/inc/WarnSymbols.h"
#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/svs/animation/inc/AnimationManager.h"
#include "pc/svs/core/inc/Framework.h"
#include "pc/svs/core/inc/ShaderManager.h"
#include "pc/svs/core/inc/SystemConf.h"

#include "cc/sm/viewmode/inc/ViewModeStateMachine.h"
#include "osgDB/ReadFile"
#include "osg/Geometry"
#include "osg/MatrixTransform"
#include "osg/Projection"
#include "osg/Texture2D"

#include "cc/assets/corner/inc/ViewportCorner.h"

#include "cc/imgui/inc/imgui_manager.h"

namespace cc
{
namespace views
{
namespace parkview
{

ParkViewBase::ParkViewBase(
    pc::core::UpscalingData*          f_upscalingData,
    const std::string&                f_name,
    const pc::core::Viewport&         f_viewport,
    const pc::virtcam::VirtualCamera& f_camPos,
    pc::core::Framework*              f_framework,
    cc::core::AssetId                 f_parkingSpotManagerAssetId,
    ReferenceFrame                    f_referenceFrame)
    : pc::core::UpscalingView(f_upscalingData, f_name, f_viewport, f_camPos, f_referenceFrame)
    , m_framework(f_framework)
    , m_cullMask(~0u)
    , m_parkingSpotManagerAssetId(f_parkingSpotManagerAssetId)
{
#if 0
  // the vertex is scaled in vertex shader thus bouding box is wrongly
  // calculated. In this case we have to calculate the clipping plan (zFar)
  // manually as below
  setComputeNearFarMode(osg::CullSettings::DO_NOT_COMPUTE_NEAR_FAR);
  setProjectionMatrixAsPerspective(f_camPos.m_fovy,
      f_viewport.computeAspectRatio(),
      0.05,
      1000.0);
#endif

    m_stencil = new osg::Stencil;
    m_stencil->setFunction(osg::Stencil::EQUAL, 0, ~0u);
    m_stencil->setOperation(osg::Stencil::KEEP, osg::Stencil::KEEP, osg::Stencil::KEEP);
}

void ParkViewBase::traverse(osg::NodeVisitor& f_nv)
{
    if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
    {
        cc::core::CustomFramework*       const l_framework    = m_framework->asCustomFramework();
        const cc::daddy::VMStateDaddy_t* const l_vmStateDaddy = l_framework->m_ViewModeState_ReceiverPort.getData();
        if (l_vmStateDaddy != nullptr)
        {
            if (EScreenID_PRK_SEARCHING == l_vmStateDaddy->m_Data.mode)
            {
                // stencil op for the whole scene
                m_stencil->setFunction(osg::Stencil::EQUAL, 0, ~0u);
                m_stencil->setOperation(osg::Stencil::KEEP, osg::Stencil::KEEP, osg::Stencil::KEEP);
                // activate the stencil test
                getOrCreateStateSet()->setAttributeAndModes(m_stencil, osg::StateAttribute::ON); // PRQA S 3143

                // stencil for the rest and already exisiting stencil operation (overwrite)
                // stencil for street asset
                //  pc::core::Asset* l_assetStreet = getAsset(cc::core::AssetId::EASSETS_STREETOVERLAY);
                //  osg::Stencil* l_stencil = fetchAndSetStencilFromAsset(l_assetStreet->getAsset(), 0u);
                //  l_assetStreet->getAsset()->getOrCreateStateSet()->setAttributeAndModes(l_stencil,
                //  osg::StateAttribute::ON);// PRQA S 3143

                // stencil for background asset
                pc::core::Asset* const l_augViewAsset = getAsset(cc::core::AssetId::EASSETS_AUGMENTED_VIEW_TRANSITION);
                cc::assets::augmentedview::AugmentedViewTransition* const l_assetBackground =
                    dynamic_cast<cc::assets::augmentedview::AugmentedViewTransition*>(
                        l_augViewAsset->getAsset()); // PRQA S 3077  // PRQA S 3400
                if (l_assetBackground != nullptr)
                {
                    osg::Stencil* const l_stencil =
                        fetchAndSetStencilFromAsset(l_assetBackground->m_backgroundQuad.get(), 0u);
                    osg::StateSet* const l_stateSetBackground = l_assetBackground->m_backgroundQuad->getOrCreateStateSet();
                    l_stateSetBackground->setAttributeAndModes(l_stencil, osg::StateAttribute::ON); // PRQA S 3143
                }
            }
            else
            {
                // deactivate the stencil test
                //  getOrCreateStateSet()->setAttributeAndModes(m_stencil,osg::StateAttribute::OFF); // PRQA S 3143
                //  pc::core::Asset* l_assetStreet = getAsset(cc::core::AssetId::EASSETS_STREETOVERLAY);
                //  osg::Node* l_streetAssetNode = l_assetStreet->getAsset();
                //  l_streetAssetNode->getOrCreateStateSet()->setMode(GL_STENCIL_TEST, osg::StateAttribute::OFF); //
                //  PRQA S 3143

                pc::core::Asset* const l_augViewAsset = getAsset(cc::core::AssetId::EASSETS_AUGMENTED_VIEW_TRANSITION);
                cc::assets::augmentedview::AugmentedViewTransition* const l_assetBackground =
                    dynamic_cast<cc::assets::augmentedview::AugmentedViewTransition*>(
                        l_augViewAsset->getAsset()); // PRQA S 3077  // PRQA S 3400
                if (l_assetBackground != nullptr)
                {
                    osg::StateSet* const l_stateSetBackground = l_assetBackground->m_backgroundQuad->getOrCreateStateSet();
                    l_stateSetBackground->setMode(GL_STENCIL_TEST, osg::StateAttribute::OFF); // PRQA S 3143
                }
            }
        }
    }
    pc::core::UpscalingView::traverse(f_nv);
}

void ParkViewBase::hideView()
{
    // deactivate the stencil test
    getOrCreateStateSet()->setMode(GL_STENCIL_TEST, osg::StateAttribute::OFF); // PRQA S 3143

    // revert to the initial stencil values for the aug. view transition
    //  pc::core::Asset* l_assetStreet = getAsset(cc::core::AssetId::EASSETS_STREETOVERLAY);
    //  osg::Node* l_streetAssetNode = l_assetStreet->getAsset();
    //  osg::Stencil* l_stencilStreet = fetchAndSetStencilFromAsset(l_streetAssetNode, 240u);
    //  l_streetAssetNode->getOrCreateStateSet()->setAttributeAndModes(l_stencilStreet, osg::StateAttribute::OFF); //
    //  PRQA S 3143

    pc::core::Asset* const l_augViewAsset = getAsset(cc::core::AssetId::EASSETS_AUGMENTED_VIEW_TRANSITION);
    cc::assets::augmentedview::AugmentedViewTransition* const l_assetBackground =
        dynamic_cast<cc::assets::augmentedview::AugmentedViewTransition*>(
            l_augViewAsset->getAsset()); // PRQA S 3077  // PRQA S 3400
    if (l_assetBackground != nullptr)
    {
        osg::StateSet* const l_stateSetBackground = l_assetBackground->m_backgroundQuad->getOrCreateStateSet();
        osg::Stencil*  const l_stencilBackground  = fetchAndSetStencilFromAsset(l_assetBackground->m_backgroundQuad, 240u);
        l_stateSetBackground->setAttributeAndModes(l_stencilBackground, osg::StateAttribute::OFF); // PRQA S 3143
    }
    pc::core::UpscalingView::hideView();
}

//!
//! parkView
//!
ParkInView::ParkInView(
    pc::core::UpscalingData*          f_upscalingData,
    const pc::core::Viewport&         f_viewport,
    const pc::core::Viewport&         f_viewportVert,
    const pc::virtcam::VirtualCamera& f_camPos,
    const pc::virtcam::VirtualCamera& f_camPosVert,
    pc::core::Framework*              f_framework,
    cc::core::AssetId                 f_parkingSpotManagerAssetId)
    : ParkViewBase(f_upscalingData, "ParkInView", f_viewport, f_camPos, f_framework, f_parkingSpotManagerAssetId)
    , m_theme(cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI)
    , m_horiViewport(f_viewport)
    , m_vertViewport(f_viewportVert)
    , m_horiCamPos(f_camPos)
    , m_vertCamPos(f_camPosVert)
{
    // using cc::assets::viewcorner::SpacingMode;
    // auto viewCornerSpaceVisitor = cc::assets::viewcorner::ViewCornerSpaceUpdateVisitor{};
    // viewCornerSpaceVisitor.setBatchSpaceWidth(/*Left*/ 0.0, /*Right*/ 0.0, /*Top*/ 0.0, /*Bottom*/ 0.0);
    // viewCornerSpaceVisitor.setBatchSpacingMode(
    //     /*Left*/ SpacingMode::FULL_WIDTH,
    //     /*Right*/ SpacingMode::HALF_WIDTH,
    //     /*Top*/ SpacingMode::FULL_WIDTH,
    //     /*Bottom*/ SpacingMode::FULL_WIDTH);
    // viewCornerSpaceVisitor.setBatchEnableSpaceSide(/*Left*/ true, /*Right*/ true, /*Top*/ true, /*Bottom*/ true);
    // viewCornerSpaceVisitor.applyViewport(m_horiViewport);
    // viewCornerSpaceVisitor.setBatchSpaceWidth(/*Left*/ 0.0, /*Right*/ 0.0, /*Top*/ 0.0, /*Bottom*/ 0.0);
    // viewCornerSpaceVisitor.setBatchSpacingMode(
    //     /*Left*/ SpacingMode::HALF_WIDTH,
    //     /*Right*/ SpacingMode::FULL_WIDTH,
    //     /*Top*/ SpacingMode::FULL_WIDTH,
    //     /*Bottom*/ SpacingMode::FULL_WIDTH);
    // viewCornerSpaceVisitor.setBatchEnableSpaceSide(/*Left*/ true, /*Right*/ true, /*Top*/ true, /*Bottom*/ true);
    // viewCornerSpaceVisitor.applyViewport(m_vertViewport);

    setViewport(m_horiViewport.toOsgViewport());
    setViewMatrix(osg::Matrixd::lookAt(m_horiCamPos.m_eye, m_horiCamPos.m_center, m_horiCamPos.m_up));
}

void ParkInView::showView()
{
    // cc::assets::handsonicon::HandsOnIcon* l_pHandsOnIcon =
    // dynamic_cast<cc::assets::handsonicon::HandsOnIcon*>(getAsset(core::EASSETS_HANDS_ON_ICON)); if (l_pHandsOnIcon)
    // {
    //   //if we show the ParkView we set the state of the HandsOnIcon on PARK_IN
    //   l_pHandsOnIcon->changeState(cc::assets::handsonicon::PARK_IN);
    // }

    // pc::core::Asset* l_parkingSpotManagerAsset = getAsset(m_parkingSpotManagerAssetId);
    // if (l_parkingSpotManagerAsset != nullptr)
    // {
    //     cc::assets::parkingspots::ParkingSpotManager* l_parkingSpotManager =
    //         dynamic_cast<cc::assets::parkingspots::ParkingSpotManager*>(
    //             l_parkingSpotManagerAsset->getAsset()); // PRQA S 3077  // PRQA S 3400
    //     if (l_parkingSpotManager != nullptr)
    //     {
    //         // initialize only the Fl signal, the internal states remain unchanged
    //         //  l_parkingSpotManager->partialInitManager();
    //         // set the virtual camera for impostor at the right place
    //         //  assets::impostor::VehicleImpostor* l_impos = l_parkingSpotManager->getImpostorParkIn();
    //         //  l_impos->updateRTTCamPos(virtcam::g_positions->getPosition(virtcam::VCAM_PARK_IN));

    //         l_parkingSpotManager->changeState(cc::target::common::EParkngTypeSeld::PARKING_IN);
    //     }
    // }
    // pc::core::Asset* l_streetOverlayAsset = getAsset(cc::core::AssetId::EASSETS_STREETOVERLAY);
    // cc::assets::streetoverlay::StreetOverlay* l_street =
    // dynamic_cast<cc::assets::streetoverlay::StreetOverlay*>(l_streetOverlayAsset->getAsset()); // PRQA S 3077  //
    // PRQA S 3400 l_street->setMatrix(osg::Matrix::identity());
    // //change the direction of the street overlay animation
    // if (true == l_street->getAnimXDirection())
    // {
    //   l_street->setAnimXDirection(false);
    // }
    ParkViewBase::showView();
}

void ParkInView::hideView()
{
    // pc::core::Asset* l_parkingSpotManagerAsset = getAsset(m_parkingSpotManagerAssetId);
    // if (l_parkingSpotManagerAsset != nullptr)
    // {
    //     cc::assets::parkingspots::ParkingSpotManager* l_parkingSpotManager =
    //         dynamic_cast<cc::assets::parkingspots::ParkingSpotManager*>(
    //             l_parkingSpotManagerAsset->getAsset()); // PRQA S 3077  // PRQA S 3400
    //     if (l_parkingSpotManager != nullptr)
    //     {
    //         // initialize only the Fl signal, the internal states remain unchanged
    //         //  l_parkingSpotManager->partialInitManager();
    //         l_parkingSpotManager->changeState(cc::target::common::EParkngTypeSeld::PARKING_NONE);
    //     }
    // }
    ParkViewBase::hideView();
}

void ParkInView::traverse(osg::NodeVisitor& f_nv) // PRQA S 6043
{
    if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
    {
        if (m_framework->asCustomFramework()->m_SVSRotateStatusDaddy_Receiver.hasData())
        {
            cc::target::common::EThemeTypeHU         l_curThemeType = cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI;
            const cc::daddy::SVSRotateStatusDaddy_t* const l_themeType =
                m_framework->asCustomFramework()->m_SVSRotateStatusDaddy_Receiver.getData();
            l_curThemeType = static_cast<cc::target::common::EThemeTypeHU>(l_themeType->m_Data); // PRQA S 3013
            if (m_theme != l_curThemeType)
            {
                if (l_curThemeType == cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI)
                {
                    setViewport(m_horiViewport.toOsgViewport());
                    setViewMatrix(osg::Matrixd::lookAt(m_horiCamPos.m_eye, m_horiCamPos.m_center, m_horiCamPos.m_up));
                }
                else
                {
                    setViewport(m_vertViewport.toOsgViewport());
                    setViewMatrix(osg::Matrixd::lookAt(m_vertCamPos.m_eye, m_vertCamPos.m_center, m_vertCamPos.m_up));
                }
                m_theme = l_curThemeType;
            }
        }

        if (m_framework->m_viewModeReceiver.isConnected())
        {
            if (true == m_framework->m_viewModeReceiver.hasData())
            {
                // check the current state machine state, if in
                // cc::core::CustomFramework*       l_framework    = m_framework->asCustomFramework();
                // const cc::daddy::VMStateDaddy_t* l_vmStateDaddy = l_framework->m_ViewModeState_ReceiverPort.getData();
                // Check the logic for showing in parking view
                // bool l_showCar = false;
                // if (l_framework->m_ParkDispLowPolyModelStsReceiver.hasData())
                // {
                //     l_showCar = l_framework->m_ParkDispLowPolyModelStsReceiver.getData()->m_Data.m_isCarAvailabe;
                // }
                // if (l_vmStateDaddy != nullptr)
                // {
                //     if ((EScreenID_HORI_PARKING_FRONT == l_vmStateDaddy->m_Data.mode ||
                //          EScreenID_HORI_PARKING_REAR == l_vmStateDaddy->m_Data.mode ||
                //          EScreenID_VERT_PARKING_FRONT == l_vmStateDaddy->m_Data.mode ||
                //          EScreenID_VERT_PARKING_REAR == l_vmStateDaddy->m_Data.mode) &&
                //         l_showCar)
                //     {
                //         setAssetValue(cc::core::AssetId::EASSETS_VEHICLE, true); // PRQA S 3803
                //         setResetCullMask(m_cullMask);
                //     }
                //     else
                //     {
                //         setAssetValue(cc::core::AssetId::EASSETS_VEHICLE, false); // PRQA S 3803
                //         setResetCullMask(~0u);
                //     }
                // }
            }
        }
        // ParkViewBase::updateParkWaitIcon();
    }
    ParkViewBase::traverse(f_nv);
}

ParkInView::~ParkInView() = default;

} // namespace parkview
} // namespace views
} // namespace cc
