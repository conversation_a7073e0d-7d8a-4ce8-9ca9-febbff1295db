//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================

#ifndef CC_VIEWS_CALLBACK_TRAILER_TRAJECTORY_UPDATE_CALLBACK_H
#define CC_VIEWS_CALLBACK_TRAILER_TRAJECTORY_UPDATE_CALLBACK_H

#include <osg/NodeCallback>
#include <osg/NodeVisitor>

#include <vfc/core/vfc_types.hpp>

namespace pc
{
namespace core
{
class Framework;
class View;
} // namespace core
} // namespace pc

namespace cc
{
namespace views
{
namespace callback
{

//======================================================
// TrailerTrajectoryUpdateCallback
//------------------------------------------------------
/// (DEPRECATED) updates the camera depending on the calibration
/// check calibration changes and set the
/// virtual camera position to match the front SatCam
/// <AUTHOR> Jose
//======================================================
class TrailerTrajectoryUpdateCallback : public osg::NodeCallback
{
public:
    TrailerTrajectoryUpdateCallback(pc::core::Framework* f_pFramework);

    virtual void operator()(osg::Node* f_node, osg::NodeVisitor* f_nv) override;

protected:
    virtual ~TrailerTrajectoryUpdateCallback() = default;

private:
    void setEnable(pc::core::View* f_view, vfc::int32_t f_assetId, bool f_enable);
    void onToggleTrailerMode(pc::core::View* f_view);

private:
    pc::core::Framework* m_pFramework;
    bool                 m_initialized   = false;
    bool                 m_isTrailerMode = false;
};

} // namespace callback
} // namespace views
} // namespace cc

#endif /* CC_VIEWS_CALLBACK_TRAILER_TRAJECTORY_UPDATE_CALLBACK_H */
