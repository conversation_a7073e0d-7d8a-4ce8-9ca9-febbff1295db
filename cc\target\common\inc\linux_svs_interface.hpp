//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: LIA1LR David Liepelt (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  linux_svs_interface.hpp
/// @brief
//=============================================================================

#ifndef CC_TARGET_COMMON_LINUXIPCINTERFACE_H
#define CC_TARGET_COMMON_LINUXIPCINTERFACE_H

#include "vfc/core/vfc_types.hpp"
#include <cstdint> //! required by pp/rpb_* headers
#ifndef TARGET_STANDALONE
#include "valin/inc/valin_types_api.hpp"
#endif

#include "pc/svs/c2w/inc/SatCam.h"

#include "cc/target/common/inc/BccReadInterface.h"
#include "cc/target/common/inc/BccWriteInterface.h"
#include "cc/target/common/inc/ttactl_output_svs.h"
#include "cc/target/common/inc/LcfInterface.h"
#include "cc/target/common/inc/ParkInterface.hpp"
#include "cc/target/common/inc/tic/timestamp_types.hpp"
#ifndef TARGET_ANDROID
#include "cc/target/linux/inc/ServiceCallBack.h"
#else
#include "cc/target/android/inc/ServiceCallBack.h"
#endif

#define USE_RADAR_WALL 1


#define RBP_CC_TPC_MAX_NUM_PATHPOINTS  13u
namespace cc
{
namespace target
{
namespace common
{
// HU resolution
constexpr vfc::float32_t HORI_X_SCALE_FACTOR = 1920.0f / 2560.0f;  //0.75
constexpr vfc::float32_t HORI_Y_SCALE_FACTOR = 1080.0f / 1440.0f;  //0.75
constexpr vfc::float32_t VERT_X_SCALE_FACTOR = 1080.0f / 1440.0f;  //0.75
constexpr vfc::float32_t VERT_Y_SCALE_FACTOR = 1792.0f / 2440.0f;  //0.7344


enum EPasStatus : vfc::uint8_t
{
  PAS_Off               = 0,
  PAS_Standby           = 1,
  PAS_FrontRearActive   = 2,
  PAS_FActiveRFailure   = 3,
  PAS_RActiveFFailure   = 4,
  PAS_SystemFailure     = 5,
  PAS_Reversed          = 6
};

enum ESdwStatus : vfc::uint8_t
{
  SDWSTS_Off     = 0,
  SDWSTS_Standby = 1,
  SDWSTS_Active  = 2,
  SDWSTS_Failure = 3
};

enum EPASWarnToneR2L : vfc::uint8_t
{
  SOUND_OFF              = 0,
  SOUND_LONG_BEEP        = 1,
  SOUND_Fast             = 2,
  SOUND_Medium           = 3,
  SOUND_Slow             = 4,
  SOUND_Mute             = 5
};

enum EPARKTypeR2L : vfc::uint8_t
{
  PARKTYPE_None         = 0,
  PARKTYPE_RPA          = 1,
  PARKTYPE_APA          = 2
};

enum EPARKTypeVariantR2L : vfc::uint8_t
{
  PARKTYPECONFIG_NONE    = 0,  // both apa and rpa not support
  PARKTYPECONFIG_APA     = 1,  // support apa only
  PARKTYPECONFIG_RPA     = 2,  // support rpa only
  PARKTYPECONFIG_APA_RPA = 3   // support both apa and rpa
};

enum EPARKStatusR2L : vfc::uint8_t
{
  PARK_Off              = 0,
  PARK_Standby          = 1,
  PARK_Searching        = 2,
  PARK_Guidance_active  = 3,
  PARK_Guidance_suspend = 4,
  PARK_Terminated       = 5,
  PARK_Completed        = 6,
  PARK_Failure          = 7,
  PARK_AssistStandby    = 8  // confirming
};

enum EPARKModeR2L : vfc::uint8_t
{
  PARKMODE_None                   = 0,
  PARKMODE_PPSC_Park_In           = 1,
  PARKMODE_CPSC_Park_In           = 2,
  PARKMODE_Park_Out               = 3
};

enum EPARKSideR2L : vfc::uint8_t
{
  PARKSIDE_None         = 0,
  PARKSIDE_Left         = 1,
  PARKSIDE_Right        = 2
};

enum EPARKFunctionIndR2L : vfc::uint8_t
{
  PARKFUC_Inactive      = 0,
  PARKFUC_cPSC          = 1,
  PARKFUC_pPSC          = 2,
  PARKFUC_cPOC          = 3,
  PARKFUC_pPOC          = 4,
  PARKFUC_Remote_cPSC   = 5,
  PARKFUC_Remote_pPSC   = 6,
  PARKFUC_Remote_cPOC   = 7,
  PARKFUC_Remote_pPOC   = 8
};

enum EPARKQuitIndR2L : vfc::uint8_t
{
  PARKQUIT_None                           =0,
  PARKQUIT_EPSFailure                     =1,
  PARKQUIT_ESCFailure                     =2,
  PARKQUIT_SCUFailure                     =3,
  PARKQUIT_SASFailure                     =4,  // not used
  PARKQUIT_APAFailure                     =5,
  PARKQUIT_InvalidVehicleSpeed            =6,
  PARKQUIT_RadarDirty                     =7,
  PARKQUIT_SASNotCalibrated               =8,  // not used
  PARKQUIT_ABSTCSESPACCAEBActive          =9,
  PARKQUIT_EPBActive                      =10,
  PARKQUIT_DriverOverride                 =11,
  PARKQUIT_ChargingGunActive              =12,
  PARKQUIT_SpaceIsLimitedInParkOutMode    =13,
  PARKQUIT_CurrentStepNumberOverThreshold =14,
  PARKQUIT_InterruptNumberOverThreshold   =15,
  PARKQUIT_APSTimeout                     =16,
  PARKQUIT_VehicleSpeedOverthreshold      =17,
  PARKQUIT_RoutePlanningFailure           =18,
  PARKQUIT_BluetoothDisconnectionTimeout  =19,  // only for RPA, not in Parkui
  PARKQUIT_EPBFailure                     =20,
  PARKQUIT_SeatBeltUnbuckle               =21,
  PARKQUIT_ExcessiveSlope                 =22,
  PARKQUIT_VehicleBlock                   =23,
  PARKQUIT_SurroundView                   =24,
  PARKQUIT_DoorLock                       =25,  // only for RPA, not in Parkui
  PARKQUIT_TBOXFailureRPA                 =26,  // only for RPA, not in Parkui
  PARKQUIT_RemoteDeviceReasonRPA          =27,  // only for RPA, not in Parkui
  PARKQUIT_UnsafeBehavior                 =28,  // only for RPA, not in Parkui
  PARKQUIT_DoorOpen                       =29,
  PARKQUIT_ExternalECUFailure             =30,
  PARKQUIT_OtherReason                    =31,
};

enum EPARKQuitIndR2LExt : vfc::uint8_t
{
  PARKQUITExt_Nofault                      = 0,
  PARKQUITExt_DisusHeightAdjust            = 1,
  PARKQUITExt_CurrentDiSusHeightNotSupport = 2,
  PARKQUITExt_TrailerHitchConnected        = 3,
  PARKQUITExt_TemperatureSensorFault       = 4,
  PARKQUITExt_ResponseTimeout              = 5,
  PARKQUITExt_BrakeSignalAbnormal          = 6,
  PARKQUITExt_LowerTirePressure            = 7,
  PARKQUITExt_CollisionHappened            = 8,
  PARKQUITExt_DriveModeUnsuitable          = 9,
  PARKQUITExt_Reserved                     = 10
};

enum EPARKRecoverIndR2L : vfc::uint8_t
{
  PARKREC_NoPrompt            = 0,
  PARKREC_PauseCommand        = 1,
  PARKREC_ObjectOnPath        = 2,
  PARKREC_DoorOpen            = 3,
  PARKREC_BrakePadal          = 4,  // not used
  PARKREC_MirrorFold          = 5,
  PARKREC_SeatBelt            = 6,  // not used
  PARKREC_BlueTooth           = 7,  // only for RPA, not in Parkui
  PARKREC_HoodOpen            = 8,
  PARKREC_TrunkOpen           = 9,
  PARKREC_RearSideCommingCar  = 10,  // not used
  PARKREC_FrontSideCommingCar = 11  // not used
};

enum EPARKDriverIndR2L : vfc::uint8_t
{
  PARKDRV_NoRequest             = 0,
  PARKDRV_ExpandedMirror        = 1,
  PARKDRV_GearD                 = 2,
  PARKDRV_SlowDown              = 3,
  PARKDRV_SearchingProcess      = 4,
  PARKDRV_Stop                  = 5,
  PARKDRV_ConnectPhone          = 6,
  PARKDRV_EPBApplied            = 7,
  PARKDRV_LeaveCar              = 8,
  PARKDRV_CloseTrunk            = 9,
  PARKDRV_SmallParkSlot         = 10,
  PARKDRV_CloseDoor             = 11,
  PARKDRV_SeatBelt              = 12,
  PARKDRV_SurroundView          = 13,
  PARKDRV_PressBrakePedal       = 14,
  PARKDRV_Confirm_Press_DM      = 15,
  PARKDRV_ReleaseBrake          = 16,
  PARKDRV_ProcessBar            = 17,
  PARKDRV_FunctionOff           = 18,
  PARKDRV_POCDirecSelect        = 19,
  PARKDRV_NoFrontObjectDetected = 20,
  PARKDRV_PSSelection           = 21,
  PARKDRV_ResponseTimeout       = 22,
  PARKDRV_ExternalECUError      = 23,
  PARKDRV_CloseHood             = 24,
  PARKDRV_HeavyRain             = 26,
  PARKDRV_Notice_Rolling        = 28
};

enum EPARKDriverIndExtR2L : vfc::uint8_t
{
  PARKDRVEXT_NoRequest                           = 0,
  PARKDRVEXT_PayAttentionToSurrounding           = 1,
  PARKDRVEXT_ParkingHasBeenCompleted             = 2,
  PARKDRVEXT_ConfirmTheParkingDirection          = 3,
  PARKDRVEXT_ParkingOutPayAttentionToSurrounding = 4,
  PARKDRVEXT_FrontIsClear                        = 5,
  PARKDRVEXT_Reserved                            = 6,
  PARKDRVEXT_SelectTheParkingMode                = 7,
  PARKDRVEXT_ConnectToBluetooth                  = 8,
  PARKDRVEXT_CleanTheCamera                      = 9
};

enum EPARKDriverIndSearchR2L : vfc::uint8_t
{
  PARKDRVSEARCH_NoRequest                       = 0,
  PARKDRVSEARCH_SlotSearching                   = 1,
  PARKDRVSEARCH_WaitingForVehicleStop           = 2,
  PARKDRVSEARCH_WaitForVehicleSlowdown          = 3,
  PARKDRVSEARCH_WaitForDriverOperateGear        = 4,  // WaitForDriverBrakePedal
  PARKDRVSEARCH_WaitForVehicleDriverConfirmPark = 5
};

enum EPARKSlotStsR2L : vfc::uint8_t
{
  PARKSLOT_NONE        = 0,
  PARKSLOT_INVALID     = 1,
  PARKSLOT_VALID       = 2,
  PARKSLOT_SELECTABLE  = 3,
  PARKSLOT_SELECTED    = 4
};

enum EFCTALeftWarnLevel : vfc::uint8_t
{
  FCTA_LEFT_NONE     = 0,
  FCTA_LEFT_LV1,
  FCTA_LEFT_LV2
};

enum EFCTARightWarnLevel : vfc::uint8_t
{
  FCTA_RIGHT_NONE     = 0,
  FCTA_RIGHT_LV1,
  FCTA_RIGHT_LV2
};

enum ERCTALeftWarnLevel : vfc::uint8_t
{
  RCTA_LEFT_NONE     = 0,
  RCTA_LEFT_LV1,
  RCTA_LEFT_LV2
};

enum ERCTARightWarnLevel : vfc::uint8_t
{
  RCTA_RIGHT_NONE     = 0,
  RCTA_RIGHT_LV1,
  RCTA_RIGHT_LV2
};

enum EPARKObjectExistR2L : vfc::uint8_t
{
  PARKOBJ_NotExist           = 0,
  PARKOBJ_Exist              = 1,
  PARKOBJ_Invalid            = 2
};

//ParkSlot_st
constexpr vfc::uint8_t    g_parkSlotQuantity  = 20;  // 10=6+2+2 per side
constexpr vfc::uint8_t    g_parkSlotCommandQuantity  = 20;
constexpr vfc::uint8_t    g_fusionObjectQuantity        = 50u; //Fusion will give us no more than 50 objects information
constexpr vfc::uint8_t    g_pedestrianDisplayQuantity   = 5u; //Considering our SVS screen area is limited, so we might not display too many pedestrian objects

struct ParkSlotPoint_st
{
    vfc::int16_t    m_x;    //unit: 2^(-10) m, default = maximum value
    vfc::int16_t    m_y;    //unit: 2^(-10) m, default = maximum value
    vfc::int16_t    m_phi;  //unit: 2^(-12) rad, default = maximum value
};

struct ParkSlotPoint_cm_st
{
    vfc::int16_t    m_x;    //unit: cm, default = maximum value
    vfc::int16_t    m_y;    //unit: cm, default = maximum value
    vfc::int16_t    m_phi;  //unit: 2^(-12) rad, default = maximum value
};

struct ParkSlotPostion_st
{
    ParkSlotPoint_st           m_slotRearAxelCenter;
    ParkSlotPoint_cm_st        m_slotCorner1;
    ParkSlotPoint_cm_st        m_slotCorner2;
    //vfc::CSI::si_metre_f32_t   m_slotDepth;
};

enum EParkSlotAvailableStatus: vfc::uint8_t
{
    PARKSLOTAVAIL_UNKNOWN        = 0,
    PARKSLOTAVAIL_OCCUPIED       = 1,
    PARKSLOTAVAIL_NOT_SELECTABLE = 2,  // slot is available, but not selectable since path planning is not OK.
    PARKSLOTAVAIL_SELECTABLE     = 3,
    PARKSLOTAVAIL_SELECTED       = 4
};

enum EParkSlotOrientationType: vfc::uint8_t
{
    PARKSLOTTYPE_UNKNOWN   = 0,
    PARKSLOTTYPE_CROSS     = 1,
    PARKSLOTTYPE_PARALLEL  = 2,
    PARKSLOTTYPE_DIAGONAL  = 3,
    PARKSLOTTYPE_UNIVERSAL = 4
};

enum EParkSlotEntryType: vfc::uint8_t
{
    PARKSLOTENTRY_BACKWARD_IN          = 0,
    PARKSLOTENTRY_FORWARD_IN           = 1,
    PARKSLOTENTRY_BACKWARD_FORWARD_IN  = 2,
    PARKSLOTENTRY_BACKWARD_OUT         = 3,
    PARKSLOTENTRY_FORWARD_OUT          = 4,
    PARKSLOTENTRY_BACKWARD_FORWARD_OUT = 5
};

struct ParkSlot_st
{
    vfc::uint16_t                m_parkSlotId;            // Parking slot number
    //EParkSlotStyleType           m_parkSlotStyleType;         // Line parking space, spatial parking space
    //EParkSlotTargetUser          m_parkSlotTargetUser;        // General, women-only, disabled, panda, Mechanical stereo parking space, lift parking
    EParkSlotAvailableStatus     m_parkSlotAvailableStatus;   //occupied, available but not selectable, selectable, selected... default = NONE
    EParkSlotOrientationType     m_parkSlotOrientationType;   //vertical, parallel, diagonal... default = NONE
    EParkSlotEntryType           m_parkSlotEntryType;         //frontin, rearin ... default = NONE
    vfc::uint16_t                m_parkSlotLength;            // Length of parking space
    vfc::float32_t               m_parkSlotDirectionAngle;    // Parking direction, horizontal 0, vertical 90
    ParkSlotPostion_st           m_parkSlotPosition;
    //ParkSlotPlanning_st          m_parkSlotPlanning;
    //EParkSlotOccupiedInfo        m_parkSlotOccupiedInfo;      // Wheel stopper, ground lock, cone, obstacle
    //EParkSlotOccupiedVehicleDirtection      m_parkSlotOccupiedVehicleDirtection;   // Direction of vehicle in occupied parking space
};

struct Position_st
{
    vfc::CSI::si_metre_f32_t  m_x;
    vfc::CSI::si_metre_f32_t  m_y;
};

// Object's velocity in ego vehicle coordinates as a x/y vector in meters per second (as float32)
struct Velocity_st
{
    vfc::CSI::si_metre_per_second_f32_t  m_x_v;
    vfc::CSI::si_metre_per_second_f32_t  m_y_v;
};

struct FusionObject_st
{
    vfc::uint32_t                                                             m_objectID;                                            //!< Object ID
    Position_st                                                               m_position;                                            //!< Reference point of object
    vfc::linalg::TMatrix22<vfc::CSI::si_square_metre_f32_t>                   m_positionCovariance;  //!< Covariance for position
    Velocity_st                                                               m_velocity;                                            //!< Estimated object absolute velocity
    vfc::linalg::TMatrix22<vfc::CSI::si_square_metre_per_square_second_f32_t> m_velocityCovariance;  //!< Covariance for velocity
    vfc::CSI::si_percent_f32_t                                                m_confidenceObject;                                          //!< Object confidence
    vfc::CSI::si_percent_f32_t                                                m_confidencePedestrian;                               //!< Probability that the object is a pedestrian
    vfc::CSI::si_percent_f32_t                                                m_confidenceDynamic;                                  //!< Probability that the object is dynamic
    vfc::CSI::si_micro_second_ui32_t                                          m_timestampLastUpdated;                               //!< Timestamp of the object's last update
};

//Fusion to SVS
struct StrippedFusionObject_st
{
    FusionObject_st  m_fusionObject[g_fusionObjectQuantity];
};

struct CollisionStatus_st
{
    bool m_front_b;
    bool m_rear_b;
    bool m_side_left_b;
    bool m_side_right_b;
    bool m_front_corner_left_b;
    bool m_front_corner_right_b;
    bool m_rear_corner_left_b;
    bool m_rear_corner_right_b;
};

//Sitocp to SVS
struct StrippedSitOcp_st
{
    bool                       m_valid_b;         //!< True there is a valid prediction, False if no valid prediction
    vfc::uint32_t              m_objectID;        //!< The ID of the critical object
    vfc::CSI::si_second_f32_t  m_ttc;             //!< TTC time until collision between ego and the object [s]
    CollisionStatus_st         m_collisionStatus; //!< Ego contour segment where the predicted collision will happen
};

//Pedestrian position
struct PedestrianPosition_st
{
    vfc::CSI::si_metre_f32_t  m_x;
    vfc::CSI::si_metre_f32_t  m_y;
    vfc::CSI::si_radian_f32_t m_phi;
};

//Pedestrian interface in virtual reality view
struct PedestrianObj_st
{
    bool                   m_isShow;        //!< This flag is for highlighting displaying this pedestrian on screen or not
    vfc::uint32_t          m_objectID;      //!< Object ID
    PedestrianPosition_st  m_pedesPosition; //!< Reference point of object
    bool                   m_criticalObj;   //!< Most Pedestrian most likely to collide
};

struct SelectedPedestrianObj_st
{
    PedestrianObj_st  m_selectedPedestrian[g_pedestrianDisplayQuantity];
};

constexpr int l_No_of_Lsmg_Parameters = 4;

struct LSMGDataSVS_st
{
  // Object Distance from the vehicle contour comprising accessories such as towbar, spare wheel etc.
  vfc::uint16_t LsmgDistanceDataFront[l_No_of_Lsmg_Parameters];
  vfc::uint16_t LsmgDistanceDataRear[l_No_of_Lsmg_Parameters];
  vfc::uint16_t LsmgDistanceDataRight[l_No_of_Lsmg_Parameters];
  vfc::uint16_t LsmgDistanceDataLeft[l_No_of_Lsmg_Parameters];

  // ThreatPresent
  bool LsmgThreatPresentFront[l_No_of_Lsmg_Parameters];
  bool LsmgThreatPresentRear[l_No_of_Lsmg_Parameters];
  bool LsmgThreatPresentLeft[l_No_of_Lsmg_Parameters];
  bool LsmgThreatPresentRight[l_No_of_Lsmg_Parameters];

  // pas & sdw status
  EPasStatus   m_pasStatus;
  ESdwStatus   m_sdwStatus;
  bool         m_sdwStatusF;       // Right[0], Left[3]
  bool         m_sdwStatusFM;      // Right[1], Left[2]
  bool         m_sdwStatusRM;      // Right[2], Left[1]
  bool         m_sdwStatusR;       // Right[3], Left[0]
  bool         m_pasButtonStatus;
  EPASWarnToneR2L m_paswarntone;
};
#if USE_RADAR_WALL
struct PasAPPDataSVS_st
{
  static constexpr int MAX_NUM_DISPLAY_AREAS = 12;

  struct CDispAreaDist
  {
    vfc::uint16_t m_dist2Veh[MAX_NUM_DISPLAY_AREAS] {255u,255u,255u,255u,255u,255u,255u,255u,255u,255u,255u,255u};
    vfc::uint16_t m_dist2VehHighObj[MAX_NUM_DISPLAY_AREAS] {255u,255u,255u,255u,255u,255u,255u,255u,255u,255u,255u,255u};
    vfc::uint16_t m_dispAreaDistOverall_Dist2Veh;
  };

  CDispAreaDist m_front;
  CDispAreaDist m_right;
  CDispAreaDist m_rear;
  CDispAreaDist m_left;
};
#else
struct PasAPPDataSVS_st
{
  //All object Distance
  vfc::uint16_t PasAPPDistanceAllObjDataFront[l_No_of_Lsmg_Parameters];
  vfc::uint16_t PasAPPDistanceAllObjDataRear[l_No_of_Lsmg_Parameters];
  vfc::uint16_t PasAPPDistanceAllObjDataRight[l_No_of_Lsmg_Parameters];
  vfc::uint16_t PasAPPDistanceAllObjDataLeft[l_No_of_Lsmg_Parameters];

  // ThreatPresent
  bool PasAPPThreatPresentFront[l_No_of_Lsmg_Parameters];
  bool PasAPPThreatPresentRear[l_No_of_Lsmg_Parameters];
  bool PasAPPThreatPresentLeft[l_No_of_Lsmg_Parameters];
  bool PasAPPThreatPresentRight[l_No_of_Lsmg_Parameters];
};
#endif
enum class EGearBoxType : vfc::uint8_t
{
  GEARBOX_NONE = 0,
  GEARBOX_MANUAL = 1,
  GEARBOX_AUTOMATIC = 2
};

enum class EGearStatus : vfc::uint8_t
{
  GEAR_PARK = 0,
  GEAR_REVERSE = 1,
  GEAR_NEUTRAL = 2,
  GEAR_DRIVE = 3,
  GEAR_NOSIGNAL = 4,
};

enum class EVehicleDrvDir : vfc::uint8_t
{
    DRVDIR_VOID = 0,
    DRVDIR_FWD = 1,
    DRVDIR_BWD = 2,
    DRVDIR_UNKNOWN = 15
};

enum class EIndicatorStatus : vfc::uint8_t
{
    INDICATE_RIGHT      = 0,
    INDICATE_LEFT       = 1,
    INDICATE_IDLE       = 2,
    INDICATE_WARNING    = 3
};

enum class EVehMoveDir : vfc::uint8_t
{
    FORWARD     = 0,
    BACKWARD    = 1,
    UNKNOWN     = 2,
    STANDSTILL  = 3,
};


struct CPfDoorAndMirrorStripped
{
  vfc::uint8_t m_stateDoorSwitchFrontLeft;
  vfc::uint8_t m_stateDoorSwitchFrontRight;
  vfc::uint8_t m_stateDoorSwitchRearLeft;
  vfc::uint8_t m_stateDoorSwitchRearRight;
  vfc::uint8_t m_stateExteriorMirrorLeft;
  vfc::uint8_t m_stateExteriorMirrorRight;
  vfc::uint8_t m_mirrFldState;
  vfc::uint8_t m_statusContactBootLid ;
  vfc::uint8_t m_statusContactFrontLid;
  vfc::uint8_t m_statusTrunk;
};

#ifndef TARGET_STANDALONE
struct CPfOdometry
{
    vfc::CSI::si_metre_per_second_f32_t        m_vehicleVelocity;
    vfc::CSI::si_newton_metre_f32_t            m_brktrq;
    vfc::CSI::si_metre_per_square_second_f32_t m_longitudinalAcceleration;
    vfc::CSI::si_metre_per_square_second_f32_t m_lateralAcceleration;
    //! TODO fix missing vfc::CSI type support
    //vfc::CSI::si_degree_per_second_f32_t       m_yawRate;
    tic::CGlobalTimestamp                      m_wheelImpCtrFLTimestamp;
    tic::CGlobalTimestamp                      m_wheelImpCtrFRTimestamp;
    tic::CGlobalTimestamp                      m_wheelImpCtrRLTimestamp;
    tic::CGlobalTimestamp                      m_wheelImpCtrRRTimestamp;
    vfc::uint16_t                              m_wheelImpCtrFL;
    vfc::uint16_t                              m_wheelImpCtrFR;
    vfc::uint16_t                              m_wheelImpCtrRL;
    vfc::uint16_t                              m_wheelImpCtrRR;
    EVehicleDrvDir                             m_vehicleDrvDir;
    valin::EWheelDrvDir                        m_wheelDrvDirFL;
    valin::EWheelDrvDir                        m_wheelDrvDirFR;
    valin::EWheelDrvDir                        m_wheelDrvDirRL;
    valin::EWheelDrvDir                        m_wheelDrvDirRR;
    vfc::uint16_t                              m_wheelRotationFL;
    vfc::uint16_t                              m_wheelRotationFR;
    vfc::uint16_t                              m_wheelRotationRL;
    vfc::uint16_t                              m_wheelRotationRR;
    valin::EWheelRotationQualifier             m_wheelRotationFLQualifier;
    valin::EWheelRotationQualifier             m_wheelRotationFRQualifier;
    valin::EWheelRotationQualifier             m_wheelRotationRLQualifier;
    valin::EWheelRotationQualifier             m_wheelRotationRRQualifier;
};

#endif
struct CValInOutputPfStripped
{
  // CPfDoorAndMirror
  CPfDoorAndMirrorStripped                   m_pfDoorAndMirror;

  // CPfGear
  EGearBoxType                               m_gearBoxType;
  EGearStatus                                m_gearStatus;

  // CPfSteering
  vfc::CSI::si_degree_f32_t                  m_frontWheelAngle;
  vfc::CSI::si_degree_f32_t                  m_driverSteeringWheelAngle;
  vfc::CSI::si_degree_f32_t                  m_rearWheelAngle;

  // CPfOdometry
  vfc::CSI::si_metre_per_second_f32_t        m_vehicleVelocity;
  EVehicleDrvDir                             m_vehicleDrvDir;

  // CPfVehicleInfo
  vfc::uint8_t                               m_ignSwState;
  EIndicatorStatus                           m_indicatorStatus;
#ifndef TARGET_STANDALONE
  CPfOdometry                                m_pfOdometry;
#endif
};

//! C2W Signal
enum class EGCMCameraAvailability : vfc::uint32_t
{
    GCM_SLEEPING  = 0,      //!< Calibration is sleeping (camera not in position (due to folded mirror or open trunk, etc) or input signals not available)
    GCM_AWAKE     = 1,      //!< Calbiration is awake.
    GCM_ERROR     = 2       //!< Calibration in error (due to invalid input)
};

enum class EGCMCalibrationState  : vfc::uint32_t
{
    GCM_NOT_CALIBRATED                  = 0,    //!< Camera not calibrated.
    GCM_PARTIALLY_CALIBRATED            = 1,    //!< Camera partially calibrated (i.e. camera was calibrated in a previous cycle).
    GCM_FULLY_CALIBRATED                = 2,    //!< Camera fully calibrated.
    GCM_DETERMINING_CALIBRATION_QUALITY = 3     //!< Camera calibration not yet started (only intial state).
};

enum class EGCMCalibrationError  : vfc::uint32_t
{
    GCM_CALIB_ERROR_NONE       = 0, //!< No calibration error.
    GCM_CALIB_ERROR_TIMEOUT    = 1, //!< Calibration timeout error (took to long to reach FULLY_CALIBRATED).
    GCM_CALIB_ERROR_DEVIATION  = 2  //!< Calbration deviation error (calibration state reached FULLY_CALIBRATED, but angles deviate significantly from expected values).
};

//Turn light signal
enum class EturnSignalstatus_ : vfc::uint8_t
{
    Turn_Signal_INVALID = 0u,
    Turn_Signal_NOTWORKING = 1u,
    Turn_Signal_LeftTurnSignalLightNormalFlashing = 2u,
    Turn_Signal_LeftTurnSignalLightFlashingQuickly = 3u,
    Turn_Signal_RightTurnSignalLightFlashingNormally = 4u,
    Turn_Signal_RightTurnSignalLightFaultyFlashingFastWarningSignal = 5u,
    Turn_Signal_DangerWarningSignal = 6u,
    Turn_Signal_EmergencyBrakingSignal = 7u,
    Turn_Signal_RearEndCollisionWarningSignal = 8u,
    Turn_Signal_NormalFlashing = 9u,
    Turn_Signal_NormalFlashing_Reserved_0xA = 10u,
    Turn_Signal_NormalFlashing_Reserved_0xB = 11u,
    Turn_Signal_NormalFlashing_Reserved_0xC = 12u,
    Turn_Signal_NormalFlashing_Reserved_0xD = 13u,
    Turn_Signal_NormalFlashing_Reserved_0xE = 14u,
    Turn_Signal_NormalFlashing_Reserved_0xF = 15u
};

struct StrippedCalibStatus_st
{
  EGCMCameraAvailability    m_camAvailability;
  EGCMCalibrationState      m_calibrationState;
  EGCMCalibrationError      m_calibrationError;
};

struct NominalCalibValues_st
{
  vfc::CSI::si_milli_metre_f32_t     m_camPositionX_mm_f32;
  vfc::CSI::si_milli_metre_f32_t     m_camPositionY_mm_f32;
  vfc::CSI::si_milli_metre_f32_t     m_camPositionZ_mm_f32;
  vfc::CSI::si_degree_f32_t          m_camNominalRoll_deg_f32;
  vfc::CSI::si_degree_f32_t          m_camNominalPitch_deg_f32;
  vfc::CSI::si_degree_f32_t          m_camNominalYaw_deg_f32;
};

struct StrippedCpjPwrTm
{
  vfc::uint8_t m_brakeLampOnStatus;
};

enum class EThemeTypeDayNight : vfc::uint8_t
{
  ETHEME_TYPE_DAYNIGHT_INVALID = 0,
  ETHEME_TYPE_DAYNIGHT_NIGHT = 1,
  ETHEHE_TYPE_DAYNIGHT_DAY = 2,
  ETHEME_TYPE_DAYNIGHT_RESERVE = 3
};

struct StrippedCpjEnvVeh
{
  bool m_mainBeamIndication;
  //bool m_alsDippedBeamReqB;
  bool m_lowBeamIndication;
  vfc::uint8_t m_carmode;
  vfc::uint8_t m_hazardLightState;
  vfc::uint8_t m_leftIndicatorBlinkState;
  vfc::uint8_t m_rightIndicatorBlinkState;
  vfc::uint8_t m_rearPosLightState;
  vfc::uint8_t m_midTailPosLightState;
  vfc::uint8_t m_dynIndicatorEnable;
  vfc::uint8_t m_daytimeRunningLights;
  bool         m_leftHeadLightState;
  bool         m_rightHeadLightState;
  bool         m_Trailer_Assist_Mode_S;
  vfc::uint8_t m_turnSignalStatus;
  vfc::uint8_t m_fogLightStatus;
  vfc::uint8_t m_frontClearanceLightState;
  vfc::uint8_t m_rearClearanceLightState;
  vfc::uint8_t m_frontLeftCornerLightState;
  vfc::uint8_t m_frontRightCornerLightState;
  vfc::uint8_t m_frontPosLightState;
  vfc::uint8_t m_reverseLightState;
  EThemeTypeDayNight m_dayNightThemeStatus;
};


// FlexRay Signal
enum class ELSMGActivationSetReq : vfc::uint8_t
{
    LSMG_ACTIVATIONSETREQ_NOREQUEST = 0u,
    LSMG_ACTIVATIONSETREQ_STANDARD  = 1u,
    LSMG_ACTIVATIONSETREQ_AUTOMATIC = 2u,
    LSMG_ACTIVATIONSETREQ_SMART     = 3u
};

// TransparentMode Signal
enum class ETransparentMode : vfc::uint8_t
{
    INVAILD               = 0u,
    HALF_TRANSPARENT      = 1u,
    COMPLETE_TRANSPARENT  = 2u,
    CRYSTAL_TRANSPARENT   = 3u
};

enum class ESvsVehTransSts : vfc::uint8_t
{
  SVS_VEH_TRANS_INVALID = 0u,
  SVS_VEH_TRANS_OPEN    = 1u,
  SVS_VEH_TRANS_CLOSE   = 2u,
  SVS_VEH_TRANS_RESERVED= 3u
};

// EhuRadarWallButton Signal
enum class EhuRadarWallButton : vfc::uint8_t
{
    RADARWALL_INVAILD      = 0u,
    CLOSERADARWALL        = 1u,
    OPENRADARWALL         = 2u,
    RADARWALL_RESERVE               = 3u
};

enum class EHUDisplayMode5x : vfc::uint8_t
{
  INVALID = 0u,
  VERTICAL_PLANVIEW_FRONT_ENLARGE__FRONT_MAIN_VIEW__LEFT_SIDE_VIEW = 1u,
  VERTICAL_PLANVIEW_FRONT_ENLARGE__FRONT_MAIN_VIEW__RIGHT_SIDE_VIEW = 2u,
  VERTICAL_PLANVIEW_REAR_ENLARGE__REAR_MAIN_VIEW__LEFT_SIDE_VIEW = 3u,
  VERTICAL_PLANVIEW_REAR_ENLARGE__REAR_MAIN_VIEW__RIGHT_SIDE_VIEW = 4u,
  VERTICAL_FULLSCREEN = 5U,
  VERTICAL_FULLSCREEN_ENLARGE_FRONT = 6U,
  VERTICAL_FULLSCREEN_ENLARGE_REAR = 7U,
  VERTICAL_PLANVIEW__DUAL_FRONT_WHEEL_VIEW__FRONT_SIDE_VIEW = 8U,
  VERTICAL_PLANVIEW__DUAL_FRONT_WHEEL_VIEW__REAR_SIDE_VIEW = 9U,
  VERTICAL_PLANVIEW__DUAL_REAR_WHEEL_VIEW__FRONT_SIDE_VIEW = 10U,
  VERTICAL_PLANVIEW__DUAL_REAR_WHEEL_VIEW__REAR_SIDE_VIEW = 11U,
  VERTICAL_PLANVIEW__DUAL_WHEEL_VIEW__FRONT_SIDE_VIEW = 12U,
  VERTICAL_PLANVIEW__DUAL_WHEEL_VIEW__REAR_SIDE_VIEW = 13U,
  HORIZONTAL_PLANVIEW_FRONT_ENLARGE = 14U,
  HORIZONTAL_PLANVIEW_REAR_ENLARGE = 15U,
  HORIZONTAL_PLANVIEW__FRONT_VIEW__LEFT_SIDE_FRONT_VIEW__RIGHT_SIDE_FRONT_VIEW = 16U,
  HORIZONTAL_PLANVIEW__REAR_VIEW__LEFT_SIDE_REAR_VIEW__RIGHT_SIDE_REAR_VIEW = 17U,
  HORIZONTAL_SURROUND_VIEW = 18U
};

struct StrippedCrabGuideline_st
{
    bool m_isVisible{false};
    // bool m_isGearVisible{false};
    vfc::float32_t m_angle{0.0f};
    // vfc::float32_t m_maxAngle{0.0f};
    vfc::float32_t m_leftRearWheelAngle{0.0f};
    vfc::float32_t m_rightRearWheelAngle{0.0f};
};

struct StrippedCpjHMI
{
  ETransparentMode      m_huTransparentMode; //Using in ST24
  EhuRadarWallButton    m_huRadarWallButton;
  ELSMGActivationSetReq m_LSMGActivationSetReq;
  bool                  m_showTrailerLine;

  vfc::uint8_t          m_powerModeRaw;
  vfc::uint8_t          m_huDisplayID;  //EScreenID
  vfc::uint8_t          m_sideEnableSts;  //SideViewEnableStatus
  vfc::uint8_t          m_topEnableSts;  //SideViewEnableStatus
  vfc::uint8_t          m_huDislayModeSwitch;
  vfc::uint8_t          m_huDislayModeExpand;
  vfc::uint8_t          m_huImageWorkMode;
  vfc::uint8_t          m_huDislayModeExpandNew;
  vfc::uint8_t          m_huRotateStatus;
  vfc::uint8_t          m_vehTransReq;
  vfc::uint8_t          m_vehTransReqCounter;
  vfc::uint8_t          m_vehTransLevel;  //0: not_transparent; 1: Half_transparent; 2: Total_transparent
  EHUDisplayMode5x      m_huDislayModeView5x;
  vfc::uint8_t          m_vehColorReq;
  vfc::uint16_t         m_touchCoorX;
  vfc::uint16_t         m_touchCoorY;
  vfc::float32_t        m_touchSwipeSpeed;
  vfc::uint16_t         m_touch1CoorX;// two finger touch
  vfc::uint16_t         m_touch1CoorY;
  vfc::uint16_t         m_touch2CoorX;
  vfc::uint16_t         m_touch2CoorY;
  vfc::uint8_t          m_touchEvenType;
  vfc::uint8_t          m_twoFingerTouchEvenType;
  vfc::uint8_t          m_huRemoteCallSVS;

  vfc::uint8_t          m_huShowReq;
  vfc::uint8_t          m_huShowSuspend;
  vfc::uint8_t          m_FCTARightWarnlevel;
  vfc::uint8_t          m_FCTALeftWarnlevel;
  vfc::uint8_t          m_RCTARightWarnlevel;
  vfc::uint8_t          m_RCTALeftWarnlevel;
  bool                  m_VRSwitchSVM;
  bool                  m_FCP_SVMButtonPressed;
  vfc::uint32_t         m_freemodeAngle;

  StrippedCrabGuideline_st m_CrabGuideline;
  bool                  m_isGoldenLogo;
};

enum class EPdmSvsSetting : vfc::uint8_t
{
  PDMSVSSETTING_NONE          = 0,
  PDMSVSSETTING_ENABLED       = 1,
  PDMSVSSETTING_DISABLED      = 2,
  PDMSVSSETTING_INHIBIT       = 3
};

enum class EPdmSvsVehColor : vfc::uint8_t
{
  PDMSVSVEHCOLOR_NONE                = 0,
  PDMSVSVEHCOLOR_SMOKY_CRYSTAL_BLACK = 1,
  PDMSVSVEHCOLOR_TIME_GRAY           = 2,
  PDMSVSVEHCOLOR_RED_EMPEROR         = 3,
  PDMSVSVEHCOLOR_SNOWY_WHITE         = 4,
  PDMSVSVEHCOLOR_TIANSHAN_WHITE      = 5,
  PDMSVSVEHCOLOR_TEHRAN              = 6,
  PDMSVSVEHCOLOR_MOUNTAIN_ASH        = 7,
  PDMSVSVEHCOLOR_INKSTONE_BLUE       = 8,
  PDMSVSVEHCOLOR_SILVERSAND_BLACK    = 9,
  PDMSVSVEHCOLOR_TITANIUM_SILVER     = 10,
  PDMSVSVEHCOLOR_STRATEGIC_BLUE      = 11,
  PDMSVSVEHCOLOR_TITANIUM_EMPTY_GRAY = 12,
  PDMSVSVEHCOLOR_CRYSTAL_WHITE       = 13,
  PDMSVSVEHCOLOR_FLANGE_RED          = 14,
  PDMSVSVEHCOLOR_SKY_BLUE            = 15,
  PDMSVSVEHCOLOR_VIBRANT_ORANGE      = 16,
  PDMSVSVEHCOLOR_WISDOM_BLUE         = 17,
  PDMSVSVEHCOLOR_HONEY_ORANGE        = 18,
  PDMSVSVEHCOLOR_RUSTLE_GREEN        = 19,
  PDMSVSVEHCOLOR_PUFFFAN             = 20,
  PDMSVSVEHCOLOR_SPARKLING_BLUE      = 21,
  PDMSVSVEHCOLOR_SURFBUE             = 22,
  PDMSVSVEHCOLOR_XUANKONG_BLACK      = 23,
  PDMSVSVEHCOLOR_QIANSHAN_CUI        = 24,
  PDMSVSVEHCOLOR_AZURE               = 25,
  PDMSVSVEHCOLOR_CYAN_SMOKE          = 26,
  PDMSVSVEHCOLOR_DOME_WHITE          = 27,
  PDMSVSVEHCOLOR_AURORA_WHITE        = 28,
  PDMSVSVEHCOLOR_ROSEMARY_GRAY       = 29,
  PDMSVSVEHCOLOR_LOSTATLANTIS        = 30,
  PDMSVSVEHCOLOR_BLUEOCEANLENS       = 31,
  PDMSVSVEHCOLOR_MERCURY_BLUE        = 32,
  PDMSVSVEHCOLOR_TROLLGRASS_GREEN    = 33,
  PDMSVSVEHCOLOR_ROCKY_GREEN         = 34,
  PDMSVSVEHCOLOR_DEMON_BLACK         = 35,
  PDMSVSVEHCOLOR_SEA_BLUE            = 36,
  PDMSVSVEHCOLOR_ROSE_GOLD           = 37,
  PDMSVSVEHCOLOR_SANDALWOOD_PURPLE   = 38,
  PDMSVSVEHCOLOR_SUNRISE_GOLD        = 39,
  PDMSVSVEHCOLOR_DOME_BLUE           = 40,
  PDMSVSVEHCOLOR_RESERVED_COLOR1     = 41,
  PDMSVSVEHCOLOR_RESERVED_COLOR2     = 42,
  PDMSVSVEHCOLOR_MUSHAN_PINK         = 43,
  PDMSVSVEHCOLOR_SILVER_GLAZE_WHITE  = 74,
  PDMSVSVEHCOLOR_DUDU_WHITE          = 75,
  PDMSVSVEHCOLOR_JUN_WARE_GRAY       = 100,
  PDMSVSVEHCOLOR_RESERVED_COLOR      = 0xFF
};

struct StrippedPdmSetting_st
{
  EPdmSvsSetting          m_pdmAutoCamData;
  EPdmSvsVehColor         m_pdmVehColorData;
  EPdmSvsSetting          m_pdmVehTransData;
};

constexpr vfc::uint8_t  F192_Data_Length = 16u;
constexpr vfc::uint8_t  F193_Data_Length = 5u;

struct StrippedCpjSWInfo
{
  vfc::uint8_t   m_InternalSoftwareID[F192_Data_Length];
  vfc::uint8_t   m_HardwareVersion[F193_Data_Length];
};


enum class EStateDoorLock : vfc::uint8_t
{
    DoorLock_OPEN = 0u,
    DoorLock_CLOSED = 1u,
    DoorLock_INVALID = 2u
};

struct StrippedCpjDoorLockSts
{
  EStateDoorLock m_FLdoorLockStatus;
  EStateDoorLock m_FRdoorLockStatus;
  EStateDoorLock m_RLdoorLockStatus;
  EStateDoorLock m_RRdoorLockStatus;
  EStateDoorLock m_TrunkLockStatus;
};

struct AirSuspensionHeight
{
    vfc::uint16_t m_mode;
    vfc::uint16_t m_leftFront; //mm
    vfc::uint16_t m_rightFront; //mm
    vfc::uint16_t m_leftRear; //mm
    vfc::uint16_t m_rightRear; //mm
};

enum class E3DZoomLevel : vfc::uint8_t
{
    LEVEL0 = 0u, // Zoom level 3 50%, please directly set to 3D wide-angle view, do not use this value
    LEVEL1, // Zoom level 2 70%
    LEVEL2, // Zoom level 1 85%
    LEVEL3, // No zoom
    LEVEL4, // Zoom in level 1 110%
    LEVEL5, // Zoom in level 2 125%
    LEVEL6, // Zoom in level 3 150%
};

struct StrippedCpjVal_st
{
    StrippedCpjPwrTm          m_cpjPwrTm;
    StrippedCpjEnvVeh         m_cpjEnvVeh;
    StrippedCpjHMI            m_cpjHMI;
    StrippedCpjSWInfo         m_cpjSWInfo;
    StrippedCpjDoorLockSts    m_cpjDoorLockSts;
    pc::c2w::SatCamArray      m_cameraPara;
    bool                      m_cameraParaFlag; //Indicate weather the intrinsic received success
    bool                      m_startCalibration;
    bool                      m_isLogEnable;
    std::string               m_cpcDumpImagePath;
    AirSuspensionHeight       m_airSuspensionHeight;
    E3DZoomLevel              m_zoomLevel;
    bool                      m_removeDistortion;
    bool                      m_intrinsicUpdated{false};
};


enum class EVehLongDirReq : vfc::uint8_t
{
  VEHDIRREQ_NOREQ            = 0,
  VEHDIRREQ_FORWARD          = 1,
  VEHDIRREQ_REVERSE          = 2
};


struct StrippedAciLmcOutput_st {
  EVehLongDirReq  m_vehLongDirectionReq;
  vfc::uint16_t   m_nfsmDistanceToStopReq;
};


enum class EParkingFunction : vfc::uint8_t
{
  PARKFUNC_NONE    = 0,
  PARKFUNC_PSC     = 1,
  PARKFUNC_POC     = 2,
  PARKFUNC_CPSC_BI = 3,
  PARKFUNC_CPSC_FO = 4,
  PARKFUNC_CPSC_FI = 5,
  PARKFUNC_CPSC_BO = 6
};

enum class EPPocSide : vfc::uint8_t
{
  PPOCSIDE_NONE    = 0,
  PPOCSIDE_LEFT    = 1,
  PPOCSIDE_RIGHT   = 2
};

enum class ECPocSide : vfc::uint8_t
{
  CPOCSIDE_NONE     = 0,
  CPOCSIDE_LEFT     = 1,
  CPOCSIDE_RIGHT    = 2,
  CPOCSIDE_STRAIGHT = 3
};

struct StrippedPmactlmrgr_st
{
  EParkingFunction    m_parkingFunction; // forward or backward, parallel or cross, park in or out
  vfc::uint8_t        m_PSID;            // selected ID for parking
  EPPocSide           m_parallelPocSide; // left or right direction for parallel poc
  ECPocSide           m_crossPocSide;    // left or right or straight direction for cross poc
};


enum class EVehMov : vfc::uint8_t
{
  /**
   * Vehicle Movement Stopped
   */
  rbp_ctl_VehMov_Stopped_enm = 0,
  /**
   * Vehicle Movement Forward
   */
  rbp_ctl_VehMov_Forward_enm = 1,
  /**
   * Vehicle Movement Backward
   */
  rbp_ctl_VehMov_Backward_enm = 2
};/* Previous Type :: rbp_Type_ctl_VehMov_en;*/


enum class EUsrMoveIntent : vfc::uint8_t
{
  /**
   * User Movement Intension Secured
   */
  rbp_ctl_UsrMoveIntent_Secured_enm = 0,
  /**
   * User Movement Intension Un-Secured
   */
  rbp_ctl_UsrMoveIntent_Unsecured_enm = 1,
  /**
   * User Movement Intension SecuredToForward
   */
  rbp_ctl_UsrMoveIntent_SecuredToFwd_enm = 2,
  /**
   * User Movement Intension Un-Secured to Forward
   */
  rbp_ctl_UsrMoveIntent_UnsecuredToFwd_enm = 3,
  /**
    * User Movement Intension Backward to Forward
    */
  rbp_ctl_UsrMoveIntent_BkwdToFwd_enm = 4,
  /**
    * User Movement Intension Secured to Backward
    */
  rbp_ctl_UsrMoveIntent_SecuredToBkwd_enm = 5,
  /**
   * User Movement Intension Un-Secured to Backward
   */
  rbp_ctl_UsrMoveIntent_UnsecuredToBkwd_enm = 6,
  /**
    * User Movement Intension Forward to Backward
    */
  rbp_ctl_UsrMoveIntent_FwdToBkwd_enm = 7
};



struct StrippedVehicleCoorResetVhm_st {

  vfc::float32_t        m_VehicleCoor_X;

  vfc::float32_t        m_VehicleCoor_Y;

  vfc::float32_t        m_VehicleCoor_Phi;

};

typedef struct rbp_Tag_Point_st_cc

{

  vfc::int16_t X_s16;

  vfc::int16_t Y_s16;

} rbp_Type_Point_cc_st;



typedef struct rbp_Tag_Line_cc_st

{

  rbp_Type_Point_cc_st P_st;

  vfc::int16_t Phi_s16;

} rbp_Type_Line_cc_st;



typedef struct rbp_Tag_PathPoint_cc_st

{
  rbp_Type_Line_cc_st Pos_st;

  vfc::int16_t S_s16;

  vfc::int16_t Kappa_s16;

  vfc::int16_t v_s16;

} rbp_Tag_PathPoint_cc_st;

struct CAPGStripped
{
  vfc::float32_t                     m_APG;
  vfc::float32_t                     m_TravelDistDesired;
  rbp_Tag_PathPoint_cc_st            m_Points_pst[RBP_CC_TPC_MAX_NUM_PATHPOINTS];
  vfc::int32_t                       m_MoveType_en;
  vfc::int32_t                       m_MoveDir_en;
};

struct StrippedPasctl_st
{
  EUsrMoveIntent    m_UsrMoveIntent;
  EVehMov           m_VehMov;
};


struct StrippedParkctl_st
{
  vfc::uint8_t    m_outLmStatusParkCtl;
};
typedef enum EFAPAParkSlotType_: vfc::uint8_t
{
    APASLOT_DEFAULT,
    APASLOT_CROSS,
    APASLOT_PARALLEL,
    APASLOT_DIAGONAL,
    APASLOT_UNIVERSAL
} EFAPAParkSlotType;

typedef enum class EAPAParkSlotOrder_: vfc::uint8_t
{

    ORDER_1st = 0,
    ORDER_2nd = 1,
    ORDER_3rd = 2,
    ORDER_4th = 3,
    ORDER_5th = 4,
    ORDER_6th = 5,
    ORDER_7th = 6,
    ORDER_8th = 7,
    ORDER_NONE = 255

} EAPAParkSlotOrder; // HC23

typedef enum class rbp_Tag_ParkManeuverType_en : vfc::uint8_t
{
  rbp_ParkBwdIn_enm = 0,
  rbp_ParkFwdIn_enm = 1,
  rbp_ParkBFwdIn_enm = 2,
  rbp_ParkBwdOut_enm = 3,
  rbp_ParkFwdOut_enm = 4,
  rbp_ParkBFwdOut_enm = 5
} rbp_Type_ParkManeuverType_en;

typedef enum class EParkngTypeSeld_ : vfc::uint8_t
{
  PARKING_NONE = 0,
  PARKING_IN = 1,
  PARKING_OUT = 2
} EParkngTypeSeld;

typedef enum class EAPAPARKMODE_ : vfc::uint8_t
{
    APAPARKMODE_IDLE = 0,
    APAPARKMODE_APA = 1,
    APAPARKMODE_RPA = 2
} EAPAPARKMODE;

static constexpr vfc::uint8_t l_L_ParkSpace_side = 2;
static constexpr vfc::uint8_t l_L_ParkSpace_NumberPerside = 4;

struct StrippedParkhmiPositionSearching
{
  vfc::int16_t m_x;   // cm, front positive +
  vfc::int16_t m_y;   // cm, left positive +
  vfc::int16_t m_phi; // 2^(-12) rad, clockwise positive +
};

struct StrippedParkhmiTargetPosition
{
  vfc::int16_t m_x;   // 2^(-10) m
  vfc::int16_t m_y;   // 2^(-10) m
  vfc::int16_t m_phi; // 2^(-12) rad
};

struct StrippedEAPAParkSpace{
  EPARKSlotStsR2L              m_APA_PrkgSlot;         // Parking in/out parking space status: seletable, selected...
  vfc::float32_t               m_APA_PrkgSlotSta_f32;  // Parking direction, horizontal 0, vertical 90
  EFAPAParkSlotType            m_APA_PSType;           // Indicates the type of parking space found: cross, parallel, diagonal
  rbp_Type_ParkManeuverType_en m_APA_ParkManeuverType; // Parking in/out directions that can be supported by the parking space
  vfc::int16_t                 m_APA_PSCorner2X_i16;   // The X value of parking corner2 relative to the vehicle, the unit cm, origin rear axel center, front +, default value: maximum value
  vfc::int16_t                 m_APA_PSCorner2Y_i16;   // The Y value of parking corner2 relative to the vehicle, the unit cm, origin rear axel center, left +, default value: maximum value
  vfc::uint16_t                m_APA_PSLength_u16;     // Length of parking space
  vfc::int16_t                 m_APA_PSCorner1X_i16;   // The X value of parking corner1 relative to the vehicle, the unit cm, origin rear axel center, front +, default value: maximum value
  vfc::int16_t                 m_APA_PSCorner1Y_i16;   // The Y value of parking corner1 relative to the vehicle, the unit cm, origin rear axel center, left +, default value: maximum value
  vfc::float32_t               m_APA_PSStreetOrient_f32;
  vfc::float32_t               m_APA_WorldCorner2X_f32;
  vfc::float32_t               m_APA_WorldCorner2Y_f32;
  vfc::float32_t               m_APA_WorldCorner2Phi_f32;
  vfc::uint8_t                 m_APA_PSId_u8;
  EAPAParkSlotOrder            m_APA_PSOrder_u8;
};

typedef enum class RPAAvailable_: vfc::uint8_t
{
    RPA_NotAvailable = 0,
    RPA_Available    = 1
} RPAAvailable;

typedef enum class EBrkPedlAppldFlg_: vfc::uint8_t
{
    BrkPedl_NotApplied = 0,
    BrkPedl_Applied    = 1
} EBrkPedlAppldFlg;

struct StrippedParkhmi_st {
  EPARKTypeR2L         m_parkType;    // apa or rpa
  EPARKTypeVariantR2L  m_parkTypeVariant;  // variant only APA or both APA and RPA
  EPARKModeR2L         m_parkMode;    // ppsc parkin, cpsc parkin, parkout
  EPARKSideR2L         m_parkSide;    // left or right
  EPARKStatusR2L       m_parkStatus;  // parking process status, such as searching, manovering, finish, etc.
  EPARKFunctionIndR2L  m_parkFuncInd;
  EPARKQuitIndR2L      m_parkQuitInd;  // used in byd for hints
  EPARKQuitIndR2LExt   m_parkQuitIndExt;  // used in byd for hints
  EPARKDriverIndR2L    m_parkDriverInd;  // used in byd for hints
  EPARKRecoverIndR2L   m_parkRecoverInd;  // used in byd for hints
  bool                 m_reqReleasebtn;
  EPARKObjectExistR2L  m_parkObjectExist;
  bool                 m_parkSlotFlashReq;
  vfc::uint8_t         m_parkBackDisplayReq;
  bool                 m_parkRPADriverSelected;
  bool                 m_parkSmallPSrecommandRPA;
  EPARKDriverIndSearchR2L m_APADriverReq_Search;  // used in byd for hints
  EPARKDriverIndExtR2L    m_APADriverReq_Ext;  // used in byd for hints
  bool                 m_parkBreakPedalBeenReleasedBf;
  EBrkPedlAppldFlg     m_parkbrkPedlAppld;  // brake is applied or not

  vfc::uint8_t         m_parkDirection;        // park forward or backward for park in or out
  vfc::uint8_t         m_parkSlotSelectedId;   // park slot selected id
  vfc::uint8_t         m_PSParkDirection;      // whether park slot support front in or rear in
  RPAAvailable         m_parkRPAAvaliable;         // whether RPA is available to select
  vfc::uint8_t         m_parkAPAAvaliable;         // whether APA is available to select
  vfc::uint8_t         m_parkPOCAvaliable;         // whether POC is available to select
  vfc::uint8_t         m_ParkInAvaliable;      // whether park in is available to select
  vfc::uint8_t         m_FreeParkingAvaliable;  // whether free parking is available to select
  bool                 m_FreeParkingActive;
  bool                 m_parkAWPParkable;              // the free parking slot is parkable

  StrippedParkhmiTargetPosition       m_parkCurMoveTargetPosition;  // target position of current move
  StrippedParkhmiTargetPosition       m_parkFinalEndPosition;       // final end position during manoeuvering

  vfc::uint8_t                        m_parkCarmoveNumber;          // for dynamic gear progress overlay, car move number
  vfc::int16_t                        m_parkCartravelDistDesired;   // for dynamic gear progress overlay, travel distance desired
  bool                                m_parkIsLastMove;             // whether it is lat move

  StrippedEAPAParkSpace              m_APA_ParkSpace[l_L_ParkSpace_side][l_L_ParkSpace_NumberPerside];
                                                    // l_L_ParkSpace_side: 0~leftside 1~rightside,
                                                    // l_L_ParkSpace_NumberPerside: number of parking spaces, total number: 4
  rbp_Type_ParkManeuverType_en m_PSDirectionSelected; // outPsxManeuverType_en: The final parking direction (forward, backward, confirming)
  EParkngTypeSeld m_ParkngTypeSeld;
  EAPAPARKMODE m_APAPARKMODE;

  vfc::float32_t       m_APA_PrkgSlotSta_1L;
  vfc::float32_t       m_APA_PrkgSlotSta_2L;
  vfc::float32_t       m_APA_PrkgSlotSta_3L;
  vfc::float32_t       m_APA_PrkgSlotSta_4L;
  vfc::float32_t       m_APA_PrkgSlotSta_1R;
  vfc::float32_t       m_APA_PrkgSlotSta_2R;
  vfc::float32_t       m_APA_PrkgSlotSta_3R;
  vfc::float32_t       m_APA_PrkgSlotSta_4R;
  vfc::float32_t       m_APA_PrkgSlotCoor_1L;
  vfc::float32_t       m_APA_PrkgSlotCoor_2L;
  vfc::float32_t       m_APA_PrkgSlotCoor_3L;
  vfc::float32_t       m_APA_PrkgSlotCoor_4L;
  vfc::float32_t       m_APA_PrkgSlotCoor_1R;
  vfc::float32_t       m_APA_PrkgSlotCoor_2R;
  vfc::float32_t       m_APA_PrkgSlotCoor_3R;
  vfc::float32_t       m_APA_PrkgSlotCoor_4R;
  vfc::uint8_t         m_APA_PrkgSlot_1L;
  vfc::uint8_t         m_APA_PrkgSlot_1R;
  vfc::uint8_t         m_APA_PrkgSlot_2L;
  vfc::uint8_t         m_APA_PrkgSlot_2R;
  vfc::uint8_t         m_APA_PrkgSlot_3L;
  vfc::uint8_t         m_APA_PrkgSlot_3R;
  vfc::uint8_t         m_APA_PrkgSlot_4L;
  vfc::uint8_t         m_APA_PrkgSlot_4R;
  vfc::uint8_t         m_APA_PrkgSlotType_1L;
  vfc::uint8_t         m_APA_PrkgSlotType_1R;
  vfc::uint8_t         m_APA_PrkgSlotType_2L;
  vfc::uint8_t         m_APA_PrkgSlotType_2R;
  vfc::uint8_t         m_APA_PrkgSlotType_3L;
  vfc::uint8_t         m_APA_PrkgSlotType_3R;
  vfc::uint8_t         m_APA_PrkgSlotType_4L;
  vfc::uint8_t         m_APA_PrkgSlotType_4R;
};


struct StrippedCcf_st
{
  vfc::uint8_t m_ccf4x4InfoDisplay;
  vfc::uint8_t m_ccfNFSVariant;
  vfc::uint8_t m_ccfParkAssist;
  vfc::uint8_t m_ccfPerspectiveViews;
  vfc::uint8_t m_ccfSeeThroughBonnet;
  vfc::uint8_t m_ccfSteeringWheelPosn;
  vfc::uint8_t m_ccfTowBar;
  vfc::uint8_t m_ccfHitchAssistTowbar;
  vfc::uint8_t m_ccfUltrasonicSensorFit;
  vfc::uint8_t m_ccfVehicleType;
  vfc::uint8_t m_ccfVisionLegislation;
  vfc::uint8_t m_ccfDoors;
  vfc::uint8_t m_ccfCameraSensorFitment;
  vfc::uint8_t m_ccfDriveAssist;
};


struct CVhmAbstOutputStripped
{
  vfc::CSI::si_metre_per_second_f32_t m_velocity;
  vfc::int32_t                        m_posXRaw;
  vfc::int32_t                        m_posYRaw;
  vfc::CSI::si_radian_f32_t           m_yawAngleRaw;
  vfc::CSI::si_metre_f32_t            m_curOdometry_X;
  vfc::CSI::si_metre_f32_t            m_curOdometry_Y;
  vfc::CSI::si_radian_f32_t           m_curOdometry_YawAngle;
  EVehMoveDir                         m_vehMoveDir;
};


struct LSMGActivationSetStat_st
{
  vfc::uint8_t m_lsmgActivationSetStat;
};

//! PDM Variant
struct StrippedVariant_st
{
  vfc::uint8_t m_VehicleVariantIdentifier_ui8;
  vfc::uint8_t m_Val_Ref_VehicleNetworkIdentificatifier;
  vfc::uint8_t m_TireWheelCoding;
  vfc::uint8_t m_DriveConceptCoding;
  vfc::uint8_t m_VehicleColorCoding;
  vfc::uint8_t m_WheelSensorTypeCoding;
  vfc::uint8_t m_USSFunctionCoding_FPAS;
  vfc::uint8_t m_USSFunctionCoding_RPAS;
  vfc::uint8_t m_USSFunctionCoding_APA;
  vfc::uint8_t m_USSFunctionCoding_RPA;
  vfc::uint8_t m_NRCSFunctionCoding;
  vfc::uint8_t m_FusionFunctionCoding;
};

//! AEB Signal
struct StrippedAebmgr_st
{
  vfc::uint8_t                m_aebVmcOpmode;
};

//! DIAG Routine
struct StrippedDiagroutine_st
{
  vfc::uint8_t                m_diagRequestType;
  vfc::uint8_t                m_engineeringScreenType;
};

//! Degradation FIDs
struct StrippedSvsFid_st
{
  vfc::uint8_t m_FiMFID_SVSRxLampAll;
  vfc::uint8_t m_FiMFID_SVSRxDoorSt;
  vfc::uint8_t m_FiMFID_SVSRxLamp38A;
  vfc::uint8_t m_FiMFID_SVSRxLamp496A;
  vfc::uint8_t m_FiMFID_SVSRxLamp133A;
  vfc::uint8_t m_FiMFID_SVSRxExtMirrorSt;
  vfc::uint8_t m_FiMFID_SVSOdo;
  vfc::uint8_t m_FiMFID_SVSRxVehSpd;
  vfc::uint8_t m_FiMFID_SVSRxDrvDir;
  vfc::uint8_t m_FiMFID_SVSRxGear;
  vfc::uint8_t m_FiMFID_SVSRxSteeringWheelAngle;
  vfc::uint8_t m_FiMFID_SVSRxHuViewModeFailure;
  vfc::uint8_t m_FiMFID_SVSRxHUVehicleModeRotationFailure;
  vfc::uint8_t m_FiMFID_SVSRxHUPadRotateFailure;
  vfc::uint8_t m_FiMFID_SVSRxFctaWarning;
  vfc::uint8_t m_FiMFID_SVSRxRctaLeftWarning;
  vfc::uint8_t m_FiMFID_SVSRxRctaRightWarning;
  vfc::uint8_t m_FiMFID_SVSRxHUVRSVMRequest; //! for RPA

  vfc::uint8_t m_FiMFID_SVSCamFrontSt;
  vfc::uint8_t m_FiMFID_SVSCamLeftSt;
  vfc::uint8_t m_FiMFID_SVSCamRightSt;
  vfc::uint8_t m_FiMFID_SVSCamRearSt;
  vfc::uint8_t m_FiMFID_SVSEcuInternalStatus;
};

//! PDM Data
enum class EDefaultTrailerView : vfc::uint8_t
{
    TRAILER_VIEW_NOT_AVAILABLE   = 0,
    TRAILER_VIEW_SINGLE_VIEW_REQ = 1,
    TRAILER_VIEW_DUAL_VIEW_REQ   = 2,
    TRAILER_VIEW_TRIPLE_VIEW_REQ = 3
};


enum class ELSMGActivationSetStat : vfc::uint8_t
{
  LSMG_ACTIVATION_NOT_AVAILABLE = 0,
  LSMG_ACTIVATION_STANDARD      = 1,
  LSMG_ACTIVATION_AUTOMATIC     = 2,
  LSMG_ACTIVATION_SMART         = 3
};


enum class EGBCFeatureActivationStatus : vfc::uint8_t
{
  GBC_ACTIVATION_NOT_AVAILABLE = 0,
  GBC_ACTIVATION_ENABLED       = 1,
  GBC_ACTIVATION_DISABLED      = 2
};

enum class EPARkPSType : vfc::uint8_t
{
  EPARK_PS_TYPE_NONE = 0,
  EPARK_PS_TYPE_UNIVERSAL_PARALLEL = 1,
  EPARK_PS_TYPE_UNIVERSAL_CROSS = 2
};

enum class EPARKDirection : vfc::uint8_t
{
  EPARKDIRECTION_NONE         = 0,
  EPARKDIRECTION_FRONTIN      = 1,
  EPARKDIRECTION_REARIN       = 2
};

enum class EParkOutSide : vfc::uint8_t
{
  EPARK_OUT_SIDE_NONE         = 0,
  EPARK_OUT_SIDE_LEFT         = 1,
  EPARK_OUT_SIDE_RIGHT        = 2
};

enum class EPARkMode : vfc::uint8_t
{
  EPARKMODE_NONE          = 0,
  EPARKMODE_PARK_IN       = 1,
  EPARKMODE_PARK_OUT      = 2,
  EPARKMODE_FREEPARKING   = 3
};

enum class EPARkType : vfc::uint8_t
{
  EPARKTYPE_NONE = 0,
  EPARKTYPE_APA  = 1,
  EPARKTYPE_RPA  = 2
};

enum class EPARkSlot : vfc::uint8_t
{
  EPARKSLOT_NONE       = 0,
  EPARKSLOT_LEFT_1st   = 1,
  EPARKSLOT_LEFT_2nd   = 2,
  EPARKSLOT_LEFT_3rd   = 3,
  EPARKSLOT_LEFT_4th   = 4,
  EPARKSLOT_RIGHT_1st  = 5,
  EPARKSLOT_RIGHT_2nd  = 6,
  EPARKSLOT_RIGHT_3rd  = 7,
  EPARKSLOT_RIGHT_4th  = 8
};

enum class EPARKSlotSt : vfc::uint8_t
{
    APASLOT_NONE,
    APASLOT_INVALID,
    APASLOT_VALID,
    APASLOT_SELECTABLE,
    APASLOT_SELECTED,
    APASLOT_RESERVED
};

enum class EPARkConfirmButton : vfc::uint8_t
{
  EPARKCONFIRM_NOT_PRESS = 0,
  EPARKCONFIRM_PRESS     = 1
};

enum class EPARkPauseButton : vfc::uint8_t
{
  EPARKPAUSE_NOT_PRESS = 0,
  EPARKPAUSE_PRESS     = 1
};

enum class EPARkDrvFuncOff : vfc::uint8_t
{
  EPARKDRVFUNC_NONE = 0,
  EPARKDRVFUNC_OFF  = 1
};

enum class EPARkPasMuteButton : vfc::uint8_t
{
  EPASMUTE_NOT_PRESS  = 0,
  EPASMUTE_PRESS      = 1
};

enum class ESVSUnavlMsgs : vfc::uint8_t
{
  ESVSUNAVLMSG_NONE           = 0,
  ESVSUNAVLMSG_VIDOUT_ERROR   = 1,
  ESVSUNAVLMSG_IGN_OFF        = 2,
  ESVSUNAVLMSG_ALLCAM_ERROR   = 3,
  ESVSUNAVLMSG_SPEED_TOO_HIGH = 4
};

enum class EThemeTypeHU : vfc::uint8_t
{
  ETHEME_TYPE_NONE = 0,
  ETHEME_TYPE_HORI = 1,  // horizontal layout
  ETHEME_TYPE_VERT = 2   // vertical layout
};

enum EThemeTypeLang : vfc::uint8_t
{
  ETHEME_TYPE_CN = 0,   // in Chinese
  ETHEME_TYPE_EN = 1    // in English
};

enum EFreeParkingConfirmButtonSts : vfc::uint8_t
{
  FP_CONFIRM_NOT_PRESS   = 0,
  FP_CONFIRM_PRESSED   = 1
};

enum EPARkFreeParkCancel : vfc::uint8_t
{
  EFREE_PARK_FUNC_NONE   = 0,
  EFREE_PARK_FUNC_CANCEL   = 1
};

namespace linux_ipc
{

struct CLinuxipcOutput
{

  // BYD
  vfc::uint8_t m_svsViewState;
  vfc::uint8_t m_svsWorkMode;
  vfc::uint8_t m_svsOnOffState;
  vfc::uint8_t m_vidoutMode;
  vfc::uint8_t m_svsRotateStatus;
  vfc::uint8_t m_svsVehTransparentStatus;
  vfc::uint8_t m_svsTrajCfgState;
  vfc::uint8_t m_vidOutMode;
  vfc::uint8_t m_svsImageConfig;
  vfc::uint8_t m_svsCarBody;
  vfc::uint8_t m_svsExpandedViewState;
  vfc::uint8_t m_svsNewExpandedViewState;
  vfc::uint8_t m_svsVehColorAck;
  vfc::uint8_t m_svsUnavlMsgs;
  vfc::uint8_t m_APA_Resolution; //For HC/ST 23
  // End of BYD

  vfc::uint8_t         m_svsFrViewSts;
  vfc::uint8_t         m_svsReViewSts;
  vfc::uint8_t         m_svsLeViewSts;
  vfc::uint8_t         m_svsRiViewSts;

  vfc::uint8_t         m_svsShowReq;
  EPARkPauseButton     m_svsPauseButton;
  bool                 m_svsVRSwitchFailSt;
  EPARkMode            m_svsParkMode;
  EPARkType            m_svsParkTypeSelected;
  EPARkPSType          m_svsPSTypeSelected;
  EPARKDirection       m_svsPSDirectionSelected;
  EParkOutSide         m_svsParkOutsideSelected;
  EPARkSlot            m_svsParkSlotID;
  EPARkConfirmButton   m_svsParkConfirmButton;
  EPARkDrvFuncOff      m_svsParkDrvFuncOff;
  EPARkPasMuteButton   m_svsPasMuteButton;
  vfc::uint8_t         m_svsViewMode;
  vfc::uint8_t         m_svsCurrentViewSts;
  EFreeParkingConfirmButtonSts m_fpConfirmedButton;
  EPARkFreeParkCancel          m_svsFpReturnButton;
};


} // namespace linux_ipc
struct SvsToParkHmi_st
{
    // EParkActiveStatus    m_svsParkActiveByDriver;
    EPARkPauseButton     m_svsPauseButton;
    EPARkMode            m_svsParkMode;
    EPARkType            m_svsParkTypeSelected;
    EPARkPSType          m_svsPSTypeSelected;
    // EPARkDirection       m_svsPSDirectionSelected;
    EParkOutSide         m_svsParkOutsideSelected;
    EPARkSlot            m_svsParkSlotID;  // selected slot index
    vfc::uint16_t        m_svsParkSelectedSlotID;  // selected slot id
    EPARkConfirmButton   m_svsParkConfirmButton;
    EPARkDrvFuncOff      m_svsParkDrvFuncOff;
    EFreeParkingConfirmButtonSts m_fpConfirmedButton;
    vfc::int32_t         m_suroundViewRotateAngle;
};

enum class PlatformTypeCommon : vfc::uint8_t
{
    UNKNOWN = 0
};

enum class ProjectTypeCommon : vfc::uint8_t
{
    UNKNOWN = 0
};

struct VehicleConfigCommon
{
    std::string configPath; // Algorithm configuration file path Json
    std::string externalParamInitPath; // Initial calibration external parameters path SviewTune_init.camera
    std::string externalParamPath; // Calibration external parameters path SviewTune.camera
    std::string resourcePath; // Resource file path for vehicle models, etc. KZB
    std::string vehicleType; // Vehicle type
    std::string subVehicleType; // Sub-vehicle type
    std::string commonPath; // Common algorithm resources
    int screenWidth; // Screen resolution (width)
    int screenHeight; // Screen resolution (height)
    PlatformTypeCommon platform; // Platform
    ProjectTypeCommon project; // Project
    std::list<std::string> m_uDiskPath; //U disk path for calibration
};

enum class EViewAnimation : vfc::uint8_t
{
    AVM_ANI_SLIDE_LEFT = 0u,    //  to left view
    AVM_ANI_SLIDE_RIGHT,        //  to right view
    AVM_ANI_DEFAULT_FULL_START, //  from top view to full screen top view - start
    AVM_ANI_DEFAULT_FULL_STOP,  //  from top view to full screen top view - end
    AVM_ANI_FULL_DEFAULT_START, //  from full screen top view to top view - start
    AVM_ANI_FULL_DEFAULT_STOP,  //  from full screen top view to top view - end
    AVM_ANI_SLIDE_FRONT_HUBVIEW,    //   to middle (front)
    AVM_ANI_SLIDE_REAR_HUBVIEW,     //   to middle (front)
};

struct DataContainerToSvs
{
    CVhmAbstOutputStripped  m_VHMData;
    VehicleConfigCommon     m_vehicleInfo;
    LSMGDataSVS_st          m_LSMGData;
    PasAPPDataSVS_st        m_PasAPPData;
    vfc::float32_t          m_APG;
    CValInOutputPfStripped  m_StrippedPfValData;
    StrippedCpjVal_st       m_StrippedCpjValData;
    ParkhmiToSvs            m_parkhmiToSvs;
};

struct DataContainerFromSvs
{
    SvsToParkhmi                            m_svs2ParkHmi;
    EViewAnimation                          m_viewAnimationSt;
    vfc::int32_t                            m_returnFreemodeAngle;
    vfc::int32_t                            m_returnFreemodeAngleVert;
#ifndef TARGET_ANDROID
    SlideCallback                           m_slideCallback;
    CalibrationCallback                     m_calibCallback;
    ParkingSpaceSelectedCallback            m_freeParkingSlotCallback;
#else
    std::shared_ptr<ParkingSpaceSelectedCallback> m_freeParkingSlotCallback;
    std::shared_ptr<ParkingLotSelectedCallback>   m_SeletedSlotIDListenerPtr;
    std::shared_ptr<DirectionSelectedListener>    m_DirectionSelectedListenerPtr;
#endif
    std::shared_ptr<CalibrationListener>          m_calibrationListenerPtr;
    std::shared_ptr<FreeModeAngleListener>        m_freeModeAngleListener;
    std::shared_ptr<OptionslotListener>           m_optionalSlotListener;

};

} // namespace common
} // namespace target
} // namespace cc

extern cc::target::common::DataContainerToSvs   g_dataContainerToSvs;
extern cc::target::common::DataContainerFromSvs g_dataContainerFromSvs;

#endif //CC_TARGET_COMMON_LINUXIPCINTERFACE_H
