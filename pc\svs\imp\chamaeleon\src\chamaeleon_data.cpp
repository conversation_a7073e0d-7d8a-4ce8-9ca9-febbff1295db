/// @copyright (C) 2023 Robert <PERSON> GmbH.
/// The reproduction, distribution and utilization of this file as well as the
/// communication of its contents to others without express authorization is
/// prohibited. Offenders will be held liable for the payment of damages. All
/// rights reserved in the event of the grant of a patent, utility model or
/// design.
/// @file

#include "pc/svs/imp/chamaeleon/inc/chamaeleon_data.hpp"

namespace rbp
{
namespace vis
{
namespace imp
{
namespace chamaeleon
{
ChamaeleonBaseSignalsData::ChamaeleonBaseSignalsData()
    : pc::util::coding::ISerializable{}
    , m_ignoreCarDoorState{false}
    , m_ignoreMirrorState{false}
    , m_ignoreDegradationState{false}
    , m_ignoreCalibState{true}
{
}

ChamaeleonEstimatorData::ChamaeleonEstimatorData()
    : pc::util::coding::ISerializable{}
{
}

ChamaeleonWbGainsData::ChamaeleonWbGainsData()
    : pc::util::coding::ISerializable{}
{
}

ChamaeleonRoisData::ChamaeleonRoisData()
    : pc::util::coding::ISerializable{}
{
}

ChamaeleonVisuViewport::ChamaeleonVisuViewport()
    : pc::util::coding::ISerializable{}
{
}

ChamaeleonVisuViewport::ChamaeleonVisuViewport(
    vfc::int32_t f_visuViewportX,
    vfc::int32_t f_visuViewportY,
    vfc::int32_t f_visuViewportWidth,
    vfc::int32_t f_visuViewportHeight)
    : pc::util::coding::ISerializable{}
    , m_visuViewportX{f_visuViewportX}
    , m_visuViewportY{f_visuViewportY}
    , m_visuViewportWidth{f_visuViewportWidth}
    , m_visuViewportHeight{f_visuViewportHeight}
{
}

ChamaeleonVisuRoiData::ChamaeleonVisuRoiData()
    : pc::util::coding::ISerializable{}
{
}

ChamaeleonData::ChamaeleonData()
    : pc::util::coding::ISerializable{}
{
}

void ChamaeleonData::setEnable(bool f_enable)
{
    m_enableChamaeleon = f_enable;
}

pc::util::coding::Item<ChamaeleonData> g_settings("IQ_Chamaeleon");

} // namespace chamaeleon
} // namespace imp
} // namespace vis
} // namespace rbp
