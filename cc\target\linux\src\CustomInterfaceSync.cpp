//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: EXTERNAL Castellane Florian (Altran, CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  CustomInterfaceSync.cpp
/// @brief
//=============================================================================
//-------------------------------------------------------------------------------

#include "cc/target/linux/inc/CustomInterfaceSync.h"

#include "cc/daddy/inc/CustomDaddyPorts.h"
#include "cc/daddy/inc/CustomDaddyTypes.h"
// #include "cc/util/logging/inc/LoggingContexts.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h"

#include "hw/ipcsync/inc/LoggingContext.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "valin/inc/valin_output_pf.hpp"


#define DEBUG_IPC_COUNTERS        0
#define DEBUG                     0

//g_dataContainerToSvs will modify by fd-bus
cc::target::common::DataContainerToSvs   g_dataContainerToSvs;
cc::target::common::DataContainerFromSvs g_dataContainerFromSvs;


namespace cc
{
namespace target
{
namespace linux
{

using pc::util::logging::g_EngineContext;
bool g_enableIPC = false;
static unsigned int sm_debug_out_tick = 0u;

CustomInterfaceSync::CustomInterfaceSync()
    : IEventDrivenRunnable(std::chrono::milliseconds(10u))
    , m_startNormalIpc(false) // PRQA S 4050
    , m_readCounter(0u)
    , m_writeCounter(0u)
    , m_dataSync{}
{
}

void CustomInterfaceSync::OnIterate()
{
    if (g_enableIPC)
    {
        readAndInterpretCustomerSignals(g_dataContainerToSvs);
        writeCustomerSignals(g_dataContainerFromSvs);
    }
    else
    {
        // XLOG_INFO_OS(g_EngineContext) << "IPC Disabled." << XLOG_ENDL;
    }
}
void CustomInterfaceSync::OnInit()
{
    XLOG_INFO(g_EngineContext, "QC Starting Custom Interface Sync thread.");
}
void CustomInterfaceSync::OnShutdown()
{
    XLOG_INFO(g_EngineContext, "End QC Custom Interface Sync thread.");
}

void CustomInterfaceSync::OnTimeout()
{
    this->OnIterate();
}

void CustomInterfaceSync::readAndInterpretCustomerSignals(
    const cc::target::common::DataContainerToSvs& f_dataContainerToSvs)
{
#if DEBUG
    struct timespec l_beginTime;
    struct timespec l_endTime;
    struct timespec l_deltaTime;
#endif

    const bool l_skip = (0u != (m_readCounter % 2u));
    m_readCounter += 1u;
    if (l_skip)
    {
        return;
    }

    ++sm_debug_out_tick;
    if (60u < sm_debug_out_tick)
    {
        sm_debug_out_tick = 0u;
    }

#if DEBUG
    clock_gettime(CLOCK_MONOTONIC, &l_beginTime);
#endif

    // Start with Data Sync
#if DEBUG_IPC_COUNTERS
    printInVsOutCounters(l_headerIn, &m_countersFromR5);
#endif
    // m_dataSync.inputVhmSignals(f_dataContainerToSvs.m_StrippedPfValData);
    // m_dataSync.readVHMSignals(f_dataContainerToSvs.m_VHMData);
    m_dataSync.readLSMGSignals(f_dataContainerToSvs.m_LSMGData);
    m_dataSync.readPasAPPSignals(f_dataContainerToSvs.m_PasAPPData);
    m_dataSync.readParkhmiSignals(f_dataContainerToSvs.m_parkhmiToSvs);
    // m_dataSync.readAPGSignals(static_cast<cc::daddy::PMA_TravelDistDesired> (f_dataContainerToSvs.m_APG));
    m_dataSync.readPfValSignals(f_dataContainerToSvs.m_StrippedPfValData);
    m_dataSync.readCpjValSignals(f_dataContainerToSvs.m_StrippedCpjValData);
    // m_dataSync.readDoorSignals(f_dataContainerToSvs.m_StrippedPfValData);
}

void CustomInterfaceSync::writeCustomerSignals(
    cc::target::common::DataContainerFromSvs& f_dataContainerFromSvs) // PRQA S 6041
{
    const bool l_skip = (0u != (m_writeCounter % 2u));
    m_writeCounter += 1u;
    if (l_skip)
    {
        return;
    }

    m_dataSync.writeDataFromSvs( f_dataContainerFromSvs );
    // m_dataSync.writeFreeparkingInfo(f_dataContainerFromSvs.m_CAnywhereParkingInfo);
}



} //namespace linux
} //namespace target
} //namespace cc

