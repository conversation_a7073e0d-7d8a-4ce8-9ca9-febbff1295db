//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: LIA1LR David Liepelt (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  ViewModeStateMachine.cpp
/// @brief
//=============================================================================

#include "cc/sm/viewmode/inc/ViewModeStateMachine.h"
#include "cc/sm/viewmode/inc/ViewModexViewStateMachine_R2015b_types.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/sm/viewmode/inc/ViewModeNames.h"
#include "cc/util/logging/inc/LoggingContexts.h"
#include "cc/imgui/inc/imgui_manager.h"

using cc::util::logging::g_viewModeSMContext;

pc::util::coding::Item<StateMachineParameters> g_stateMachineParams("ViewModeStateMachine");

void ViewModeStateMachine::OnTimeout( void )
{
    this->OnIterate() ;
}

ViewModeStateMachine::ViewModeStateMachine(pc::core::Framework* f_pFramework)
    : IEventDrivenRunnable(std::chrono::milliseconds(10u)) // timeout every 10ms // PRQA S 4050
    , m_pFramework(f_pFramework)
    , m_CurrMode(1000)
    , m_HUDislayModeSwitchSMReceiver()
    , m_cpcToSvsOverlaySMReceiver()
{
    WakeUpClass::setRunnable(this);
}

void ViewModeStateMachine::OnInit( void )    // PRQA S 6044
{
#ifdef TARGET_STANDALONE
    XLOG_INFO_OS( g_viewModeSMContext ) << "Starting ViewMode State Machine thread." << XLOG_ENDL ;//PRQA S 4060
    cc::daddy::CustomDaddyPorts::sm_HUDislayModeSwitchDaddy_SenderPort.connect( m_HUDislayModeSwitchSMReceiver ) ;
    cc::daddy::CustomDaddyPorts::sm_cpcToSvsOverlay_SenderPort.connect( m_cpcToSvsOverlaySMReceiver ) ;
#endif
}

void ViewModeStateMachine::OnIterate( void )         // PRQA S 6040  // PRQA S 6041  // PRQA S 6044
{
#ifdef TARGET_STANDALONE
    m_HUDislayModeSwitchSMReceiver.update();
    m_cpcToSvsOverlaySMReceiver.update();
    cc::daddy::PlanViewEnlargeStatusDaddy& l_planViewEnlargeStatusContainer = cc::daddy::CustomDaddyPorts::sm_planViewEnlargeStatus_SenderPort.reserve();
    cc::daddy::SVSDisplayedViewDaddy_t& l_rSVSDisplayedViewContainer = cc::daddy::CustomDaddyPorts::sm_SVSDisplayedViewDaddy_SenderPort.reserve() ;

    static vfc::int32_t l_NewMode = EScreenID_NO_CHANGE;

    if (m_HUDislayModeSwitchSMReceiver.isConnected() && m_HUDislayModeSwitchSMReceiver.hasData())
    {
        const cc::daddy::HUDislayModeSwitchDaddy_t* l_pData = m_HUDislayModeSwitchSMReceiver.getData();
        l_NewMode = (EScreenID)l_pData->m_Data;
        // XLOG_INFO_OS( g_viewModeSMContext ) << "Setting DislayModeSwitch to: "<< static_cast<vfc::int32_t> (l_pData->m_Data) << XLOG_ENDL;//PRQA S 4060
    }

    if (m_cpcToSvsOverlaySMReceiver.isConnected() && m_cpcToSvsOverlaySMReceiver.hasData())
    {
        const cc::daddy::CpcToSvsOverlay_t* l_pData = m_cpcToSvsOverlaySMReceiver.getData();
        if (false == l_pData->m_Data.m_isQuit)
        {
            l_NewMode = EScreenID_QUAD_RAW;
        }
    }

    l_rSVSDisplayedViewContainer.m_Data = static_cast<EScreenID>(l_NewMode);

    IMGUI_LOG("ViewModeStateMachine", "CurrentScreenID", std::to_string(l_NewMode) + " - " + cc::sm::getViewName(static_cast<EScreenID>(l_NewMode)));

    switch (l_NewMode)
    {
        case EScreenID_VERT_PLAN_FRONT_ENLARGE__FRONT_MAIN__LEFT_SIDE:
        case EScreenID_VERT_PLAN_FRONT_ENLARGE__FRONT_MAIN__RIGHT_SIDE:
        case EScreenID_VERT_FULLSCREEN_FRONT_ENLARGE:
        case EScreenID_MODEL_F_VIEW_ENLARGEMENT:
        case EScreenID_FULLSCREEN_FRONT_ENLARGE:
        case EScreenID_IMAGE_IN_IMAGE_PLANVIEW_FRONT_ENLARGE:
            l_planViewEnlargeStatusContainer.m_Data = cc::daddy::ENLARGE_FRONT; break;
        case EScreenID_VERT_PLAN_REAR_ENLARGE__REAR_MAIN__LEFT_SIDE:
        case EScreenID_VERT_PLAN_REAR_ENLARGE__REAR_MAIN__RIGHT_SIDE:
        case EScreenID_VERT_FULLSCREEN_REAR_ENLARGE:
        case EScreenID_MODEL_B_VIEW_ENLARGEMENT:
        case EScreenID_FULLSCREEN_REAR_ENLARGE:
        case EScreenID_IMAGE_IN_IMAGE_PLANVIEW_REAR_ENLARGE:
            l_planViewEnlargeStatusContainer.m_Data = cc::daddy::ENLARGE_REAR; break;
        default:
            l_planViewEnlargeStatusContainer.m_Data = cc::daddy::NO_ENLARGE; break;
    }

    if (l_NewMode != m_CurrMode)
    {
        pc::daddy::ViewModeDaddy& l_vm = pc::daddy::BaseDaddyPorts::sm_viewModeSenderPort.reserve();
        l_vm.m_Data = pc::daddy::ViewMode(m_CurrMode, l_NewMode);
        pc::daddy::BaseDaddyPorts::sm_viewModeSenderPort.deliver();
        m_CurrMode = l_NewMode;
    }

    cc::daddy::CustomDaddyPorts::sm_planViewEnlargeStatus_SenderPort.deliver();
    cc::daddy::CustomDaddyPorts::sm_SVSDisplayedViewDaddy_SenderPort.deliver();
    m_HUDislayModeSwitchSMReceiver.cleanup();
    m_cpcToSvsOverlaySMReceiver.cleanup();

#endif
    // door & mirror degradation handling
    const pc::daddy::DoorStateDaddy * l_pCDoors = nullptr ;
    const pc::daddy::MirrorStateDaddy * l_pCSideMirrors = nullptr ;

    if ( m_pFramework->m_doorStateReceiver.isConnected() )
    {
        static vfc::int32_t l_ctr = -1 ;
        const pc::daddy::DoorStateDaddy* const l_pData = m_pFramework->m_doorStateReceiver.getData() ;
        if (nullptr != l_pData && static_cast<vfc::int32_t>((*l_pData).m_sequenceNumber) != l_ctr)
        {
            l_pCDoors = l_pData ;
            l_ctr = static_cast<vfc::int32_t>(l_pData->m_sequenceNumber) ;
        }
    }

    if ( m_pFramework->m_mirrorStateReceiver.isConnected() )
    {
        static vfc::int32_t l_ctr = -1 ;
        const pc::daddy::MirrorStateDaddy* const l_pData = m_pFramework->m_mirrorStateReceiver.getData() ;
        if ( nullptr != l_pData && static_cast<vfc::int32_t>((*l_pData).m_sequenceNumber) != l_ctr )
        {
            l_pCSideMirrors = l_pData ;
            l_ctr = static_cast<vfc::int32_t>(l_pData->m_sequenceNumber) ;
        }
    }

    // this->computeAndDaddySendCameraDeactivationMask( l_pCDoors, l_pCSideMirrors/*, l_fids */) ;
}

void ViewModeStateMachine::computeAndDaddySendCameraDeactivationMask(   //PRQA S 1724
    const pc::daddy::DoorStateDaddy* f_pCDoors,
    const pc::daddy::MirrorStateDaddy* /*f_pCSideMirrors*//*, const cc::daddy::DegradationFid_t* f_fids*/)
{
    static pc::daddy::daddy4bitfield l_CameraDeactivationMask = 0u;
    static bool l_TrunkNotClosed = false;
    static bool l_SideDoorNotClosed[pc::daddy::NUMBER_OF_SIDEMIRRORS] = { false }; //assume doors are closed by default (SIL case only)
    // static bool l_SideMirrorNotFlapped[pc::daddy::NUMBER_OF_SIDEMIRRORS] = { false };
    // static bool l_frontCamDeg = false;
    // static bool l_leftCamDeg  = false;
    constexpr static bool l_rearCamDeg  = false;
    // static bool l_rightCamDeg = false;

    static bool l_isSurroundView = false;

    auto const scene = m_pFramework->getScene();
    if (scene == nullptr)
    {
        return;
    }
    const auto horiSurroundView = scene->getView(cc::core::CustomViews::SURROUND_VIEW);
    if (horiSurroundView == nullptr)
    {
        return;
    }
    const auto vertSurroundView = scene->getView(cc::core::CustomViews::VERTICAL_SURROUND_VIEW);
    if (vertSurroundView == nullptr)
    {
        return;
    }

    l_isSurroundView = (horiSurroundView->getNodeMask() != 0u) || (vertSurroundView->getNodeMask() != 0u);

    // if (f_pCSideMirrors)
    // {
        // const pc::daddy::MirrorStateArray& l_SideMirrorsStatus = f_pCSideMirrors->m_Data;
        // l_SideMirrorNotFlapped[pc::daddy::SIDEMIRROR_RIGHT] =
        //   (l_SideMirrorsStatus[pc::daddy::SIDEMIRROR_RIGHT] != pc::daddy::MIRRORSTATE_NOT_FLAPPED);
        // l_SideMirrorNotFlapped[pc::daddy::SIDEMIRROR_LEFT ] =
        //   (l_SideMirrorsStatus[pc::daddy::SIDEMIRROR_LEFT ] != pc::daddy::MIRRORSTATE_NOT_FLAPPED);
    // }

    if (f_pCDoors != nullptr)
    {
        const vfc::int32_t* const l_DoorsStatus = f_pCDoors->m_Data;
        l_TrunkNotClosed = ( l_DoorsStatus[pc::daddy::CARDOOR_TRUNK] != pc::daddy::CARDOORSTATE_CLOSED );
        l_SideDoorNotClosed[pc::daddy::SIDEMIRROR_RIGHT] = (
            ( l_DoorsStatus[pc::daddy::CARDOOR_FRONT_RIGHT] != pc::daddy::CARDOORSTATE_CLOSED ) );
        // ! BYD requriement rear door not degrade
        //  || ( l_DoorsStatus[pc::daddy::CARDOOR_REAR_RIGHT ] != pc::daddy::CARDOORSTATE_CLOSED )  );
        l_SideDoorNotClosed[pc::daddy::SIDEMIRROR_LEFT ] = (
            ( l_DoorsStatus[pc::daddy::CARDOOR_FRONT_LEFT ] != pc::daddy::CARDOORSTATE_CLOSED ) );
        // ! BYD requriement rear door not degrade
        //  || ( l_DoorsStatus[pc::daddy::CARDOOR_REAR_LEFT  ] != pc::daddy::CARDOORSTATE_CLOSED )  );
    }

    // if ( f_pCSideMirrors || f_pCDoors || f_fids)
    // if ( f_pCSideMirrors || f_pCDoors )
    if ( f_pCDoors  != nullptr)
    {
        //! Front camera: never disabled
        pc::daddy::unsetBit( pc::core::sysconf::FRONT_CAMERA, l_CameraDeactivationMask);
        pc::daddy::unsetBit( pc::core::sysconf::REAR_CAMERA, l_CameraDeactivationMask);
        pc::daddy::unsetBit( pc::core::sysconf::LEFT_CAMERA, l_CameraDeactivationMask);
        pc::daddy::unsetBit( pc::core::sysconf::RIGHT_CAMERA, l_CameraDeactivationMask);
        //! Rear camera: disabled when trunk open
        if ( l_TrunkNotClosed || l_rearCamDeg)
        // if (l_rearCamDeg)  // ! BYD requirement, trunk door not degrade
        {
            pc::daddy::setBit( pc::core::sysconf::REAR_CAMERA, l_CameraDeactivationMask);
        }
        else
        {
            pc::daddy::unsetBit( pc::core::sysconf::REAR_CAMERA, l_CameraDeactivationMask);
        }
        //! Side cameras: disabled if mirror flapped or one of the two side doors open
        //side doors
        // if ( l_SideDoorNotClosed[pc::daddy::SIDEMIRROR_RIGHT] || l_SideMirrorNotFlapped[pc::daddy::SIDEMIRROR_RIGHT] || l_rightCamDeg)
        if (l_SideDoorNotClosed[pc::daddy::SIDEMIRROR_RIGHT]) // ! BYD requirement, side mirror not degrade
        {
            pc::daddy::setBit( pc::core::sysconf::RIGHT_CAMERA, l_CameraDeactivationMask);
        }
        else
        {
            pc::daddy::unsetBit( pc::core::sysconf::RIGHT_CAMERA, l_CameraDeactivationMask);
        }
        // if ( l_SideDoorNotClosed[pc::daddy::SIDEMIRROR_LEFT ] || l_SideMirrorNotFlapped[pc::daddy::SIDEMIRROR_LEFT ] || l_leftCamDeg)
        if (l_SideDoorNotClosed[pc::daddy::SIDEMIRROR_LEFT]) // ! BYD requirement, side mirror not degrade
        {
            pc::daddy::setBit( pc::core::sysconf::LEFT_CAMERA, l_CameraDeactivationMask);
        }
        else
        {
            pc::daddy::unsetBit( pc::core::sysconf::LEFT_CAMERA, l_CameraDeactivationMask);
        }

        pc::daddy::CameraDeactivationMaskDaddy & l_refCamDeaMsk =
            pc::daddy::BaseDaddyPorts::sm_CameraDeactivationMaskDaddySenderPort.reserve();
        l_refCamDeaMsk.m_Data = l_CameraDeactivationMask;
        pc::daddy::BaseDaddyPorts::sm_CameraDeactivationMaskDaddySenderPort.deliver();

        // XLOG_INFO_OS( g_viewModeSMContext ) << "camera mask 0x" << std::hex << static_cast<vfc::int32_t>(l_CameraDeactivationMask) // PRQA S 4060
        // << std::dec << XLOG_ENDL;//PRQA S 4060
        // XLOG_INFO_OS( g_viewModeSMContext ) // PRQA S 4060
        // << " front "  << ( pc::daddy::isBitSet(pc::core::sysconf::FRONT_CAMERA, l_CameraDeactivationMask ) ? "DISABLED" : "ENABLED" )
        // << " right "  << ( pc::daddy::isBitSet(pc::core::sysconf::RIGHT_CAMERA, l_CameraDeactivationMask ) ? "DISABLED" : "ENABLED" )
        // << " rear  "  << ( pc::daddy::isBitSet(pc::core::sysconf::REAR_CAMERA , l_CameraDeactivationMask ) ? "DISABLED" : "ENABLED" )
        // << " left  "  << ( pc::daddy::isBitSet(pc::core::sysconf::LEFT_CAMERA , l_CameraDeactivationMask ) ? "DISABLED" : "ENABLED" )
        // << XLOG_ENDL;//PRQA S 4060

    }
    return;
}

void ViewModeStateMachine::OnShutdown( void )
{
#ifdef TARGET_STANDALONE
    XLOG_INFO_OS( g_viewModeSMContext ) << "Stopping ViewMode State Machine thread." << XLOG_ENDL;//PRQA S 4060
    cc::daddy::CustomDaddyPorts::sm_HUDislayModeSwitchDaddy_SenderPort.disconnect( m_HUDislayModeSwitchSMReceiver ) ;
    cc::daddy::CustomDaddyPorts::sm_cpcToSvsOverlay_SenderPort.disconnect( m_cpcToSvsOverlaySMReceiver ) ;
#endif
}

