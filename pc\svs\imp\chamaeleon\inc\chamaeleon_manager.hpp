/// @copyright (C) 2023 Robert <PERSON> GmbH.
/// The reproduction, distribution and utilization of this file as well as the
/// communication of its contents to others without express authorization is
/// prohibited. Offenders will be held liable for the payment of damages. All
/// rights reserved in the event of the grant of a patent, utility model or
/// design.
/// @file

#ifndef PC_SVS_IMP_CHAMAELEONMANAGER_H
#define PC_SVS_IMP_CHAMAELEONMANAGER_H

#include "pc/svs/core/inc/Framework.h"
#include "pc/svs/factory/inc/StitchingLinesManager.h"
#include "pc/svs/factory/inc/SV3DGeode.h"
#include "pc/svs/imp/chamaeleon/inc/chamaeleon_manager_lines.hpp"
#include "pc/svs/imp/chamaeleon/inc/chamaeleon_tabs.hpp"

namespace rbp
{
namespace vis
{
namespace factory
{
class RenderManager;
class SV3DStateGraph;
} // namespace factory
namespace imp
{
namespace chamaeleon
{

///
/// ChamaeleonManager
///
class ChamaeleonManager
{
public:
    ChamaeleonManager(pc::core::Framework* f_pFramework, EChamaeleonView f_settingChamaeleon);

    virtual ~ChamaeleonManager() = default;

    //------------------------------------------------------------------------------------------------------------------
    /// @brief Apply the chamaeleonManager (Set the chamaeleon specific shader uniforms)
    ///
    /// @param[in] f_stateGraph     Reference to the stateGraph
    /// @param[in] f_renderManager  Pointer to the renderManager
    //------------------------------------------------------------------------------------------------------------------
    virtual void apply(pc::factory::SV3DStateGraph& f_stateGraph, pc::factory::RenderManager* f_renderManager) const;

    //------------------------------------------------------------------------------------------------------------------
    /// @brief Update the chamaeleonManager using daddyPorts and StitchingLinesManager.
    ///
    /// @param[in] f_stitchLineMng  Pointer to the stitchingLineManager used in the renderManager
    //------------------------------------------------------------------------------------------------------------------
    void update(const pc::factory::StitchingLinesManager* const f_stitchLineMng = nullptr);

protected:
    const EChamaeleonView        m_chamaeleonViewId;
    bool                         m_useChamaeleonShader{false};
    osg::ref_ptr<osg::Texture2D> m_gainsAsTexture{nullptr};
    ArraySingleCamVec3f          m_gainsAsVecSideBySide{};
    ChamaeleonManagerLines       m_chamaeleonMngLines{};

    void receiveChamaeleonInput();

private:
    ChamaeleonManager();
    ChamaeleonManager(const ChamaeleonManager&)            = delete;
    ChamaeleonManager& operator=(const ChamaeleonManager&) = delete;

    pc::core::Framework* m_framework;
    bool                 m_updateShaderLogic{false};
};

} // namespace chamaeleon
} // namespace imp
} // namespace vis
} // namespace rbp

#endif // PC_SVS_IMP_CHAMAELEONMANAGER_H
