//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: BYD SAAP
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CXH5SZH Chen Xiangqin (CC-DA/EAV2-CN)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS BYD SAAP
/// @file  FisheyeDistortionTable.cpp
/// @brief
//=============================================================================

#include "cc/views/customwarpfisheyeview/inc/FisheyeDistortionTable.h"
#include "cc/target/common/inc/commonInterface.h"
#include "CustomSystemConf.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h"

#include "vfc/core/vfc_types.hpp"
#include <iostream>
#include <vector>
#include <cmath>
#include <algorithm>

using pc::util::logging::g_AppContext;

namespace cc
{
namespace views
{
namespace warpfisheye
{
struct DistortionEntry
{
    double fovAngle;  // Field of view (degree)
    double radius;    // Corresponding radial distance (unit: image height, mm)
};

constexpr double PI_CONST = 3.14159265358979323846;
constexpr double PIXEL_NR_PER_MM = 1000.0/3.0; // 3um/pixel; How many pixels per mm
constexpr double IMAGE_WIDTH_HALF = static_cast<vfc::float64_t>(cc::target::sysconf::E_IMAGE_FORMAT_TEXTURE_WIDTH) * 0.5;
constexpr double IMAGE_HEIGHT_HALF = static_cast<vfc::float64_t>(cc::target::sysconf::E_IMAGE_FORMAT_TEXTURE_HEIGHT) * 0.5;

static std::vector<DistortionEntry> distortionTable;

static bool parseDistortionTable()
{
    std::ifstream file(CONCAT_PATH_VEHICLE_MODEL("cc/vehicle_model/camera_parameters/distortiontable.txt"));
    if (!file.is_open())
    {
        XLOG_ERROR(g_AppContext, "[svs]: Unable to open distortion table" );
        return false;
    }

    double x = 0.0, y = 0.0;
    while (file >> x >> y)
    {
        distortionTable.push_back({x, y});
    }
    file.close();

    return true;
}


// Linear interpolation using binary search
static double interpolate(const std::vector<DistortionEntry>& table, double angle)
{
    if (angle <= table.front().fovAngle)
    {
        return table.front().radius;
    }
    if (angle >= table.back().fovAngle)
    {
        return table.back().radius;
    }

    const auto it = std::lower_bound(table.begin(), table.end(), angle,
        [](const DistortionEntry& entry, double a) -> bool { return entry.fovAngle < a; });

    const size_t i = std::distance(table.begin(), it);
    if (i == 0)
    {
        return table.front().radius;
    }

    const double a1 = table[i - 1].fovAngle, r1 = table[i - 1].radius;
    const double a2 = table[i].fovAngle, r2 = table[i].radius;

    const double t = (angle - a1) / (a2 - a1);
    return r1 + t * (r2 - r1);
}

// 3D camera coordinate system vector -> 2D pixel plane coordinates
bool getFisheyeProject(osg::Vec3f f_vecInCam, osg::Vec2f& f_pixelPos)
{
    // read distortion table
    static bool s_isRead = false;
    static bool s_isValid = false;
    if (false == s_isRead)  // only parse for one time
    {
        s_isValid = parseDistortionTable();
        s_isRead = true;
    }

    if (false == s_isValid)
    {
        return false;
    }

    // m_intrinsicCalib.cam2img(osg::Vec3f(-f_camCoord.y(), -f_camCoord.z(), f_camCoord.x()));
    const double X = -f_vecInCam.y();
    const double Y = -f_vecInCam.z();
    const double Z = f_vecInCam.x();

    // Calculate the Angle of view (in degrees)
    const double theta = std::atan2(std::sqrt(X * X + Y * Y), Z) * 180.0 / PI_CONST;

    // Find the corresponding radial distance
    const double r = interpolate(distortionTable, theta);

    // Normalize the direction vector
    const double nx = X / std::sqrt(X * X + Y * Y);
    const double ny = Y / std::sqrt(X * X + Y * Y);

    // Map to the pixel plane
    const double px = IMAGE_WIDTH_HALF + r * PIXEL_NR_PER_MM * nx;
    const double py = IMAGE_HEIGHT_HALF + r * PIXEL_NR_PER_MM * ny;

    f_pixelPos = osg::Vec2f(static_cast<vfc::float32_t>(px), static_cast<vfc::float32_t>(py));

    return true;
}

} // namespace warpfisheye
} // namespace views
} // namespace cc
