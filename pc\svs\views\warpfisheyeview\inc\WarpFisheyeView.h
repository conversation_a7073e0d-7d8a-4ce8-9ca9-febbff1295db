//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: SVS
//  Target systems: Cross platform
//       Compilers: ISO C++11 compliant
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: ESJ1LR Jose Esparza (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SV3D CORE
/// @file  WarpFisheyeView.h
/// @brief
//=============================================================================

#ifndef PC_SVS_VIEWS_WARPVIEW_H
#define PC_SVS_VIEWS_WARPVIEW_H

#include "pc/svs/core/inc/View.h"
#include "pc/svs/util/osgx/inc/ProjectionObjects.h"

#include "pc/svs/views/warpfisheyeview/inc/FisheyeModels.h"
#include "pc/svs/factory/inc/CameraImageShaders.h"
#include "pc/svs/imp/sh/inc/sharpness_harmonization_manager.hpp"
#include "pc/svs/imp/tnf/inc/temporal_noise_filter_manager.hpp"

extern std::array<std::vector<osg::Vec3f>, 4> g_warpVecInCamArray;

namespace pc
{
namespace core
{
class Framework;
} // namespace core
} // namespace pc


namespace pc
{
namespace core
{
class Framework;
} // namespace core
namespace views
{
//! Contains all classes related to image warping, for example for (but not limited to) fisheye views.
namespace warpfisheye
{

//======================================================
// FisheyeViewSettings
//------------------------------------------------------
/// Coding of fisheye view: Virtual rotation + horizontal fov.
/// <AUTHOR> Jose (CC-DA/EAV3)
/// @ingroup WarpFisheye
//======================================================
class FisheyeViewSettings : public pc::util::coding::ISerializable
{
public:

  FisheyeViewSettings()
    : m_virtualYaw(0.f)
    , m_virtualPitch(30.f)
    , m_virtualRoll(0.f)
  {
  }

  SERIALIZABLE(FisheyeViewSettings)
  {
    ADD_FLOAT_MEMBER(virtualYaw);
    ADD_FLOAT_MEMBER(virtualPitch);
    ADD_FLOAT_MEMBER(virtualRoll);
  }

  float m_virtualYaw;         //!< Virtual Yaw angle [deg]
  float m_virtualPitch;       //!< Virtual Pitch angle [deg]
  float m_virtualRoll;        //!< Virtual Roll angle [deg]
};


//======================================================
// WarpFisheyeView
//------------------------------------------------------
/// Provides the infrastructure to warp input images into
/// any custom model.
/// A projection mesh is used, aligned with the viewport.
/// For each of the vertices in the mesh, texture coordinates
/// are provided according to both input ([C. Mei](http://www.robots.ox.ac.uk/~cmei/articles/projection_model.pdf))
/// and output (e.g. Stereographic, etc) models.
/// <AUTHOR> Jose (CC-DA/EAV3)
/// @ingroup WarpFisheye
//======================================================
class WarpFisheyeView : public pc::core::View
{
public:

  /**
   * Constructor (standard)
   * @param f_name: View name
   * @param f_viewport: Coordinates in output screen, also used for aspect ration calculations
   * @param f_framework:
   * @param f_camId: Each WarpFisheyeView is defined for one camera only.
   * @param f_pModel: Warping model to be used for the output image.
   * @param f_settings: Reference to Fisheye Parameters (e.g. virtual camera rotation)
   * @param f_settingSharpnessHarmonization: Sharpness harmonization settings
   * @param f_settingTemporalNoiseFilter: Temporal noise filter settings
   */
  WarpFisheyeView(  const std::string& f_name,
                     const pc::core::Viewport& f_viewport,
                     pc::core::Framework* f_framework,
                     pc::core::sysconf::Cameras f_camId,
                     pc::views::warpfisheye::FisheyeModel* f_pModel,
                     const pc::views::warpfisheye::FisheyeViewSettings* f_settings,
                     rbp::vis::imp::sh::ESharpnessView f_settingSharpnessHarmonization = rbp::vis::imp::sh::ESharpnessView::FIXED,
                     rbp::vis::imp::tnf::ETnfView f_settingTemporalNoiseFilter = rbp::vis::imp::tnf::ETnfView::DEFAULT);
  /**
   * Constructor with specified crop bounds
   * @param f_name: View name
   * @param f_viewport: Coordinates in output screen, also used for aspect ration calculations
   * @param f_framework:
   * @param f_camId: Each WarpFisheyeView is defined for one camera only.
   * @param f_pModel: Warping model to be used for the output image.
   * @param f_settings: Reference to Fisheye Parameters (e.g. virtual camera rotation)
   * @param f_cropBounds: Crop bounds of the fisheye view when you want to specify them explicitely
   * @param f_settingSharpnessHarmonization: Sharpness harmonization settings
   * @param f_settingTemporalNoiseFilter: Temporal noise filter settings
   */
  WarpFisheyeView(  const std::string& f_name,
                    const pc::core::Viewport& f_viewport,
                    pc::core::Framework* f_framework,
                    pc::core::sysconf::Cameras f_camId,
                    pc::views::warpfisheye::FisheyeModel* f_pModel,
                    const pc::views::warpfisheye::FisheyeViewSettings* f_settings,
                    osg::Vec4f f_cropBounds,
                    rbp::vis::imp::sh::ESharpnessView f_settingSharpnessHarmonization = rbp::vis::imp::sh::ESharpnessView::FIXED,
                    rbp::vis::imp::tnf::ETnfView f_settingTemporalNoiseFilter = rbp::vis::imp::tnf::ETnfView::DEFAULT);

    void setEnableSharpnessHarmonization(bool f_isEnabled, bool f_isDebug);

    void setEnableTemporalNoiseFilter(bool f_isEnabled);

    pc::core::sysconf::Cameras getCamId() const
    {
        return m_camId;
    }

    osg::StateSet* getProjObjStateSet() const
    {
        return m_projObj->getOrCreateStateSet();
    }

  /**
   * custom traverse instructions
   * @param nv
   */
  void traverse(osg::NodeVisitor& f_nv) override;

  std::vector<osg::Vec2us> getUVMap(unsigned int f_resX, unsigned int f_resY);

protected:

  virtual ~WarpFisheyeView() = default;

  /**
   * Common initialisation calls for Fisheye View
   */
  void init();

  /**
   * Called from constructor
   * Set up camera view & projection matrix,
   * create mesh and texture coordinates,
   * assign shader to StateSet.
   */
  void createView(osg::Vec4f f_cropBounds);

  /**
   * Called from constructor
   * In case the crop bounds are not explicitly fetch them by camera ID.
   */
  osg::Vec4f deduceCropBounds() const;

  virtual void handleCameraDegradation();

  void computeTextureCoordinates();

  /**
   * Compute texture coordinates for each Vertex in the projection mesh.
   * Texture coordinates will be computed by lifting the normalized coordinates based in a well-known fisheye model,
   * and re-projected into SatCam by using \ref pc::c2w::IntrinsicCalibration & \ref pc::c2w::ExtrinsicCalibration.
   * Calibration is accessed via \ref pc::core::Framework::m_cameraCalibrationReceiver.
   */
  void updateViewportBounds(float f_left, float f_right, float f_bottom, float f_top);

  void setAspectRatio(vfc::float32_t f_aspectRatio){m_aspectRatio = f_aspectRatio;}

  pc::core::Framework* m_framework;   //!< Access calibration via \ref pc::core::Framework
  pc::core::sysconf::Cameras m_camId; //!< View belongs to a camera only
  osg::ref_ptr<pc::util::osgx::ProjectionObjects> m_projObj;     //!< Projection Mesh
  // member to handle the sharpness harmonization depending on its internal state
  rbp::vis::imp::sh::SharpnessHarmonizationManager m_sharpnessHarmonizationMng;
  // member to handle the temporal noise filter depending on its internal state
  rbp::vis::imp::tnf::TemporalNoiseFilterManager m_temporalNoiseFilterMng;

  factory::RawTexShaderParameters m_shaderParameters{};

private:

  /**
   * Calls \ref WarpFisheyeView::computeTextureCoordinates
   */
  void update();

  void getSubdivisions(unsigned int& f_meshSubdivisionU, unsigned int& f_meshSubdivisionV) const;

  float m_aspectRatio;                //!< Matches output Viewport, used to create projection mesh
  osg::ref_ptr<pc::views::warpfisheye::FisheyeModel> m_model;    //!< Output model
  const pc::views::warpfisheye::FisheyeViewSettings* m_settings; //!< Reference to Fisheye Serializable Element

  vfc::uint32_t m_codingModifCount;         //!< Counter for coding updates
  vfc::uint16_t m_calibSeqNumber;           //!< Seq Number for camera calibration
  vfc::uint16_t m_fisheyeBoundsSeqNumber;   //!< Seq Number for fisheye crop bounds
  std::string m_viewName;

};

} // namespace warpfisheye
} // namespace views
} // namespace pc

#endif // PC_SVS_VIEWS_WARPVIEW_H
