
/// @copyright (C) 2023 Robert <PERSON>.
/// The reproduction, distribution and utilization of this file as well as the
/// communication of its contents to others without express authorization is
/// prohibited. Offenders will be held liable for the payment of damages. All
/// rights reserved in the event of the grant of a patent, utility model or
/// design.
/// @file

#ifndef CC_COM_LINUX_USER_H
#define CC_COM_LINUX_USER_H

#include "ServiceBase.h"
#include "ComDataStructure.h"



namespace svs {

class com_linux_user : public ServiceBase
{
private:
    /* data */
    uint8_t m_tranparentLevel{0};
    int     m_screemIDRequestPre{-1};
    int     m_screemIDRequestCur{-1};
    bool    m_preAppStatus{false};
public:
    com_linux_user(/* args */){}
    ~com_linux_user() = default;

    com_linux_user(const com_linux_user&) = delete;
    com_linux_user& operator=(const com_linux_user&) = delete;

    int init(std::shared_ptr<ServiceCallBack> callBack, std::shared_ptr<ServiceConfig> cfg);
    int deInit() override;

    int setCameraIntrinsicParameters(CameraParameter *pCameraParam, int size) override;

    int setAvmView(int viewId, EWindowId windowId = E_WINDOW_ID__MAIN) override;
    int OnTouchEvent(AVMTouchEventType action, int x, int y, float panVelocity) override;
    int setSlideCallback(SlideCallback callback) override;

    int startCalibration(CalibrationMode mode, CalibrationCallback callback) override;
    int stopCalibration() override;

    int setCarSpeed(float speed) override;
    void setCarGear(CarGearStatus gears) override;
    int SetVehicleWheelPulse(unsigned char* wheelSpeedPulse, int size) override;
    void setVehicleWheelSpeed(unsigned char* speed) override;
    void setVehicleWheelDirection(char *direction, int length) override;
    void setVehicleSteerAngleArray(char* steerAngle, int length) override;
    int setRearWheelAngle(bool valid, int left, int right) override;

    int setFrontRearRadarDistance(int* distance, int length) override;
    int setLeftRightRadarDistance(int* distance, int length) override;

    void setCarTransparent(bool transparent) override;
    void setCarTransparentMode(AVMTransparentMode mode) override;
    void setCarColor(CarColor carColor) override;

    int setLightStatus(AVMLightType light, AVMState state) override;
    int setAirSuspensionHeight(int mode, int leftFront, int rightFront, int leftRear, int rightRear) override;

    void setDoorStatus(AVMDoorType door, AVMState status) override;
    void showDoorWarningWidth(bool show, float angle, int carDoorClassify) override;
    void setDoorAngle(AVMDoorType door,float angle) override;
    int setDoorWidthIndicationStatus(AVMDoorType door, AVMState state) override;

    void setGuidelineDisplay (CarGuideLine mode) override;
    int setRadarWallVisible(bool visible) override;
    int setTrailerLine(bool flag) override;

    int setParkMode(AVMParkMode mode) override;
    int setParkingSpaceSelectedCb(ParkingSpaceSelectedCallback callback) override;
    int setSpaceSlotState(AVMParkSlotState state) override;
    int setParkingRealTimeDataServiceData(AVMParkingSlotVertex vertexPos) override;

    int setCarRotateAngle(float hAngle, float vAngle) override;
    int getCarRotateAngle(float *hAngle, float *vAngle) override;
    int setSurround(int angle) override;
    void set3DCarZoomLevel(AVMZoomLevel zoomLevel) override;

    int setBevFOV(bool fovScale) override;
    int setRemoveDistortion( bool bRemoveDis) override;

    int getSDKVersion(char *sdkVersion, int len) override;
    void setLogEnable(bool enable) override;
    void setLogCallback(CB_FUNC_LOG_PRINT callback) override;

    int get3DCarZoomLevel(int *zoomLevel) override;

    int onTwoFingersTouchEvent(AVMTouchEventType action, int x1, int y1, int x2, int y2) override;
    int getPhysicalXDistance(int *distance, int x, int y) override;
    int getWarningBoundCoordinate(BoundCoordinate *pBoundCoord, int left, int top, int right, int bottom) override;
    int calPixelByPhysicalDistance(int *pixel, int distance) override;
    void notifyAppStatus(bool status) override;
    int setCrabAngles(CrabAngle angles) override;
    void setRearViewMirrorStatus(AVMRearViewMirrorState status) override;
    void setVotState(VotState status) override;
    void setVotRequestRotationDirection(VotDirRotation requestRotationDirection) override;
    void  setGoldLogo(bool isGold) override;
    // int setCrabShowGear(int isShow) override;
};
}
#endif CC_COM_LINUX_USER_H
