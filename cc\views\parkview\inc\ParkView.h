//-------------------------------------------------------------------------------
// Copyright (c) 2017 by <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#ifndef CC_VIEWS_PARKVIEW_INC_PARKVIEW_H_
#define CC_VIEWS_PARKVIEW_INC_PARKVIEW_H_

#include <osg/Node>
#include <osg/Stencil>

#include "pc/svs/core/inc/View.h"
#include "pc/svs/animation/inc/Action.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/assets/parkingspots/inc/ParkingSpot.h"
#include "cc/assets/parkingspots/inc/ParkingSpotManager.h"
// #include "cc/assets/parkingspots/inc/ParkingWaitIcon.h"

namespace cc
{
namespace views
{
namespace parkview
{

//!
//! ParkViewBase
//!
class ParkViewBase : public pc::core::UpscalingView
{
public:
  static osg::Stencil* fetchAndSetStencilFromAsset(osg::Node* f_node, vfc::uint8_t f_stencilRefVal)
  {
    osg::Stencil* l_stencil = dynamic_cast<osg::Stencil*>(f_node->getOrCreateStateSet()->getAttribute(osg::StateAttribute::STENCIL));
    if (!l_stencil)
    {
      //if not create a new one
      l_stencil = new osg::Stencil;
    }
    l_stencil->setFunction(osg::Stencil::EQUAL, static_cast<vfc::int32_t>(f_stencilRefVal), ~0u);
    l_stencil->setOperation(osg::Stencil::KEEP, osg::Stencil::KEEP, osg::Stencil::KEEP);
    return(l_stencil);
  }

  ParkViewBase(
    pc::core::UpscalingData* f_upscalingData,
    const std::string& f_name,
    const pc::core::Viewport& f_viewport,
    const pc::virtcam::VirtualCamera& f_camPos,
    pc::core::Framework* f_framework,
    cc::core::AssetId f_parkingSpotManagerAssetId,
    ReferenceFrame f_referenceFrame = osg::Transform::ABSOLUTE_RF);

  ParkViewBase(const ParkViewBase&)            = delete;
  ParkViewBase& operator=(const ParkViewBase&) = delete;

  // void updateParkWaitIcon(bool f_rightSide = true);
  virtual void traverse(osg::NodeVisitor& f_nv) override;
  virtual void hideView() override;

  void addCullMask(osg::Node::NodeMask f_cullMask) {m_cullMask = f_cullMask;}

protected:

  pc::core::Framework* m_framework;
  osg::Node::NodeMask m_cullMask;
  cc::core::AssetId m_parkingSpotManagerAssetId;
  osg::Stencil* m_stencil;
};


//!
//! ParkInView
//!
class ParkInView : public ParkViewBase
{
public:

  ParkInView(
    pc::core::UpscalingData* f_upscalingData,
    const pc::core::Viewport& f_viewport,
    const pc::core::Viewport& f_viewportVert,
    const pc::virtcam::VirtualCamera& f_camPos,
    const pc::virtcam::VirtualCamera& f_camPosVert,
    pc::core::Framework* f_framework,
    cc::core::AssetId f_parkingSpotManagerAssetId);

  virtual void showView() override;

  virtual void hideView() override;

  virtual void traverse(osg::NodeVisitor& f_nv) override;

protected:

  virtual ~ParkInView();

private:
  //! Copy constructor is not permitted.
  ParkInView (const ParkInView& other); // = delete
  //! Copy assignment operator is not permitted.
  ParkInView& operator=(const ParkInView& other); // = delete

  cc::target::common::EThemeTypeHU m_theme;
  pc::core::Viewport m_horiViewport;
  pc::core::Viewport m_vertViewport;
  pc::virtcam::VirtualCamera m_horiCamPos;
  pc::virtcam::VirtualCamera m_vertCamPos;
};

} // namespace parkview
} // namespace views
} // namespace cc

#endif /* CC_VIEWS_PARKVIEW_INC_PARKVIEW_H_ */