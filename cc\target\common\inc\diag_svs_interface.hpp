#ifndef DIAG_SVS_INTERFACE_HPP_INCLUDED
#define DIAG_SVS_INTERFACE_HPP_INCLUDED

#include "vfc/core/vfc_types.hpp"
#include "daddy_ifbase.hpp"

namespace diag_svs
{
  // SVS_Perspective
  class CDiag2SVS_Perspective : public daddy::CInterfaceBase
  {
    public:
    CDiag2SVS_Perspective() : daddy::CInterfaceBase() {}

    vfc::uint8_t m_view;
  };

  // SVS_Camera - Eye Center Up
  class CDiag2SVS_Camera : public daddy::CInterfaceBase
  {
    public:
    CDiag2SVS_Camera() : daddy::CInterfaceBase() {}

    vfc::int16_t m_eyeX;
    vfc::int16_t m_eyeY;
    vfc::int16_t m_eyeZ;

    vfc::int16_t m_centerX;
    vfc::int16_t m_centerY;
    vfc::int16_t m_centerZ;

    vfc::int16_t m_upX;
    vfc::int16_t m_upY;
    vfc::int16_t m_upZ;
  };
}

#endif // DIAG_SVS_INTERFACE_HPP_INCLUDED
