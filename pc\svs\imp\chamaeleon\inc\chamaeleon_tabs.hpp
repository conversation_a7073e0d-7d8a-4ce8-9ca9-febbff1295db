/// @copyright (C) 2023 <PERSON> GmbH.
/// The reproduction, distribution and utilization of this file as well as the
/// communication of its contents to others without express authorization is
/// prohibited. Offenders will be held liable for the payment of damages. All
/// rights reserved in the event of the grant of a patent, utility model or
/// design.
/// @file

#ifndef PC_SVS_IMP_CHAMAELEONTABS_H
#define PC_SVS_IMP_CHAMAELEONTABS_H

#include "pc/svs/factory/inc/SV3DGeode.h"
#include "pc/svs/imp/common/inc/indexed_array.hpp"

#include "osg/Texture2D"
#include "osg/Vec3"

#include <algorithm>
#include <array>

namespace rbp
{
namespace vis
{
namespace imp
{
namespace chamaeleon
{

//------------------------------------------------------------------------------------------------------------------
/// @brief enum enumerating the available chamaeleon settings which can be view specific
//------------------------------------------------------------------------------------------------------------------
enum class EChamaeleonView : vfc::int32_t
{
    // enumeration values must be kept as index
    NO_CHAMAELEON,
    DEFAULT,      ///< Default setting
    SIDE_BY_SIDE, ///< Setting for side-by-side views
};

//------------------------------------------------------------------------------------------------------------------
/// @brief enum enumerating the overlap ROIs between two adjacent cameras.
//------------------------------------------------------------------------------------------------------------------
enum class ETabWorldOverlapROI : vfc::int32_t
{
    // enumeration values must be kept as index
    WORLD_FRONT_RIGHT, ///< overlap between front and right camera
    WORLD_REAR_RIGHT,  ///< overlap between rear and right  camera
    WORLD_REAR_LEFT,   ///< overlap between rear and left camera
    WORLD_FRONT_LEFT,  ///< overlap between front and left camera

    NUM_WORLD_ROIS ///< number of overlaps (must be last entry)
};

static_assert(
    static_cast<vfc::int32_t>(ETabWorldOverlapROI::WORLD_FRONT_RIGHT) ==
        static_cast<vfc::int32_t>(pc::factory::TwoCamArea::TWO_CAM_FRONT_RIGHT),
    "Expecting WORLD_FRONT_RIGHT == TwoCamArea::TWO_CAM_FRONT_RIGHT");
static_assert(
    static_cast<vfc::int32_t>(ETabWorldOverlapROI::WORLD_REAR_RIGHT) ==
        static_cast<vfc::int32_t>(pc::factory::TwoCamArea::TWO_CAM_REAR_RIGHT),
    "Expecting WORLD_REAR_RIGHT == TwoCamArea::TWO_CAM_REAR_RIGHT");
static_assert(
    static_cast<vfc::int32_t>(ETabWorldOverlapROI::WORLD_REAR_LEFT) ==
        static_cast<vfc::int32_t>(pc::factory::TwoCamArea::TWO_CAM_REAR_LEFT),
    "Expecting WORLD_REAR_LEFT == TwoCamArea::TWO_CAM_REAR_LEFT");
static_assert(
    static_cast<vfc::int32_t>(ETabWorldOverlapROI::WORLD_FRONT_LEFT) ==
        static_cast<vfc::int32_t>(pc::factory::TwoCamArea::TWO_CAM_FRONT_LEFT),
    "Expecting WORLD_FRONT_LEFT == TwoCamArea::TWO_CAM_FRONT_LEFT");
static_assert(
    static_cast<vfc::int32_t>(ETabWorldOverlapROI::NUM_WORLD_ROIS) ==
        static_cast<vfc::int32_t>(pc::factory::TwoCamArea::NUM_TWO_CAM_AREAS),
    "Expecting NUM_WORLD_ROIS == TwoCamArea::NUM_TWO_CAM_AREAS");

enum class ETabImageOverlapROI : vfc::int32_t
{
    // enumeration values must be kept as index
    IMAGE_FRONT_TO_RIGHT, ///< ROI in front image belonging to world overlap between front and right camera
    IMAGE_FRONT_TO_LEFT,  ///< ROI in front image belonging to world overlap between front and left camera

    IMAGE_RIGHT_TO_REAR,  ///< ROI in right image belonging to world overlap between right and rear camera
    IMAGE_RIGHT_TO_FRONT, ///< ROI in right image belonging to world overlap between right and front camera

    IMAGE_REAR_TO_LEFT,  ///< ROI in rear image belonging to world overlap between rear and left camera
    IMAGE_REAR_TO_RIGHT, ///< ROI in rear image belonging to world overlap between rear and right camera

    IMAGE_LEFT_TO_FRONT, ///< ROI in left image belonging to world overlap between left and front camera
    IMAGE_LEFT_TO_REAR,  ///< ROI in left image belonging to world overlap between left and rear camera

    NUM_IMAGE_ROIS ///< number of image ROIs (must be last entry)
};

static_assert(
    static_cast<vfc::int32_t>(ETabImageOverlapROI::NUM_IMAGE_ROIS) / 2 ==
        static_cast<vfc::int32_t>(ETabWorldOverlapROI::NUM_WORLD_ROIS),
    "Expecting two rois per overlap region");

constexpr inline pc::factory::TwoCamArea getTwoCamArea(ETabWorldOverlapROI f_worldOverlap)
{
    return static_cast<pc::factory::TwoCamArea>(static_cast<vfc::int32_t>(f_worldOverlap));
}

static_assert(
    getTwoCamArea(ETabWorldOverlapROI::WORLD_FRONT_RIGHT) == pc::factory::TwoCamArea::TWO_CAM_FRONT_RIGHT,
    "Expecting getTwoCamArea(WORLD_FRONT_RIGHT) == TwoCamArea::TWO_CAM_FRONT_RIGHT");
static_assert(
    getTwoCamArea(ETabWorldOverlapROI::WORLD_REAR_RIGHT) == pc::factory::TwoCamArea::TWO_CAM_REAR_RIGHT,
    "Expecting getTwoCamArea(WORLD_REAR_RIGHT) == TwoCamArea::TWO_CAM_REAR_RIGHT");
static_assert(
    getTwoCamArea(ETabWorldOverlapROI::WORLD_REAR_LEFT) == pc::factory::TwoCamArea::TWO_CAM_REAR_LEFT,
    "Expecting getTwoCamArea(WORLD_REAR_LEFT) == TwoCamArea::TWO_CAM_REAR_LEFT");
static_assert(
    getTwoCamArea(ETabWorldOverlapROI::WORLD_FRONT_LEFT) == pc::factory::TwoCamArea::TWO_CAM_FRONT_LEFT,
    "Expecting getTwoCamArea(WORLD_FRONT_LEFT) == TwoCamArea::TWO_CAM_FRONT_LEFT");
static_assert(
    getTwoCamArea(ETabWorldOverlapROI::NUM_WORLD_ROIS) == pc::factory::TwoCamArea::NUM_TWO_CAM_AREAS,
    "Expecting getTwoCamArea(NUM_WORLD_ROIS) == TwoCamArea::NUM_TWO_CAM_AREAS");

constexpr inline ETabWorldOverlapROI getWorldOverlap(pc::factory::TwoCamArea f_twoCamArea)
{
    return static_cast<ETabWorldOverlapROI>(static_cast<vfc::int32_t>(f_twoCamArea));
}

static_assert(
    getWorldOverlap(pc::factory::TwoCamArea::TWO_CAM_FRONT_RIGHT) == ETabWorldOverlapROI::WORLD_FRONT_RIGHT,
    "Expecting getWorldOverlap(TwoCamArea::TWO_CAM_FRONT_RIGHT) == WORLD_FRONT_RIGHT");
static_assert(
    getWorldOverlap(pc::factory::TwoCamArea::TWO_CAM_REAR_RIGHT) == ETabWorldOverlapROI::WORLD_REAR_RIGHT,
    "Expecting getWorldOverlap(TwoCamArea::TWO_CAM_REAR_RIGHT) == WORLD_REAR_RIGHT");
static_assert(
    getWorldOverlap(pc::factory::TwoCamArea::TWO_CAM_REAR_LEFT) == ETabWorldOverlapROI::WORLD_REAR_LEFT,
    "Expecting getWorldOverlap(TwoCamArea::TWO_CAM_REAR_LEFT) == WORLD_REAR_LEFT");
static_assert(
    getWorldOverlap(pc::factory::TwoCamArea::TWO_CAM_FRONT_LEFT) == ETabWorldOverlapROI::WORLD_FRONT_LEFT,
    "Expecting getWorldOverlap(TwoCamArea::TWO_CAM_FRONT_LEFT) == WORLD_FRONT_LEFT");
static_assert(
    getWorldOverlap(pc::factory::TwoCamArea::NUM_TWO_CAM_AREAS) == ETabWorldOverlapROI::NUM_WORLD_ROIS,
    "Expecting getWorldOverlap(TwoCamArea::NUM_TWO_CAM_AREAS) == NUM_WORLD_ROIS");

enum class EChamaeleonRoiRoundRobin : vfc::int32_t
{
    // The integer value of the enum represents the number of image ROIs that need to be copied per iteration
    SINGLE_WORLD_ROI = 2, ///< One World Roi is rendered and analyzed in a call
    DOUBLE_WORLD_ROI = 4, ///< Two World Rois are rendered and analyzed in a call
    NO_ROUND_ROBIN   = 8, ///< Every World Roi is rendered and analyzed  in a call
};

constexpr EChamaeleonRoiRoundRobin RTPBO_ROUND_ROBIN_MODE{EChamaeleonRoiRoundRobin::SINGLE_WORLD_ROI};

//------------------------------------------------------------------------------------------------------------------
/// @brief Maps specified id of an image ROI to the id of the corresponding world overlap. (E.g.
/// ::chamaeleon::ROI_REAR_TO_LEFT and ::chamaeleon::ROI_LEFT_TO_REAR on ::chamaeleon::REAR_LEFT).
///
/// @param[in] f_roiId  Id of image ROI
/// @return id of world overlap
//------------------------------------------------------------------------------------------------------------------
inline ETabWorldOverlapROI getWorldOverlap(ETabImageOverlapROI f_roiId)
{
    const vfc::int32_t l_res{static_cast<vfc::int32_t>(f_roiId) / 2};
    const vfc::int32_t l_rest{static_cast<vfc::int32_t>(f_roiId) % 2};

    const vfc::int32_t l_overlapIdx{(((0 == l_res) && (1 == l_rest)) ? 3 : (l_res - l_rest))};

    /// The validity is ensured by the check of the number of overlap regions compared to rois
    return (static_cast<ETabWorldOverlapROI>(l_overlapIdx)); // PRQA S 3013
}

//------------------------------------------------------------------------------------------------------------------
/// @brief Get the image overlap ROI 1 which corresponds to a specified world overlap.
///
/// @param[in] f_worldOverlap  Id of specified world overlap (e.g. ::chamaeleon::REAR_LEFT)
/// @return Id of image ROI 1
//------------------------------------------------------------------------------------------------------------------
constexpr inline ETabImageOverlapROI getOverlapROI1(ETabWorldOverlapROI f_worldOverlap)
{
    /// The validity is ensured by the modulo operation above.
    return (f_worldOverlap == ETabWorldOverlapROI::NUM_WORLD_ROIS)
               ? ETabImageOverlapROI::NUM_IMAGE_ROIS
               : static_cast<ETabImageOverlapROI>(
                     (static_cast<vfc::int32_t>(f_worldOverlap) * 2) %
                     static_cast<vfc::int32_t>(ETabImageOverlapROI::NUM_IMAGE_ROIS)); // PRQA S 3013
}

static_assert(
    getOverlapROI1(ETabWorldOverlapROI::WORLD_FRONT_RIGHT) == ETabImageOverlapROI::IMAGE_FRONT_TO_RIGHT,
    "Expecting getOverlapROI1(WORLD_FRONT_RIGHT) == IMAGE_FRONT_TO_RIGHT");
static_assert(
    getOverlapROI1(ETabWorldOverlapROI::WORLD_REAR_RIGHT) == ETabImageOverlapROI::IMAGE_RIGHT_TO_REAR,
    "Expecting getOverlapROI1(WORLD_REAR_RIGHT) == IMAGE_RIGHT_TO_REAR");
static_assert(
    getOverlapROI1(ETabWorldOverlapROI::WORLD_REAR_LEFT) == ETabImageOverlapROI::IMAGE_REAR_TO_LEFT,
    "Expecting getOverlapROI1(WORLD_REAR_LEFT) == IMAGE_REAR_TO_LEFT");
static_assert(
    getOverlapROI1(ETabWorldOverlapROI::WORLD_FRONT_LEFT) == ETabImageOverlapROI::IMAGE_LEFT_TO_FRONT,
    "Expecting getOverlapROI1(WORLD_FRONT_LEFT) == IMAGE_LEFT_TO_FRONT");
static_assert(
    getOverlapROI1(ETabWorldOverlapROI::NUM_WORLD_ROIS) == ETabImageOverlapROI::NUM_IMAGE_ROIS,
    "Expecting getOverlapROI1(NUM_WORLD_ROIS) == NUM_IMAGE_ROIS");

//------------------------------------------------------------------------------------------------------------------
/// @brief Get the image overlap ROI 2 which corresponds to a specified world overlap.
///
/// @param[in] f_worldOverlap  Id of specified world overlap (e.g. ::chamaeleon::REAR_LEFT)
/// @return Id of image ROI 2
//------------------------------------------------------------------------------------------------------------------
constexpr inline ETabImageOverlapROI getOverlapROI2(ETabWorldOverlapROI f_worldOverlap)
{
    /// The validity is ensured by the modulo operation above.
    return (f_worldOverlap == ETabWorldOverlapROI::NUM_WORLD_ROIS)
               ? ETabImageOverlapROI::NUM_IMAGE_ROIS
               : static_cast<ETabImageOverlapROI>(
                     (static_cast<vfc::int32_t>(f_worldOverlap) * 2 + 3) %
                     static_cast<vfc::int32_t>(ETabImageOverlapROI::NUM_IMAGE_ROIS)); // PRQA S 3013
}

static_assert(
    getOverlapROI2(ETabWorldOverlapROI::WORLD_FRONT_RIGHT) == ETabImageOverlapROI::IMAGE_RIGHT_TO_FRONT,
    "Expecting getOverlapROI2(WORLD_FRONT_RIGHT) == IMAGE_RIGHT_TO_FRONT");
static_assert(
    getOverlapROI2(ETabWorldOverlapROI::WORLD_REAR_RIGHT) == ETabImageOverlapROI::IMAGE_REAR_TO_RIGHT,
    "Expecting getOverlapROI2(WORLD_REAR_RIGHT) == IMAGE_REAR_TO_RIGHT");
static_assert(
    getOverlapROI2(ETabWorldOverlapROI::WORLD_REAR_LEFT) == ETabImageOverlapROI::IMAGE_LEFT_TO_REAR,
    "Expecting getOverlapROI2(WORLD_REAR_LEFT) == IMAGE_LEFT_TO_REAR");
static_assert(
    getOverlapROI2(ETabWorldOverlapROI::WORLD_FRONT_LEFT) == ETabImageOverlapROI::IMAGE_FRONT_TO_LEFT,
    "Expecting getOverlapROI2(WORLD_FRONT_LEFT) == IMAGE_FRONT_TO_LEFT");
static_assert(
    getOverlapROI2(ETabWorldOverlapROI::NUM_WORLD_ROIS) == ETabImageOverlapROI::NUM_IMAGE_ROIS,
    "Expecting getOverlapROI2(NUM_WORLD_ROIS) == NUM_IMAGE_ROIS");

constexpr inline pc::factory::SingleCamArea getSingleCamLeft(pc::factory::TwoCamArea f_twoCam)
{
    using enum_type = std::underlying_type_t<pc::factory::SingleCamArea>;
    /// The validity is ensured by the modulo operation above.
    return (f_twoCam == pc::factory::TwoCamArea::NUM_TWO_CAM_AREAS)
               ? pc::factory::SingleCamArea::NUM_SINGLE_CAM_AREAS
               : static_cast<pc::factory::SingleCamArea>(
                     static_cast<enum_type>(f_twoCam) %
                     static_cast<enum_type>(pc::factory::SingleCamArea::NUM_SINGLE_CAM_AREAS)); // PRQA S 3013
}

static_assert(
    getSingleCamLeft(pc::factory::TwoCamArea::TWO_CAM_FRONT_RIGHT) == pc::factory::SingleCamArea::SINGLE_CAM_FRONT,
    "Expecting getSingleCamLeft(TwoCamArea::TWO_CAM_FRONT_RIGHT) == SingleCamArea::SINGLE_CAM_FRONT");
static_assert(
    getSingleCamLeft(pc::factory::TwoCamArea::TWO_CAM_REAR_RIGHT) == pc::factory::SingleCamArea::SINGLE_CAM_RIGHT,
    "Expecting getSingleCamLeft(TwoCamArea::TWO_CAM_REAR_RIGHT) == SingleCamArea::SINGLE_CAM_RIGHT");
static_assert(
    getSingleCamLeft(pc::factory::TwoCamArea::TWO_CAM_REAR_LEFT) == pc::factory::SingleCamArea::SINGLE_CAM_REAR,
    "Expecting getSingleCamLeft(TwoCamArea::TWO_CAM_REAR_LEFT) == SingleCamArea::SINGLE_CAM_REAR");
static_assert(
    getSingleCamLeft(pc::factory::TwoCamArea::TWO_CAM_FRONT_LEFT) == pc::factory::SingleCamArea::SINGLE_CAM_LEFT,
    "Expecting getSingleCamLeft(TwoCamArea::TWO_CAM_FRONT_LEFT) == SingleCamArea::SINGLE_CAM_LEFT");
static_assert(
    getSingleCamLeft(pc::factory::TwoCamArea::NUM_TWO_CAM_AREAS) == pc::factory::SingleCamArea::NUM_SINGLE_CAM_AREAS,
    "Expecting getSingleCamLeft(TwoCamArea::NUM_TWO_CAM_AREAS) == SingleCamArea::NUM_SINGLE_CAM_AREAS");

constexpr inline pc::factory::SingleCamArea getSingleCamRight(pc::factory::TwoCamArea f_twoCam)
{
    using enum_type = std::underlying_type_t<pc::factory::SingleCamArea>;
    /// The validity is ensured by the modulo operation above.
    return (f_twoCam == pc::factory::TwoCamArea::NUM_TWO_CAM_AREAS)
               ? pc::factory::SingleCamArea::NUM_SINGLE_CAM_AREAS
               : static_cast<pc::factory::SingleCamArea>(
                     (static_cast<enum_type>(f_twoCam) + 1U) %
                     static_cast<enum_type>(pc::factory::SingleCamArea::NUM_SINGLE_CAM_AREAS)); // PRQA S 3013
}

static_assert(
    getSingleCamRight(pc::factory::TwoCamArea::TWO_CAM_FRONT_RIGHT) == pc::factory::SingleCamArea::SINGLE_CAM_RIGHT,
    "Expecting getSingleCamLeft(TwoCamArea::TWO_CAM_FRONT_RIGHT) == SingleCamArea::SINGLE_CAM_RIGHT");
static_assert(
    getSingleCamRight(pc::factory::TwoCamArea::TWO_CAM_REAR_RIGHT) == pc::factory::SingleCamArea::SINGLE_CAM_REAR,
    "Expecting getSingleCamLeft(TwoCamArea::TWO_CAM_REAR_RIGHT) == SingleCamArea::SINGLE_CAM_REAR");
static_assert(
    getSingleCamRight(pc::factory::TwoCamArea::TWO_CAM_REAR_LEFT) == pc::factory::SingleCamArea::SINGLE_CAM_LEFT,
    "Expecting getSingleCamLeft(TwoCamArea::TWO_CAM_REAR_LEFT) == SingleCamArea::SINGLE_CAM_LEFT");
static_assert(
    getSingleCamRight(pc::factory::TwoCamArea::TWO_CAM_FRONT_LEFT) == pc::factory::SingleCamArea::SINGLE_CAM_FRONT,
    "Expecting getSingleCamLeft(TwoCamArea::TWO_CAM_FRONT_LEFT) == SingleCamArea::SINGLE_CAM_FRONT");
static_assert(
    getSingleCamRight(pc::factory::TwoCamArea::NUM_TWO_CAM_AREAS) == pc::factory::SingleCamArea::NUM_SINGLE_CAM_AREAS,
    "Expecting getSingleCamLeft(TwoCamArea::NUM_TWO_CAM_AREAS) == SingleCamArea::NUM_SINGLE_CAM_AREAS");

constexpr inline ETabImageOverlapROI getOverlapROILeft(pc::factory::SingleCamArea f_singleCam)
{
    using enum_type = std::underlying_type_t<ETabImageOverlapROI>;
    /// The validity is ensured by the modulo operation above.
    return (f_singleCam == pc::factory::SingleCamArea::NUM_SINGLE_CAM_AREAS)
               ? ETabImageOverlapROI::NUM_IMAGE_ROIS
               : static_cast<ETabImageOverlapROI>(
                     ((static_cast<enum_type>(f_singleCam) * 2) + 1) %
                     static_cast<enum_type>(ETabImageOverlapROI::NUM_IMAGE_ROIS)); // PRQA S 3013
}

static_assert(
    getOverlapROILeft(pc::factory::SingleCamArea::SINGLE_CAM_FRONT) == ETabImageOverlapROI::IMAGE_FRONT_TO_LEFT,
    "Expecting getOverlapROILeft(SingleCamArea::SINGLE_CAM_FRONT) == IMAGE_FRONT_TO_LEFT");
static_assert(
    getOverlapROILeft(pc::factory::SingleCamArea::SINGLE_CAM_RIGHT) == ETabImageOverlapROI::IMAGE_RIGHT_TO_FRONT,
    "Expecting getOverlapROILeft(SingleCamArea::SINGLE_CAM_RIGHT) == IMAGE_RIGHT_TO_FRONT");
static_assert(
    getOverlapROILeft(pc::factory::SingleCamArea::SINGLE_CAM_REAR) == ETabImageOverlapROI::IMAGE_REAR_TO_RIGHT,
    "Expecting getOverlapROILeft(SingleCamArea::SINGLE_CAM_REAR) == IMAGE_REAR_TO_RIGHT");
static_assert(
    getOverlapROILeft(pc::factory::SingleCamArea::SINGLE_CAM_LEFT) == ETabImageOverlapROI::IMAGE_LEFT_TO_REAR,
    "Expecting getOverlapROILeft(SingleCamArea::SINGLE_CAM_LEFT) == IMAGE_LEFT_TO_REAR");
static_assert(
    getOverlapROILeft(pc::factory::SingleCamArea::NUM_SINGLE_CAM_AREAS) == ETabImageOverlapROI::NUM_IMAGE_ROIS,
    "Expecting getOverlapROILeft(SingleCamArea::NUM_SINGLE_CAM_AREAS) == NUM_IMAGE_ROIS");

constexpr inline ETabImageOverlapROI getOverlapROIRight(pc::factory::SingleCamArea f_singleCam)
{
    /// The validity is ensured by the modulo operation above.
    return (f_singleCam == pc::factory::SingleCamArea::NUM_SINGLE_CAM_AREAS)
               ? ETabImageOverlapROI::NUM_IMAGE_ROIS
               : static_cast<ETabImageOverlapROI>(
                     (static_cast<vfc::int32_t>(f_singleCam) * 2) %
                     static_cast<vfc::int32_t>(ETabImageOverlapROI::NUM_IMAGE_ROIS)); // PRQA S 3013
}

static_assert(
    getOverlapROIRight(pc::factory::SingleCamArea::SINGLE_CAM_FRONT) == ETabImageOverlapROI::IMAGE_FRONT_TO_RIGHT,
    "Expecting getSingleCamLeft(SingleCamArea::SINGLE_CAM_FRONT) == IMAGE_FRONT_TO_RIGHT");
static_assert(
    getOverlapROIRight(pc::factory::SingleCamArea::SINGLE_CAM_RIGHT) == ETabImageOverlapROI::IMAGE_RIGHT_TO_REAR,
    "Expecting getSingleCamLeft(SingleCamArea::SINGLE_CAM_RIGHT) == IMAGE_RIGHT_TO_REAR");
static_assert(
    getOverlapROIRight(pc::factory::SingleCamArea::SINGLE_CAM_REAR) == ETabImageOverlapROI::IMAGE_REAR_TO_LEFT,
    "Expecting getSingleCamLeft(SingleCamArea::SINGLE_CAM_REAR) == IMAGE_REAR_TO_LEFT");
static_assert(
    getOverlapROIRight(pc::factory::SingleCamArea::SINGLE_CAM_LEFT) == ETabImageOverlapROI::IMAGE_LEFT_TO_FRONT,
    "Expecting getSingleCamLeft(SingleCamArea::SINGLE_CAM_LEFT) == IMAGE_LEFT_TO_FRONT");
static_assert(
    getOverlapROIRight(pc::factory::SingleCamArea::NUM_SINGLE_CAM_AREAS) == ETabImageOverlapROI::NUM_IMAGE_ROIS,
    "Expecting getSingleCamLeft(SingleCamArea::NUM_SINGLE_CAM_AREAS) == NUM_IMAGE_ROIS");

constexpr inline pc::factory::SingleCamArea getSingleCam(ETabImageOverlapROI f_roiId)
{
    using enum_type = std::underlying_type_t<pc::factory::SingleCamArea>;
    /// The validity is ensured by the modulo operation above.
    return (f_roiId == ETabImageOverlapROI::NUM_IMAGE_ROIS)
               ? pc::factory::SingleCamArea::NUM_SINGLE_CAM_AREAS
               : static_cast<pc::factory::SingleCamArea>(
                     (static_cast<enum_type>(f_roiId) / 2U) %
                     static_cast<enum_type>(pc::factory::SingleCamArea::NUM_SINGLE_CAM_AREAS)); // PRQA S 3013
}

static_assert(
    getSingleCam(ETabImageOverlapROI::IMAGE_FRONT_TO_RIGHT) == pc::factory::SingleCamArea::SINGLE_CAM_FRONT,
    "Expecting getSingleCam(IMAGE_FRONT_TO_RIGHT) == SingleCamArea::SINGLE_CAM_FRONT");
static_assert(
    getSingleCam(ETabImageOverlapROI::IMAGE_FRONT_TO_LEFT) == pc::factory::SingleCamArea::SINGLE_CAM_FRONT,
    "Expecting getSingleCam(IMAGE_FRONT_TO_LEFT) == SingleCamArea::SINGLE_CAM_FRONT");
static_assert(
    getSingleCam(ETabImageOverlapROI::IMAGE_RIGHT_TO_REAR) == pc::factory::SingleCamArea::SINGLE_CAM_RIGHT,
    "Expecting getSingleCam(IMAGE_RIGHT_TO_REAR) == SingleCamArea::SINGLE_CAM_RIGHT");
static_assert(
    getSingleCam(ETabImageOverlapROI::IMAGE_RIGHT_TO_FRONT) == pc::factory::SingleCamArea::SINGLE_CAM_RIGHT,
    "Expecting getSingleCam(IMAGE_RIGHT_TO_FRONT) == SingleCamArea::SINGLE_CAM_RIGHT");
static_assert(
    getSingleCam(ETabImageOverlapROI::IMAGE_REAR_TO_LEFT) == pc::factory::SingleCamArea::SINGLE_CAM_REAR,
    "Expecting getSingleCam(IMAGE_REAR_TO_LEFT == SingleCamArea::SINGLE_CAM_REAR)");
static_assert(
    getSingleCam(ETabImageOverlapROI::IMAGE_REAR_TO_RIGHT) == pc::factory::SingleCamArea::SINGLE_CAM_REAR,
    "Expecting getSingleCam(IMAGE_REAR_TO_RIGHT) == SingleCamArea::SINGLE_CAM_REAR");
static_assert(
    getSingleCam(ETabImageOverlapROI::IMAGE_LEFT_TO_FRONT) == pc::factory::SingleCamArea::SINGLE_CAM_LEFT,
    "Expecting getSingleCam(IMAGE_LEFT_TO_FRONT) == SingleCamArea::SINGLE_CAM_LEFT");
static_assert(
    getSingleCam(ETabImageOverlapROI::IMAGE_LEFT_TO_REAR) == pc::factory::SingleCamArea::SINGLE_CAM_LEFT,
    "Expecting getSingleCam(IMAGE_LEFT_TO_REAR) == SingleCamArea::SINGLE_CAM_LEFT");
static_assert(
    getSingleCam(ETabImageOverlapROI::NUM_IMAGE_ROIS) == pc::factory::SingleCamArea::NUM_SINGLE_CAM_AREAS,
    "Expecting getSingleCam(NUM_IMAGE_ROIS) == SingleCamArea::NUM_SINGLE_CAM_AREAS");

enum class ETabColorSpaceRGB : vfc::int32_t
{
    RED,
    GREEN,
    BLUE,
    NUM_COLORS_RGB
};

////////////////////////////////////////////////////////////////////////////////////////////////////////////
/// Index for ETabWorldOverlapROI used in Chamaeleon
using CWorldOverlapIndex =
    common::TIndex<ETabWorldOverlapROI, ETabWorldOverlapROI::WORLD_FRONT_RIGHT, ETabWorldOverlapROI::NUM_WORLD_ROIS>;
////////////////////////////////////////////////////////////////////////////////////////////////////////////

////////////////////////////////////////////////////////////////////////////////////////////////////////////
/// Index for ETabImageOverlapROI used in Chamaeleon
using CImageOverlapIndex =
    common::TIndex<ETabImageOverlapROI, ETabImageOverlapROI::IMAGE_FRONT_TO_RIGHT, ETabImageOverlapROI::NUM_IMAGE_ROIS>;
////////////////////////////////////////////////////////////////////////////////////////////////////////////

////////////////////////////////////////////////////////////////////////////////////////////////////////////
/// Index for ETabColorSpaceRGB used in Chamaeleon Rois
using CColorSpaceRGBIndex =
    common::TIndex<ETabColorSpaceRGB, ETabColorSpaceRGB::RED, ETabColorSpaceRGB::NUM_COLORS_RGB>;
////////////////////////////////////////////////////////////////////////////////////////////////////////////

////////////////////////////////////////////////////////////////////////////////////////////////////////////
/// @brief            An array with exactly one entry per ETabWorldOverlapROI
/// @tparam ValueType Type of the values to be contained in the array
template <class ValueType>
using TArrayWorldOverlap = common::TIndexedArray<CWorldOverlapIndex, ValueType>;
////////////////////////////////////////////////////////////////////////////////////////////////////////////

////////////////////////////////////////////////////////////////////////////////////////////////////////////
/// @brief            An array with exactly one entry per ETabImageOverlapROI
/// @tparam ValueType Type of the values to be contained in the array
template <class ValueType>
using TArrayImageOverlap = common::TIndexedArray<CImageOverlapIndex, ValueType>;
////////////////////////////////////////////////////////////////////////////////////////////////////////////

////////////////////////////////////////////////////////////////////////////////////////////////////////////
/// @brief            An array with exactly one entry per ETabColorSpaceRGB
/// @tparam ValueType Type of the values to be contained in the array
template <class ValueType>
using TArrayColorSpaceRGB = common::TIndexedArray<CColorSpaceRGBIndex, ValueType>;
////////////////////////////////////////////////////////////////////////////////////////////////////////////

constexpr vfc::int32_t ROI_DIM{16};
static_assert(vfc::TIsPow2<ROI_DIM>::value, "ROI_DIM should be a power of two");

constexpr vfc::int32_t ROI_DIM_LOD{4};
static_assert((1 < ROI_DIM_LOD) && (10 >= ROI_DIM_LOD), "We expect (1 < ROI_DIM_LOD) && (10 >= ROI_DIM_LOD)");
static_assert(ROI_DIM == (1 << ROI_DIM_LOD), "We expect ROI_DIM == (1 << ROI_DIM_LOD)");

constexpr vfc::int32_t RTPBO_ROI_WIDTH{ROI_DIM};
constexpr vfc::int32_t RTPBO_ROI_HEIGHT{ROI_DIM};
static_assert(RTPBO_ROI_HEIGHT == RTPBO_ROI_WIDTH, "We expect RTPBO_ROI_HEIGHT == RTPBO_ROI_WIDTH");

using ArraySingleCamVec3f = common::TArraySingleCamArea<osg::Vec3f>;

} // namespace chamaeleon
} // namespace imp
} // namespace vis
} // namespace rbp

#endif // PC_SVS_IMP_CHAMAELEONTABS_H
