//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================

#include "cc/views/planview/inc/PlanViewEnlargeCallback.h"
#include "cc/views/planview/inc/PlanView.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/core/src/ViewModeToggle.h"
#include "cc/util/logging/inc/LoggingContexts.h"
#include "cc/assets/uielements/inc/Vehicle2DOverlay.h"
#include "pc/svs/util/math/inc/CommonMath.h"

#include "cc/imgui/inc/imgui_manager.h"

using pc::util::logging::g_AppContext;

namespace cc
{
namespace views
{
namespace planview
{

pc::util::coding::Item<PlanViewEnlargeSettings> g_planViewEnlargeSettings("PlanViewEnlargeSettings");

PlanViewEnlargeCallback::PlanViewEnlargeCallback(
    osg::Camera*               f_camera,
    pc::core::Framework*       f_framework,
    pc::virtcam::VirtualCamera f_camPos,
    const std::string&         f_name,
    const EnlargeSettings*     f_enlargeSettings,
    bool f_verbose)
    : pc::core::CameraUpdater{}
    , m_verbose(f_verbose)
    , m_name{}
    , m_modifiedCount(~0u)
    , m_enlargeSettings(f_enlargeSettings)
    , m_normalMode(false)
    , m_enlargeStatus(cc::daddy::NO_ENLARGE)
    , m_top()
    , m_bottom()
    , m_left()
    , m_right()
    , m_zNear()
    , m_zFar()
    , m_framework(f_framework)
    , m_camPos(f_camPos)
    , m_vehicle2dManager(nullptr)
{
    if (nullptr != f_camera)
    {
        m_name = f_name;
        f_camera->getProjectionMatrixAsOrtho(m_left, m_right, m_bottom, m_top, m_zNear, m_zFar);
        m_currentTop    = m_top;
        m_currentBottom = m_bottom;
        m_currentLeft   = m_left;
        m_currentRight  = m_right;
        m_currentZNear  = m_zNear;
        m_currentZFar   = m_zFar;
        m_targetTop     = m_top;
        m_targetBottom  = m_bottom;
        m_targetLeft    = m_left;
        m_targetRight   = m_right;
        m_targetZNear   = m_zNear;
        m_targetZFar    = m_zFar;
        // imguiInit();
    }
}

bool PlanViewEnlargeCallback::isParameterDirty()
{
    return
        (m_targetXOffset != m_currentXOffset) ||
        (m_targetYOffset != m_currentYOffset) ||
        (m_targetTop != m_currentTop) ||
        (m_targetBottom != m_currentBottom) ||
        (m_targetLeft != m_currentLeft) ||
        (m_targetRight != m_currentRight) ||
        (m_targetZNear != m_currentZNear) ||
        (m_targetZFar != m_currentZFar);
}

void PlanViewEnlargeCallback::imguiInit()
{
    IMGUI_SET_DEFAULT_SLIDER_INT("Settings", "Enlarge Status", 0);

    IMGUI_SET_DEFAULT_SLIDER_FLOAT(getName(), "enlargeFactor", m_enlargeSettings->m_enlargeFactor);
    IMGUI_SET_DEFAULT_SLIDER_FLOAT(getName(), "Front Offset X", m_enlargeSettings->m_enlargeFrontOffset.x());
    IMGUI_SET_DEFAULT_SLIDER_FLOAT(getName(), "Front Offset Y", m_enlargeSettings->m_enlargeFrontOffset.y());
    IMGUI_SET_DEFAULT_SLIDER_FLOAT(getName(), "Rear Offset X", m_enlargeSettings->m_enlargeRearOffset.x());
    IMGUI_SET_DEFAULT_SLIDER_FLOAT(getName(), "Rear Offset Y", m_enlargeSettings->m_enlargeRearOffset.y());
}

void PlanViewEnlargeCallback::imguiUpdate()
{
    // FOR TUNING ONLY, DO NOT USE IN TARGET :D
    const_cast<EnlargeSettings*>(m_enlargeSettings)->m_enlargeFactor =
        IMGUI_GET_SLIDER_FLOAT(getName(), "enlargeFactor", 0.1f, 2.0f);
    const_cast<EnlargeSettings*>(m_enlargeSettings)->m_enlargeFrontOffset.x() =
        (double)IMGUI_GET_SLIDER_FLOAT(getName(), "Front Offset X", -10.0f, 10.0f);
    const_cast<EnlargeSettings*>(m_enlargeSettings)->m_enlargeFrontOffset.y() =
        (double)IMGUI_GET_SLIDER_FLOAT(getName(), "Front Offset Y", -10.0f, 10.0f);
    const_cast<EnlargeSettings*>(m_enlargeSettings)->m_enlargeRearOffset.x() =
        (double)IMGUI_GET_SLIDER_FLOAT(getName(), "Rear Offset X", -10.0f, 10.0f);
    const_cast<EnlargeSettings*>(m_enlargeSettings)->m_enlargeRearOffset.y() =
        (double)IMGUI_GET_SLIDER_FLOAT(getName(), "Rear Offset Y", -10.0f, 10.0f);
}

void PlanViewEnlargeCallback::updateTargetParameter(osg::Camera* f_camera)
{
    if (nullptr == f_camera)
    {
        return;
    }
    f_camera->getProjectionMatrixAsOrtho(m_currentLeft, m_currentRight, m_currentBottom, m_currentTop, m_currentZNear, m_currentZFar);
    m_startLeft    = m_currentLeft;
    m_startRight   = m_currentRight;
    m_startBottom  = m_currentBottom;
    m_startTop     = m_currentTop;
    m_startXOffset = m_currentXOffset;
    m_startYOffset = m_currentYOffset;

    switch (m_enlargeStatus)
    {
    // default:
    case cc::daddy::NO_ENLARGE:
    {
        // if (m_zoomMode)
        // {
        //     m_targetLeft    = m_left * m_enlargeSettings->m_enlargeFactor;
        //     m_targetRight   = m_right * m_enlargeSettings->m_enlargeFactor;
        //     m_targetBottom  = m_bottom * m_enlargeSettings->m_enlargeFactor;
        //     m_targetTop     = m_top * m_enlargeSettings->m_enlargeFactor;
        //     m_targetXOffset = 0.0;
        //     m_targetYOffset = 0.0;
        //     IMGUI_LOG("PlanViewEnlargeCallback", "Status", ": BIRD EYE ENLARGE");
        // }
        // else
        {

            m_targetLeft    = m_left;
            m_targetRight   = m_right;
            m_targetBottom  = m_bottom;
            m_targetTop     = m_top;
            m_targetXOffset = 0.0;
            m_targetYOffset = 0.0;
            IMGUI_LOG("PlanViewEnlargeCallback", "Status", ": NO_ENLARGE");
            if (m_verbose)
            {
                XLOG_INFO(g_AppContext, "DEBUG ENLARGE - updateTargetPrameter() : set target parameters to NO_ENLARGE");
            }
        }
        break;
    }
    case cc::daddy::ENLARGE_MIDDLE:
    {
        m_targetLeft    = m_left * m_enlargeSettings->m_enlargeFactor;
        m_targetRight   = m_right * m_enlargeSettings->m_enlargeFactor;
        m_targetBottom  = m_bottom * m_enlargeSettings->m_enlargeFactor;
        m_targetTop     = m_top * m_enlargeSettings->m_enlargeFactor;
        m_targetXOffset = 0.0;
        m_targetYOffset = 0.0;
        IMGUI_LOG("PlanViewEnlargeCallback", "Status", "ENLARGE_MIDDLE");
        if (m_verbose)
        {
            XLOG_INFO(g_AppContext, "DEBUG ENLARGE - updateTargetPrameter() : set target parameters to ENLARGE_MIDDLE");
        }
        break;
    }
    case cc::daddy::ENLARGE_FRONT:
    {
        m_targetLeft    = m_left * m_enlargeSettings->m_enlargeFactor;
        m_targetRight   = m_right * m_enlargeSettings->m_enlargeFactor;
        m_targetBottom  = m_bottom * m_enlargeSettings->m_enlargeFactor;
        m_targetTop     = m_top * m_enlargeSettings->m_enlargeFactor;
        m_targetXOffset = m_enlargeSettings->m_enlargeFrontOffset.x() * (m_targetRight - m_targetLeft);
        m_targetYOffset = m_enlargeSettings->m_enlargeFrontOffset.y() * (m_targetTop - m_targetBottom);
        IMGUI_LOG("PlanViewEnlargeCallback", "Status", ": ENLARGE_FRONT");
        if (m_verbose)
        {
            XLOG_INFO(g_AppContext, "DEBUG ENLARGE - updateTargetPrameter() : set target parameters to ENLARGE_FRONT");
        }
        break;
    }
    case cc::daddy::ENLARGE_REAR:
    {
        m_targetLeft    = m_left * m_enlargeSettings->m_enlargeFactor;
        m_targetRight   = m_right * m_enlargeSettings->m_enlargeFactor;
        m_targetBottom  = m_bottom * m_enlargeSettings->m_enlargeFactor;
        m_targetTop     = m_top * m_enlargeSettings->m_enlargeFactor;
        m_targetXOffset = m_enlargeSettings->m_enlargeRearOffset.x() * (m_targetRight - m_targetLeft);
        m_targetYOffset = m_enlargeSettings->m_enlargeRearOffset.y() * (m_targetTop - m_targetBottom);
        IMGUI_LOG("PlanViewEnlargeCallback", "Status", ": ENLARGE_REAR");
        if (m_verbose)
        {
            XLOG_INFO(g_AppContext, "DEBUG ENLARGE - updateTargetPrameter() : set target parameters to ENLARGE_REAR");
        }
        break;
    }
    default:
    {
        {
            m_targetLeft    = m_left;
            m_targetRight   = m_right;
            m_targetBottom  = m_bottom;
            m_targetTop     = m_top;
            m_targetXOffset = 0.0;
            m_targetYOffset = 0.0;
            IMGUI_LOG("PlanViewEnlargeCallback", "Status", ": NO_ENLARGE");
            if (m_verbose)
            {
                XLOG_INFO(g_AppContext, "DEBUG ENLARGE - updateTargetPrameter() : set target parameters to NO_ENLARGE");
            }
        }
        break;
    }
    }

    // f_camera->setProjectionMatrixAsOrtho2D(left, right, bottom, top);
    // f_camera->setViewMatrix(
    //     osg::Matrixd::lookAt(m_camPos.m_eye, m_camPos.m_center, m_camPos.m_up) *
    //     osg::Matrixd::translate(xOffset, yOffset, 0.0));
}

void PlanViewEnlargeCallback::updateCameraMatrix(osg::Camera* f_camera)
{
    if (f_camera == nullptr)
    {
        return;
    }
    const vfc::float32_t l_smoothPercentage = pc::util::smootherstep(0.0f, 1.0f, static_cast<vfc::float32_t>(m_progressPercentage));
    m_currentLeft    = m_startLeft    * (1.0 - l_smoothPercentage)  + m_targetLeft    * l_smoothPercentage;
    m_currentRight   = m_startRight   * (1.0 - l_smoothPercentage)  + m_targetRight   * l_smoothPercentage;
    m_currentBottom  = m_startBottom  * (1.0 - l_smoothPercentage)  + m_targetBottom  * l_smoothPercentage;
    m_currentTop     = m_startTop     * (1.0 - l_smoothPercentage)  + m_targetTop     * l_smoothPercentage;
    m_currentXOffset = m_startXOffset * (1.0 - l_smoothPercentage)  + m_targetXOffset * l_smoothPercentage;
    m_currentYOffset = m_startYOffset * (1.0 - l_smoothPercentage)  + m_targetYOffset * l_smoothPercentage;

    f_camera->setProjectionMatrixAsOrtho2D(
        m_currentLeft,
        m_currentRight,
        m_currentBottom,
        m_currentTop);
    f_camera->setViewMatrix(
        osg::Matrixd::lookAt(m_camPos.m_eye, m_camPos.m_center, m_camPos.m_up) *
        osg::Matrixd::translate(m_currentXOffset, m_currentYOffset, 0.0));

    // if (m_verbose)
    // {
    //     XLOG_INFO(g_AppContext, "DEBUG ENLARGE - updateCameraMatrix() : smoothPercentage = " << l_smoothPercentage * 100.0f);
    // }

    if (m_vehicle2dManager != nullptr)
    {
        m_vehicle2dManager->setEnlargeFactor(static_cast<vfc::float32_t>(m_currentLeft / m_left));
        m_vehicle2dManager->setEnlargeProgressPercentage(l_smoothPercentage);
        // m_vehicle2dManager->updateIconsSizePosition();
    }
}

// void PlanViewEnlargeCallback::operator()(osg::Node* f_node, osg::NodeVisitor* f_nv)
void PlanViewEnlargeCallback::updateCamera(osg::Camera* f_camera, const osg::FrameStamp* /*f_frameStamp */)
{
    using namespace cc::daddy;
    const auto view = dynamic_cast<pc::core::View*>(f_camera);
    if (view != nullptr)
    {
        const auto vehicle2d = dynamic_cast<cc::assets::uielements::Vehicle2DOverlay*>(view->getAsset(cc::core::AssetId::EASSETS_VEHICLE_2D_ICON));
        if (vehicle2d != nullptr)
        {
            m_vehicle2dManager = vehicle2d->getManager();
        }
    }

    bool hasNewData = false;
    bool newBevData = false;
    constexpr bool newViewId = false;
    constexpr bool newEnlargeData = false;

    if (m_framework->asCustomFramework()->m_BirdEyeViewSwitch_ReceiverPort.hasData())
    {
        const auto birdEyeViewSwitch =
            m_framework->asCustomFramework()->m_BirdEyeViewSwitch_ReceiverPort.getData()->m_Data;
        newBevData = (m_zoomMode != birdEyeViewSwitch);
        m_dirty = m_dirty || newBevData;
        m_zoomMode = birdEyeViewSwitch;
        m_normalMode = !birdEyeViewSwitch;
        hasNewData = hasNewData || m_framework->asCustomFramework()->m_BirdEyeViewSwitch_ReceiverPort.hasNewData();
    }

    bool isFullscreenPrev = m_isFullscreen;
    switch (m_framework->getCurrentScreenId())
    {
    case EScreenID_FULLSCREEN:
    case EScreenID_FULLSCREEN_REAR_ENLARGE:
    case EScreenID_FULLSCREEN_FRONT_ENLARGE:
    {
        m_isFullscreen = true;
        break;
    }
    default:
    {
        m_isFullscreen = false;
        break;
    }
    }

    if (isFullscreenPrev != m_isFullscreen)
    {
        m_dirty = true;
    }
    if (m_dirty)
    {
        m_dirty = false;
        auto l_enlargeStatusPrev = m_enlargeStatus;
        if (m_enlargeStatus == PlanViewEnlargeStatus::NO_ENLARGE || m_enlargeStatus == PlanViewEnlargeStatus::ENLARGE_MIDDLE)
        {
            m_enlargeStatus = (m_isFullscreen) ? PlanViewEnlargeStatus::NO_ENLARGE
                                : (m_zoomMode)   ? PlanViewEnlargeStatus::ENLARGE_MIDDLE
                                                : PlanViewEnlargeStatus::NO_ENLARGE;
        }
        if (m_enlargeStatus != l_enlargeStatusPrev)
        {
            updateTargetParameter(f_camera);
        }
        // XLOG_INFO_OS(g_AppContext) << m_framework->getCurrentScreenId() << ": after m_currentLeft:" << m_currentLeft<< XLOG_ENDL;
        // XLOG_INFO_OS(g_AppContext) << m_framework->getCurrentScreenId() << ": after m_targetLeft:" << m_targetLeft<< XLOG_ENDL;
        // XLOG_INFO_OS(g_AppContext) << m_framework->getCurrentScreenId() << ": after m_startLeft:" << m_startLeft<< XLOG_ENDL;
        if (g_planViewEnlargeSettings->m_enableAnimation)
        {
            m_isAnimate = m_isAnimate || (newBevData && hasNewData); // qac fixed
            // m_isAnimate |= (newBevData && hasNewData);
            // m_isAnimate |= (isFullscreenPrev != m_isFullscreen);
        }
        if (m_isAnimate && m_isFullscreen && (m_enlargeStatus == PlanViewEnlargeStatus::NO_ENLARGE || m_enlargeStatus == PlanViewEnlargeStatus::ENLARGE_MIDDLE))
        {
            m_isAnimate= false;
        }
        if (m_isAnimate)
        {
            m_progressPercentage = 0.0f;
            if (m_vehicle2dManager != nullptr)
            {
                m_vehicle2dManager->setStartPosition();
                m_vehicle2dManager->setEnlargeStatus(m_enlargeStatus);
                // m_vehicle2dManager->setBevMode(m_zoomMode && (m_enlargeStatus == PlanViewEnlargeStatus::NO_ENLARGE));
            }
        }
        if (m_verbose)
        {
            XLOG_INFO(g_AppContext, "DEBUG ENLARGE - operator(): m_dirty=true, m_enlargeStatus=" << m_enlargeStatus
                        << ", newBevData=" << newBevData
                        << ", m_isFullscreen=" << m_isFullscreen
                        << ", m_isAnimate=" << m_isAnimate
                        << ", m_progressPercentage=" << (m_progressPercentage * 100.0f));
        }
    }

    const auto viewName = view->getName();
    IMGUI_LOG("Vehicle2DOverlay", "m_progressPercentage", m_progressPercentage);


    //! Instant Jump
    if (isParameterDirty() && !m_isAnimate && (isFullscreenPrev == m_isFullscreen))
    {
        if (m_verbose)
        {
            XLOG_INFO(g_AppContext, "DEBUG ENLARGE - operator(): Instant jump");
        }
        m_progressPercentage = 1.0;
        updateCameraMatrix(f_camera);
        IMGUI_LOG("PlanViewEnlargeCallback", "Animating", false);
    }
    //! Animating
    if (isParameterDirty() && m_isAnimate)
    {
        m_progressPercentage += m_enlargeSettings->m_enlargeSpeed;
        m_progressPercentage = std::min(1.0, m_progressPercentage);
        updateCameraMatrix(f_camera);
        IMGUI_LOG("PlanViewEnlargeCallback", "Animating", true);
        if (m_verbose)
        {
            XLOG_INFO(g_AppContext, "DEBUG ENLARGE - operator(): Animating, m_progressPercentage=" << m_progressPercentage * 100.0f);
        }
    }
    //! Finished
    if (!isParameterDirty() && m_isAnimate)
    {
        if (m_verbose)
        {
            XLOG_INFO(g_AppContext, "DEBUG ENLARGE - operator(): Finished animation");
        }
        XLOG_INFO(g_AppContext, getName() << ": finished animation");
        m_isAnimate = false;
        // m_progressPercentage = 0.0;
        IMGUI_LOG("PlanViewEnlargeCallback", "Animating", false);
    }
    //! Idle
    // if (!isParameterDirty() && !m_isAnimate)
    // {
    // }
}

} // namespace planview
} // namespace views
} // namespace cc
