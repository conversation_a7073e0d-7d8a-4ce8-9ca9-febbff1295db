//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#ifndef SV3D_PC_DADDYTYPES_H
#define SV3D_PC_DADDYTYPES_H

#define USE_BITSET 0

#include "vfc/core/vfc_siunits_convenienttypes.hpp"

#include "adapter/daddy_monitor.hpp"
#include "daddy_ifbase.hpp" //CInterfaceBase

#include "pc/svs/core/inc/SystemConf.h"
#include "pc/svs/c2w/inc/SatCam.h"
#include "pc/svs/imp/chamaeleon/inc/chamaeleon_tabs.hpp"
#include "pc/svs/imp/common/inc/common_tabs.hpp"
#include "pc/svs/imp/common/inc/utils.hpp"
#include "pc/svs/imp/sh/inc/sharpness_harmonization_tabs.hpp"
#include "pc/svs/imp/tnf/inc/temporal_noise_filter_tabs.hpp"

#ifdef WITH_FUSION
#include "pc/svs/daddy-interfaces/fusionobjects/FusionObjects.h"
#endif

#ifdef WITH_RSP
#include "pc/svs/daddy-interfaces/rsp/radial_obstacle_fan_output.hpp"
#include "pc/svs/daddy-interfaces/rsp/triangulation_vodometry_output.hpp"
#endif

#include "pc/svs/worker/fusion/inc/fs/conv_xv_types.h"
#include "pc/svs/factory/inc/AdaptMeshPoints.h"
#include "pc/svs/factory/inc/Masking.h"
#include "pc/svs/vehicle/inc/Ultrasonic.h"

#include <osg/Vec4f>
#include <array>
#include <time.h>

#if USE_BITSET
#include <bitset> // http://en.cppreference.com/w/cpp/utility/bitset
#endif


namespace pc
{
namespace daddy
{

//!
//! CDaddy template base class
//!
template <typename T>
struct CDaddy : public ::daddy::CInterfaceBase
{
  typedef T value_type;
  T m_Data;
};

//! Generic automobile data *******************************************************************************************

//! vehicle speed
typedef float kph_float_t ; //use vfc::CSI::si_kilometre_per_hour_f32_t ?
typedef CDaddy<kph_float_t> SpeedDaddy;

//! driving direction
enum DrivingDirection : signed int
{
  DRVDIR_NONE = 0,
  DRVDIR_FORWARD = 1,
  DRVDIR_BACKWARD = 2,
  DRVDIR_DRIVING = 3,
  DRVDIR_RESERVED = 4,
  DRVDIR_ERROR = 5,
  DRVDIR_UNFILLED = 6
};

enum EVehMovingDirection : vfc::uint8_t
{
    FORWARD     = 0,
    BACKWARD    = 1,
    UNKNOWN     = 2,
    STANDSTILL  = 3,
};

typedef CDaddy<signed int> DrivingDirDaddy;

//! steering angle
// Must be a CCW value.
typedef CDaddy<vfc::CSI::si_degree_f32_t> SteeringAngleDaddy;


//! Gear
enum EGear : int
{
  GEAR_INIT    = 0,
  GEAR_1       = 1,
  GEAR_2       = 2,
  GEAR_3       = 3,
  GEAR_4       = 4,
  GEAR_5       = 5,
  GEAR_6       = 6,
  GEAR_7       = 7,
  GEAR_8       = 8,
  GEAR_9       = 9,
  GEAR_N       = 10,
  GEAR_R       = 11,
  GEAR_P       = 12,
  GEAR_D       = 13,
  GEAR_INVALID = 14
};
typedef CDaddy<int> GearDaddy;

//! Odometry
struct OdometryData
{
  vfc::CSI::si_metre_f32_t m_xPos;
  vfc::CSI::si_metre_f32_t m_yPos;
  vfc::CSI::si_radian_f32_t m_yawAngle;
  vfc::CSI::si_metre_per_second_f32_t m_velocity;
  vfc::CSI::si_per_metre_f32_t m_kappaRaw;
  vfc::CSI::si_per_metre_f32_t m_kappaBwd;
  vfc::CSI::si_per_metre_f32_t m_kappaFwd;
  vfc::CSI::si_radian_f32_t m_steeringAngleRear;
  vfc::CSI::si_radian_f32_t m_steeringAngleRearBwd;
  vfc::CSI::si_radian_f32_t m_steeringAngleRearFwd;
  EVehMovingDirection       m_vehMoveDir;     //!< VHM vehicle motion direction based on WIC directions
  EVehMovingDirection       m_vehDriveDir;    //!< VHM vehicle motion direction based on gear signal
};
typedef CDaddy<OdometryData> OdometryDataDaddy;

//! Door state
enum ECarDoorID : int
{
  CARDOOR_NONE = -1,
  CARDOOR_FRONT_RIGHT = 0,
  CARDOOR_FRONT_LEFT = 1,
  CARDOOR_TRUNK,
  CARDOOR_REAR_RIGHT,
  CARDOOR_REAR_LEFT,
  CARDOOR_HOOD,
  CARDOOR_SPOILER,
  NUMBER_OF_CARDOORS
};

enum ECarDoorState : int
{
  CARDOORSTATE_NONE = -1,
  CARDOORSTATE_CLOSED,
  CARDOORSTATE_OPEN,
  CARDOORSTATE_NOT_PLAUSIBLE,
  CARDOORSTATE_INVALID,
  NUMBER_OF_CARDOORSTATES
};
typedef CDaddy<int[NUMBER_OF_CARDOORS]> DoorStateDaddy;

//! Door Animation State
struct DoorAnimationState
{
  bool AnimationOngoingOrOpenFL;
  bool AnimationOngoingOrOpenFR;
  bool AnimationOngoingOrOpenRL;
  bool AnimationOngoingOrOpenRR;
  bool AnimationOngoingOrOpenTrunk;
  bool AnimationOngoingOrOpenHood;
  bool AnimationOngoingOrOpenMirrorLeft;
  bool AnimationOngoingOrOpenMirrorRight;
};
typedef CDaddy< DoorAnimationState > DoorAnimationStateDaddy ;

//! Side mirror state
enum ESideMirrorID : int
{
  SIDEMIRROR_LEFT = 0,
  SIDEMIRROR_RIGHT = 1,
  NUMBER_OF_SIDEMIRRORS = 2
};
enum EMirrorState : int
{
  MIRRORSTATE_NOT_FLAPPED,
  MIRRORSTATE_FLAPPED,
  MIRRORSTATE_UNFOLDING,
  MIRRORSTATE_FOLDING,
  MIRRORSTATE_INVALID,
  NUMBER_OF_CARMIRRORSTATES
};

typedef std::array<int, NUMBER_OF_SIDEMIRRORS> MirrorStateArray;
typedef CDaddy<MirrorStateArray> MirrorStateDaddy;

//! Change View of WarpFisheyeView
struct WarpFisheyeViewChanger
{
  WarpFisheyeViewChanger()
    : m_left(-1.f)
    , m_right(1.f)
    , m_bottom(-1.f)
    , m_top(1.f)
  {
  }

  double m_left;
  double m_right;
  double m_bottom;
  double m_top;
};

typedef CDaddy<WarpFisheyeViewChanger> WarpFisheyeViewChangerDaddy;

//! Indicator state
enum EIndicatorState : int
{
  INDICATOR_OFF = 0,
  INDICATOR_LEFT = 1,
  INDICATOR_RIGHT = 2,
  INDICATOR_WARN = 3
};
typedef CDaddy<int> IndicatorStateDaddy;

//! Brake Light state
enum EBrakeLightState : int
{
  BRAKE_LIGHT_OFF = 0,
  BRAKE_LIGHT_ON = 1
};
typedef CDaddy<int> BrakeLightStateDaddy;

//! Vehicle Light state
enum EVehicleLightState : int
{
  LIGHT_OFF = 0,
  DAY_LIGHT_ON = 1,
  BEAMS_ON = 2,
  HIGH_BEAMS_ON = 3
};
typedef CDaddy<int> VehicleLightStateDaddy;

enum ETrailerConnectedState : int
{
  TRAILER_DISCONNECTED = 0,
  TRAILER_CONNECTED = 1
};
typedef CDaddy<int> TrailerConnectedDaddy;

//! Camera calibration ************************************************************************************************
typedef CDaddy<c2w::SatCamArray> CameraCalibrationDaddy;

//! Camera masking ****************************************************************************************************
typedef CDaddy<factory::MaskArray> CameraMasksDaddy;

//! Camera status *****************************************************************************************************
//! Degradation mask
#if USE_BITSET
typedef std::bitset<pc::core::sysconf::E_SYSTEM_CONFIG_NUMBER_OF_CAMERAS> daddy4bitfield ;
#else
typedef unsigned int daddy4bitfield;
#endif
//! Degradation mask (e.g. camera does not work). 1 => degraded, 0 => nominal
typedef CDaddy<daddy4bitfield> CameraDegradationMaskDaddy; // system input
//! Deactivation mask (e.g. door open). 1 => deactivated, 0 => activated
typedef CDaddy<daddy4bitfield> CameraDeactivationMaskDaddy; // state machine output

//! Convenience function for flipping bits in these bitmasks
inline bool isBitSet(int f_bitNo, unsigned int& f_mask)
{
  return (f_mask & (1 << f_bitNo));
}

inline void setBit(int f_bitNo, unsigned int& f_mask)
{
  f_mask |= (1 << f_bitNo); // flip bit to 1
}

inline void unsetBit(int f_bitNo, unsigned int& f_mask)
{
  f_mask &= ~(1 << f_bitNo); // flip bit to 0
}

inline void flipBit(int f_bitNo, unsigned int& f_mask)
{                                // flappy bit
  if (isBitSet(f_bitNo, f_mask)) // if bit is set (1)
  {
    unsetBit(f_bitNo, f_mask);
  }
  else // bit is not set (0)
  {
    setBit(f_bitNo, f_mask);
  }
  return;
}

//! Ultrasonic data ***************************************************************************************************
typedef CDaddy<vehicle::UltrasonicData> UltrasonicDataDaddy;

//! TrajCutDistDaddy
typedef pc::daddy::CDaddy< float > TrajCutDistDaddy;

//! TrajCutFadeStartOffsetDaddy
typedef pc::daddy::CDaddy< float > TrajCutFadeStartOffsetDaddy;

//! TrajCutFadeEndOffsetDaddy
typedef pc::daddy::CDaddy< float > TrajCutFadeEndOffsetDaddy;

//! Fusion data *******************************************************************************************************
typedef CDaddy<fs_conv_xv::CReducedExtrObstDataArray> FusionDataReducedExtr2DDaddy;

#ifdef WITH_RSP
//! Radial obstacle fans from SfM *************************************************************************************
typedef CDaddy<rsp::CRadialObstacleFanOutput> RadialObstacleFanDaddy;

//! Visual odometry from SfM *************************************************************************************
typedef CDaddy<triangulation::CTriangulationVOdometryOutput> VisualOdometryDaddy;
#endif

#ifdef WITH_FUSION
//! Fusion objects ****************************************************************************************************
typedef CDaddy<envproc::fusionobjects::FusionObjects> FusionObjectsDaddy;
#endif

//! Stitching data ****************************************************************************************************
typedef CDaddy<osg::Vec4f> StitchDataDaddy;

//! AdaptMeshPoints ***************************************************************************************************
typedef CDaddy<factory::AdaptMeshPoints> AdaptMeshPointsDaddy;

//! SIL stuff *******************************************************************************************************
//! keyboard
typedef CDaddy< int > KbdDaddy ; //actually it is an enum (avoiding pulling a ton of deps)

//! Floor Plate Toggle
struct FloorPlateType
{
  bool texture_floor_plate;
};
typedef pc::daddy::CDaddy < FloorPlateType > FloorPlateDaddy ;

//! Current Screen Id
typedef pc::daddy::CDaddy < unsigned int > CurrentScreenIdDaddy;

//info to control the viewport size
typedef pc::daddy::CDaddy<vfc::uint32_t> AdaptViewPortDaddy;
//! ViewMode
struct ViewMode
{
  ViewMode()
    : m_prev(0)
    , m_curr(0)
    , m_dumpPixmap(false)
  {
  }

  ViewMode(int f_prev, int f_curr)
    : m_prev(f_prev)
    , m_curr(f_curr)
    , m_dumpPixmap(false)
  {
  }

  bool changed() const
  {
    return m_prev != m_curr;
  }

  int m_prev;
  int m_curr;
  bool m_dumpPixmap;
};

typedef pc::daddy::CDaddy<ViewMode> ViewModeDaddy;

struct ChamaeleonOutputType
{
    ChamaeleonOutputType()
    {
    }

    bool                                           m_enableTopview{false};
    osg::Texture2D*                                m_gainsAsTexture{nullptr};
    bool                                           m_enableSideBySide{false};
    rbp::vis::imp::chamaeleon::ArraySingleCamVec3f m_gainsAsVecSideBySide{};
};
typedef pc::daddy::CDaddy<ChamaeleonOutputType> ChamaeleonOutputDaddy;

struct SharpnessSettings
{
    SharpnessSettings()
        : m_viewSettings()
    {
        rbp::vis::imp::sh::CSharpnessSettingsView l_default;
        vfc::contiguous_fill_n(m_viewSettings.begin(), rbp::vis::imp::common::usize(m_viewSettings), l_default);
    }

    rbp::vis::imp::sh::TArraySharpnessViews<rbp::vis::imp::sh::CSharpnessSettingsView> m_viewSettings;
};
typedef pc::daddy::CDaddy<SharpnessSettings> SharpnessSettingsDaddy;

struct TnfSettings
{
    TnfSettings()
        : m_viewSettings{}
    {
        const rbp::vis::imp::tnf::CTnfSettingsView l_default;
        vfc::contiguous_fill_n(m_viewSettings.begin(), rbp::vis::imp::common::usize(m_viewSettings), l_default);
    }

    rbp::vis::imp::tnf::TArrayTnfViews<rbp::vis::imp::tnf::CTnfSettingsView> m_viewSettings;
};
typedef pc::daddy::CDaddy<TnfSettings> TnfSettingsDaddy;

/// ISP data
struct CInternalISPData
{
    CInternalISPData() // PRQA S 4207 # R2
        : m_luminance(0.0F)
        , m_agcGain(0)
    {
        vfc::contiguous_fill_n(m_wbGains.begin(), rbp::vis::imp::common::usize(m_wbGains), 0U);
    }

    vfc::float32_t m_luminance; ///< luminance value approximated from ISP settings in lux
    vfc::int32_t   m_agcGain;   ///< automatic gain control value
    rbp::vis::imp::common::TArraySRGB<vfc::uint32_t> m_wbGains; ///< white balance gains
};

struct ISPData
{
    ISPData()
        : m_ispData()
    {
        CInternalISPData l_default;
        vfc::contiguous_fill_n(m_ispData.begin(), rbp::vis::imp::common::usize(m_ispData), l_default);
    }

    rbp::vis::imp::common::TArrayCamera<CInternalISPData> m_ispData;
};
typedef pc::daddy::CDaddy<ISPData> ISPDataDaddy;

//info of static calibration status
typedef pc::daddy::CDaddy<bool> CalibrationStsDaddy;

} // namespace daddy
} // namespace pc

#endif
