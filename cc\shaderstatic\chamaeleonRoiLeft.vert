//-------------------------------------------------------------------------------
// Copyright (c) 2023 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON> GmbH. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

// #version 320 es

attribute highp vec4 osg_Vertex;
attribute highp vec2 osg_MultiTexCoord0;

uniform highp mat4 osg_ModelViewProjectionMatrix;

varying highp vec2 v_texLeftCam;

void main()
{
  v_texLeftCam = osg_MultiTexCoord0;

  gl_Position = osg_ModelViewProjectionMatrix * osg_Vertex;
}
