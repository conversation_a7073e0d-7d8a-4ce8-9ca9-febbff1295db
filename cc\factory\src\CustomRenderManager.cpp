//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include "cc/factory/inc/CustomRenderManager.h"

namespace cc
{
namespace factory
{

void CustomRenderManager::update(osg::NodeVisitor* f_nv)
{
  if (f_nv == nullptr)
  {
    return;
  }
  if (!m_initialized)
  {
    setupStateGraph(getWallStateGraph());
    setupStateGraph(getFloorStateGraph());
    m_initialized = true;
  }

  updateInputData(f_nv->getFrameStamp());

  m_stitchLineMng.update(this, f_nv);
  m_chamaeleonMng.update(this->getStitchMng());
  m_sharpnessHarmonizationMng.update();
  m_temporalNoiseFilterMng.update();

  if ( m_degradationMaskCurrent != m_degradationMaskTarget )
  {
    if (!isAnimationRunningOnAffectedCameraOrNeighbours(m_degradationMaskCurrent, m_degradationMaskTarget))
    {
      m_shaderSelectionLogic.setAllShaderTransitionConfigurations(m_degradationMaskTarget, m_deactivationMaskCurrent);

      onCameraOnOffEvent(m_degradationMaskCurrent, m_degradationMaskTarget);
      m_degradationMaskCurrent = m_degradationMaskTarget;
    }
  }

  m_stitchLineMng.apply(getWallStateGraph());
  m_stitchLineMng.apply(getFloorStateGraph());

  m_chamaeleonMng.apply(m_stateGraphWall, this);
  m_chamaeleonMng.apply(m_stateGraphFloor, this);

  m_sharpnessHarmonizationMng.apply(m_stateGraphWall, this);
  m_sharpnessHarmonizationMng.apply(m_stateGraphFloor, this);

  m_temporalNoiseFilterMng.apply(m_stateGraphWall, this);
  m_temporalNoiseFilterMng.apply(m_stateGraphFloor, this);

  getWallStateGraph().update(f_nv);
  getFloorStateGraph().update(f_nv);
}

} // namespace cc
} // namespace factory

