/// @copyright (C) 2023 Robert <PERSON> GmbH.
/// The reproduction, distribution and utilization of this file as well as the
/// communication of its contents to others without express authorization is
/// prohibited. Offenders will be held liable for the payment of damages. All
/// rights reserved in the event of the grant of a patent, utility model or
/// design.
/// @file

#include "pc/svs/imp/chamaeleon/inc/visu/chamaeleon_visu_rois.hpp"
#include "pc/svs/imp/chamaeleon/inc/chamaeleon_tabs.hpp"
#include "pc/svs/util/osgx/inc/Utils.h"

namespace rbp
{
namespace vis
{
namespace imp
{
namespace chamaeleon
{
namespace visu
{

ChamaeleonVisuRois::ChamaeleonVisuRois(const ChamaeleonRois& f_chamaeleonRois)
    : osg::Group{}
    , m_chamaeleonRois{f_chamaeleonRois}
    , m_roiDebugCam{}
{
    setNumChildrenRequiringUpdateTraversal(getNumChildrenRequiringUpdateTraversal() + 1U);

    m_roiDebugCam = osg_ext::make_ref<pc::texfloor::core::TextureDisplayCamera>();
    osg::Group::addChild(m_roiDebugCam);
    m_roiDebugCam->setTexture(m_chamaeleonRois.getRoisAsTexture());

    const ChamaeleonVisuViewport l_visuViewport{
        0, 100, 150 * static_cast<vfc::int32_t>(ETabImageOverlapROI::NUM_IMAGE_ROIS), 150};
    setViewport(l_visuViewport);
}

void ChamaeleonVisuRois::traverse(osg::NodeVisitor& f_nv)
{
    if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
    {
        m_roiDebugCam->setTexture(m_chamaeleonRois.getRoisAsTexture());
    }

    osg::Group::traverse(f_nv);
}

void ChamaeleonVisuRois::setViewport(const ChamaeleonVisuViewport& f_visuViewport)
{
    m_roiDebugCam.get()->setViewport(
        f_visuViewport.m_visuViewportX,
        f_visuViewport.m_visuViewportY,
        f_visuViewport.m_visuViewportWidth * static_cast<vfc::int32_t>(ETabImageOverlapROI::NUM_IMAGE_ROIS),
        f_visuViewport.m_visuViewportHeight);
}

} // namespace visu
} // namespace chamaeleon
} // namespace imp
} // namespace vis
} // namespace rbp
