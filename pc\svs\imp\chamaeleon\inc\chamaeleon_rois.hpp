/// @copyright (C) 2023 <PERSON> GmbH.
/// The reproduction, distribution and utilization of this file as well as the
/// communication of its contents to others without express authorization is
/// prohibited. Offenders will be held liable for the payment of damages. All
/// rights reserved in the event of the grant of a patent, utility model or
/// design.
/// @file

#ifndef PC_SVS_IMP_CHAMAELEONROIS_H
#define PC_SVS_IMP_CHAMAELEONROIS_H

#include "pc/svs/util/osgx/inc/RenderToTextureCamera.h"
#include "pc/svs/imp/chamaeleon/inc/chamaeleon_data.hpp"
#include "pc/svs/imp/chamaeleon/inc/chamaeleon_roi_render_manager.hpp"
#include "pc/svs/imp/chamaeleon/inc/chamaeleon_tabs.hpp"

namespace rbp
{
namespace vis
{
namespace imp
{
namespace chamaeleon_test
{
class ChamaeleonRoisFixture;
} // namespace chamaeleon_test
namespace chamaeleon
{

///
/// ChamaeleonR<PERSON>
///
class ChamaeleonRois
{
public:
    explicit Chamaeleon<PERSON><PERSON>(
        pc::factory::RenderManagerRegistry* f_pRegistry,
        pc::factory::SV3DNode*              f_pFloor,
        const pc::virtcam::VirtualCamera*   f_virtCam);

    ~ChamaeleonRois() = default;

    //------------------------------------------------------------------------------------------------------------------
    /// @brief Sets the parameters for the ChamaeleonRois.
    ///
    /// @param[in] f_params The parameters to set.
    //------------------------------------------------------------------------------------------------------------------
    void setParameters(const ChamaeleonRoisData& f_params = ChamaeleonRoisData{});

    //------------------------------------------------------------------------------------------------------------------
    /// @brief Updates the ROI cameras by accepting a node visitor.
    ///
    /// @param[in] f_nv The node visitor to accept.
    //------------------------------------------------------------------------------------------------------------------
    void updateRoiCameras(osg::NodeVisitor& f_nv);

    osg::Texture2D* getRoisAsTexture() const
    {
        return m_useFilterRois ? m_roisAsTextureFiltered.get() : m_roisAsTexture.get();
    }

    TArrayImageOverlap<osg::ref_ptr<pc::util::osgx::RenderToTextureCamera>> m_chamaeleonRoisCamera;
    TArrayImageOverlap<osg::ref_ptr<pc::util::osgx::RenderToTextureCamera>> m_chamaeleonRoisFilteredCamera;

private:
    // The utf fixture class is a friend to be able to access private members
    // qacpp-2107-R1: Friend declaration for testing purposes.
    friend class chamaeleon_test::ChamaeleonRoisFixture; // PRQA S 2107 # R1

    ChamaeleonRois();
    ChamaeleonRois(const ChamaeleonRois&)            = delete;
    ChamaeleonRois& operator=(const ChamaeleonRois&) = delete;

    static constexpr bool IS_RIGHT{true};

    //------------------------------------------------------------------------------------------------------------------
    /// @brief Sets the view matrix of the ROI cameras to look at a specified point.
    ///
    /// @param[in] f_worldRoi The world overlap to set the view matrix for.
    /// @param[in] f_roiEye The eye position of the camera.
    /// @param[in] f_roiUp The up vector of the camera.
    //------------------------------------------------------------------------------------------------------------------
    void
    setRoiViewMatrixAsLookAt(ETabWorldOverlapROI f_worldRoi, const osg::Vec3f& f_roiEye, const osg::Vec3f& f_roiUp);

    //------------------------------------------------------------------------------------------------------------------
    /// @brief Using the stitchingMgr to update the view matrix.
    ///        We use the origin as the starting point and extend it in the direction of the stitching line by a factor.
    ///
    /// @param[in] f_stitchingLineFactor   The factor to scale the stitching line by.
    /// @param[in] f_useOrthoLine          Using the orthogonal line from the stitchingMgr as upVector.
    ///                                    Otherwise use the default upVector (1.F, 0.F, 0.F).
    //------------------------------------------------------------------------------------------------------------------
    void updateMatrixAsLookAt(vfc::float32_t f_stitchingLineFactor, bool f_useOrthoLine);

    //------------------------------------------------------------------------------------------------------------------
    /// @brief Updates the projection matrix of the ROI with an orthographic projection.
    ///
    /// @param[in] f_width The width of the projection.
    /// @param[in] f_hight The height of the projection.
    //------------------------------------------------------------------------------------------------------------------
    void updateProjectionMatrix(vfc::float32_t f_width, vfc::float32_t f_hight);

    bool                                     m_useFilterRois;
    vfc::float32_t                           m_thresholdFilterDeltaE;
    osg::ref_ptr<ChamaeleonRoiRenderManager> m_renderManagerLeft{nullptr};
    osg::ref_ptr<ChamaeleonRoiRenderManager> m_renderManagerRight{nullptr};
    CWorldOverlapIndex                       m_roundRobinCounter{0};
    osg::ref_ptr<osg::Texture2D>             m_roisAsTexture;
    osg::ref_ptr<osg::Texture2D>             m_roisAsTextureFiltered;
};

} // namespace chamaeleon
} // namespace imp
} // namespace vis
} // namespace rbp

#endif // PC_SVS_IMP_CHAMAELEONROIS_H
