//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>sch GmbH. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include "cc/virtcam/inc/HemisphereCameraUpdater.h"
#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/coding/inc/ISerializable.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h"
#include "cc/virtcam/inc/CameraPositions.h"
#include "pc/svs/util/math/inc/FloatComp.h"
#include <cassert>

using pc::util::logging::g_AppContext;

namespace cc
{
namespace virtcam
{

pc::util::coding::Item<HemisphereCameraParameters> g_hemisphereCameraParameters("HemisphereCamera");


HemisphereCameraUpdater::HemisphereCameraUpdater()
{
  const pc::util::coding::Item<HemisphereCameraParameters>& p(g_hemisphereCameraParameters);
  m_previousTime = -1.0;
  m_startTime = -1.0;
  m_animationDuration = p->m_animationDuration;

  m_minDistance = p->m_minDistance;
  m_maxDistance = p->m_maxDistance;
  m_targetDistance = m_maxDistance;
  m_currentDistance = m_maxDistance;

  m_currentAzimuth = 0.0f;
  m_targetAzimuth = m_currentAzimuth;

  m_minElevation = p->m_minElevation;
  m_maxElevation = p->m_maxElevation;
  m_currentElevation = (m_minElevation + m_maxElevation)*0.5f;
  m_targetElevation = m_currentElevation;

  m_currentHemisphereCenter = osg::Vec3f(0.f, 0.f, 0.0f);
  m_targetHemisphereCenter = osg::Vec3f(0.f, 0.f, 0.0f);

  m_startDistance = -1.0f;
  m_startHemisphereCenter = osg::Vec3f(-1.f, -1.f, -1.0f);

  m_deriveParameters = true;
}

void HemisphereCameraUpdater::setZoomRange(vfc::float32_t f_minDistance, vfc::float32_t f_maxDistance)
{
  if (f_minDistance >= f_maxDistance)
  {
    std::swap(f_minDistance, f_maxDistance);
  }
  m_targetDistance = osg::clampTo(m_targetDistance, f_minDistance, f_maxDistance);
  m_minDistance = f_minDistance;
  m_maxDistance = f_maxDistance;
}

void HemisphereCameraUpdater::getZoomRange(vfc::float32_t& f_minDistance, vfc::float32_t& f_maxDistance)
{
  f_minDistance = m_minDistance;
  f_maxDistance = m_maxDistance;
}

void HemisphereCameraUpdater::setElevationRange(vfc::float32_t f_minElevation, vfc::float32_t f_maxElevation)
{
  m_minElevation = f_minElevation;
  m_maxElevation = f_maxElevation;
  m_targetElevation = osg::clampTo(m_targetElevation, f_minElevation, f_maxElevation);
}

void HemisphereCameraUpdater::getElevationRange(vfc::float32_t& f_minElevation, vfc::float32_t& f_maxElevation) const
{
  f_minElevation = m_minElevation;
  f_maxElevation = m_maxElevation;
}

void HemisphereCameraUpdater::normalizeAndClampParameters(vfc::float32_t& f_elevation, vfc::float32_t& f_azimuth, vfc::float32_t& f_distance) const
{
  // distance must be projected to the available range and must be flipped - cause the zoom value 62 is ZoomedIn and 0 is ZoomedOut
  f_distance = (f_distance - m_minDistance)/(m_maxDistance - m_minDistance);
  f_distance = 1.0f - f_distance;
  f_distance = osg::clampTo(f_distance, 0.0f, 1.0f);

  f_elevation = mod2pi(f_elevation);
  f_azimuth = mod2pi(f_azimuth);
  f_elevation = osg::clampTo(f_elevation, m_minElevation, m_maxElevation);
}

void HemisphereCameraUpdater::setHemisphereCenter(const osg::Vec3f& f_center)
{
  m_targetHemisphereCenter = f_center;
}

osg::Vec3f HemisphereCameraUpdater::getCurrentHemisphereCenter() const
{
  return m_currentHemisphereCenter;
}


void HemisphereCameraUpdater::zoomTo(vfc::float32_t f_zoomLevel)
{
  m_targetDistance = f_zoomLevel*m_minDistance + (1.0f-f_zoomLevel)*m_maxDistance;
}

//! Rotate to the given spherical coordinates (given in radians)
void HemisphereCameraUpdater::rotateTo(vfc::float32_t f_elevation, vfc::float32_t f_azimuth)
{
  m_targetElevation = osg::clampTo(f_elevation, m_minElevation, m_maxElevation);
  m_targetAzimuth = f_azimuth;
}

bool HemisphereCameraUpdater::estimateCenter(const osg::Matrix& f_viewMatrix, osg::Vec3f& f_center)
{
  osg::Vec3f eye;
  osg::Vec3f center;
  osg::Vec3f up;
  f_viewMatrix.getLookAt(eye, center, up, 1.0f);
  osg::Vec3f dir = (center-eye);
  if (std::abs(dir.z()) < 1E-3f)
  {
    return false;
  }
  // to calculate the center on the height of the baseplate
  f_center = eye - dir*(eye.z()/dir.z());


  return true;
}

osg::Matrix HemisphereCameraUpdater::getViewMatrix(vfc::float32_t f_elevation, vfc::float32_t f_azimuth, vfc::float32_t f_distance, const osg::Vec3f& f_hemisphereCenter)
{
  const osg::Vec3f currentDirection = transformSphericalToWorld(f_elevation, f_azimuth, 1.0f);
  osg::Vec3f eye;
  osg::Vec3f up;
  eye = osg::Vec3f(f_hemisphereCenter + currentDirection*f_distance);
  if (f_elevation < osg::PI)
  {
      up = osg::Vec3f(0.0f, 0.0f, 1.0f);
  }
  else
  {
    up = osg::Vec3f(std::cos(f_azimuth), -std::sin(f_azimuth), 0.0f);
  }
  return osg::Matrix::lookAt(eye, f_hemisphereCenter, up);
}

// Derives hemispherical parametrization (zoom factor, elevation, azimuth, distance) from given camera matrix
void HemisphereCameraUpdater::projectCameraToParametricDomain( // PRQA S 4678
    const osg::Camera* f_camera,
    osg::Vec3f&        f_center, // PRQA S 4287
    vfc::float32_t&    f_elevation, // PRQA S 4287
    vfc::float32_t&    f_azimuth, // PRQA S 4287
    vfc::float32_t&    f_distance, // PRQA S 4287
    const bool         f_svsCenter) const
{
    if (f_camera != nullptr)
    {
        projectCameraToParametricDomain(
        f_camera->getViewMatrix(), f_center, f_elevation, f_azimuth, f_distance, f_svsCenter);
    }
    else
    {

    }
}

// Derives hemispherical parametrization (zoom factor, elevation, azimuth, distance) from given camera position
void HemisphereCameraUpdater::projectCameraToParametricDomain(const osg::Matrix& f_viewMatrix, osg::Vec3f& f_center, vfc::float32_t& f_elevation, vfc::float32_t& f_azimuth, vfc::float32_t& f_distance, const bool f_svsCenter) const
{
  osg::Vec3f eye;
  osg::Vec3f center;
  osg::Vec3f up;
  f_viewMatrix.getLookAt(eye, center, up, 1.0f);
  osg::Vec3f dir = (center-eye);
  if (f_svsCenter)
  {
    f_center = cc::virtcam::g_positions->m_overTheRoof.m_center;
  }
  else
  {
    // to calculate the center on the height of the baseplate
    if (dir.z() != 0.0f)
    {
        f_center = eye - dir*(eye.z()/dir.z());
    }
    else
    {
        f_center = cc::virtcam::g_positions->m_overTheRoof.m_center;
    }
  }

  // Couldn't derive azimuth from position due to singularity at pole, use the up vector to define orientation
  if (!transformWorldToSpherical(eye - f_center, f_elevation, f_azimuth, f_distance))
  {
    // we also have to clamp the elevation cause the value we are setting in transformWorldToSperical is outsite of our range
    f_elevation = osg::clampTo(f_elevation, m_minElevation, m_maxElevation);
    f_azimuth = -std::atan2(up.y(), up.x());
  }
}

//! Returns current zoom level, in the range [0..1]
vfc::float32_t HemisphereCameraUpdater::getCurrentZoomLevel() const
{
  return (m_maxDistance-m_currentDistance)/(m_maxDistance - m_minDistance);
}

void HemisphereCameraUpdater::reset()
{
  m_deriveParameters = true;
  m_startTime = -1.0;
}

bool HemisphereCameraUpdater::isAnimating() const
{
  return (isEnabled() && (m_deriveParameters ||
      isNotEqual(m_currentAzimuth , m_targetAzimuth) ||
      isNotEqual(m_currentDistance , m_targetDistance) ||
      isNotEqual(m_currentElevation , m_targetElevation) ||
      (m_currentHemisphereCenter != m_targetHemisphereCenter)));
}

void HemisphereCameraUpdater::updateCamera(osg::Camera* f_camera, const osg::FrameStamp* f_frameStamp)
{
  if (f_camera == nullptr || f_frameStamp == nullptr)
  {
    return;
  }
  const vfc::float64_t currentTime = f_frameStamp->getReferenceTime();

  // when entering this the first time the current cam position has to be derived
  if (m_deriveParameters)
  {
    // get and set the current parameter: m_currentHemisphereCenter, m_currentElevation, m_currentAzimuth, m_currentDistance
    projectCameraToParametricDomain(f_camera);

    f_camera->getViewMatrix().getLookAt(m_startEye, m_startCenter, m_startUp, 1.0);

    // safe the startPosition of the camera to calculate the animationPath
    m_startDistance = m_currentDistance;
    m_startHemisphereCenter = m_currentHemisphereCenter;

    m_deriveParameters = false;
    m_previousTime = currentTime;
    m_startTime = currentTime;
    return; // return here else there is no deltaTime between current and previous, so there is no animation
  }

  const vfc::float64_t l_timeSinceStart = currentTime - m_startTime;

  // after a change from sideview left or right or from default to svs we have to animate the center movement and distance movement
  // so we are in between the limiting hemispheres
  if((m_startTime >= 0.0) && (l_timeSinceStart <= m_animationDuration))
  {
    const vfc::float64_t l_deltaTime = currentTime - m_previousTime;
    // take the azimuth and elevation coming in - there is no animation of those values
    m_currentElevation = m_targetElevation;
    m_currentAzimuth = m_targetAzimuth;

    // we need the current camera position eye, center, up
    osg::Vec3f l_currentEye;
    osg::Vec3f l_currentCenter;
    osg::Vec3f l_currentUp;
    f_camera->getViewMatrix().getLookAt(l_currentEye, l_currentCenter, l_currentUp, 1.0);
    // we also need the eye of the target camera to move the camera with the right distance to its final position
    // - so we set the currentCamera to the target position to get the Eye, Center, Up
    f_camera->setViewMatrix(getViewMatrix(m_targetElevation, m_targetAzimuth, m_targetDistance, m_targetHemisphereCenter));

    osg::Vec3f l_targetEye;
    osg::Vec3f l_targetCenter;
    osg::Vec3f l_targetUp;
    f_camera->getViewMatrix().getLookAt(l_targetEye, l_targetCenter, l_targetUp, 1.0);
    // set it back - just to be sure
    f_camera->setViewMatrix(getViewMatrix(m_currentElevation, m_currentAzimuth, m_currentDistance, m_currentHemisphereCenter));

    // #1 - move the current camera eye in direction to the final position
    const osg::Vec3f l_dirSVS = (l_targetEye - m_startEye);
    const osg::Vec3f l_finalEye = m_startEye + l_dirSVS * static_cast<vfc::float32_t>(l_timeSinceStart) / m_animationDuration;

    // #2 - recalculate the up vector
    const osg::Vec3f l_deltaUp = l_targetUp - m_startUp;
    const osg::Vec3f l_finalUp = m_startUp + l_deltaUp * static_cast<vfc::float32_t>(l_timeSinceStart) / m_animationDuration;

    // #3 - the center will move along the connection line between the startCenter and the targetCenter
    const osg::Vec3f l_deltaCenter = m_targetHemisphereCenter - m_startHemisphereCenter;
    m_currentHemisphereCenter += l_deltaCenter * static_cast<vfc::float32_t>(l_deltaTime);
    const osg::Vec3f l_finalHemisphereCenter = m_startHemisphereCenter + l_deltaCenter * static_cast<vfc::float32_t>(l_timeSinceStart) / m_animationDuration;

    // #4 - recalculate the currentDistance
    const osg::Vec3f l_currentDirSVS = (m_targetHemisphereCenter - l_finalEye);
    const vfc::float32_t l_lengthCurrentDirSVS = l_currentDirSVS.length();
    m_currentDistance = l_lengthCurrentDirSVS;

    f_camera->setViewMatrixAsLookAt(l_finalEye, l_finalHemisphereCenter, l_finalUp);

    XLOG_INFO(g_AppContext, "HuDebug - HemisphereCameraUpdater - updateCamera() - ANIMATING, "
        << "l_timeSinceStart: " << l_timeSinceStart
        << ", m_startTime: " << m_startTime
        << ", m_animationDuration: " << m_animationDuration
        << ", l_deltaTime: " << l_deltaTime
        << ", m_currentDistance: " << m_currentDistance
        << ", m_targetDistance: " << m_targetDistance
        << ", m_currentElevation: " << osg::RadiansToDegrees(m_currentElevation)
        << ", m_targetElevation: " << osg::RadiansToDegrees(m_targetElevation)
        << ", m_currentAzimuth: " << osg::RadiansToDegrees(m_currentAzimuth)
        << ", m_targetAzimuth: " << osg::RadiansToDegrees(m_targetAzimuth)
        << ", l_finalEye: " << l_finalEye.x() << "," << l_finalEye.y() << "," << l_finalEye.z()
        << ", l_finalCenter: " << l_finalHemisphereCenter.x() << "," << l_finalHemisphereCenter.y() << "," << l_finalHemisphereCenter.z());
  }
  else // if the animation is done the current camera positioning is just set to the target values
  {
    m_currentElevation = m_targetElevation;
    m_currentAzimuth = m_targetAzimuth;
    m_currentDistance = m_targetDistance;
    m_currentHemisphereCenter = m_targetHemisphereCenter;
    f_camera->setViewMatrix(getViewMatrix(m_currentElevation, m_currentAzimuth, m_currentDistance, m_currentHemisphereCenter));
  }

  // update the previous time
  m_previousTime = currentTime;
}

// Derives internal parametrization (zoom factor, elevation, azimuth, distance) from given camera position
void HemisphereCameraUpdater::projectCameraToParametricDomain(const osg::Camera* f_camera)
{
  projectCameraToParametricDomain(f_camera, m_currentHemisphereCenter, m_currentElevation, m_currentAzimuth, m_currentDistance);
}

osg::Vec3f HemisphereCameraUpdater::transformSphericalToWorld(vfc::float32_t f_elevation, vfc::float32_t f_azimuth, vfc::float32_t f_distance)
{
    const osg::Vec3f direction = osg::Vec3f(-std::cos(f_azimuth)*std::cos(f_elevation), std::sin(f_azimuth)*std::cos(f_elevation), std::sin(f_elevation));
    return direction*f_distance;
}


bool HemisphereCameraUpdater::transformWorldToSpherical(const osg::Vec3f& f_world, vfc::float32_t& f_elevation, vfc::float32_t& f_azimuth, vfc::float32_t& f_distance)
{
    f_distance = f_world.length();
    assert(f_distance > 0.f);
    osg::Vec3f n = f_world/f_distance;

    const vfc::float32_t nl = osg::Vec2f(n.x(), n.y()).length();
    // Singularity at the pole; we may want to keep current azimuth in this case
    if (nl < 1E-3)
    {
        f_elevation = static_cast<vfc::float32_t>(osg::PI)/2.0f;
        f_azimuth = 0.0f;
        return false;
    }
    f_elevation = std::acos(nl);
    f_azimuth = -std::atan2(n.y(), n.x());
    f_azimuth = f_azimuth - static_cast<vfc::float32_t>(osg::PI); //rotate 180 degrees
    if (f_azimuth < 0.0f)
    {
        f_azimuth+= 2.0f*static_cast<vfc::float32_t>(osg::PI);
    }

    return true;
}

vfc::float32_t HemisphereCameraUpdater::mod2pi(vfc::float32_t f_radians)
{
  f_radians = std::fmod(f_radians, 2.0f*static_cast<vfc::float32_t>(osg::PI));
  if (f_radians < 0.0f)
  {
      f_radians+= 2.0f*static_cast<vfc::float32_t>(osg::PI);
  }
  return f_radians;
}



} // namespace virtcam
} // namespace cc
