/// @copyright (C) 2025 Robert <PERSON> GmbH.
/// The reproduction, distribution and utilization of this file as well as the
/// communication of its contents to others without express authorization is
/// prohibited. Offenders will be held liable for the payment of damages. All
/// rights reserved in the event of the grant of a patent, utility model or
/// design.
/// @file

#include "pc/svs/imp/tnf/inc/temporal_noise_filter_manager.hpp"

#include "pc/svs/core/inc/VideoTexture.h"
#include "pc/svs/factory/inc/RenderManager.h"
#include "pc/svs/factory/inc/SV3DStateGraph.h"
#include "pc/svs/imp/tnf/inc/temporal_noise_filter_data.hpp"
#include "pc/svs/views/warpfisheyeview/inc/WarpFisheyeView.h"

#include "pc/generic/util/logging/inc/LoggingContexts.h"

#include <cassert>

namespace rbp
{
namespace vis
{
namespace imp
{
namespace tnf
{

using pc::util::logging::g_AppContext;

TemporalNoiseFilterManager::TemporalNoiseFilterManager(pc::core::Framework* f_pFramework, ETnfView f_tnfView)
    : m_tnfViewId{f_tnfView}
    , m_tnfEnabled{false}
    , m_framework{f_pFramework}
    , m_updateShaderLogic{false}
{
}

void TemporalNoiseFilterManager::apply(
    pc::factory::SV3DStateGraph& f_stateGraph,
    pc::factory::RenderManager*  f_renderManager) const
{
    if (m_tnfViewId == ETnfView::NO_TNF)
    {
        return;
    }

    // only update state at initialization and when enable settings change
    if (m_updateShaderLogic)
    {
        f_renderManager->getShaderLogic()->setEnableTNF(m_tnfEnabled);

        pc::core::VideoTexture* const l_videoTexture = m_framework->getVideoTexture();
        assert(l_videoTexture);

        for (vfc::uint32_t i = 0U; i < pc::core::sysconf::E_SYSTEM_CONFIG_NUMBER_OF_CAMERAS; ++i)
        {
            f_stateGraph.setSingleCamShader(
                i, f_renderManager->getShaderLogic()->getSingleCamShaderParameters(!f_stateGraph.m_isFloor, i));

            f_stateGraph.setTwoCamShader(
                i,
                f_renderManager->getShaderLogic()->getTwoCamShaderParameters(!f_stateGraph.m_isFloor, i),
                l_videoTexture);
        }
    }

    if (m_tnfEnabled)
    {
        // Assert that all entries of g_tnfBufferIndex are equal
        assert(
            (g_tnfBufferIndex[0U] == g_tnfBufferIndex[1U]) && (g_tnfBufferIndex[1U] == g_tnfBufferIndex[2U]) &&
            (g_tnfBufferIndex[2U] == g_tnfBufferIndex[3U]));
        // the Tnf manager runs before the Tnf node which will toggle the index. This has to be anticipated here.
        pc::core::VideoTexture* const l_tnfTextures =
            (m_framework->getVideoTextureFilteredArray())[1 - g_tnfBufferIndex[0U]];

        for (const auto singleCamArea : common::CSingleCamAreaIndex::ALL)
        {
            if (nullptr != l_tnfTextures)
            {
                osg::StateSet* const l_stateSet =
                    f_stateGraph.m_singleCam[static_cast<vfc::size_t>(singleCamArea.asInteger())];
                // bind the filtered texture to texture unit 2
                osg::Uniform* const l_texUniformFiltered =
                    l_stateSet->getOrCreateUniform("s_filteredY", osg::Uniform::SAMPLER_2D);
                vfc::nop(l_texUniformFiltered->set(2));
                l_stateSet->setTextureAttribute(
                    2U,
                    l_tnfTextures->getCameraTexture(static_cast<vfc::uint32_t>(singleCamArea.asInteger())),
                    osg::StateAttribute::ON);
            }
            else
            {
                XLOG_WARN(g_AppContext, "No filtered textures available for TNF.");
            }
        }

        for (const auto twoCamArea : common::CTwoCamAreaIndex::ALL)
        {
            if (nullptr != l_tnfTextures)
            {
                osg::StateSet* const l_stateSet =
                    f_stateGraph.m_twoCam[static_cast<vfc::size_t>(twoCamArea.asInteger())];

                // bind the filtered textures to texture units 3 and 4
                osg::Uniform* const l_texUniformFilteredLeft =
                    l_stateSet->getOrCreateUniform("s_filteredYLeft", osg::Uniform::SAMPLER_2D);
                vfc::nop(l_texUniformFilteredLeft->set(3));
                osg::Uniform* const l_texUniformFilteredRight =
                    l_stateSet->getOrCreateUniform("s_filteredYRight", osg::Uniform::SAMPLER_2D);
                vfc::nop(l_texUniformFilteredRight->set(4));

                osg::StateAttribute* l_filteredTexLeft  = nullptr;
                osg::StateAttribute* l_filteredTexRight = nullptr;
                switch (twoCamArea.asEnum())
                {
                case pc::factory::TwoCamArea::TWO_CAM_FRONT_RIGHT:
                {
                    l_filteredTexLeft = l_tnfTextures->getCameraTexture(
                        static_cast<vfc::uint32_t>(pc::core::sysconf::Cameras::FRONT_CAMERA));
                    l_filteredTexRight = l_tnfTextures->getCameraTexture(
                        static_cast<vfc::uint32_t>(pc::core::sysconf::Cameras::RIGHT_CAMERA));
                    break;
                }
                case pc::factory::TwoCamArea::TWO_CAM_REAR_RIGHT:
                {
                    l_filteredTexLeft = l_tnfTextures->getCameraTexture(
                        static_cast<vfc::uint32_t>(pc::core::sysconf::Cameras::RIGHT_CAMERA));
                    l_filteredTexRight = l_tnfTextures->getCameraTexture(
                        static_cast<vfc::uint32_t>(pc::core::sysconf::Cameras::REAR_CAMERA));
                    break;
                }
                case pc::factory::TwoCamArea::TWO_CAM_REAR_LEFT:
                {
                    l_filteredTexLeft = l_tnfTextures->getCameraTexture(
                        static_cast<vfc::uint32_t>(pc::core::sysconf::Cameras::REAR_CAMERA));
                    l_filteredTexRight = l_tnfTextures->getCameraTexture(
                        static_cast<vfc::uint32_t>(pc::core::sysconf::Cameras::LEFT_CAMERA));
                    break;
                }
                case pc::factory::TwoCamArea::TWO_CAM_FRONT_LEFT:
                {
                    l_filteredTexLeft = l_tnfTextures->getCameraTexture(
                        static_cast<vfc::uint32_t>(pc::core::sysconf::Cameras::LEFT_CAMERA));
                    l_filteredTexRight = l_tnfTextures->getCameraTexture(
                        static_cast<vfc::uint32_t>(pc::core::sysconf::Cameras::FRONT_CAMERA));
                    break;
                }
                default:
                {
                    break;
                }
                }

                l_stateSet->setTextureAttribute(3U, l_filteredTexLeft, osg::StateAttribute::ON);
                l_stateSet->setTextureAttribute(4U, l_filteredTexRight, osg::StateAttribute::ON);
            }
        }
    }
}

void TemporalNoiseFilterManager::applyFisheye(pc::views::warpfisheye::WarpFisheyeView* f_warpFisheyeView) const
{
    if (m_tnfViewId == ETnfView::NO_TNF)
    {
        return;
    }

    if (m_updateShaderLogic)
    {
        f_warpFisheyeView->setEnableTemporalNoiseFilter(m_tnfEnabled);
    }

    if (m_tnfEnabled)
    {
        // Assert that all entries of g_tnfBufferIndex are equal
        assert(
            (g_tnfBufferIndex[0U] == g_tnfBufferIndex[1U]) && (g_tnfBufferIndex[1U] == g_tnfBufferIndex[2U]) &&
            (g_tnfBufferIndex[2U] == g_tnfBufferIndex[3U]));
        // this runs after the Tnf node when the index is already toggled and consistent to the buffer being used
        pc::core::VideoTexture* const l_tnfTextures =
            (m_framework->getVideoTextureFilteredArray())[g_tnfBufferIndex[0U]];

        if (nullptr != l_tnfTextures)
        {
            // bind the filtered texture to texture unit 1
            vfc::nop(f_warpFisheyeView->getProjObjStateSet()
                                  ->getOrCreateUniform("u_filteredY", osg::Uniform::SAMPLER_2D)
                                  ->set(1));

            f_warpFisheyeView->getProjObjStateSet()->setTextureAttribute(
                1U,
                l_tnfTextures->getCameraTexture(static_cast<vfc::uint32_t>(f_warpFisheyeView->getCamId())),
                osg::StateAttribute::ON);
        }
        else
        {
            XLOG_WARN(g_AppContext, "No filtered textures in warp fisheye views available for TNF.");
        }
    }
}

void TemporalNoiseFilterManager::update()
{
    if (m_tnfViewId == ETnfView::NO_TNF)
    {
        return;
    }

    const pc::daddy::TnfSettingsDaddy* const l_tnfSettingsReceiverData = m_framework->m_tnfSettingsReceiver.getData();
    if (nullptr != l_tnfSettingsReceiverData)
    {
        m_updateShaderLogic = m_tnfEnabled != l_tnfSettingsReceiverData->m_Data.m_viewSettings[m_tnfViewId].m_enabled;
        m_tnfEnabled        = l_tnfSettingsReceiverData->m_Data.m_viewSettings[m_tnfViewId].m_enabled;
    }
}

} // namespace tnf
} // namespace imp
} // namespace vis
} // namespace rbp
