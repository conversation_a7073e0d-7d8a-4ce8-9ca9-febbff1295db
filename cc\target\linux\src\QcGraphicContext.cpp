//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON> GmbH. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

//#include "cc/imc/qualcomm/qcengine/inc/QcGraphicsContext.h"
#include "cc/target/linux/inc/QcGraphicsContext.h"
#include "pc/svs/core/inc/SystemConf.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h"
// #include "pc/generic/util/logging/inc/Logging.h"
#include "pc/svs/daddy/inc/BaseDaddyPorts.h"
#include "pc/generic/util/chrono/inc/chrono.h"

#include <EGL/egl.h>
#include <cassert>
#include <unistd.h>
#include <fcntl.h>
#include <libgen.h>

#include "jpeglib.h"

using pc::util::logging::g_EngineContext;
using pc::util::logging::g_PerfContext;

// using namespace osg;
using osg::QcGraphicsContext;
using osg::State;

constexpr std::size_t g_minNumSurfaces = 2u;


QcGraphicsContext::QcGraphicsContext(osg::GraphicsContext::Traits* f_traits)
  : GraphicsContext()
  , m_valid(false)
  , m_initialized(false)
  , m_realized(false)
{
  _traits = f_traits;
  const std::size_t l_bitsPerPix = _traits->red +_traits->green + _traits->blue + _traits->alpha;

  XLOG_INFO_OS(g_EngineContext) << "GLFW depth: " << _traits->depth << " alpha: " << _traits->alpha << " samples: " << _traits->samples << XLOG_ENDL;
  XLOG_INFO_OS(g_EngineContext) << "GLFW red: " << _traits->red << " green: " << _traits->green << " blue: " << _traits->blue << XLOG_ENDL;
  XLOG_INFO_OS(g_EngineContext) << "GLFW width: " << _traits->width << " height: " << _traits->height << XLOG_ENDL;

  setState(new osg::State);
  getState()->setGraphicsContext(this);

  if (_traits.valid() && _traits->sharedContext.valid())
  {
    getState()->setContextID(_traits->sharedContext->getState()->getContextID());
    incrementContextIDUsageCount(getState()->getContextID());
  }
  else
  {
    getState()->setContextID(osg::GraphicsContext::createNewContextID());
  }

  screen_win_width  = _traits->width;
  screen_win_height = _traits->height;


  m_realized = true;
  m_valid    = true;
  m_initialized = true;
  XLOG_INFO_OS(g_EngineContext) << "QcGraphicsContext ---- Init"<< XLOG_ENDL;
}

QcGraphicsContext::~QcGraphicsContext() = default;
// {
// }


//!
//! Realize the GraphicsContext.
//!
bool QcGraphicsContext::realizeImplementation()
{
	return false;
}

//!
//! Return true if the graphics context has been realized and is ready to use.
//!
bool QcGraphicsContext::isRealizedImplementation() const
{
  return m_realized;
}

//!
//! Close the graphics context.
//!
void QcGraphicsContext::closeImplementation()
{
    //eglMakeCurrent(egl_display, EGL_NO_SURFACE, EGL_NO_SURFACE, EGL_NO_CONTEXT);
}

//!
//! Make this graphics context current.
//!
//!
//!
bool QcGraphicsContext::makeCurrentImplementation()
{
  //eglMakeCurrent(egl_display, egl_surface, egl_surface, egl_ctx);

  glBindFramebuffer(GL_FRAMEBUFFER, m_FBOForDefaultRendering);

  return true;
}

//!
//! Make this graphics context current with specified read context implementation.
//! not used now!
//!
bool QcGraphicsContext::makeContextCurrentImplementation ( GraphicsContext* /*f_gc*/)
{

  //eglMakeCurrent(egl_display, egl_surface, egl_surface, egl_ctx);
  return true;
}

//!
//! Release the graphics context.
//!
bool QcGraphicsContext::releaseContextImplementation()
{
    return true;
}

//!
//! Swap the front and back buffers.
//!
void QcGraphicsContext::swapBuffersImplementation()
{

}

void QcGraphicsContext::checkEvents()
{
  //TODO Implementation of checkEvents
}

//!
//! Pure virtual, Bind the graphics context to associated texture implementation.
//!
void QcGraphicsContext::bindPBufferToTextureImplementation(GLenum /* f_buffer */)
{
  XLOG_DEBUG_OS(g_EngineContext) << "GraphicsWindow::bindPBufferToTextureImplementation(..) not implemented." << XLOG_ENDL;
}


int QcGraphicsContext::intEGLOfflineEnv()
{
    EGLint eglRet = EGL_TRUE;

    GLint oglPbufferConfigAttrbs[30] =
    {
        EGL_SURFACE_TYPE,       EGL_PBUFFER_BIT,
        EGL_RENDERABLE_TYPE,    EGL_OPENGL_ES3_BIT,
        EGL_RED_SIZE,           8,
        EGL_BLUE_SIZE,          8,
        EGL_GREEN_SIZE,         8,
        EGL_ALPHA_SIZE,         8,
        EGL_DEPTH_SIZE,         24,
        EGL_NONE,
    };

    GLint oglPbufferSurfaceAttribs[30] =
    {
        EGL_WIDTH,              2560,
        EGL_HEIGHT,             1320,
        EGL_LARGEST_PBUFFER,    EGL_TRUE,
        EGL_NONE,
    };

    GLint oglPbufferContextAttribs[30] =
    {
        EGL_CONTEXT_CLIENT_VERSION,3,
        EGL_NONE,
    };

    egl_display = eglGetDisplay(EGL_DEFAULT_DISPLAY);
    eglInitialize(egl_display, nullptr, nullptr);

    int configNUM = 0;
    eglChooseConfig(egl_display,static_cast<const GLint*>(oglPbufferConfigAttrbs),&egl_conf,1,&configNUM);
    if(0 == configNUM)
    {
        XLOG_INFO_OS(g_EngineContext) << "QcGraphicsContext ---- Fail to find a config for pbuffer surface"<< XLOG_ENDL;
    }
    egl_surface = eglCreatePbufferSurface(egl_display,egl_conf, static_cast<const GLint*>(oglPbufferSurfaceAttribs));
    egl_ctx = eglCreateContext(egl_display,egl_conf,EGL_NO_CONTEXT,static_cast<const GLint*>(oglPbufferContextAttribs));

    eglRet = eglMakeCurrent(egl_display,egl_surface,egl_surface,egl_ctx);
    if(eglRet == EGL_TRUE)
    {
      XLOG_INFO_OS(g_EngineContext) << "QcGraphicsContext ---- Succeed to intEGLOfflineEnv"<< XLOG_ENDL;
      XLOG_INFO_OS(g_EngineContext) << "QcGraphicsContext ---- Succeed to intEGLOfflineEnv"<< XLOG_ENDL;
      XLOG_INFO_OS(g_EngineContext) << "QcGraphicsContext ---- Succeed to intEGLOfflineEnv"<< XLOG_ENDL;
    }
    else
    {
      XLOG_INFO_OS(g_EngineContext) << "QcGraphicsContext ---- Fail to intEGLOfflineEnv"<< XLOG_ENDL;
      XLOG_INFO_OS(g_EngineContext) << "QcGraphicsContext ---- Fail to intEGLOfflineEnv"<< XLOG_ENDL;
      XLOG_INFO_OS(g_EngineContext) << "QcGraphicsContext ---- Fail to intEGLOfflineEnv"<< XLOG_ENDL;
    }


    return 0;
}


int QcGraphicsContext::setExternalDefaultFBOId(int fboID)
{
  XLOG_INFO_OS(g_EngineContext) << "QcGraphicsContext ---- setExternalDefaultFBOId = "<< fboID << XLOG_ENDL;

  m_FBOForDefaultRendering = fboID;

  setDefaultFboId(m_FBOForDefaultRendering);

  return 0;
}
