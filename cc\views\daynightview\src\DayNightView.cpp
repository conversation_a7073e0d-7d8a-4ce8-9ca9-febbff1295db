//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: ESJ1LR Esparza Jose (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  DayNightView.cpp
/// @brief
//=============================================================================

#include "cc/views/daynightview/inc/DayNightView.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"
#include "pc/svs/util/math/inc/FloatComp.h"
#include "osg/Scissor"


namespace cc
{
namespace views
{
namespace daynightview
{

pc::util::coding::Item<DayNightData> g_dayNight("DayNightData");

//!
//! ColorCallback
//!
class ColorCallback : public osg::Drawable::UpdateCallback
{
public:
    explicit ColorCallback(cc::core::CustomFramework* f_pCustomFramework)
      : m_pCustomFramework( f_pCustomFramework )
      , m_counterCurrent(-1)
    {
    }

    void update(osg::NodeVisitor* /* f_nv */, osg::Drawable* f_drawable) override
    {
      const cc::daddy::PIVI_DayNightThemeReq_t* const l_pData = m_pCustomFramework->m_dayNightModeReceiver.getData();

      // Check if data is valid and if it has changed
      if (nullptr != l_pData && static_cast<vfc::int32_t>(l_pData->m_sequenceNumber) != m_counterCurrent)
      {
        m_counterCurrent = static_cast<vfc::int32_t>(l_pData->m_sequenceNumber);

        osg::Geometry *const l_pGeo = dynamic_cast<osg::Geometry*>(f_drawable); // PRQA S 3077  // PRQA S 3400
        osg::Vec3Array *const l_pColorArray = dynamic_cast<osg::Vec3Array*>(l_pGeo->getColorArray()); // PRQA S 3077  // PRQA S 3400

        if(static_cast<vfc::uint8_t>(cc::daddy::DAY_MODE) == l_pData->m_Data.m_DayNightThemeReq_u8)
        {
            l_pColorArray->at(0u) = g_dayNight->m_dayColor;
        }
        else // NIGHT_MODE
        {
            l_pColorArray->at(0u) = g_dayNight->m_nightColor;
        }
        l_pColorArray->dirty();
      }
    }

private:
    cc::core::CustomFramework* m_pCustomFramework;
    vfc::int32_t m_counterCurrent;
};

//! Helper function to create a HUD containing a black rectangle
osg::Node* createBlackNode(cc::core::CustomFramework* f_pCustomFramework)
{
  osg::Camera* const backgroundQuad = new osg::Camera();
  backgroundQuad->setReferenceFrame(osg::Transform::ABSOLUTE_RF);

  backgroundQuad->setViewMatrix(osg::Matrix::identity());
  backgroundQuad->setRenderOrder(osg::Camera::POST_RENDER);

  backgroundQuad->setProjectionMatrixAsOrtho2D(0.0f, 1.0f, 0.0f, 1.0f);
  osg::StateSet* const stateSet = backgroundQuad->getOrCreateStateSet();
  stateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF); //PRQA S 3143

  // day night as default
  const osg::ref_ptr<osg::Vec3Array> colorArray = new osg::Vec3Array(1u);
  (*colorArray)[0u] = g_dayNight->m_dayColor;


  const osg::ref_ptr<osg::Vec2Array> vertexArray = new osg::Vec2Array(6u);
  (*vertexArray)[0u] = osg::Vec2f(0.0f, 0.0f);
  (*vertexArray)[1u] = osg::Vec2f(1.0f, 0.0f);
  (*vertexArray)[2u] = osg::Vec2f(1.0f, 1.0f);
  (*vertexArray)[3u] = osg::Vec2f(1.0f, 1.0f);
  (*vertexArray)[4u] = osg::Vec2f(0.0f, 0.0f);
  (*vertexArray)[5u] = osg::Vec2f(0.0f, 1.0f);

  const osg::ref_ptr<osg::Geode> blackQuad = new osg::Geode();
  const osg::ref_ptr<osg::Geometry> blackQuadGeometry = new osg::Geometry();

  blackQuadGeometry->setVertexArray(vertexArray);
  blackQuadGeometry->setColorArray(colorArray, osg::Array::BIND_OVERALL);
  blackQuadGeometry->addPrimitiveSet(new osg::DrawArrays(GL_TRIANGLES, 0u, 6u));  // PRQA S 3803 // PRQA S 3143
  blackQuad->addDrawable(blackQuadGeometry);  // PRQA S 3803

  blackQuadGeometry->setUpdateCallback( new ColorCallback(f_pCustomFramework) );

  backgroundQuad->addChild(blackQuad);  // PRQA S 3803

  return backgroundQuad;
}

//!
//! DayNightView
//!
DayNightView::DayNightView(
      const std::string& f_name,
      const pc::core::Viewport& f_viewport,
      pc::core::Framework* f_pFramework, bool f_useCallbackForTA, const pc::core::Viewport& f_usableViewport, vfc::uint32_t f_viewportSize, vfc::uint32_t f_type)
  : pc::core::View(f_name, f_viewport)
  , m_pFramework( f_pFramework )
  , m_initialViewport(f_viewport)
  , m_pixelsToMove(0)
  , m_counterReq(-1)
  , m_counterCurrent(-1)
  , m_viewportSize(f_viewportSize)
  , m_type(f_type)
{
  //! Create geometries
  osg::Group::addChild( createBlackNode( dynamic_cast<cc::core::CustomFramework*>(m_pFramework) ) );  // PRQA S 3803  // PRQA S 3077  // PRQA S 3400
  if (true == f_useCallbackForTA)
  {
    setNewViewportPosition(0);
    addUpdateCallback(new DayNightViewportCallback());

    if (true == pc::vehicle::g_mechanicalData->m_leftHandDrive)
    {
      osg::Scissor* const l_scissor = new osg::Scissor(
          f_usableViewport.m_origin.x(),
          f_usableViewport.m_origin.y(),
          f_usableViewport.m_size.x(),
          f_usableViewport.m_size.y());
      osg::StateSet* const l_pStateSet = getOrCreateStateSet();
      l_pStateSet->setMode(GL_BLEND, osg::StateAttribute::ON); //PRQA S 3143
      l_pStateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF); //PRQA S 3143
      l_pStateSet->setRenderBinDetails(pc::core::g_renderOrder->m_overlays, "RenderBin");
      l_pStateSet->setAttributeAndModes(l_scissor, osg::StateAttribute::ON); //PRQA S 3143
    }
  }

}

DayNightView::~DayNightView() = default;

// void DayNightView::updateSeparatorPosition()
// {
  // const cc::daddy::TrailerSvsDaddy* l_pData = m_pFramework->asCustomFramework()->m_trailerSvsReceiver.getData();

  // //check if data is valid and if it has changed
  // if (0 != l_pData && l_pData->m_sequenceNumber != m_counterReq)
  // {
  //   m_counterReq = l_pData->m_sequenceNumber;
  //   const vfc::float32_t l_hitchAngle_deg = l_pData->m_Data.m_requestedAngle.value();
  //   const vfc::float32_t l_hitchAngle_rad = osg::DegreesToRadians(l_hitchAngle_deg);
  //   const vfc::float32_t l_maxAngleValue_rad = osg::DegreesToRadians(towassist::g_taSettings->m_maxRequestedAngle);

  //   if ((isGreater(std::abs(l_hitchAngle_deg), towassist::g_taSettings->m_jitterTolerance)) && (!(isZero(l_maxAngleValue_rad))))
  //   {
  //     m_pixelsToMove = std::floor((m_viewportSize + 4.0f) * l_hitchAngle_rad / l_maxAngleValue_rad + 0.5f);
  //     int l_maxViewportReduction = (m_viewportSize + 4.0f)*towassist::g_taSettings->m_viewportReduction*0.01f;
  //     if (l_maxViewportReduction < std::abs(m_pixelsToMove))
  //     {
  //       vfc::uint32_t l_viewMode = m_pFramework->getCurrentScreenId();
  //       if (EScreenID_TRIPLE_REAR_ML_MR_TOW_ASSIST == l_viewMode)
  //       {
  //         if (m_pixelsToMove < 0)
  //         {
  //           m_pixelsToMove = -(m_viewportSize + 4.0f);

  //           if( (false == pc::vehicle::g_mechanicalData->m_leftHandDrive) && (m_type == RIGHT_TRIPPLEVIEW ) )
  //           {
  //              m_pixelsToMove = -(m_viewportSize + 3.8f);
  //           }
  //         }
  //         else
  //         {
  //           m_pixelsToMove = m_viewportSize + 4.0f;

  //           if( (false != pc::vehicle::g_mechanicalData->m_leftHandDrive) && (m_type == RIGHT_TRIPPLEVIEW ) )
  //           {
  //              m_pixelsToMove = m_viewportSize + 3.8f;
  //           }
  //         }
  //       }
        // else if (EScreenID_DUAL_ML_MR_TRAILER == l_viewMode)
        // {
        //   if (m_pixelsToMove < 0)
        //   {
        //     m_pixelsToMove = -(m_viewportSize*towassist::g_taSettings->m_viewportReduction*0.01f + 4.0f);
        //   }
        //   else
        //   {
        //     m_pixelsToMove = m_viewportSize*towassist::g_taSettings->m_viewportReduction*0.01f + 4.0f;
        //   }
        // }
//       }
//     }
//     else
//     {
//       m_pixelsToMove = 0;
//     }
//     setNewViewportPosition(m_pixelsToMove);
//   }
// }

void DayNightView::setNewViewportPosition(vfc::int32_t f_deltaViewportPosition)
{
  setViewport(m_initialViewport.m_origin.x() + f_deltaViewportPosition,
    m_initialViewport.m_origin.y(),
    m_initialViewport.m_size.x(),
    m_initialViewport.m_size.y());
}

DayNightViewportCallback::DayNightViewportCallback() = default;

DayNightViewportCallback::~DayNightViewportCallback() = default;

void DayNightViewportCallback::operator()(osg::Node* f_node, osg::NodeVisitor* f_nv)
{
    if (f_node == nullptr || f_nv == nullptr)
    {
        return;
    }
    views::daynightview::DayNightView* const l_dayNight = dynamic_cast<views::daynightview::DayNightView*>(f_node); // PRQA S 3077  // PRQA S 3400

    if (l_dayNight != nullptr)
    {
      if (osg::NodeVisitor::UPDATE_VISITOR == f_nv->getVisitorType())
      {
        // l_dayNight->updateSeparatorPosition();
        traverse(f_node, f_nv);
      }
    }
}

} // namespace daynightview
} // namespace assets
} // namespace cc
