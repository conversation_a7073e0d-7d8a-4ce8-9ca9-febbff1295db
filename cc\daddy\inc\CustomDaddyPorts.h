//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: EIK2LR Karim Eid (CC-DA/EAV1)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  CustomDaddyPorts.h
/// @brief
//=============================================================================

#ifndef CUSTOMDADDYPORTS_H
#define CUSTOMDADDYPORTS_H

#include "cc/daddy/inc/CustomDaddyTypes.h"

#include "pc/svs/daddy/inc/BaseDaddyPorts.h"

namespace cc
{
namespace daddy
{


//======================================================
// CustomDaddyPorts
//------------------------------------------------------
/// Class encompassing all customer-specific sender Daddy Ports
/// This is a wrapper class containing all Sender ports and matching mempool.
/// These sender ports are responsible for transmitting data from the IPC to the SM.
/// The class inherits from the BaseDaddyPorts class and is functionally "fully static".
/// <AUTHOR> Eid (CC-DA/EAV1)
/// @ingroup daddy
//======================================================
class CustomDaddyPorts : public pc::daddy::BaseDaddyPorts
{
public:
  //! connect sender to memory pools
  static void connectMemPools();

  //! load inital port data form coding
  static void initPorts();

  static void resetPorts()
  {
    sm_customerMemoryPoolsHaveInitialData = false;
    CustomDaddyPorts::initPorts();
  }

public:
  static  ::daddy::TSenderPort< SocketCmdDaddy_t > sm_SocketCmdDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< SocketCmdDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_SocketCmdDaddy_MemPool;

public:
  static ::daddy::TSenderPort< ParkingSpotsListDaddy > sm_parkingInSpots_SenderPort;
private:
  static ::daddy::TMemPool< ParkingSpotsListDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_parkingInSpots_MemPool;

public:
  static ::daddy::TSenderPort< ParkingSpotDaddy > sm_parkingOutSpot_SenderPort;
private:
  static ::daddy::TMemPool< ParkingSpotDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_parkingOutSpot_MemPool;

public:
  static ::daddy::TSenderPort< APG_VehiclePathDaddy > sm_PMA_TravelDistDesired_SenderPort;
private:
  static ::daddy::TMemPool< APG_VehiclePathDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_PMA_TravelDistDesired_MemPool;

public:
  static ::daddy::TSenderPort< PmaCtlMrgrDaddy > sm_pmaCtlMrgr_SenderPort;
private:
  static ::daddy::TMemPool< PmaCtlMrgrDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_pmaCtlMrgr_MemPool;

public:
  static  ::daddy::TSenderPort< PasButtonStatusDaddy_t > sm_PasButtonStatusDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< PasButtonStatusDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_PasButtonStatusDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< ParkConfirmButtonStDaddy_t > sm_ParkConfirmButtonStDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< ParkConfirmButtonStDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_ParkConfirmButtonStDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< FreeParkingSpaceTypeButtonStDaddy_t > sm_FreeParkingSpaceTypeButtonStDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< FreeParkingSpaceTypeButtonStDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_FreeParkingSpaceTypeButtonStDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< ButtonPress_t > sm_StartPauseConfirmButtonPressDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< ButtonPress_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_StartPauseConfirmButtonPressDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< ButtonPress_t > sm_ContinueButtonPressDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< ButtonPress_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_ContinueButtonPressDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< ButtonPress_t > sm_QuitButtonPressDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< ButtonPress_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_QuitButtonPressDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< ButtonPress_t > sm_SuspendButtonPressDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< ButtonPress_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_SuspendButtonPressDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< ButtonPress_t > sm_parkoutLeftButtonPressDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< ButtonPress_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_parkoutLeftButtonPressDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< ButtonPress_t > sm_parkoutRightButtonPressDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< ButtonPress_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_parkoutRightButtonPressDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< ParkOutSideButtonStDaddy_t > sm_ParkOutSideButtonStDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< ParkOutSideButtonStDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_ParkOutSideButtonStDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< PasButtonPressedStDaddy_t > sm_PasButtonPressedStDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< PasButtonPressedStDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_PasButtonPressedStDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< SettingAutoCamStDaddy_t > sm_AutoCamActivButtonPressedStDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< SettingAutoCamStDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_AutoCamActivButtonPressedStDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< ParkSlotSelectedStDaddy_t > sm_ParkSlotSelectedStDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< ParkSlotSelectedStDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_ParkSlotSelectedStDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< ParkDrvFuncOffStDaddy_t > sm_ParkDrvFuncOffStDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< ParkDrvFuncOffStDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_ParkDrvFuncOffStDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< ParkPauseButton_t > sm_ParkPauseButtonStDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< ParkPauseButton_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_ParkPauseButtonStDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< PARkDirection_t > sm_ParkDirectionStDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< PARkDirection_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_ParkDirectionStDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< ParkDriverIndSearchDaddy_t > sm_ParkDriverIndSearchDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< ParkDriverIndSearchDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_ParkDriverIndSearchDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< ParkRPAAvaliableDaddy_t > sm_ParkRPAAvaliableDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< ParkRPAAvaliableDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_ParkRPAAvaliableDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< PasWarnToneDaddy_t > sm_PasWarnToneDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< PasWarnToneDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_PasWarnToneDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< PasStatusDaddy_t > sm_PasStatusDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< PasStatusDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_PasStatusDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< SdwStatusDaddy_t > sm_SdwStatusDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< SdwStatusDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_SdwStatusDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< SdwStatusFDaddy_t > sm_SdwFStatusDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< SdwStatusFDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_SdwFStatusDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< SdwStatusFMDaddy_t > sm_SdwFMStatusDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< SdwStatusFMDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_SdwFMStatusDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< SdwStatusRMDaddy_t > sm_SdwRMStatusDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< SdwStatusRMDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_SdwRMStatusDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< SdwStatusRDaddy_t > sm_SdwRStatusDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< SdwStatusRDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_SdwRStatusDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< ParkStatusDaddy_t > sm_ParkStatusDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< ParkStatusDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_ParkStatusDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< ParkTypeDaddy_t > sm_ParkTypeDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< ParkTypeDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_ParkTypeDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< ParkTypeVariantDaddy_t > sm_ParkTypeVariantDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< ParkTypeVariantDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_ParkTypeVariantDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< ParkTypeSelectedStDaddy_t > sm_ParkTypeSelectedStDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< ParkTypeSelectedStDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_ParkTypeSelectedStDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< ParkModeDaddy_t > sm_ParkModeDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< ParkModeDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_ParkModeDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< ParkModeSelectedStDaddy_t > sm_ParkModeSelectedStDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< ParkModeSelectedStDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_ParkModeSelectedStDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< ParkSideDaddy_t > sm_ParkSideDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< ParkSideDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_ParkSideDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< ParkFunctionIndDaddy_t > sm_ParkFunctionIndDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< ParkFunctionIndDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_ParkFunctionIndDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< ParkQuitIndDaddy_t > sm_ParkQuitIndDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< ParkQuitIndDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_ParkQuitIndDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< ParkQuitIndExtDaddy_t > sm_ParkQuitIndExtDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< ParkQuitIndExtDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_ParkQuitIndExtDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< ParkRecoverIndDaddy_t > sm_ParkRecoverIndDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< ParkRecoverIndDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_ParkRecoverIndDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< ParkDriverIndDaddy_t > sm_ParkDriverIndDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< ParkDriverIndDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_ParkDriverIndDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< ParkDriverIndExtDaddy_t > sm_ParkDriverIndExtDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< ParkDriverIndExtDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_ParkDriverIndExtDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< ParkOutSideAvlDaddy_t > sm_ParkOutSideAvlDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< ParkOutSideAvlDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_ParkOutSideAvlDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< ParkReqReleaseBtnDaddy_t > sm_ParkReqReleaseBtnDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< ParkReqReleaseBtnDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_ParkReqReleaseBtnDaddy_MemPool;

public:
  static  ::daddy::TSenderPort < ParkAPA_ParkSpace_t > sm_ParkAPA_ParkSpaceDaddy_SenderPort;
private:
  static  ::daddy::TMemPool    < ParkAPA_ParkSpace_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_ParkAPA_ParkSpaceDaddy_MemPool;

public:
  static  ::daddy::TSenderPort < ParkAPA_ParkSpaceMark_t > sm_ParkAPA_ParkSpaceMarkDaddy_SenderPort;
private:
  static  ::daddy::TMemPool    < ParkAPA_ParkSpaceMark_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_ParkAPA_ParkSpaceMarkDaddy_MemPool;

public:
  static  ::daddy::TSenderPort < ParkPSDirectionSelected_t > sm_ParkPSDirectionSelectedDaddy_SenderPort;
private:
  static  ::daddy::TMemPool    < ParkPSDirectionSelected_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_ParkPSDirectionSelectedDaddy_MemPool;

public:
  static  ::daddy::TSenderPort < ParkFreeParkingActive_t > sm_ParkFreeParkingActiveDaddy_SenderPort;
private:
  static  ::daddy::TMemPool    < ParkFreeParkingActive_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_ParkFreeParkingActiveDaddy_MemPool;

public:
  static ::daddy::TSenderPort< FreeParkingButtonPress_t > sm_FreeParkingButtonPressDaddy_SenderPort;
private:
  static ::daddy::TMemPool< FreeParkingButtonPress_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_FreeParkingButtonPressDaddy_MemPool;

public:
  static  ::daddy::TSenderPort < ParkParkngTypeSeld_t > sm_ParkParkngTypeSeldDaddy_SenderPort;
private:
  static  ::daddy::TMemPool    < ParkParkngTypeSeld_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_ParkParkngTypeSeldDaddy_MemPool;

public:
  static  ::daddy::TSenderPort < ParkAPAPARKMODE_t > sm_ParkAPAPARKMODE_SenderPort;
private:
  static  ::daddy::TMemPool    < ParkAPAPARKMODE_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_ParkAPAPARKMODE_MemPool;

public:
  static  ::daddy::TSenderPort< ParkSpaceDaddy_t > sm_ParkSpaceDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< ParkSpaceDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_ParkSpaceDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< ParkFrontObjDaddy_t > sm_ParkFrontObjDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< ParkFrontObjDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_ParkFrontObjDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< ParkEndPositionSearchingDaddy_t > sm_ParkEndPositionSearchingDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< ParkEndPositionSearchingDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_ParkEndPositionSearchingDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< ParkFinalEndPositionDaddy_t > sm_ParkFinalEndPositionDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< ParkFinalEndPositionDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_ParkFinalEndPositionDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< ParkCurMoveTargetPositionDaddy_t > sm_ParkCurMoveTargetPositionDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< ParkCurMoveTargetPositionDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_ParkCurMoveTargetPositionDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< ParkCarmoveNumberDaddy_t > sm_ParkCarmoveNumberDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< ParkCarmoveNumberDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_ParkCarmoveNumberDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< ParkCartravelDistDesiredDaddy_t > sm_ParkCartravelDistDesiredDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< ParkCartravelDistDesiredDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_ParkCartravelDistDesiredDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< ParkIsLastMoveDaddy_t > sm_ParkIsLastMoveDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< ParkIsLastMoveDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_ParkIsLastMoveDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< ParkRPADriverSelectedDaddy_t > sm_ParkRPADriverSelectedDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< ParkRPADriverSelectedDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_ParkRPADriverSelectedDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< ParkBreakPedalBeenReleasedBf_t > sm_brakePedalBeenReleasedBeforeDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< ParkBreakPedalBeenReleasedBf_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_brakePedalBeenReleasedBeforeDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< ParkbrkPedlAppld_t > sm_brakePedalAppliedDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< ParkbrkPedlAppld_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_brakePedalAppliedDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< ParkDisp2TouchStsDaddy_t > sm_ParkDisp2TouchStsDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< ParkDisp2TouchStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_ParkDisp2TouchStsDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< ParkDisp2TouchSlotSelectionDaddy_t > sm_ParkDisp2TouchSlotSelectionDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< ParkDisp2TouchSlotSelectionDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_ParkDisp2TouchSlotSelectionDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< ParkDispLowPolyModelStsDaddy_t > sm_ParkDispLowPolyModelStsDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< ParkDispLowPolyModelStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_ParkDispLowPolyModelStsDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< SMObstacleSwitchDaddy_t > sm_SMObstacleSwitchDaddy_Socket_SenderPort;
private:
  static  ::daddy::TMemPool< SMObstacleSwitchDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_SMObstacleSwitchDaddy_Socket_MemPool;

public:
  static  ::daddy::TSenderPort< HUDislayModeSwitchDaddy_t > sm_HUDislayModeSwitchDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< HUDislayModeSwitchDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_HUDislayModeSwitchDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< HUCalibrationFlagDaddy_t > sm_HUCalibrationStartDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< HUCalibrationFlagDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_HUCalibrationStartDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< HUDislayModeView5xDaddy_t > sm_HUDislayModeView5xDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< HUDislayModeView5xDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_HUDislayModeView5xDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< HUDislayModeExpandDaddy_t > sm_HUDislayModeExpandDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< HUDislayModeExpandDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_HUDislayModeExpandDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< HUImageWorkModeDaddy_t > sm_HUImageWorkModeDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< HUImageWorkModeDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_HUImageWorkModeDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< HUTransparencyModeDaddy_t > sm_HUTransparentModeDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< HUTransparencyModeDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_HUTransparentModeDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< HURadarWallButtonDaddy_t > sm_HURadarWallButtonDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< HURadarWallButtonDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_HURadarWallButtonDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< TrailerModeDaddy_t > sm_HUTrailerButtonDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< TrailerModeDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_HUTrailerButtonDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< HUDislayModeExpandNewDaddy_t > sm_HUDislayModeExpandNewDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< HUDislayModeExpandNewDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_HUDislayModeExpandNewDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< HURotateStatusDaddy_t > sm_HURotateStatusDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< HURotateStatusDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_HURotateStatusDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< HUShoWReqDaddy_t > sm_HUShoWReqDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< HUShoWReqDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_HUShoWReqDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< HURemoteScreenReqDaddy_t > sm_HURemoteScreenReqDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< HURemoteScreenReqDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_HURemoteScreenReqDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< HUShoWSuspendDaddy_t > sm_HUShoWSuspendDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< HUShoWSuspendDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_HUShoWSuspendDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< DriverSteeringWheelAngleDaddy_t > sm_DriverSteeringWheelAngleDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< DriverSteeringWheelAngleDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_DriverSteeringWheelAngleDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< HUselSVSModeDaddy_t > sm_HUselSVSModeDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< HUselSVSModeDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_HUselSVSModeDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< HUsvsAutoCamDaddy_t > sm_HUsvsAutoCamDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< HUsvsAutoCamDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_HUsvsAutoCamDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< HUsvsIntegtOpenDaddy_t > sm_HUsvsIntegtOpenDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< HUsvsIntegtOpenDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_HUsvsIntegtOpenDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< HUtouchEvenTypeDaddy_t > sm_HUtouchEvenTypeDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< HUtouchEvenTypeDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_HUtouchEvenTypeDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< HUTwoFingerTouchEvenTypeDaddy_t > sm_HUTwoFingerTouchEvenTypeDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< HUTwoFingerTouchEvenTypeDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_HUTwoFingerTouchEvenTypeDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< HUShowStsDaddy_t > sm_HUShowStsDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< HUShowStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_HUShowStsDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< HUrestoreSVSSetDaddy_t > sm_HUrestoreSVSSetDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< HUrestoreSVSSetDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_HUrestoreSVSSetDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< HUbasePlateReqDaddy_t > sm_HUbasePlateReqDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< HUbasePlateReqDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_HUbasePlateReqDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< HUvehTransReq_t > sm_HUvehTransReqDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< HUvehTransReq_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_HUvehTransReqDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< HUvehTransLevel_t > sm_HUvehTransLevelDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< HUvehTransLevel_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_HUvehTransLevelDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< HUFreemodeAngleDaddy_t > sm_HUfreemodeAngleAzimuthDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< HUFreemodeAngleDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_HUfreemodeAngleAzimuthDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< HUFreemodeAngleDaddy_t > sm_HUfreemodeAngleElevationDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< HUFreemodeAngleDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_HUfreemodeAngleElevationDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< HUoverlayDistReqDaddy_t > sm_HUoverlayDistReqDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< HUoverlayDistReqDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_HUoverlayDistReqDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< HUoverlayReqDaddy_t > sm_HUoverlayReqDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< HUoverlayReqDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_HUoverlayReqDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< HU3DCruseDaddy_t > sm_HU3DCruseDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< HU3DCruseDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_HU3DCruseDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< HUgestureStatusDaddy_t > sm_HUgestureStatusDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< HUgestureStatusDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_HUgestureStatusDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< SVSShowReqDaddy_t > sm_SVSShowReqDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< SVSShowReqDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_SVSShowReqDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< SVSCurrentViewDaddy_t > sm_SVSCurrentViewDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< SVSCurrentViewDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_SVSCurrentViewDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< SVSIntegtOpenStsDaddy_t > sm_SVSIntegtOpenStsDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< SVSIntegtOpenStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_SVSIntegtOpenStsDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< SVSAutoCamStsDaddy_t > sm_SVSAutoCamStsDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< SVSAutoCamStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_SVSAutoCamStsDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< SVSViewModeStsDaddy_t > sm_SVSViewModeStsDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< SVSViewModeStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_SVSViewModeStsDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< SVSResolution_t > sm_SVSResolutionDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< SVSResolution_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_SVSResolutionDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< SVSOverlayDistStsDaddy_t > sm_SVSOverlayDistStsDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< SVSOverlayDistStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_SVSOverlayDistStsDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< SVSDynOverlayStsDaddy_t > sm_SVSDynOverlayStsDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< SVSDynOverlayStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_SVSDynOverlayStsDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< SVSVehTransStsDaddy_t > sm_SVSVehTransStsDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< SVSVehTransStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_SVSVehTransStsDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< SVSVehTransStsInternalDaddy_t > sm_SVSVehTransStsInternalDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< SVSVehTransStsInternalDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_SVSVehTransStsInternalDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< SVSBasePlateStsDaddy_t > sm_SVSBasePlateStsDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< SVSBasePlateStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_SVSBasePlateStsDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< SVS3DCruStsDaddy_t > sm_SVS3DCruStsDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< SVS3DCruStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_SVS3DCruStsDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< SVSzoomStsDaddy_t > sm_SVSzoomStsDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< SVSzoomStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_SVSzoomStsDaddy_MemPool;

// Start of BYD
public:
  static  ::daddy::TSenderPort< SVSViewStateDaddy_t > sm_SVSViewStateDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< SVSViewStateDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_SVSViewStateDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< SVSWorkModeDaddy_t > sm_SVSWorkModeDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< SVSWorkModeDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > smSVSWorkModeDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< SVSOnOffStateDaddy_t > sm_SVSOnOffStateDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< SVSOnOffStateDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_SVSOnOffStateDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< SVSvidoutModeDaddy_t > sm_SVSvidoutModeDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< SVSvidoutModeDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_SVSvidoutModeDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< SVSRotateStatusDaddy_t > sm_SVSRotateStatusDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< SVSRotateStatusDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_SVSRotateStatusDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< DayNightThemeDaddy_t > sm_dayNightThemeDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< DayNightThemeDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_dayNightThemeDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< SVSTrajCfgStateDaddy_t > sm_SVSTrajCfgStateDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< SVSTrajCfgStateDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_SVSTrajCfgStateDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< SVSLVDSvidOutModeDaddy_t > sm_SVSLVDSvidOutModeDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< SVSLVDSvidOutModeDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_SVSLVDSvidOutModeDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< SVSImageConfigDaddy_t > sm_SVSImageConfigDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< SVSImageConfigDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_SVSImageConfigDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< SVSCarBodyDaddy_t > sm_SVSCarBodyDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< SVSCarBodyDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_SVSCarBodyDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< SVSExpandedViewStateDaddy_t > sm_SVSExpandedViewStateDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< SVSExpandedViewStateDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_SVSExpandedViewStateDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< SVSNewExpandedViewStateDaddy_t > sm_SVSNewExpandedViewStateDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< SVSNewExpandedViewStateDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_SVSNewExpandedViewStateDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< SVSVehColorAckDaddy_t > sm_SVSVehColorAckDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< SVSVehColorAckDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_SVSVehColorAckDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< SVSUnavlMsgsDaddy_t > sm_SVSUnavlMsgsDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< SVSUnavlMsgsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_SVSUnavlMsgsDaddy_MemPool;

public:
  static ::daddy::TSenderPort< pc::daddy::UltrasonicDataDaddy > sm_customUltrasonicDataDaddySenderPort;
private:
  static ::daddy::TMemPool< pc::daddy::UltrasonicDataDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_customUltrasonicDataDaddy_MemPool;

public:
  static ::daddy::TSenderPort< cc::daddy::TileSplineInterpolateArrayDaddy > sm_tileSplineInterpolateArrayDaddySenderPort;
private:
  static ::daddy::TMemPool< cc::daddy::TileSplineInterpolateArrayDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_tileSplineInterpolateArrayDaddy_MemPool;

// End of BYD

public:
  static  ::daddy::TSenderPort< SVSViewStsDaddy_t > sm_SVSFrViewStsDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< SVSViewStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_SVSFrViewStsDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< SVSViewStsDaddy_t > sm_SVSLeViewStsDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< SVSViewStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_SVSLeViewStsDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< SVSViewStsDaddy_t > sm_SVSReViewStsDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< SVSViewStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_SVSReViewStsDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< SVSViewStsDaddy_t > sm_SVSRiViewStsDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< SVSViewStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_SVSRiViewStsDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< CpcOverlaySwitchDaddy_t > sm_CpcOverlaySwitchDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< CpcOverlaySwitchDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_CpcOverlaySwitchDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< SwVersionShowSwitchDaddy_t > sm_SwVersionShowSwitchDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< SwVersionShowSwitchDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_SwVersionShowSwitchDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< SWInfoDaddy_t > sm_SWInfoDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< SWInfoDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_SWInfoDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< DoorLockStsDaddy_t > sm_DoorLockSts_SenderPort;
private:
  static  ::daddy::TMemPool< DoorLockStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_DoorLockSts_MemPool;

public:
  static ::daddy::TSenderPort< ThreatDetectedDaddy_t > sm_threatDetected_Socket_SenderPort;
private:
  static ::daddy::TMemPool< ThreatDetectedDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_threatDetected_Socket_MemPool;

  //! Closest obstacle
public:
  static ::daddy::TSenderPort< ClosestObstacleDaddy > sm_EnvProcSoundUnitClosestObstacleDaddy_SenderPort;
private:
  static ::daddy::TMemPool< ClosestObstacleDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_ClosestObstacleDaddy_MemPool;

  //! Current View Mode State (for debug purposes)
public:
  static ::daddy::TSenderPort< VMStateDaddy_t > sm_ViewModeState_SenderPort;
private:
  static ::daddy::TMemPool< VMStateDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_Debug_ViewModeState_MemPool;

  //! Current system state
public:
  static ::daddy::TSenderPort< SystemStateDaddy > sm_systemState_SenderPort;
private:
  static ::daddy::TMemPool< SystemStateDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_systemState_MemPool;

  //! User Movement Intention
public:
  static ::daddy::TSenderPort< UserMovementIntentionDaddy_t > sm_userMovementIntention_Socket_SenderPort;
private:
  static ::daddy::TMemPool< UserMovementIntentionDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_userMovementIntentionDaddy_Socket_MemPool;

  //!LSMG Vehicle Movement
public:
  static ::daddy::TSenderPort< LSMG_VehicleMovementDaddy_t > sm_LSMG_VehicleMovement_Socket_SenderPort;
private:
  static ::daddy::TMemPool< LSMG_VehicleMovementDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_LSMG_VehicleMovement_Socket_MemPool;

  //! Vehicle Alpha
public:
  static ::daddy::TSenderPort< VehicleAlphaDaddy > sm_VehicleAlpha_Socket_SenderPort;
private:
  static ::daddy::TMemPool< VehicleAlphaDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_VehicleAlpha_Socket_MemPool;

  //! Vehicle diffuse color
public:
  static ::daddy::TSenderPort< ColorDaddy > sm_VehicleDiffuseColor_Socket_SenderPort;
private:
  static ::daddy::TMemPool< ColorDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_VehicleDiffuseColor_Socket_MemPool;

  //! Vehicle color index
public:
  static ::daddy::TSenderPort< ColorIndexDaddy > sm_VehicleDiffuseColorIndex_SenderPort;
private:
  static ::daddy::TMemPool< ColorIndexDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_VehicleDiffuseColorIndex_MemPool;

  //! Vehicle specular color1
public:
  static ::daddy::TSenderPort< ColorDaddy > sm_VehicleSpecColor1_Socket_SenderPort;
private:
  static ::daddy::TMemPool< ColorDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_VehicleSpecColor1_Socket_MemPool;

  //! Vehicle specular color2
public:
  static ::daddy::TSenderPort< ColorDaddy > sm_VehicleSpecColor2_Socket_SenderPort;
private:
  static ::daddy::TMemPool< ColorDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_VehicleSpecColor2_Socket_MemPool;

//! Other Vehicle Model Parameters
public:
  static ::daddy::TSenderPort< VehicleModelParamDaddy > sm_VehicleModelParam_Socket_SenderPort;
private:
  static ::daddy::TMemPool< VehicleModelParamDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_VehicleModelParam_Socket_MemPool;

//! Ambient Light - Dipped Beam
public:
  static ::daddy::TSenderPort< DippedBeamStateDaddy > sm_AlsDippedBeamState_SenderPort;
private:
  static ::daddy::TMemPool< DippedBeamStateDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_AlsDippedBeamState_MemPool;

//! Distance To Stop Location
public:
  static ::daddy::TSenderPort< StopLineLocationDaddy > sm_StopLineLocationDaddy_SenderPort;
private:
  static ::daddy::TMemPool< StopLineLocationDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_StopLineLocationDaddy_MemPool;

//! Distance To Stop
public:
  static ::daddy::TSenderPort< DistanceToStopDaddy > sm_DistanceToStopDaddy_SenderPort;
private:
  static ::daddy::TMemPool< DistanceToStopDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_DistanceToStopDaddy_MemPool;


//! Automatic Parking Daddy
//TODO: This needs to be cleaned up together with the MainLogic
public:
  static ::daddy::TSenderPort< AutomaticParkingDaddy > sm_AutomaticParkingDaddy_SenderPort;
private:
  static ::daddy::TMemPool< AutomaticParkingDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_AutomaticParkingDaddy_MemPool;

//!LMStatus
public:
  static ::daddy::TSenderPort< LMStatusDaddy_t > sm_LMStatusDaddy_SenderPort;
private:
  static ::daddy::TMemPool< LMStatusDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_LMStatusDaddy_MemPool;

//! IsOffroad
public:
  static ::daddy::TSenderPort< IsOffroadDaddy_t > sm_IsOffroadDaddy_SenderPort;
private:
  static ::daddy::TMemPool< IsOffroadDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_IsOffroadDaddy_MemPool;

//! TrailerSVS
public:
  static ::daddy::TSenderPort< TrailerSvsDaddy > sm_TrailerSvs_SenderPort;
private:
  static ::daddy::TMemPool< TrailerSvsDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_TrailerSvs_MemPool;

//! CarMode
public:
  static ::daddy::TSenderPort< CarModeDaddy_t > sm_CarModeDaddy_SenderPort;
private:
  static ::daddy::TMemPool< CarModeDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_CarModeDaddy_MemPool;

//! PowerMode
public:
  static ::daddy::TSenderPort< PowerModeDaddy_t > sm_PowerModeDaddy_SenderPort;
private:
  static ::daddy::TMemPool< PowerModeDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_PowerModeDaddy_MemPool;

// ! FCTA/RCTA warning
public:
  static ::daddy::TSenderPort< FCTARightDaddy_t > sm_FCTARightDaddy_SenderPort;
private:
  static ::daddy::TMemPool< FCTARightDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_FCTARightDaddy_MemPool;

public:
  static ::daddy::TSenderPort< FCTALeftDaddy_t > sm_FCTALeftDaddy_SenderPort;
private:
  static ::daddy::TMemPool< FCTALeftDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_FCTALeftDaddy_MemPool;

public:
  static ::daddy::TSenderPort< RCTARightDaddy_t > sm_RCTARightDaddy_SenderPort;
private:
  static ::daddy::TMemPool< RCTARightDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_RCTARightDaddy_MemPool;

public:
  static ::daddy::TSenderPort< RCTALeftDaddy_t > sm_RCTALeftDaddy_SenderPort;
private:
  static ::daddy::TMemPool< RCTALeftDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_RCTALeftDaddy_MemPool;

//VR Switch / FCP SVM button
public:
  static ::daddy::TSenderPort< VRSwitchSVMDaddy_t > sm_VRSwitchSVMDaddy_SenderPort;
private:
  static ::daddy::TMemPool< VRSwitchSVMDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_VRSwitchSVMDaddy_MemPool;

public:
  static ::daddy::TSenderPort< FreeparkingParkable_t > sm_FreeParkingParkableDaddy_SenderPort;
private:
  static ::daddy::TMemPool< FreeparkingParkable_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_FreeParkingParkableDaddy_MemPool;

public:
  static ::daddy::TSenderPort< VRSwitchFailStDaddy_t > sm_VRSwitchFailStDaddy_SenderPort;
private:
  static ::daddy::TMemPool< VRSwitchFailStDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_VRSwitchFailStDaddy_MemPool;

public:
  static ::daddy::TSenderPort< FCP_SVMButtonPressed_t > sm_FCP_SVMButtonPressedDaddy_SenderPort;
private:
  static ::daddy::TMemPool< FCP_SVMButtonPressed_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_FCP_SVMButtonPressedDaddy_MemPool;

//! LSAEB
public:
  static ::daddy::TSenderPort< AebVmcOpModeDaddy_t > sm_LSAEBVmcOpModeDaddy_SenderPort;
private:
  static ::daddy::TMemPool< AebVmcOpModeDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_LSAEBVmcOpModeDaddy_MemPool;

//! C2W: calibration status
public:
  static ::daddy::TSenderPort< C2WCalibStatusDaddy_t > sm_CamCalibrationStatusDaddy_SenderPort;
private:
  static ::daddy::TMemPool< C2WCalibStatusDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_CamCalibrationStatusDaddy_MemPool;

//! Backchannel Rx Signals, remaining in case being used in other logic
public:
  static ::daddy::TSenderPort< PIVI_ManualVideoSetupReq_t > sm_PIVI_ManualVideoSetupReq_SenderPort;
private:
  static ::daddy::TMemPool< PIVI_ManualVideoSetupReq_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_PIVI_ManualVideoSetupReq_MemPool;

public:
  static ::daddy::TSenderPort< PIVI_DayNightThemeReq_t > sm_PIVI_DayNightThemeReq_SenderPort;
private:
  static ::daddy::TMemPool< PIVI_DayNightThemeReq_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_PIVI_DayNightThemeReq_MemPool;

public:
  static ::daddy::TSenderPort< PIVI_ViewBufferStatusACK_t > sm_PIVI_ViewBufferStatusACK_SenderPort;
private:
  static ::daddy::TMemPool< PIVI_ViewBufferStatusACK_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_PIVI_ViewBufferStatusACK_MemPool;

//! Backchannel Tx Signals, remaining in case being used in other logic
public:
  static ::daddy::TSenderPort< NFSM_MovementDirectionUpdate_t > sm_NFSM_MovementDirectionUpdate_SenderPort;
private:
  static ::daddy::TMemPool< NFSM_MovementDirectionUpdate_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_NFSM_MovementDirectionUpdate_MemPool;

public:
  static ::daddy::TSenderPort< NFSM_ViewBufferStatus_t > sm_NFSM_ViewBufferStatus_SenderPort;
private:
  static ::daddy::TMemPool< NFSM_ViewBufferStatus_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_NFSM_ViewBufferStatus_MemPool;
// FlexRay LSMGActivationSetReq
public:
  static ::daddy::TSenderPort< LSMGActivationSetReq_t > sm_FlexRayRx_LSMGActivationSetReq_SenderPort;
private:
  static ::daddy::TMemPool< LSMGActivationSetReq_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_FlexRayRx_LSMGActivationSetReq_MemPool;


// PDM R5->Linux ( Rx )
public:
  static ::daddy::TSenderPort< Pdm3DCruStsDaddy_t > sm_PdmR5ToLinux_3DCruStsDaddy_SenderPort;
private:
  static ::daddy::TMemPool< Pdm3DCruStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_PdmR5ToLinux_3DCruStsDaddy_MemPool;

public:
  static ::daddy::TSenderPort< PdmIntegtOpenStsDaddy_t > sm_PdmR5ToLinux_IntegtOpenStsDaddy_SenderPort;
private:
  static ::daddy::TMemPool< PdmIntegtOpenStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_PdmR5ToLinux_IntegtOpenStsDaddy_MemPool;

public:
  static ::daddy::TSenderPort< PdmAutoCamStsDaddy_t > sm_PdmR5ToLinux_AutoCamStsDaddy_SenderPort;
private:
  static ::daddy::TMemPool< PdmAutoCamStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_PdmR5ToLinux_AutoCamStsDaddy_MemPool;

public:
  static ::daddy::TSenderPort< PdmDynOverlayStsDaddy_t > sm_PdmR5ToLinux_DynOverlayStsDaddy_SenderPort;
private:
  static ::daddy::TMemPool< PdmDynOverlayStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_PdmR5ToLinux_DynOverlayStsDaddy_MemPool;

public:
  static ::daddy::TSenderPort< PdmOverlayDistStsDaddy_t > sm_PdmR5ToLinux_OverlayDistStsDaddy_SenderPort;
private:
  static ::daddy::TMemPool< PdmOverlayDistStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_PdmR5ToLinux_OverlayDistStsDaddy_MemPool;

public:
  static ::daddy::TSenderPort< PdmBasePlateStsDaddy_t > sm_PdmR5ToLinux_BasePlateStsDaddy_SenderPort;
private:
  static ::daddy::TMemPool< PdmBasePlateStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_PdmR5ToLinux_BasePlateStsDaddy_MemPool;

public:
  static ::daddy::TSenderPort< PdmVehTransStsDaddy_t > sm_PdmR5ToLinux_VehTransStsDaddy_SenderPort;
private:
  static ::daddy::TMemPool< PdmVehTransStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_PdmR5ToLinux_VehTransStsDaddy_MemPool;

public:
  static ::daddy::TSenderPort< ColorIndexDaddy > sm_PdmR5ToLinux_VehColorStsDaddy_SenderPort;
private:
  static ::daddy::TMemPool< ColorIndexDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_PdmR5ToLinux_VehColorStsDaddy_MemPool;

// PDM Linux->R5 ( Tx )
public:
  static ::daddy::TSenderPort< Pdm3DCruStsDaddy_t > sm_PdmLinuxToR5_3DCruStsDaddy_SenderPort;
private:
  static ::daddy::TMemPool< Pdm3DCruStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_PdmLinuxToR5_3DCruStsDaddy_MemPool;

public:
  static ::daddy::TSenderPort< PdmIntegtOpenStsDaddy_t > sm_PdmLinuxToR5_IntegtOpenStsDaddy_SenderPort;
private:
  static ::daddy::TMemPool< PdmIntegtOpenStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_PdmLinuxToR5_IntegtOpenStsDaddy_MemPool;

public:
  static ::daddy::TSenderPort< PdmAutoCamStsDaddy_t > sm_PdmLinuxToR5_AutoCamStsDaddy_SenderPort;
private:
  static ::daddy::TMemPool< PdmAutoCamStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_PdmLinuxToR5_AutoCamStsDaddy_MemPool;

public:
  static ::daddy::TSenderPort< PdmDynOverlayStsDaddy_t > sm_PdmLinuxToR5_DynOverlayStsDaddy_SenderPort;
private:
  static ::daddy::TMemPool< PdmDynOverlayStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_PdmLinuxToR5_DynOverlayStsDaddy_MemPool;

public:
  static ::daddy::TSenderPort< PdmOverlayDistStsDaddy_t > sm_PdmLinuxToR5_OverlayDistStsDaddy_SenderPort;
private:
  static ::daddy::TMemPool< PdmOverlayDistStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_PdmLinuxToR5_OverlayDistStsDaddy_MemPool;

public:
  static ::daddy::TSenderPort< PdmBasePlateStsDaddy_t > sm_PdmLinuxToR5_BasePlateStsDaddy_SenderPort;
private:
  static ::daddy::TMemPool< PdmBasePlateStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_PdmLinuxToR5_BasePlateStsDaddy_MemPool;

public:
  static ::daddy::TSenderPort< PdmVehTransStsDaddy_t > sm_PdmLinuxToR5_VehTransStsDaddy_SenderPort;
private:
  static ::daddy::TMemPool< PdmVehTransStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_PdmLinuxToR5_VehTransStsDaddy_MemPool;

public:
  static ::daddy::TSenderPort< ColorIndexDaddy > sm_PdmLinuxToR5_VehColorStsDaddy_SenderPort;
private:
  static ::daddy::TMemPool< ColorIndexDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_PdmLinuxToR5_VehColorStsDaddy_MemPool;

//! CCF
public:
  static ::daddy::TSenderPort< CcfDaddy > sm_CCF_SenderPort;
private:
  static ::daddy::TMemPool< CcfDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_CCF_MemPool;

//! LCF
public:
  static ::daddy::TSenderPort< LcfDaddy > sm_LCF_SenderPort;
private:
  static ::daddy::TMemPool< LcfDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_LCF_MemPool;

//! Variant
public:
  static ::daddy::TSenderPort< VariantDaddy > sm_Variant_SenderPort;
private:
  static ::daddy::TMemPool< VariantDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_Variant_MemPool;

//! Vehicle Lights
public:
  static ::daddy::TSenderPort< CustomVehicleLightsDaddy > sm_CustomVehicleLights_SenderPort;
private:
  static ::daddy::TMemPool< CustomVehicleLightsDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_CustomVehicleLights_MemPool;


//! Internal View Buffer Status
public:
  static ::daddy::TSenderPort< NFSM_ViewBufferStatus_t > sm_ViewBufferStatus_SenderPort;
private:
  static ::daddy::TMemPool< NFSM_ViewBufferStatus_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_ViewBufferStatus_MemPool;

//! GBC Vehicle Transparency
public:
  static ::daddy::TSenderPort< GbcVehicleTransparency_t > sm_GbcVehicleTransparency_SenderPort;
private:
  static ::daddy::TMemPool< GbcVehicleTransparency_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_GbcVehicleTransparency_MemPool;

//! GBC Wheel Transparency
public:
  static ::daddy::TSenderPort< GbcWheelTransparency_t > sm_GbcWheelTransparency_SenderPort;
private:
  static ::daddy::TMemPool< GbcWheelTransparency_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_GbcWheelTransparency_MemPool;

//! View Animation Completed
public:
  static ::daddy::TSenderPort< ViewAnimationCompleted_t > sm_viewAnimationCompleted_SenderPort;
private:
  static ::daddy::TMemPool< ViewAnimationCompleted_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_viewAnimationCompleted_MemPool;

//! Diag Routine
public:
  static ::daddy::TSenderPort< DiagRoutine_t > sm_diagRoutine_SenderPort;
private:
  static ::daddy::TMemPool< DiagRoutine_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_diagRoutine_MemPool;

  //! HMI
public:
  static ::daddy::TSenderPort< HmiData_Daddy > sm_HmiData_SenderPort;
private:
  static ::daddy::TMemPool< HmiData_Daddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_HmiData_MemPool;

//! Hemispherical camera controlller - return channel from controller to state machine
public:
  static ::daddy::TSenderPort< HemisphereParameterData_Daddy > sm_HemisphereParameterData_SenderPort;
private:
  static ::daddy::TMemPool< HemisphereParameterData_Daddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_HemisphereParameterData_MemPool;

//! Head Unit Camera Commands
public:
  static ::daddy::TSenderPort< HUCameraCommandsDaddy > sm_HUCameraCommandsDaddySenderPort;
private:
  static ::daddy::TMemPool< HUCameraCommandsDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_HUCameraCommandsDaddyMemPool;

//! free view mode camera position
public:
  static ::daddy::TSenderPort< CameraPositionDaddy_t > sm_CameraPositionDaddySenderPort;
private:
  static ::daddy::TMemPool< CameraPositionDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_CameraPositionDaddyMemPool;

//! Degradation FIDs
public:
  static ::daddy::TSenderPort< DegradationFid_t > sm_degradationFid_SenderPort;
private:
  static ::daddy::TMemPool< DegradationFid_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_degradationFid_MemPool;

//! impostor transparency
public:
  static ::daddy::TSenderPort<ImpostorTransparencyDaddy> sm_impostorTransparencySenderPort;
private:
  static ::daddy::TMemPool<ImpostorTransparencyDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE> sm_impostorTransparencyMemPool;

//! SVS displayed view
public:
  static ::daddy::TSenderPort< SVSDisplayedViewDaddy_t > sm_SVSDisplayedViewDaddy_SenderPort;
private:
  static ::daddy::TMemPool< SVSDisplayedViewDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_SVSDisplayedViewDaddy_MemPool;

//! SVS free view mode status
public:
  static ::daddy::TSenderPort< SVSFreeModeStDaddy_t > sm_SVSFreeModeStDaddy_SenderPort;
private:
  static ::daddy::TMemPool< SVSFreeModeStDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_SVSFreeModeStDaddy_MemPool;

//! Notice Rolling
public:
  static ::daddy::TSenderPort< NoticeRolling_t > sm_noticeRolling_SenderPort;
private:
  static ::daddy::TMemPool< NoticeRolling_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_noticeRolling_MemPool;

//! Park UI Spot data
public:
  static ::daddy::TSenderPort< ParkUISpotData_t > sm_SVSParkUISpotDataDaddy_SenderPort;
private:
  static ::daddy::TMemPool< ParkUISpotData_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_SVSParkUISpotDataDaddy_MemPool;

//! SVS to StateMachine parkconfirm interface exist or not
public:
  static ::daddy::TSenderPort< ParkConfirmInterfaceExist_t > sm_SVSParkConfirmInterfaceDataDaddy_SenderPort;
private:
  static ::daddy::TMemPool< ParkConfirmInterfaceExist_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_SVSParkConfirmInterfaceDataDaddy_MemPool;

//! Park UI Available Parking Slot
public:
  static ::daddy::TSenderPort< ParkUIAvailableParkingSlot_t > sm_SVSParkUIAvailableParkingSlotDaddy_SenderPort;
private:
  static ::daddy::TMemPool< ParkUIAvailableParkingSlot_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_SVSParkUIAvailableParkingSlotDaddy_MemPool;


//! Raw Data for Odometry
public:
  static ::daddy::TSenderPort< CustomVhmAbstRaw_t > sm_customVhmAbstRawDataDaddy_SenderPort;
private:
  static ::daddy::TMemPool< CustomVhmAbstRaw_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_customVhmAbstRawDataDaddy_MemPool;

//! Free parking
public:
  static ::daddy::TSenderPort<FreeparkingData_Daddy> sm_freeparkingDataSenderPort;
private:
  static ::daddy::TMemPool<FreeparkingData_Daddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE> sm_freeparkingDataMemPool;

public:
  static ::daddy::TSenderPort<FreeparkingRectInfo_Daddy> sm_freeparkingRectInfoSenderPort;
private:
  static ::daddy::TMemPool<FreeparkingRectInfo_Daddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE> sm_freeparkingRectInfoMemPool;

public:
  static ::daddy::TSenderPort<FreeparkingSocket_Daddy> sm_freeparkingSocketSenderPort;
private:
  static ::daddy::TMemPool<FreeparkingSocket_Daddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE> sm_freeparkingSocketMemPool;

public:
  static  ::daddy::TSenderPort< FreeParkingConfirmButtonStDaddy_t > sm_FreeParkingConfirmButtonStDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< FreeParkingConfirmButtonStDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_FreeParkingConfirmButtonStDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< FreeParkingReturnButtonStDaddy_t > sm_FreeParkingReturnButtonStDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< FreeParkingReturnButtonStDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_FreeParkingReturnButtonStDaddy_MemPool;

// //!cpc Extrinsic Data
// public:
//   static ::daddy::TSenderPort< CpcCornerDetectionStatus_t > sm_cpcCornerDetectionDataDaddy_SenderPort;
// private:
//   static ::daddy::TMemPool< CpcCornerDetectionStatus_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_cpcCornerDetectionDataDaddy_MemPool;

public:
  static ::daddy::TSenderPort< NormalCalibrationDaddy > sm_CpcNorminalCalibDaddy_SenderPort;
private:
  static ::daddy::TMemPool< NormalCalibrationDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_CpcNorminalCalibDaddy_MemPool;

// //!cpc status
// public:
//   static ::daddy::TSenderPort< CpcStatus_st_t > sm_cpcStatusDaddy_SenderPort;
// private:
//   static ::daddy::TMemPool< CpcStatus_st_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_cpcStatusDaddy_MemPool;

//! CPC to CPC_WRAPPER
public:
  static ::daddy::TSenderPort<CpcToCpcWrapper_t> sm_cpcToCpcWrapper_SenderPort;
private:
  static ::daddy::TMemPool<CpcToCpcWrapper_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE> sm_cpcToCpcWrapper_MemPool;

//! CPC_WRAPPER to CPC
public:
  static ::daddy::TSenderPort<CpcWrapperToCpc_t> sm_cpcWrapperToCpc_SenderPort;
private:
  static ::daddy::TMemPool<CpcWrapperToCpc_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE> sm_cpcWrapperToCpc_MemPool;

//! CPC to SVS
public:
  static ::daddy::TSenderPort<CpcToSvsOverlay_t> sm_cpcToSvsOverlay_SenderPort;
private:
  static ::daddy::TMemPool<CpcToSvsOverlay_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE> sm_cpcToSvsOverlay_MemPool;

//! SVS to CPC
public:
  static ::daddy::TSenderPort<SvsOverlayToCpc_t> sm_svsOverlayToCpc_SenderPort;
private:
  static ::daddy::TMemPool<SvsOverlayToCpc_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE> sm_svsOverlayToCpc_MemPool;

public:
  static ::daddy::TSenderPort< DynamicGearActive_t > sm_dynamicGearStatus_SenderPort;
private:
  static ::daddy::TMemPool< DynamicGearActive_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_dynamicGearStatus_MemPool;

//! solid baseplate toggle
public:
  static ::daddy::TSenderPort<SolidBasePlateStateDaddy> sm_groundplane_triangle_SenderPort;
private:
  static ::daddy::TMemPool<SolidBasePlateStateDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE> sm_groundplane_triangle_MemPool;

//! PlanViewEnlargeStatus
public:
  static ::daddy::TSenderPort<PlanViewEnlargeStatusDaddy> sm_planViewEnlargeStatus_SenderPort;
private:
  static ::daddy::TMemPool<PlanViewEnlargeStatusDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE> sm_planViewEnlargeStatus_MemPool;

//! SideViewEnableStatusDaddy
public:
  static ::daddy::TSenderPort<SideViewEnableStatusDaddy> sm_sideViewEnableStatus_SenderPort;
private:
  static ::daddy::TMemPool<SideViewEnableStatusDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE> sm_sideViewEnableStatus_MemPool;

//! TopViewEnableStatusDaddy
public:
  static ::daddy::TSenderPort<TopViewEnableStatusDaddy> sm_topViewEnableStatus_SenderPort;
private:
  static ::daddy::TMemPool<TopViewEnableStatusDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE> sm_topViewEnableStatus_MemPool;

//! EViewAnimationDaddy
public:
  static ::daddy::TSenderPort<EViewAnimationDaddy> sm_animationDaddy_SenderPort;
private:
  static ::daddy::TMemPool<EViewAnimationDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE> sm_animationDaddy_MemPool;

//! CamPosAxis2RqDaddy
public:
  static ::daddy::TSenderPort<CamPosAxis2RqDaddy> sm_camPosAxis2Rq_SenderPort;
private:
  static ::daddy::TMemPool<CamPosAxis2RqDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE> sm_camPosAxis2Rq_MemPool;

//! Component test trigger
public:
  static ::daddy::TSenderPort<ComponentTestSwitchDaddy> sm_componentTestSwitch_SenderPort;
private:
  static ::daddy::TMemPool<ComponentTestSwitchDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE> sm_componentTestSwitch_MemPool;

//! Virtual Realitiy
public:
  static  ::daddy::TSenderPort< ParkSlotDaddy_t > sm_ParkSlotDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< ParkSlotDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_ParkSlotDaddy_MemPool;

public:
  static  ::daddy::TSenderPort< ParkSlotDaddy_t > sm_ParkSlotRefinedDaddy_SenderPort;
private:
  static  ::daddy::TMemPool< ParkSlotDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_ParkSlotRefinedDaddy_MemPool;

//! Fusion object
public:
  static ::daddy::TSenderPort< FusionObjectDaddy_t > sm_fusionObject_SenderPort;
private:
  static ::daddy::TMemPool< FusionObjectDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_fusionObject_MemPool;

//! SitOcp
public:
  static ::daddy::TSenderPort< SitOcpDaddy_t > sm_sitOcp_SenderPort;
private:
  static ::daddy::TMemPool< SitOcpDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_sitOcp_MemPool;

//! Pedestrian
public:
  static ::daddy::TSenderPort< PedestrianDaddy_t > sm_pedestrianObj_SenderPort;
private:
  static ::daddy::TMemPool< PedestrianDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_pedestrianObj_MemPool;

//! ParkHmiToSVS
public:
  static ::daddy::TSenderPort< ParkhmiToSvs_t > sm_ParkhmiToSvs_SenderPort;
private:
  static ::daddy::TMemPool< ParkhmiToSvs_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_ParkhmiToSvs_MemPool;

//! SvsToParkhmi
public:
  static ::daddy::TSenderPort< SvsToParkhmi_t > sm_SvsToParkhmi_SenderPort;
private:
  static ::daddy::TMemPool< SvsToParkhmi_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_SvsToParkhmi_MemPool;

//! FreeParkingSlot
public:
  static ::daddy::TSenderPort< FreeParkingSlot_t > sm_FreeParkingSlot_SenderPort;
private:
  static ::daddy::TMemPool< FreeParkingSlot_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_FreeParkingSlot_MemPool;

//! FreeParkingSlotInternal
public:
  static ::daddy::TSenderPort< FreeParkingCorners_t > sm_FreeParkingSlotInternal_SenderPort;
private:
  static ::daddy::TMemPool< FreeParkingCorners_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_FreeParkingSlotInternal_MemPool;

//! SlotSelectedId
public:
  static ::daddy::TSenderPort< SlotSelectedId_t > sm_SlotSelectedId_SenderPort;
private:
  static ::daddy::TMemPool< SlotSelectedId_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_SlotSelectedId_MemPool;

//! ParkoutSelectedDirection
public:
  static ::daddy::TSenderPort< ParkoutSelectedDirection_t > sm_ParkoutSelectedDirection_SenderPort;
private:
  static ::daddy::TMemPool< ParkoutSelectedDirection_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_ParkoutSelectedDirection_MemPool;

//! AirSuspensionHeight
public:
  static ::daddy::TSenderPort< AirSuspensionHeight_t > sm_AirSuspensionHeight_SenderPort;
private:
  static ::daddy::TMemPool< AirSuspensionHeight_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_AirSuspensionHeight_MemPool;

//! ZoomLevel HU -> SVS
public:
  static ::daddy::TSenderPort< ZoomLevel_t > sm_ZoomLevel_SenderPort;
private:
  static ::daddy::TMemPool< ZoomLevel_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_ZoomLevel_MemPool;

//! ZoomLevel SVS -> HU
public:
  static ::daddy::TSenderPort< ZoomLevel_t > sm_ZoomLevelIPC_SenderPort;
private:
  static ::daddy::TMemPool< ZoomLevel_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_ZoomLevelIPC_MemPool;

//! RemoveDistortion
public:
  static ::daddy::TSenderPort< RemoveDistortion_t > sm_RemoveDistortion_SenderPort;
private:
  static ::daddy::TMemPool< RemoveDistortion_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_RemoveDistortion_MemPool;

//! BirdEyeView switch
public:
  static ::daddy::TSenderPort< BirdEyeViewSwitch_t > sm_BirdEyeViewSwitch_SenderPort;
private:
  static ::daddy::TMemPool< BirdEyeViewSwitch_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_BirdEyeViewSwitch_MemPool;

//! SurroundViewAngle
public:
  static ::daddy::TSenderPort< SurroundViewRotateAngle_t > sm_SurroundViewRotateAngle_SenderPort;
private:
  static ::daddy::TMemPool< SurroundViewRotateAngle_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_SurroundViewRotateAngle_MemPool;

//! CrabGuideline
public:
  static ::daddy::TSenderPort< CrabGuideline_t > sm_CrabGuideline_SenderPort;
private:
  static ::daddy::TMemPool< CrabGuideline_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_CrabGuideline_MemPool;

//! GoldenEmblem
public:
  static ::daddy::TSenderPort< GoldenEmblem_t > sm_GoldenEmblem_SenderPort;
private:
  static ::daddy::TMemPool< GoldenEmblem_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > sm_GoldenEmblem_MemPool;

private:
  CustomDaddyPorts(); //this is completely static, so constructor is private

  static bool sm_customerMemoryPoolsAreConnected;
  static bool sm_customerMemoryPoolsHaveInitialData;
};


} //namespace daddy
} //cc

#endif
