/// @copyright (C) 2023 Robert <PERSON> GmbH.
/// The reproduction, distribution and utilization of this file as well as the
/// communication of its contents to others without express authorization is
/// prohibited. Offenders will be held liable for the payment of damages. All
/// rights reserved in the event of the grant of a patent, utility model or
/// design.
/// @file

#include "pc/svs/imp/chamaeleon/inc/chamaeleon_manager.hpp"
#include "vfc/core/vfc_algorithm.hpp"
#include "pc/svs/core/inc/Framework.h"
#include "pc/svs/factory/inc/RenderManager.h"
#include "pc/svs/factory/inc/SV3DStateGraph.h"

namespace rbp
{
namespace vis
{
namespace imp
{
namespace chamaeleon
{
ChamaeleonManager::ChamaeleonManager(pc::core::Framework* f_pFramework, EChamaeleonView f_settingChamaeleon)
    : m_chamaeleonViewId{f_settingChamaeleon}
    , m_useChamaeleonShader{false}
    , m_chamaeleonMngLines{}
    , m_framework{f_pFramework}
    , m_updateShaderLogic{false}
{
}

static void updateShaderLogic(
    bool                         f_useChamaeleonShader,
    EChamaeleonView              f_chamaeleonViewId,
    pc::core::Framework*         f_framework,
    pc::factory::RenderManager*  f_renderManager,
    pc::factory::SV3DStateGraph& f_stateGraph)
{
    const bool l_useChamaeleonShader{(f_chamaeleonViewId == EChamaeleonView::DEFAULT) && f_useChamaeleonShader};
    const bool l_useChamaeleonSideBySideShader{
        (f_chamaeleonViewId == EChamaeleonView::SIDE_BY_SIDE) && f_useChamaeleonShader};

    f_renderManager->getShaderLogic()->setEnableChamaeleon(l_useChamaeleonShader);

    pc::core::VideoTexture* const l_videoTexture{f_framework->getVideoTexture()};
    assert(l_videoTexture);

    for (vfc::uint32_t i{0U}; i < pc::core::sysconf::E_SYSTEM_CONFIG_NUMBER_OF_CAMERAS; ++i)
    {
        f_stateGraph.setSingleCamShader(
            i, f_renderManager->getShaderLogic()->getSingleCamShaderParameters(!f_stateGraph.m_isFloor, i));

        f_stateGraph.setTwoCamShader(
            i,
            f_renderManager->getShaderLogic()->getTwoCamShaderParameters(!f_stateGraph.m_isFloor, i),
            l_videoTexture);
    }
}

static pc::factory::SingleCamArea getSingleCam(pc::core::sysconf::Cameras f_sysConfCam)
{
    switch (f_sysConfCam)
    {
    case pc::core::sysconf::Cameras::FRONT_CAMERA:
    {
        return pc::factory::SingleCamArea::SINGLE_CAM_FRONT;
    }
    case pc::core::sysconf::Cameras::RIGHT_CAMERA:
    {
        return pc::factory::SingleCamArea::SINGLE_CAM_RIGHT;
    }
    case pc::core::sysconf::Cameras::REAR_CAMERA:
    {
        return pc::factory::SingleCamArea::SINGLE_CAM_REAR;
    }
    case pc::core::sysconf::Cameras::LEFT_CAMERA:
    {
        return pc::factory::SingleCamArea::SINGLE_CAM_LEFT;
    }
    default:
    {
        return pc::factory::SingleCamArea::SINGLE_CAM_FRONT;
    }
    }
}

static void getOrCreateUniformsSingleCam(
    pc::factory::SV3DStateGraph&  f_stateGraph,
    const ChamaeleonManagerLines& f_chamaeleonMngLines,
    osg::Texture2D*               f_gainsAsTexture)
{
    for (const auto singleCamArea : common::CSingleCamAreaIndex::ALL)
    {
        osg::StateSet* const l_stateSet{f_stateGraph.m_singleCam[static_cast<vfc::size_t>(singleCamArea.asInteger())]};

        const ETabImageOverlapROI indexChamLineLeft{getOverlapROILeft(singleCamArea.asEnum())};
        const ETabImageOverlapROI indexChamLineRight{getOverlapROIRight(singleCamArea.asEnum())};

        osg::Uniform* const l_stitchLineUniformLeft{
            l_stateSet->getOrCreateUniform("u_cham_direction_left", osg::Uniform::FLOAT_VEC2)};

        l_stitchLineUniformLeft->set(f_chamaeleonMngLines.getLineDirection(indexChamLineLeft));
        osg::Uniform* const l_stitchLineUniformRight{
            l_stateSet->getOrCreateUniform("u_cham_direction_right", osg::Uniform::FLOAT_VEC2)};
        l_stitchLineUniformRight->set(f_chamaeleonMngLines.getLineDirection(indexChamLineRight));

        osg::Uniform* const l_originLineUniformLeft{
            l_stateSet->getOrCreateUniform("u_cham_origin_left", osg::Uniform::FLOAT_VEC3)};
        l_originLineUniformLeft->set(f_chamaeleonMngLines.getOrigin(getWorldOverlap(indexChamLineLeft)));
        osg::Uniform* const l_originLineUniformRight{
            l_stateSet->getOrCreateUniform("u_cham_origin_right", osg::Uniform::FLOAT_VEC3)};
        l_originLineUniformRight->set(f_chamaeleonMngLines.getOrigin(getWorldOverlap(indexChamLineRight)));

        osg::Uniform* const l_worldRoiIdxUniform{
            l_stateSet->getOrCreateUniform("u_cham_worldRoiIdx", osg::Uniform::INT_VEC2)};
        l_worldRoiIdxUniform->set(static_cast<vfc::int32_t>(indexChamLineLeft), static_cast<vfc::int32_t>(indexChamLineRight));

        l_stateSet->setTextureAttribute(
            1U,
            f_gainsAsTexture,
            static_cast<osg::StateAttribute::OverrideValue>(osg::StateAttribute::ON) |
                static_cast<osg::StateAttribute::OverrideValue>(osg::StateAttribute::OVERRIDE));
        osg::Uniform* const l_samplerUniformRoiAsTexture =
            l_stateSet->getOrCreateUniform("s_chamGainsAsTexture", osg::Uniform::SAMPLER_2D);
        l_samplerUniformRoiAsTexture->set(1);
    }
}

static void getOrCreateUniformsSingleCamSideBySide(
    pc::factory::SV3DStateGraph& f_stateGraph,
    const ArraySingleCamVec3f&   f_wbgSideCam)
{
    for (const auto singleCamArea : common::CSingleCamAreaIndex::ALL)
    {
        osg::StateSet* const l_stateSet{f_stateGraph.m_singleCam[static_cast<vfc::size_t>(singleCamArea.asInteger())]};

        osg::Uniform* const l_gainUniform{l_stateSet->getOrCreateUniform("u_cham_gain", osg::Uniform::FLOAT_VEC3)};
        l_gainUniform->set(f_wbgSideCam[singleCamArea]);
    }
}

static void getOrCreateUniformsTwoCam(
    pc::factory::SV3DStateGraph&  f_stateGraph,
    const ChamaeleonManagerLines& f_chamaeleonMngLines,
    osg::Texture2D*               f_gainsAsTexture)
{
    for (const auto twoCamArea : common::CTwoCamAreaIndex::ALL)
    {
        const pc::factory::SingleCamArea l_singleCamLeft{getSingleCamLeft(twoCamArea.asEnum())};
        const pc::factory::SingleCamArea l_singleCamRight{getSingleCamRight(twoCamArea.asEnum())};

        // To realize the colorCorrection weight based on a gradient, we need four chamLines (2 x 2 corresponding)
        // The gradient weight is implemented between two corresponding chamLines.
        // As usual, the viewer is in the vehicle and looks in the direction of the stitching line.

        // The chamLineIndex  left next to the stitchingLine
        const ETabImageOverlapROI l_indexChamLineMidLeft{getOverlapROIRight(l_singleCamLeft)};
        // The chamLineIndex right next to the stitchingLine
        const ETabImageOverlapROI l_indexChamLineMidRight{getOverlapROILeft(l_singleCamRight)};
        // The index of the corresponding chamLine to l_indexChamLineMidLeft
        const ETabImageOverlapROI l_indexChamLineLeft{getOverlapROILeft(l_singleCamLeft)};
        // The index of the corresponding chamLine to l_indexChamLineMidRight
        const ETabImageOverlapROI l_indexChamLineRight{getOverlapROIRight(l_singleCamRight)};

        osg::StateSet* const l_stateSet{f_stateGraph.m_twoCam[static_cast<vfc::size_t>(twoCamArea.asInteger())]};

        osg::Uniform* const l_stitchLineUniformMidLeft{
            l_stateSet->getOrCreateUniform("u_cham_direction_mid_left", osg::Uniform::FLOAT_VEC2)};
        l_stitchLineUniformMidLeft->set(f_chamaeleonMngLines.getLineDirection(l_indexChamLineMidLeft));
        osg::Uniform* const l_stitchLineUniformMidRight{
            l_stateSet->getOrCreateUniform("u_cham_direction_mid_right", osg::Uniform::FLOAT_VEC2)};
        l_stitchLineUniformMidRight->set(f_chamaeleonMngLines.getLineDirection(l_indexChamLineMidRight));
        osg::Uniform* const l_stitchLineUniformLeft{
            l_stateSet->getOrCreateUniform("u_cham_direction_left", osg::Uniform::FLOAT_VEC2)};
        l_stitchLineUniformLeft->set(f_chamaeleonMngLines.getLineDirection(l_indexChamLineLeft));
        osg::Uniform* const l_stitchLineUniformRight{
            l_stateSet->getOrCreateUniform("u_cham_direction_right", osg::Uniform::FLOAT_VEC2)};
        l_stitchLineUniformRight->set(f_chamaeleonMngLines.getLineDirection(l_indexChamLineRight));

        osg::Uniform* const l_originLineUniformMid{
            l_stateSet->getOrCreateUniform("u_cham_origin_mid", osg::Uniform::FLOAT_VEC3)};
        l_originLineUniformMid->set(f_chamaeleonMngLines.getOrigin(getWorldOverlap(twoCamArea.asEnum())));
        osg::Uniform* const l_originLineUniformLeft{
            l_stateSet->getOrCreateUniform("u_cham_origin_left", osg::Uniform::FLOAT_VEC3)};
        l_originLineUniformLeft->set(f_chamaeleonMngLines.getOrigin(getWorldOverlap(l_indexChamLineLeft)));
        osg::Uniform* const l_originLineUniformRight{
            l_stateSet->getOrCreateUniform("u_cham_origin_right", osg::Uniform::FLOAT_VEC3)};
        l_originLineUniformRight->set(f_chamaeleonMngLines.getOrigin(getWorldOverlap(l_indexChamLineRight)));

        osg::Uniform* const l_worldRoiIdxUniform{
            l_stateSet->getOrCreateUniform("u_cham_worldRoiIdx", osg::Uniform::INT_VEC4)};
        l_worldRoiIdxUniform->set(
            static_cast<vfc::int32_t>(l_indexChamLineLeft),
            static_cast<vfc::int32_t>(l_indexChamLineMidRight),
            static_cast<vfc::int32_t>(l_indexChamLineMidLeft),
            static_cast<vfc::int32_t>(l_indexChamLineRight));

        l_stateSet->setTextureAttribute(
            2U,
            f_gainsAsTexture,
            static_cast<osg::StateAttribute::OverrideValue>(osg::StateAttribute::ON) |
                static_cast<osg::StateAttribute::OverrideValue>(osg::StateAttribute::OVERRIDE));
        osg::Uniform* const l_samplerUniformRoiAsTexture =
            l_stateSet->getOrCreateUniform("s_chamGainsAsTexture", osg::Uniform::SAMPLER_2D);
        l_samplerUniformRoiAsTexture->set(2);
    }
}

static void
getOrCreateUniformsTwoCamSideBySide(pc::factory::SV3DStateGraph& f_stateGraph, const ArraySingleCamVec3f& f_wbgSideCam)
{
    for (const auto twoCamArea : common::CTwoCamAreaIndex::ALL)
    {
        const pc::factory::SingleCamArea l_singleCamLeft{getSingleCamLeft(twoCamArea.asEnum())};
        const pc::factory::SingleCamArea l_singleCamRight{getSingleCamRight(twoCamArea.asEnum())};

        osg::StateSet* const l_stateSet{f_stateGraph.m_twoCam[static_cast<vfc::size_t>(twoCamArea.asInteger())]};

        osg::Uniform* const l_gainUniformLeft{
            l_stateSet->getOrCreateUniform("u_cham_gain_left", osg::Uniform::FLOAT_VEC3)};
        l_gainUniformLeft->set(f_wbgSideCam[l_singleCamLeft]);

        osg::Uniform* const l_gainUniformRight{
            l_stateSet->getOrCreateUniform("u_cham_gain_right", osg::Uniform::FLOAT_VEC3)};
        l_gainUniformRight->set(f_wbgSideCam[l_singleCamRight]);
    }
}

void ChamaeleonManager::apply(pc::factory::SV3DStateGraph& f_stateGraph, pc::factory::RenderManager* f_renderManager)
    const
{
    if (m_chamaeleonViewId == EChamaeleonView::NO_CHAMAELEON)
    {
        return;
    }

    if (m_updateShaderLogic)
    {
        updateShaderLogic(m_useChamaeleonShader, m_chamaeleonViewId, m_framework, f_renderManager, f_stateGraph);
    }

    if (m_useChamaeleonShader)
    {
        switch (m_chamaeleonViewId)
        {
        case EChamaeleonView::DEFAULT:
        {
            getOrCreateUniformsSingleCam(f_stateGraph, m_chamaeleonMngLines, m_gainsAsTexture);
            getOrCreateUniformsTwoCam(f_stateGraph, m_chamaeleonMngLines, m_gainsAsTexture);
            break;
        }
        case EChamaeleonView::SIDE_BY_SIDE:
        {
            getOrCreateUniformsSingleCam(f_stateGraph, m_chamaeleonMngLines, m_gainsAsTexture);
            getOrCreateUniformsSingleCamSideBySide(f_stateGraph, m_gainsAsVecSideBySide);
            getOrCreateUniformsTwoCamSideBySide(f_stateGraph, m_gainsAsVecSideBySide);
            break;
        }
        case EChamaeleonView::NO_CHAMAELEON:
        default:
        {
            break;
        }
        }
    }
}

void ChamaeleonManager::receiveChamaeleonInput()
{
    const pc::daddy::ChamaeleonOutputDaddy* const l_chamaeleonReceiverData{m_framework->m_chamaeleonReceiver.getData()};
    if (nullptr != l_chamaeleonReceiverData)
    {
        const bool l_receivedEnableChamaeleon{
            (m_chamaeleonViewId == EChamaeleonView::DEFAULT && l_chamaeleonReceiverData->m_Data.m_enableTopview) ||
            (m_chamaeleonViewId == EChamaeleonView::SIDE_BY_SIDE &&
             l_chamaeleonReceiverData->m_Data.m_enableSideBySide)};

        m_updateShaderLogic    = (m_useChamaeleonShader != l_receivedEnableChamaeleon);
        m_useChamaeleonShader  = l_receivedEnableChamaeleon;
        m_gainsAsTexture       = l_chamaeleonReceiverData->m_Data.m_gainsAsTexture;
        m_gainsAsVecSideBySide = l_chamaeleonReceiverData->m_Data.m_gainsAsVecSideBySide;
    }
}

void ChamaeleonManager::update(const pc::factory::StitchingLinesManager* const f_stitchLineMng)
{
    if (m_chamaeleonViewId == EChamaeleonView::NO_CHAMAELEON)
    {
        return;
    }

    receiveChamaeleonInput();

    if (nullptr != f_stitchLineMng)
    {
        m_chamaeleonMngLines.update(f_stitchLineMng);
    }
}

} // namespace chamaeleon
} // namespace imp
} // namespace vis
} // namespace rbp
