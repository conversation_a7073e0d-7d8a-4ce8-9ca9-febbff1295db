/// @copyright (C) 2023 Robert <PERSON> GmbH.
/// The reproduction, distribution and utilization of this file as well as the
/// communication of its contents to others without express authorization is
/// prohibited. Offenders will be held liable for the payment of damages. All
/// rights reserved in the event of the grant of a patent, utility model or
/// design.
/// @file

#include "pc/svs/imp/chamaeleon/inc/visu/chamaeleon_visu.hpp"
#include "pc/svs/util/osgx/inc/Utils.h"

namespace rbp
{
namespace vis
{
namespace imp
{
namespace chamaeleon
{
namespace visu
{

ChamaeleonVisu::ChamaeleonVisu(const Chamaeleon* f_pCham)
    : osg::Switch{}
    , m_chamaeleonVisuRois{osg_ext::make_ref<ChamaeleonVisuRois>(f_pCham->getChamaeleonRois())}
{
    // by default all added children should be not visible
    setNewChildDefaultValue(false);

    osg::Switch::addChild(m_chamaeleonVisuR<PERSON>);
}

void ChamaeleonVisu::setParameters(const ChamaeleonVisuData& f_params)
{
    // ChamaeleonVisuRois
    setChildValue(m_chamaeleonVisuRois.get(), f_params.m_visuRoiData.m_enable);
    m_chamaeleonVisuRois->setViewport(f_params.m_visuRoiData.m_viewport);
}

void ChamaeleonVisu::traverse(osg::NodeVisitor& f_nv)
{
    osg::Switch::traverse(f_nv);
}

} // namespace visu
} // namespace chamaeleon
} // namespace imp
} // namespace vis
} // namespace rbp
