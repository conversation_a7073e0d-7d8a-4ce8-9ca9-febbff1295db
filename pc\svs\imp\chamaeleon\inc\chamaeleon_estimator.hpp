/// @copyright (C) 2025 Robert <PERSON> GmbH.
/// The reproduction, distribution and utilization of this file as well as the
/// communication of its contents to others without express authorization is
/// prohibited. Offenders will be held liable for the payment of damages. All
/// rights reserved in the event of the grant of a patent, utility model or
/// design.
/// @file

#ifndef PC_SVS_IMP_CHAMAELEONESTIMATOR_H
#define PC_SVS_IMP_CHAMAELEONESTIMATOR_H

#include "pc/svs/imp/chamaeleon/inc/chamaeleon_data.hpp"
#include "pc/svs/imp/chamaeleon/inc/chamaeleon_tabs.hpp"
#include "pc/svs/util/osgx/inc/RenderToTextureCamera.h"

namespace rbp
{
namespace vis
{
namespace imp
{
namespace chamaeleon
{

///
/// ChamaeleonEstimator
///
class ChamaeleonEstimator
{
public:
    ChamaeleonEstimator();

    ~ChamaeleonEstimator() = default;

    //------------------------------------------------------------------------------------------------------------------
    /// @brief Sets the parameters for the ChamaeleonEstimator.
    ///
    /// @param[in] f_params The parameters to set.
    //------------------------------------------------------------------------------------------------------------------
    void setParameters(const ChamaeleonEstimatorData& f_params = ChamaeleonEstimatorData{});

    //------------------------------------------------------------------------------------------------------------------
    /// @brief Updates the Estimator cameras by accepting a node visitor.
    ///
    /// @param[in] f_nv The node visitor to accept.
    //------------------------------------------------------------------------------------------------------------------
    void updateEstimator(
        osg::Texture2D*       f_roisAsTexture,
        const osg::Vec4&      f_worldRoiDegradationMask,
        const osg::Matrix4x3& f_wbgRawGains,
        osg::NodeVisitor&     f_nv);

    osg::Texture2D* getGainsAsTexture() const
    {
        return m_chamaeleonPingPongEstimator[m_pingPongCounter].m_cam.get()->getTexture();
    }

private:
    ChamaeleonEstimator(const ChamaeleonEstimator&)            = delete;
    ChamaeleonEstimator& operator=(const ChamaeleonEstimator&) = delete;

    struct EstiPingPong
    {
        ::osg::ref_ptr<::osg::Geode>                            m_geode;
        ::osg::ref_ptr<::pc::util::osgx::RenderToTextureCamera> m_cam;
        ::osg::ref_ptr<::osg::Texture2D>                        m_estiAsTexture;
    };

    vfc::float32_t                       m_rampFac{0.5F};
    bool                                 m_useWbGainsAsFallback{false};
    bool                                 m_callUpdateEstiFirstTime{true};
    osg::Vec2f                           m_lowerUpperThresholdDeltaL;
    osg::Vec2f                           m_lowerUpperThresholdDeltaAB;
    osg::Vec2f                           m_minMaxOutputGain;
    vfc::TCArray<struct EstiPingPong, 2> m_chamaeleonPingPongEstimator;
    vfc::int32_t                         m_pingPongCounter{0};

    const osg::Matrix4x3 m_neutralGains{
        /*FRONT_R*/ 1.F,
        /*FRONT_G*/ 1.F,
        /*FRONT_B*/ 1.F,
        /*RIGHT_R*/ 1.F,
        /*RIGHT_G*/ 1.F,
        /*RIGHT_B*/ 1.F,
        /*REAR_R*/ 1.F,
        /*REAR_G*/ 1.F,
        /*REAR_B*/ 1.F,
        /*LEFT_R*/ 1.F,
        /*LEFT_G*/ 1.F,
        /*LEFT_B*/ 1.F};
};

} // namespace chamaeleon
} // namespace imp
} // namespace vis
} // namespace rbp

#endif // PC_SVS_IMP_CHAMAELEONESTIMATOR_H
