//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: LIA1LR David Liepelt (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  HeadUnitHemisphereCameraUpdater.h
/// @brief
//=============================================================================

#ifndef CC_VIRTCAM_HEADUNIT_HEMISPHERE_CAMERA_UPDATER_H
#define CC_VIRTCAM_HEADUNIT_HEMISPHERE_CAMERA_UPDATER_H

#include <osg/Vec3f>
#include "cc/daddy/inc/CustomDaddyPorts.h"
#include "cc/virtcam/inc/HemisphereCameraUpdater.h"
#include <cmath>
#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/svs/util/osgx/inc/MemberWrapper.h"
#include "cc/sm/viewmode/inc/ViewModexViewStateMachine_R2015b_types.h"

namespace pc
{
namespace core
{
class Framework;
} // namespace core
} // namespace pc

namespace cc
{
namespace virtcam
{

template <typename T>
bool update(T& f_a, T f_b)
{
  if (f_a != f_b)
  {
    f_a = f_b;
    return true;
  }
  return false;
}

//!
//! CarCenter
//!
class CarCenter : public pc::util::coding::ISerializable
{
public:

  CarCenter()
    : m_carCenterHori(0.0f, 0.0f, 1.0f),
      m_carCenterVert(0.0f, 0.0f, 1.0f)
  {
  }

  SERIALIZABLE(CarCenter)
  {
    ADD_MEMBER(osg::Vec3f, carCenterHori);
    ADD_MEMBER(osg::Vec3f, carCenterVert);
  }
  osg::Vec3f m_carCenterHori;
  osg::Vec3f m_carCenterVert;

};

extern pc::util::coding::Item<cc::virtcam::CarCenter> g_carCenter;

//!
//! HeadUnitHemisphereCameraData
//!
class HeadUnitHemisphereCameraData : public pc::util::coding::ISerializable
{
public:

  HeadUnitHemisphereCameraData()
    : m_zoomScale(2u)
    , m_minElevationHu(0.0f)
    , m_maxElevationHu(1.57f)
    , m_zoomfactorHori(0.5f)
    , m_zoomfactorVert(0.2f)
    , m_fullscreenElevation(0.6f)
    , m_normalElevation(0.4f)
    , m_minInputEleVationAngle(26.0f)
    , m_minInputEleVationAngleFullScreen(26.0f)
    , m_zoomL0(1.0f)
    , m_zoomL1(1.0f)
    , m_zoomL2(1.0f)
    , m_zoomL3(1.0f)
    , m_zoomL4(1.0f)
    , m_zoomL5(1.0f)
    , m_zoomL6(1.0f)
    , m_defaultDistanceFactor(0.5f)
  {
  }

  SERIALIZABLE(HeadUnitHemisphereCameraData)
  {
    ADD_UINT32_MEMBER(zoomScale);
    ADD_FLOAT_MEMBER(minElevationHu);
    ADD_FLOAT_MEMBER(maxElevationHu);
    ADD_FLOAT_MEMBER(zoomfactorHori);
    ADD_FLOAT_MEMBER(zoomfactorVert);
    ADD_FLOAT_MEMBER(fullscreenElevation);
    ADD_FLOAT_MEMBER(normalElevation);
    ADD_FLOAT_MEMBER(rotateSpeed);
    ADD_FLOAT_MEMBER(minInputEleVationAngle);
    ADD_FLOAT_MEMBER(minInputEleVationAngleFullScreen);
    ADD_FLOAT_MEMBER(zoomL0);
    ADD_FLOAT_MEMBER(zoomL1);
    ADD_FLOAT_MEMBER(zoomL2);
    ADD_FLOAT_MEMBER(zoomL3);
    ADD_FLOAT_MEMBER(zoomL4);
    ADD_FLOAT_MEMBER(zoomL5);
    ADD_FLOAT_MEMBER(zoomL6);
    ADD_FLOAT_MEMBER(defaultDistanceFactor);
  }

  vfc::uint32_t m_zoomScale;
  vfc::float32_t m_minElevationHu;
  vfc::float32_t m_maxElevationHu;
  vfc::float32_t m_zoomfactorHori;
  vfc::float32_t m_zoomfactorVert;
  vfc::float32_t m_fullscreenElevation;
  vfc::float32_t m_normalElevation;
  vfc::float32_t m_rotateSpeed{0.0038f}; // percentage of progress per frame
  vfc::float32_t m_minInputEleVationAngle;
  vfc::float32_t m_minInputEleVationAngleFullScreen;
  vfc::float32_t m_zoomL0;
  vfc::float32_t m_zoomL1;
  vfc::float32_t m_zoomL2;
  vfc::float32_t m_zoomL3;
  vfc::float32_t m_zoomL4;
  vfc::float32_t m_zoomL5;
  vfc::float32_t m_zoomL6;
  vfc::float32_t m_defaultDistanceFactor;
};

extern pc::util::coding::Item<HeadUnitHemisphereCameraData> g_huhemisPara;

/**
 * \brief Reads HU daddy messages and submits them to the camera update callback
 * Implemented as update callback for now, but we should add the ability to register daddy update callbacks
 * with the framework for once-a-frame updates.
 */
class HeadUnitHemisphereCameraUpdater : public cc::virtcam::HemisphereCameraUpdater
{
public:

  HeadUnitHemisphereCameraUpdater(pc::core::Framework* f_framwork);

  virtual void updateCamera(osg::Camera* f_camera, const osg::FrameStamp* f_frameStamp);

  virtual void reset();

  virtual osg::Matrix getViewMatrix(vfc::float32_t f_elevation, vfc::float32_t f_azimuth, vfc::float32_t f_distance, const osg::Vec3f& f_hemisphereCenter);

  void calculateFunctionParams();

  void setToCamera(const osg::Camera* f_cam);

  void addDebugLogSIL(osg::Camera* f_camera);

  void setSuspended(bool f_suspended)
  {
    m_suspended = f_suspended;
  }

  bool isSuspended()
  {
    return m_suspended;
  }

  void enableReturnChannelDefaultValue(osg::Matrix f_viewMatrix);

  void enableReturnChannelDefaultValue(vfc::int32_t f_axis1, vfc::int32_t f_axis2, vfc::int32_t f_zoom)
  {
    m_returnChannelUpdateCallback->enableDefaultValue(f_axis1, f_axis2, f_zoom);
  }

  void disableReturnChannelDefaultValue()
  {
    m_returnChannelUpdateCallback->disableDefaultValue();
  }

  void cancelRotateMode()
  {
    m_cancelRotateModeRq = true;
  }

  //! Attaches an update callback which encodes position of given camera and sends it back to the HU
  void attachReturnChannelUpdateCallback(osg::Camera* f_camera, osg::Node* f_scene) const;

  virtual void enable();

protected:
  ~HeadUnitHemisphereCameraUpdater() noexcept;

protected:
  static const vfc::int32_t ZOOM_RANGE = 63; // [0,62]
  static const vfc::int32_t AXIS1_RANGE = 1800; // [0,1799]
  static const vfc::int32_t AXIS2_RANGE = 1800;

  void quantizeAxisParameters(vfc::float32_t f_elevation, vfc::float32_t f_azimuth, vfc::int32_t& f_axis1, vfc::int32_t& f_axis2) const;

  void dequantizeAxisParameters(vfc::int32_t f_axis1, vfc::int32_t f_axis2, vfc::float32_t& f_elevation, vfc::float32_t& f_azimuth) const;

  void quantizeZoomParameter(vfc::float32_t f_contzoom, vfc::int32_t& f_zoom) const;

  void dequantizeZoomParameter(vfc::int32_t f_zoom, vfc::float32_t& f_contzoom) const
  {
    const vfc::float32_t d = 1.0f/(static_cast<vfc::float32_t>(ZOOM_RANGE)-2.0f);
    f_contzoom = osg::clampTo((static_cast<vfc::float32_t>(f_zoom)-0.5f)*d, 0.0f, 1.0f);
  }

  void quantizeParameters(vfc::float32_t f_elevation, vfc::float32_t f_azimuth, vfc::float32_t f_contzoom, vfc::int32_t& f_axis1, vfc::int32_t& f_axis2, vfc::int32_t& f_zoom) const
  {
    quantizeAxisParameters(f_elevation, f_azimuth, f_axis1, f_axis2);
    quantizeZoomParameter(f_contzoom, f_zoom);
  }

  void dequantizeParameters(vfc::int32_t f_axis1, vfc::int32_t f_axis2, vfc::int32_t f_zoom, vfc::float32_t& f_elevation, vfc::float32_t& f_azimuth, vfc::float32_t& f_contzoom) const
  {
    dequantizeAxisParameters(f_axis1, f_axis2, f_elevation, f_azimuth);
    dequantizeZoomParameter(f_zoom, f_contzoom);
  }

private:
  class ReturnChannel : public osg::NodeCallback
  {
  public:
    ReturnChannel(const HeadUnitHemisphereCameraUpdater* f_updater)
    : m_hemisphereUpdater(f_updater)
    {
      m_returnDefaultValues = false;
      m_defaultAxis1 = 200;
      m_defaultAxis2 = 0;
      m_defaultZoom = 0;
    }

    void setScene(osg::Node* f_scene) { m_sceneNode = f_scene; }

    void disableDefaultValue() { m_returnDefaultValues = false; }

    void enableDefaultValue(vfc::int32_t f_axis1, vfc::int32_t f_axis2, vfc::int32_t f_zoom)
    {
      m_returnDefaultValues = true;
      m_defaultAxis1 = f_axis1;
      m_defaultAxis2 = f_axis2;
      m_defaultZoom = f_zoom;
    }

  protected:
    virtual void operator()(osg::Node* f_node, osg::NodeVisitor* f_nv);

  protected:
    bool m_returnDefaultValues;
    vfc::int32_t m_defaultAxis1, m_defaultAxis2, m_defaultZoom;
    osg::ref_ptr<osg::Node> m_sceneNode;
    osg::observer_ptr<const HeadUnitHemisphereCameraUpdater> m_hemisphereUpdater;
  };

  osg::ref_ptr<ReturnChannel> m_returnChannelUpdateCallback;

  bool updateDaddyInputs();

  bool isPenMovingOnScreen(vfc::uint16_t f_huX, vfc::uint16_t f_huY, vfc::int32_t& f_camPosAxis1, vfc::int32_t& f_camPosAxis2);

  void getHUCoordinatesToAxis(const vfc::uint32_t f_huX, const vfc::uint32_t f_huY, vfc::int32_t& f_camPosAxis1, vfc::int32_t& f_camPosAxis2);

  bool isInsideTouchArea(const vfc::uint32_t& f_huX, const vfc::uint32_t& f_huY);

  // void getPreviewidToAxis(const EScreenID f_previewid);

  void updateHorizontalVerticalCoordinates(vfc::uint32_t& f_huX, vfc::uint32_t& f_huY);

  void retrieveZoomReferenceDistance(const osg::Camera* f_camera);

  void onRotateMode(osg::Camera* f_camera, const osg::FrameStamp* f_frameStamp);

private:
  pc::core::Framework* m_framework;

  bool m_rotateMode{false};
  bool m_suspended;
  bool m_updateOnce;
  bool m_cancelRotateModeRq{false};
  bool m_rotateLeft;
  vfc::int32_t m_setSurroundAngle = 0;
  vfc::float32_t m_rotateProgress = 1.0f;
  vfc::float32_t m_referenceDistance = 0.67f;
  vfc::int32_t m_angleRotate;
  vfc::int32_t m_camPosAxis1Rq;
  vfc::int32_t m_camPosAxis2Rq;
  vfc::int32_t m_camPosAxis1RqPre;
  vfc::int32_t m_camPosAxis2RqPre;
  vfc::uint32_t m_zoomFactorRq;
  vfc::uint32_t m_zoomFactorIpc;
  vfc::float32_t m_functionM;
  vfc::float32_t m_functionA;
  vfc::uint32_t m_zoomFactor;
  vfc::float32_t m_zoomLevel;
  vfc::int32_t m_axis1Range;
  vfc::int32_t m_axis2Range;
  vfc::float32_t m_touchWidth;
  vfc::float32_t m_touchHeight;
  vfc::uint32_t m_movingStartX;
  vfc::uint32_t m_movingStartY;
  bool m_moving;
  vfc::int32_t m_dragStartCamPosX;
  vfc::int32_t m_dragStartCamPosY;
  EScreenID m_curviewid;
  vfc::uint32_t m_touchEvent;
  bool m_freeviewSt;
  bool m_isFreeModeSt;
  bool m_isFreeModeStPre;

  vfc::uint32_t m_asix1Offset;
  bool m_horizontalPad;
  pc::core::Viewport m_horiViewport;
  pc::core::Viewport m_vertViewport;
  ::daddy::TLatestReceiverPort<cc::daddy::SurroundViewRotateAngle_t> m_SurroundViewRotateAngle_ReceiverPort;
  ::daddy::TLatestReceiverPort< cc::daddy::HUFreemodeAngleDaddy_t >  m_HUfreemodeAngleAzimuthReceiver;
  ::daddy::TLatestReceiverPort < cc::daddy::HUFreemodeAngleDaddy_t >           m_HUfreemodeAngleElevationReceiver;
};



} // namespace virtcam
} // namespace cc

#endif

