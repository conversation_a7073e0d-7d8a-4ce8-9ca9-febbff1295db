/// @copyright (C) 2025 Robert <PERSON> GmbH.
/// The reproduction, distribution and utilization of this file as well as the
/// communication of its contents to others without express authorization is
/// prohibited. Offenders will be held liable for the payment of damages. All
/// rights reserved in the event of the grant of a patent, utility model or
/// design.
/// @file

#ifndef RBP_VIS_IMP_TEMPORAL_NOISE_FILTER_HPP
#define RBP_VIS_IMP_TEMPORAL_NOISE_FILTER_HPP

#include "pc/svs/imp/tnf/inc/temporal_noise_filter_data.hpp"

#include "pc/svs/texfloor/core/inc/TextureDisplayCamera.h"
#include "pc/svs/core/inc/Framework.h"
#include "pc/svs/core/inc/VideoTexture.h"
#include "pc/svs/util/osgx/inc/RenderToTextureCamera.h"

#include "osg/Camera"
#include "osg/GLExtensions"

namespace rbp
{
namespace vis
{
namespace imp
{

namespace tnf_test
{
class TemporalNoiseFilterFixture;
} // namespace tnf_test

namespace tnf
{

class RenderToTextureCameraPingPong : public osg::Camera
{
public:
    class FBOBindDrawCallbackTnf : public osg::Camera::DrawCallback
    {
    public:
        FBOBindDrawCallbackTnf() = default;
        FBOBindDrawCallbackTnf(
            const FBOBindDrawCallbackTnf& f_other,
            const osg::CopyOp&            f_copyOp = osg::CopyOp::SHALLOW_COPY);

        OSG_EXT_META_Object(pc::util::osgx, FBOBindDrawCallbackTnf);

        void operator()(osg::RenderInfo& f_renderInfo) const override;

    protected:
        ~FBOBindDrawCallbackTnf() override = default;

    private:
        FBOBindDrawCallbackTnf& operator=(const FBOBindDrawCallbackTnf&) = delete;
    };

    class FBOUnbindDrawCallbackTnf : public osg::Camera::DrawCallback
    {
    public:
        explicit FBOUnbindDrawCallbackTnf(pc::util::osgx::DiscardFramebuffer* f_discardFramebuffer = nullptr);
        FBOUnbindDrawCallbackTnf(
            const FBOUnbindDrawCallbackTnf& f_other,
            const osg::CopyOp&              f_copyOp = osg::CopyOp::SHALLOW_COPY);

        OSG_EXT_META_Object(pc::util::osgx, FBOUnbindDrawCallbackTnf);

        void operator()(osg::RenderInfo& f_renderInfo) const override;

        pc::util::osgx::DiscardFramebuffer* getDiscardFrameBuffer() const
        {
            return m_discardFramebuffer.get();
        }

        void setDiscardFramebuffer(pc::util::osgx::DiscardFramebuffer* f_discardFramebuffer)
        {
            m_discardFramebuffer = f_discardFramebuffer;
        }

    protected:
        ~FBOUnbindDrawCallbackTnf() override = default;

    private:
        FBOUnbindDrawCallbackTnf& operator=(const FBOUnbindDrawCallbackTnf&) = delete;

        osg::ref_ptr<pc::util::osgx::DiscardFramebuffer> m_discardFramebuffer;
    };

    RenderToTextureCameraPingPong(
        const pc::core::TnfTextures& f_textures,
        vfc::uint32_t                f_camIndex,
        std::size_t&                 f_bufferIndex);

    inline void toggleBufferIndex()
    {
        m_bufferIndex = 1 - m_bufferIndex;
    }

    inline std::size_t getBufferIndex() const
    {
        return m_bufferIndex;
    }

    osg::StateAttribute* getInputTexture() const
    {
        return m_textures[1U - m_bufferIndex]->getCameraTexture(m_camIndex);
    }

    osg::StateAttribute* getOutputTexture() const
    {
        return m_textures[m_bufferIndex]->getCameraTexture(m_camIndex);
    }

    osg::StateAttribute* getTnfTexture(std::size_t f_bufferIndex) const
    {
        return m_textures[f_bufferIndex]->getCameraTexture(m_camIndex);
    }

    void setFrameBufferId(GLuint fboId, std::size_t f_bufferIndex)
    {
        m_fboIds[f_bufferIndex] = fboId;
    }

    GLuint getFrameBufferId(std::size_t f_bufferIndex) const
    {
        return m_fboIds[f_bufferIndex];
    }

    osg::Texture2D* getMotionMap() const
    {
        return m_motionMap.get();
    }

    void setMotionMap(osg::ref_ptr<osg::Texture2D> f_motionMap)
    {
        m_motionMap = f_motionMap;
    }

    void setInitializeFBOs(bool f_initialize)
    {
        m_initializeFBOs = f_initialize;
    }

    bool getInitializeFBOs() const
    {
        return m_initializeFBOs;
    }

    vfc::uint32_t getCamIndex() const
    {
        return m_camIndex;
    }

private:
    pc::core::TnfTextures        m_textures;
    vfc::uint32_t                m_camIndex;
    std::size_t&                 m_bufferIndex;
    std::array<GLuint, NUM_FBOS> m_fboIds;
    osg::ref_ptr<osg::Texture2D> m_motionMap;
    bool                         m_initializeFBOs;
};

class UpdateAndSwapBuffersCallback : public osg::NodeCallback
{
public:
    explicit UpdateAndSwapBuffersCallback(RenderToTextureCameraPingPong* f_camera)
        : osg::Object{}
        , osg::NodeCallback{}
        , m_camera{f_camera}
    {
    }

    void operator()(osg::Node* f_node, osg::NodeVisitor* f_nv) override;

protected:
    ~UpdateAndSwapBuffersCallback() override = default;

private:
    UpdateAndSwapBuffersCallback& operator=(const UpdateAndSwapBuffersCallback&) = delete;
    UpdateAndSwapBuffersCallback(const UpdateAndSwapBuffersCallback&)            = delete;

    osg::ref_ptr<RenderToTextureCameraPingPong> m_camera;
    bool                                        m_initialize{true};
};

class TemporalNoiseFilter : public osg::Group
{
public:
    TemporalNoiseFilter(pc::core::Framework* f_pFramework, vfc::uint32_t f_camIndex);
    OSG_EXT_META_Node(rbp::vis::imp::tnf, TemporalNoiseFilter);

    void initialize();
    void traverse(osg::NodeVisitor& f_nv) override;

protected:
    ~TemporalNoiseFilter() override;

private:
    TemporalNoiseFilter() = default;
    TemporalNoiseFilter(const TemporalNoiseFilter& f_other, const osg::CopyOp& f_copyOp = osg::CopyOp::SHALLOW_COPY);
    TemporalNoiseFilter(const TemporalNoiseFilter&)            = delete;
    TemporalNoiseFilter(TemporalNoiseFilter&&)                 = delete;
    TemporalNoiseFilter& operator=(const TemporalNoiseFilter&) = delete;
    TemporalNoiseFilter& operator=(TemporalNoiseFilter&&)      = delete;

    void setDebugTextureOutput(bool f_enabled);

    pc::core::Framework*                                   m_framework;
    vfc::uint32_t                                          m_camIndex;
    osg::ref_ptr<RenderToTextureCameraPingPong>            m_tnfCamera;
    bool                                                   m_initialized;
    osg::ref_ptr<pc::texfloor::core::TextureDisplayCamera> m_debugView;

    // The utf fixture class is a friend to be able to access private members
    // qacpp-2107-R1: Friend declaration for testing purposes.
    friend class tnf_test::TemporalNoiseFilterFixture; // PRQA S 2107 # R1
};

} // namespace tnf
} // namespace imp
} // namespace vis
} // namespace rbp

#endif // RBP_VIS_IMP_TEMPORAL_NOISE_FILTER_HPP
