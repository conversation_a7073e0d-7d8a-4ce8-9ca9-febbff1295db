/// @copyright (C) 2025 Robert <PERSON> GmbH.
/// The reproduction, distribution and utilization of this file as well as the
/// communication of its contents to others without express authorization is
/// prohibited. Offenders will be held liable for the payment of damages. All
/// rights reserved in the event of the grant of a patent, utility model or
/// design.
/// @file

// #define DEBUG_WRITE_FILTERED_TEXTURE
#define DEBUG_VIEW_OUTPUT_FRAMEBUFFER

#include "pc/svs/imp/tnf/inc/temporal_noise_filter.hpp"

#include "pc/generic/util/logging/inc/LoggingContexts.h"
#include "pc/svs/core/inc/ShaderManager.h"
#include "pc/svs/core/inc/VideoTexture.h"
#include "pc/svs/util/osgx/inc/Utils.h"

#include "vfc/core/vfc_util.hpp"

#include "osgDB/WriteFile"
#include "osg/Camera"
#include "osg/Image"
#include "osg/Texture2D"

namespace rbp
{
namespace vis
{
namespace imp
{
namespace tnf
{
using pc::util::logging::g_AppContext;
using pc::util::osgx::DiscardFramebuffer;
using pc::util::osgx::FrameBufferObjectGLExtensions;

static void updateUniforms(osg::StateSet* f_stateSet, vfc::uint32_t f_camId)
{
    vfc::nop(f_stateSet->getOrCreateUniform("u_cam", osg::Uniform::INT)->set(static_cast<int>(f_camId)));

    if (pc::core::g_systemConf->m_isCameraCombine)
    {
        vfc::nop(f_stateSet->getOrCreateUniform("u_isTexCombine", osg::Uniform::INT)->set(1));
    }
    else
    {
        vfc::nop(f_stateSet->getOrCreateUniform("u_isTexCombine", osg::Uniform::INT)->set(0));
    }

    vfc::nop(f_stateSet->getOrCreateUniform("u_filteredImageWeight", osg::Uniform::FLOAT)
                          ->set(g_temporalNoiseFilterData->m_filteredImageWeight));
    vfc::nop(
        f_stateSet->getOrCreateUniform("u_threshold", osg::Uniform::FLOAT)
            ->set(static_cast<vfc::float32_t>(g_temporalNoiseFilterData->m_motionDetectionThreshold) / 255.F));
    vfc::nop(f_stateSet->getOrCreateUniform("u_setToPassThrough", osg::Uniform::BOOL)
                          ->set(g_temporalNoiseFilterData->m_setToPassThrough));
    vfc::nop(f_stateSet->getOrCreateUniform("u_combineMotionMap", osg::Uniform::BOOL)
                          ->set(g_temporalNoiseFilterData->m_combineMotionMap));
    vfc::nop(f_stateSet->getOrCreateUniform("u_outputMotionMap", osg::Uniform::BOOL)
                          ->set(g_temporalNoiseFilterData->m_outputMotionMap));
}

struct TnfShaderAssignment
{
    static void assign(osg::StateSet* f_stateSet, osg::Program* f_program)
    {
        f_stateSet->setAttribute(f_program);
        vfc::nop(f_stateSet->getOrCreateUniform("u_texCurrent", osg::Uniform::SAMPLER_2D)->set(0));
        vfc::nop(f_stateSet->getOrCreateUniform("u_texPreviousY", osg::Uniform::SAMPLER_2D)->set(1));
    };
};

namespace
{

void setupPingPongCamera(RenderToTextureCameraPingPong* f_rttCam, osg::State& f_state)
{
    const vfc::uint32_t l_contextId = f_state.getContextID();
    const auto          l_fboExt    = FrameBufferObjectGLExtensions::getInstance(l_contextId);

    const bool l_fboSupported = (nullptr != l_fboExt) && (l_fboExt->isFrameBufferObjectSupported());
    if (!l_fboSupported)
    {
        return;
    }

    // create FBOs
    for (vfc::uint32_t i = 0U; i < NUM_FBOS; ++i)
    {
        GLuint l_fboId{0U};
        l_fboExt->m_glGenFramebuffers(1, &l_fboId);
        if (0U == l_fboId)
        {
            XLOG_ERROR(pc::util::logging::g_AppContext, "setupPingPongCamera: FBO creation failed");
            continue;
        }

        l_fboExt->m_glBindFramebuffer(GL_FRAMEBUFFER_EXT, l_fboId);

        // Attach the main texture
        osg::StateAttribute* const l_texture = f_rttCam->getTnfTexture(i);
        auto l_textureObject                 = dynamic_cast<osg::Texture*>(l_texture)->getTextureObject(l_contextId);
        if ((nullptr == l_textureObject) || (0 == l_textureObject->id()))
        {
            dynamic_cast<osg::Texture*>(l_texture)->compileGLObjects(f_state);
            l_textureObject = dynamic_cast<osg::Texture*>(l_texture)->getTextureObject(l_contextId);
        }
        const GLenum l_textureId{(nullptr != l_textureObject) ? l_textureObject->id() : 0};
        l_fboExt->m_glFramebufferTexture2D(GL_FRAMEBUFFER_EXT, GL_COLOR_ATTACHMENT0_EXT, GL_TEXTURE_2D, l_textureId, 0);

        // Attach the debug texture if available
        osg::Texture2D* const l_debugTexture = f_rttCam->getMotionMap();
        if (nullptr != l_debugTexture)
        {
            auto l_debugTextureObject = l_debugTexture->getTextureObject(l_contextId);
            if ((nullptr == l_debugTextureObject) || (0 == l_debugTextureObject->id()))
            {
                l_debugTexture->compileGLObjects(f_state);
                l_debugTextureObject = l_debugTexture->getTextureObject(l_contextId);
            }
            const GLenum l_debugTextureId{(nullptr != l_debugTextureObject) ? l_debugTextureObject->id() : 0};
            l_fboExt->m_glFramebufferTexture2D(
                GL_FRAMEBUFFER_EXT, GL_COLOR_ATTACHMENT1_EXT, GL_TEXTURE_2D, l_debugTextureId, 0);

            // Set the draw buffers
            constexpr GLenum l_drawBuffers[2] = {GL_COLOR_ATTACHMENT0_EXT, GL_COLOR_ATTACHMENT1_EXT};
            l_fboExt->m_glDrawBuffers(2, l_drawBuffers);
        }
        else
        {
            // Set the draw buffer for the main texture only
            constexpr GLenum l_drawBuffers[1] = {GL_COLOR_ATTACHMENT0_EXT};
            l_fboExt->m_glDrawBuffers(1, l_drawBuffers);
        }

        const GLenum l_framebufferStatus{l_fboExt->m_glCheckFramebufferStatus(GL_FRAMEBUFFER_EXT)};
        if (GL_FRAMEBUFFER_COMPLETE_EXT != l_framebufferStatus)
        {
            XLOG_ERROR(
                pc::util::logging::g_AppContext,
                "setupPingPongCamera: Framebuffer completion issue" << l_framebufferStatus);
        }
        else
        {
            XLOG_INFO(pc::util::logging::g_AppContext, "setupPingPongCamera: Framebuffer complete\n");
        }

        f_rttCam->setFrameBufferId(l_fboId, i);
    }
}

osg::Geode* createTexturedQuad(osg::StateAttribute* f_textureCurrent)
{
    osg::ref_ptr<osg::Geode>          l_geode = osg_ext::make_ref<osg::Geode>();
    const osg::ref_ptr<osg::Geometry> l_quad  = osg::createTexturedQuadGeometry(
        osg::Vec3(0.0F, 0.0F, 0.0F), osg::Vec3(1.0F, 0.0F, 0.0F), osg::Vec3(0.0F, 1.0F, 0.0F), 0.0F, 0.0F, 1.0F, 1.0F);
    vfc::nop(l_geode->addDrawable(l_quad));
    pc::core::TemplateShaderProgramDescriptor<TnfShaderAssignment> l_tnfTexShader("tnf");
    const osg::ref_ptr<osg::StateSet>                              l_stateSet = l_geode->getOrCreateStateSet();
    vfc::nop(l_tnfTexShader.apply(l_stateSet));
    l_stateSet->setTextureAttribute(0U, f_textureCurrent, osg::StateAttribute::ON);

    return l_geode.release();
}

// When texture IDs are set in custom engine, they are not available at the time of node construction.
// That is why a callback is used to initialize the TemporalNoiseFilter node later.
class TemporalNoiseFilterInitCallback : public osg::NodeCallback
{
public:
    TemporalNoiseFilterInitCallback() = default;

    void operator()(osg::Node* f_node, osg::NodeVisitor* f_nv) override
    {
        TemporalNoiseFilter* const l_tnfNode = dynamic_cast<TemporalNoiseFilter*>(f_node);
        if (nullptr != l_tnfNode)
        {
            l_tnfNode->initialize();
            l_tnfNode->traverse(*f_nv);
        }
    }

protected:
    ~TemporalNoiseFilterInitCallback() override = default;
};

} // namespace

void UpdateAndSwapBuffersCallback::operator()(osg::Node* f_node, osg::NodeVisitor* f_nv)
{
    m_camera->toggleBufferIndex();

    osg::Geode* const l_geode = dynamic_cast<osg::Geode*>(f_node);
    if (nullptr != l_geode)
    {
        osg::StateSet* const l_stateSet = l_geode->getOrCreateStateSet();
        l_stateSet->setTextureAttribute(1U, m_camera->getInputTexture(), osg::StateAttribute::ON);
        updateUniforms(l_stateSet, m_camera->getCamIndex());
        if (m_initialize)
        {
            // Let the TNF run in pass-through mode for the first frame to initialize the previous texture
            vfc::nop(l_stateSet->getOrCreateUniform("u_setToPassThrough", osg::Uniform::BOOL)->set(true));
            m_initialize = false;
        }
    }

    // Continue traversing the scene graph
    traverse(f_node, f_nv);
};

///
/// FBOBindDrawCallbackTnf
///
RenderToTextureCameraPingPong::FBOBindDrawCallbackTnf::FBOBindDrawCallbackTnf(
    const RenderToTextureCameraPingPong::FBOBindDrawCallbackTnf& f_other,
    const osg::CopyOp&                                           f_copyOp)
    : osg::Object{f_other, f_copyOp}
    // osg::Camera::DrawCallback doesn't call the copy constructor of it's base class osg::Object
    , osg::Camera::DrawCallback{f_other, f_copyOp}
{
}

void RenderToTextureCameraPingPong::FBOBindDrawCallbackTnf::operator()(osg::RenderInfo& f_renderInfo) const
{
    auto const l_rttCam = dynamic_cast<RenderToTextureCameraPingPong*>(f_renderInfo.getCurrentCamera());
    if (nullptr != l_rttCam)
    {
        osg::State* const l_state = f_renderInfo.getState();
        if (l_rttCam->getInitializeFBOs())
        {
            setupPingPongCamera(l_rttCam, *l_state);
            l_rttCam->setInitializeFBOs(false);
        }

        const GLuint l_fboId = l_rttCam->getFrameBufferId(l_rttCam->getBufferIndex());
        if (0U != l_fboId)
        {
            auto const l_fboExt = FrameBufferObjectGLExtensions::getInstance(l_state->getContextID());
            l_fboExt->m_glBindFramebuffer(GL_FRAMEBUFFER_EXT, l_fboId);
        }
    }
}

///
/// FBOUnbindDrawCallbackTnf
///
RenderToTextureCameraPingPong::FBOUnbindDrawCallbackTnf::FBOUnbindDrawCallbackTnf(
    DiscardFramebuffer* f_discardFramebuffer)
    : osg::Object{}
    , osg::Camera::DrawCallback{}
    , m_discardFramebuffer{f_discardFramebuffer}
{
}

RenderToTextureCameraPingPong::FBOUnbindDrawCallbackTnf::FBOUnbindDrawCallbackTnf(
    const RenderToTextureCameraPingPong::FBOUnbindDrawCallbackTnf& f_other,
    const osg::CopyOp&                                             f_copyOp)
    : osg::Object{f_other, f_copyOp}
    // osg::Camera::DrawCallback doesn't call the copy constructor of it's base class osg::Object
    , osg::Camera::DrawCallback{f_other, f_copyOp}
    , m_discardFramebuffer{dynamic_cast<DiscardFramebuffer*>(f_copyOp(f_other.m_discardFramebuffer))}
{
}

void RenderToTextureCameraPingPong::FBOUnbindDrawCallbackTnf::operator()(osg::RenderInfo& f_renderInfo) const
{
    auto const l_rttCam = dynamic_cast<RenderToTextureCameraPingPong*>(f_renderInfo.getCurrentCamera());
    if (nullptr != l_rttCam)
    {
        osg::State* const   l_state     = f_renderInfo.getState();
        const vfc::uint32_t l_contextId = l_state->getContextID();

        auto const l_fboExt = FrameBufferObjectGLExtensions::getInstance(l_contextId);

        if (m_discardFramebuffer.valid())
        {
            m_discardFramebuffer->apply(*l_state);
        }

        // unbind FBO
        const GLuint l_fboId =
            (nullptr != l_state->getGraphicsContext()) ? l_state->getGraphicsContext()->getDefaultFboId() : 0U;
        l_fboExt->m_glBindFramebuffer(GL_FRAMEBUFFER_EXT, l_fboId);
    }
}

RenderToTextureCameraPingPong::RenderToTextureCameraPingPong(
    const std::array<osg::ref_ptr<pc::core::VideoTexture>, NUM_FBOS>& f_textures,
    vfc::uint32_t                                                     f_camIndex,
    std::size_t&                                                      f_bufferIndex)
    : osg::Camera{}
    , m_textures{f_textures}
    , m_camIndex{f_camIndex}
    , m_bufferIndex{f_bufferIndex}
    , m_fboIds{0U, 0U}
    , m_motionMap{nullptr}
    , m_initializeFBOs{true}
{
    // Set up the camera to render to texture
    setRenderOrder(osg::Camera::PRE_RENDER);
    setReferenceFrame(osg::Camera::ABSOLUTE_RF);
    setViewMatrix(osg::Matrix::identity());
    setProjectionMatrix(osg::Matrix::ortho2D(0.0, 1.0, 0.0, 1.0));
    setClearColor(osg::Vec4f(0.0F, 0.0F, 0.0F, 0.0F));

    const vfc::int32_t l_width =
        dynamic_cast<osg::Texture*>(m_textures[m_bufferIndex]->getCameraTexture(m_camIndex))->getTextureWidth();
    const vfc::int32_t l_height =
        dynamic_cast<osg::Texture*>(m_textures[m_bufferIndex]->getCameraTexture(m_camIndex))->getTextureHeight();
    setViewport(0, 0, l_width, l_height);

    setPreDrawCallback(new FBOBindDrawCallbackTnf);
    constexpr pc::util::osgx::DiscardFramebuffer::DiscardMask l_discardMask =
        static_cast<pc::util::osgx::DiscardFramebuffer::DiscardMask>(
            pc::util::osgx::DiscardFramebuffer::DiscardFlag::DISCARD_DEPTH);
    setClearMask(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);
    setPostDrawCallback(new FBOUnbindDrawCallbackTnf(new pc::util::osgx::DiscardFramebuffer(l_discardMask)));
}

TemporalNoiseFilter::TemporalNoiseFilter(pc::core::Framework* f_pFramework, vfc::uint32_t f_camIndex)
    : osg::Group{}
    , m_framework{f_pFramework}
    , m_camIndex{f_camIndex}
    , m_tnfCamera{}
    , m_initialized{false}
    , m_debugView{nullptr}
{
    setNumChildrenRequiringUpdateTraversal(getNumChildrenRequiringUpdateTraversal() + 1U);

    setName("TemporalNoiseFilter");

    setUpdateCallback(new TemporalNoiseFilterInitCallback());
}

TemporalNoiseFilter::TemporalNoiseFilter(const TemporalNoiseFilter& f_other, const osg::CopyOp& f_copyOp)
    : osg::Group{f_other, f_copyOp}
    , m_framework{f_other.m_framework}
    , m_camIndex{f_other.m_camIndex}
    , m_tnfCamera{f_other.m_tnfCamera}
    , m_initialized{f_other.m_initialized}
    , m_debugView{f_other.m_debugView}
{
}

void TemporalNoiseFilter::initialize()
{
    if (!m_initialized)
    {
        std::array<osg::ref_ptr<pc::core::VideoTexture>, NUM_FBOS> l_videoTexturesArray =
            m_framework->getVideoTextureFilteredArray();
        assert((nullptr != l_videoTexturesArray[0]) && (nullptr != l_videoTexturesArray[1]));

        osg::StateAttribute* const l_videoTextureCurr = m_framework->getVideoTexture()->getCameraTexture(m_camIndex);
        assert(nullptr != l_videoTextureCurr);

        if ((nullptr != l_videoTexturesArray[0]) && (nullptr != l_videoTexturesArray[1]) &&
            (nullptr != l_videoTextureCurr))
        {
            g_tnfBufferIndex[m_camIndex] = 0U;
            m_tnfCamera                  = osg_ext::make_ref<RenderToTextureCameraPingPong>(
                l_videoTexturesArray, m_camIndex, g_tnfBufferIndex[m_camIndex]);
            const osg::ref_ptr<osg::Geode> l_texturedQuad = createTexturedQuad(l_videoTextureCurr);
            vfc::nop(m_tnfCamera->addChild(l_texturedQuad));
            vfc::nop(osg::Group::addChild(m_tnfCamera));
            l_texturedQuad->setUpdateCallback(new UpdateAndSwapBuffersCallback(m_tnfCamera.get()));
            m_initialized = true;
        }
        else
        {
            XLOG_WARN(g_AppContext, "No textures for framebuffers available for TNF.");
        }
    }
}

void TemporalNoiseFilter::setDebugTextureOutput(bool f_enabled)
{
    if (f_enabled)
    {
        if (nullptr == m_debugView)
        {
            // Initialize motion map texture
            const osg::ref_ptr<osg::Texture2D> l_motionMap = osg_ext::make_ref<osg::Texture2D>();
            const vfc::int32_t l_width = dynamic_cast<osg::Texture*>(m_tnfCamera->getInputTexture())->getTextureWidth();
            const vfc::int32_t l_height =
                dynamic_cast<osg::Texture*>(m_tnfCamera->getInputTexture())->getTextureHeight();
            l_motionMap->setTextureSize(l_width, l_height);
            l_motionMap->setInternalFormat(GL_R8);
            l_motionMap->setSourceFormat(GL_RED);
            l_motionMap->setSourceType(GL_UNSIGNED_BYTE);
            l_motionMap->setFilter(osg::Texture::MIN_FILTER, osg::Texture::NEAREST);
            l_motionMap->setFilter(osg::Texture::MAG_FILTER, osg::Texture::NEAREST);

            m_tnfCamera->setMotionMap(l_motionMap);
            // Set the flag to reinitialize the FBOs
            m_tnfCamera->setInitializeFBOs(true);

            m_debugView                                = osg_ext::make_ref<pc::texfloor::core::TextureDisplayCamera>();
            constexpr vfc::int32_t l_debugOutputWidth  = 400;
            constexpr vfc::int32_t l_debugOutputHeight = 320;
            m_debugView->setViewport(
                70 + static_cast<vfc::int32_t>(m_camIndex) * (l_debugOutputWidth + 5),
                100,
                l_debugOutputWidth,
                l_debugOutputHeight);
            m_debugView->setTexture(m_tnfCamera->getMotionMap());
            vfc::nop(osg::Group::addChild(m_debugView));
        }
    }
    else
    {
        if (nullptr != m_debugView)
        {
            vfc::nop(osg::Group::removeChild(m_debugView));
            m_debugView = nullptr;
            m_tnfCamera->setMotionMap(nullptr); // Detach the motion map texture
            // Set the flag to reinitialize the FBOs
            m_tnfCamera->setInitializeFBOs(true);
        }
    }
}

void TemporalNoiseFilter::traverse(osg::NodeVisitor& f_nv)
{
    if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
    {
        bool l_calibState = true;
        const pc::daddy::CalibrationStsDaddy* l_calibStsReceiverData = m_framework->m_calibStsReceiver.getData();
        if (nullptr != l_calibStsReceiverData)
        {
            l_calibState = l_calibStsReceiverData->m_Data;
        }
        if (pc::daddy::BaseDaddyPorts::sm_tnfSettingsDaddySenderPort.isConnected())
        {
            pc::daddy::TnfSettingsDaddy& l_data = pc::daddy::BaseDaddyPorts::sm_tnfSettingsDaddySenderPort.reserve();
            l_data.m_Data.m_viewSettings[ETnfView::DEFAULT].m_enabled = g_temporalNoiseFilterData->m_enable && l_calibState;
            pc::daddy::BaseDaddyPorts::sm_tnfSettingsDaddySenderPort.deliver();
        }

        setDebugTextureOutput(g_temporalNoiseFilterData->m_outputMotionMap);
    }
    osg::Group::traverse(f_nv);
}

TemporalNoiseFilter::~TemporalNoiseFilter()
{
    // to prevent that texture from frame buffer object is used after destruction of TNF node
    if (pc::daddy::BaseDaddyPorts::sm_tnfSettingsDaddySenderPort.isConnected())
    {
        pc::daddy::TnfSettingsDaddy& l_data = pc::daddy::BaseDaddyPorts::sm_tnfSettingsDaddySenderPort.reserve();
        l_data.m_Data.m_viewSettings[ETnfView::DEFAULT].m_enabled = false;
        pc::daddy::BaseDaddyPorts::sm_tnfSettingsDaddySenderPort.deliver();
    }
}

} // namespace tnf
} // namespace imp
} // namespace vis
} // namespace rbp
