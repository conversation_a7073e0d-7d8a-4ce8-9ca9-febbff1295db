#version 300 es


#define CAM_ON 0
#define CAM_DISABLED 1
#define CAM_OFF 2

#if (SOURCE == 0) || (TARGET == 0)
// hatching is currently not implemented (just constant gray), so no need for hatch setup
// #define ENABLE_HATCH
#endif


out vec2 v_texCam;


#ifdef ENABLE_HATCH
out float v_incrementXY;
#ifdef IS_WALL
out float v_incrementWall;
#endif
#endif

in vec3 osg_Vertex;
in vec2 osg_MultiTexCoord0;
uniform highp mat4 osg_ModelViewProjectionMatrix;

#ifdef ENABLE_CHAMAELEON

uniform vec2 u_cham_direction_left;
uniform vec2 u_cham_direction_right;

uniform mediump vec3 u_cham_origin_left;
uniform mediump vec3 u_cham_origin_right;

out float v_cham_weightLeftRight;

out vec3 v_cham_correctionLeft;
out vec3 v_cham_correctionRight;

uniform sampler2D s_chamGainsAsTexture;
uniform ivec2 u_cham_worldRoiIdx;

float getDistToLine(vec2 pt1, vec2 pt2, vec2 testPt)
{
  vec2 lineDir = pt2 - pt1;
  vec2 perpDir = vec2(lineDir.y, -lineDir.x);
  vec2 dirToPt1 = pt1 - testPt;
  return abs(dot(normalize(perpDir), dirToPt1));
}

float crossVec2(vec2 v, vec2 w)
{
  return (v.x * w.y) - (v.y * w.x);
}

float getColorCorrectionWeight(vec2 f_root0, vec2 f_line0,
                               vec2 f_root1, vec2 f_line1,
                               vec2 f_vertTemp)
{
  float u0 = crossVec2((f_vertTemp - f_root0), f_line0) / (crossVec2(f_line0, f_root1 - f_root0));
  float u1 = crossVec2((f_vertTemp - f_root1), f_line1) / (crossVec2(f_line1, f_root1 - f_root0));

  vec2 intersectionLine0 = f_vertTemp + u0 * (f_root1 - f_root0);
  vec2 intersectionLine1 = f_vertTemp + u1 * (f_root1 - f_root0);

  float distance0 = distance(intersectionLine0, f_vertTemp);
  float distance1 = distance(intersectionLine1, f_vertTemp);
  float distanceAll = distance(intersectionLine0, intersectionLine1);

  float diff = distance1 - min(distanceAll, distance1);

  return clamp(((distance0 - diff) / distanceAll), 0.0, 1.0);
}
#endif // ENABLE_CHAMAELEON

#ifdef ENABLE_SHARPNESS_HARMONIZATION
// currently no special content except OpenGL ES 3 conformant shader
#endif // ENABLE_SHARPNESS_HARMONIZATION

void main()
{
  v_texCam = osg_MultiTexCoord0;

#ifdef ENABLE_HATCH
  v_incrementXY = osg_Vertex.y - osg_Vertex.x;

#ifdef IS_WALL
  vec2 l_V = normalize (osg_Vertex.xy);
  vec2 l_XDirection = vec2 (1, 0);
  vec2 l_XDirectionNormalized = normalize (l_XDirection);
  float l_CosineAngle = dot (l_V, l_XDirectionNormalized);
  float l_Angle = acos (l_CosineAngle);
  v_incrementWall = float (l_Angle) + osg_Vertex.z;
#endif
#endif

#ifdef ENABLE_CHAMAELEON
  v_cham_weightLeftRight = getColorCorrectionWeight(u_cham_origin_left.xy, u_cham_direction_left, u_cham_origin_right.xy, u_cham_direction_right, osg_Vertex.xy);

  v_cham_correctionLeft = texelFetch(s_chamGainsAsTexture, ivec2(u_cham_worldRoiIdx[0],0), 0).rgb + vec3(0.5);
  v_cham_correctionRight = texelFetch(s_chamGainsAsTexture, ivec2(u_cham_worldRoiIdx[1],0), 0).rgb + vec3(0.5);
#endif // ENABLE_CHAMAELEON

  gl_Position = osg_ModelViewProjectionMatrix * vec4(osg_Vertex, 1.0);
}
