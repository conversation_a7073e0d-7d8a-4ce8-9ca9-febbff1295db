/// @copyright (C) 2023 Robert <PERSON>.
/// The reproduction, distribution and utilization of this file as well as the
/// communication of its contents to others without express authorization is
/// prohibited. Offenders will be held liable for the payment of damages. All
/// rights reserved in the event of the grant of a patent, utility model or
/// design.
/// @file

#ifndef CC_VHM_HPP
#define CC_VHM_HPP

#include "pc/generic/core/inc/Threads.h"
#include "pc/generic/util/coding/inc/ISerializable.h"
#include "pc/generic/util/coding/inc/CodingManager.h"
#include "vfc/core/vfc_types.hpp"

namespace cc
{
namespace vhm
{
extern bool g_enableVhm;
class DifFrontWheelRadiiTableSettings : public pc::util::coding::ISerializable
    {
    public:
        DifFrontWheelRadiiTableSettings()
        : m_FrontWheelRadii0(0)
        , m_FrontWheelRadii1(0)
        , m_FrontWheelRadii2(0)
        , m_FrontWheelRadii3(0)
        , m_FrontWheelRadii4(0)
        , m_FrontWheelRadii5(0)
        , m_FrontWheelRadii6(0)
        , m_FrontWheelRadii7(0)
        , m_FrontWheelRadii8(0)
        , m_FrontWheelRadii9(0)
        , m_FrontWheelRadii10(0)
        , m_FrontWheelRadii11(0)
        , m_FrontWheelRadii12(0)
        , m_FrontWheelRadii13(0)
        , m_FrontWheelRadii14(0)
        , m_FrontWheelRadii15(0)
        , m_FrontWheelRadii16(0)
        , m_FrontWheelRadii17(0)
        , m_FrontWheelRadii18(0)
        , m_FrontWheelRadii19(0)
        , m_FrontWheelRadii20(0)
        , m_FrontWheelRadii21(0)
        , m_FrontWheelRadii22(0)
        , m_FrontWheelRadii23(0)
        , m_FrontWheelRadii24(0)
        , m_FrontWheelRadii25(0)
        , m_FrontWheelRadii26(0)
        , m_FrontWheelRadii27(0)
        , m_FrontWheelRadii28(0)
        , m_FrontWheelRadii29(0)
        , m_FrontWheelRadii30(0)
        , m_FrontWheelRadii31(0)
        , m_FrontWheelRadii32(0)
        {

        }
        SERIALIZABLE(DifFrontWheelRadiiTableSettings)
        {
            ADD_INT_MEMBER(FrontWheelRadii0);
            ADD_INT_MEMBER(FrontWheelRadii1);
            ADD_INT_MEMBER(FrontWheelRadii2);
            ADD_INT_MEMBER(FrontWheelRadii3);
            ADD_INT_MEMBER(FrontWheelRadii4);
            ADD_INT_MEMBER(FrontWheelRadii5);
            ADD_INT_MEMBER(FrontWheelRadii6);
            ADD_INT_MEMBER(FrontWheelRadii7);
            ADD_INT_MEMBER(FrontWheelRadii8);
            ADD_INT_MEMBER(FrontWheelRadii9);
            ADD_INT_MEMBER(FrontWheelRadii10);
            ADD_INT_MEMBER(FrontWheelRadii11);
            ADD_INT_MEMBER(FrontWheelRadii12);
            ADD_INT_MEMBER(FrontWheelRadii13);
            ADD_INT_MEMBER(FrontWheelRadii14);
            ADD_INT_MEMBER(FrontWheelRadii15);
            ADD_INT_MEMBER(FrontWheelRadii16);
            ADD_INT_MEMBER(FrontWheelRadii17);
            ADD_INT_MEMBER(FrontWheelRadii18);
            ADD_INT_MEMBER(FrontWheelRadii19);
            ADD_INT_MEMBER(FrontWheelRadii20);
            ADD_INT_MEMBER(FrontWheelRadii21);
            ADD_INT_MEMBER(FrontWheelRadii22);
            ADD_INT_MEMBER(FrontWheelRadii23);
            ADD_INT_MEMBER(FrontWheelRadii24);
            ADD_INT_MEMBER(FrontWheelRadii25);
            ADD_INT_MEMBER(FrontWheelRadii26);
            ADD_INT_MEMBER(FrontWheelRadii27);
            ADD_INT_MEMBER(FrontWheelRadii28);
            ADD_INT_MEMBER(FrontWheelRadii29);
            ADD_INT_MEMBER(FrontWheelRadii30);
            ADD_INT_MEMBER(FrontWheelRadii31);
            ADD_INT_MEMBER(FrontWheelRadii32);
        }

        vfc::int32_t m_FrontWheelRadii0;
        vfc::int32_t m_FrontWheelRadii1;
        vfc::int32_t m_FrontWheelRadii2;
        vfc::int32_t m_FrontWheelRadii3;
        vfc::int32_t m_FrontWheelRadii4;
        vfc::int32_t m_FrontWheelRadii5;
        vfc::int32_t m_FrontWheelRadii6;
        vfc::int32_t m_FrontWheelRadii7;
        vfc::int32_t m_FrontWheelRadii8;
        vfc::int32_t m_FrontWheelRadii9;
        vfc::int32_t m_FrontWheelRadii10;
        vfc::int32_t m_FrontWheelRadii11;
        vfc::int32_t m_FrontWheelRadii12;
        vfc::int32_t m_FrontWheelRadii13;
        vfc::int32_t m_FrontWheelRadii14;
        vfc::int32_t m_FrontWheelRadii15;
        vfc::int32_t m_FrontWheelRadii16;
        vfc::int32_t m_FrontWheelRadii17;
        vfc::int32_t m_FrontWheelRadii18;
        vfc::int32_t m_FrontWheelRadii19;
        vfc::int32_t m_FrontWheelRadii20;
        vfc::int32_t m_FrontWheelRadii21;
        vfc::int32_t m_FrontWheelRadii22;
        vfc::int32_t m_FrontWheelRadii23;
        vfc::int32_t m_FrontWheelRadii24;
        vfc::int32_t m_FrontWheelRadii25;
        vfc::int32_t m_FrontWheelRadii26;
        vfc::int32_t m_FrontWheelRadii27;
        vfc::int32_t m_FrontWheelRadii28;
        vfc::int32_t m_FrontWheelRadii29;
        vfc::int32_t m_FrontWheelRadii30;
        vfc::int32_t m_FrontWheelRadii31;
        vfc::int32_t m_FrontWheelRadii32;
    };

class VhmMechanicalSettingData : public pc::util::coding::ISerializable
{
public:

  VhmMechanicalSettingData()
    : m_oneCyclePulseValue(48)
    , m_WICTeethCount{48}
    , m_WICCycleTime_MS{20}
    , m_wheelRadius(0.4169f)
    , m_pulseMaxValue(1022)
    , m_WICLength_u16(1000)
    , m_MaxWICValue{96}
    , m_SWAMax{12500}
    , m_SteeringRatio{6831}
    , m_MultMonomA_ForwSWA2SA{155}
    , m_MultMonomB_ForwSWA2SA{-282}
    , m_CoeffC_ForwSWA2SA{48835}
    , m_CoeffD_ForwSWA2SA{25198}
    , m_MultMonomA_BackSWA2SA{155}
    , m_MultMonomB_BackSWA2SA{-282}
    , m_CoeffC_BackSWA2SA{48835}
    , m_CoeffD_BackSWA2SA{25198}
    , m_ScalingMonomA_SWA2SA{1}
    , m_ScalingMonomB_SWA2SA{3}
    , m_ScalingMonomCD_SWA2SA{0}
    , m_Tuningfactor{0.0}
    , m_ClippingAngle1_s16(-1114)
    , m_ClippingAngle2_s16(1122)
    , m_CoeffC_BackSA2SWAC_s32(64464)
    , m_CoeffC_BackSA2SWAL_s32(51552)
    , m_CoeffC_BackSA2SWAR_s32(87452)
    , m_CoeffC_ForwSA2SWAC_s32(64464)
    , m_CoeffC_ForwSA2SWAL_s32(67778)
    , m_CoeffC_ForwSA2SWAR_s32(88105)
    , m_CoeffD_BackSA2SWAC_s32(25700)
    , m_CoeffD_BackSA2SWAL_s32(-811352)
    , m_CoeffD_BackSA2SWAR_s32(-1402021)
    , m_CoeffD_ForwSA2SWAC_s32(25700)
    , m_CoeffD_ForwSA2SWAL_s32(282403)
    , m_CoeffD_ForwSA2SWAR_s32(-2241576)
    , m_DrivenAxle_en(2)
    , m_MultMonomA_BackSA2SWAC_s16(-396)
    , m_MultMonomA_BackSA2SWAL_s16(-175)
    , m_MultMonomA_BackSA2SWAR_s16(110)
    , m_MultMonomA_ForwSA2SWAC_s16(-396)
    , m_MultMonomA_ForwSA2SWAL_s16(432)
    , m_MultMonomA_ForwSA2SWAR_s16(110)
    , m_MultMonomB_BackSA2SWAC_s16(836)
    , m_MultMonomB_BackSA2SWAL_s16(-496)
    , m_MultMonomB_BackSA2SWAR_s16(-1005)
    , m_MultMonomB_ForwSA2SWAC_s16(836)
    , m_MultMonomB_ForwSA2SWAL_s16(170)
    , m_MultMonomB_ForwSA2SWAR_s16(-815)
    , m_ScalingMonomA_SA2SWAC_u8(3)
    , m_ScalingMonomA_SA2SWAL_u8(3)
    , m_ScalingMonomA_SA2SWAR_u8(2)
    , m_ScalingMonomB_SA2SWAC_u8(3)
    , m_ScalingMonomB_SA2SWAL_u8(3)
    , m_ScalingMonomB_SA2SWAR_u8(3)
    , m_ScalingMonomCD_SA2SWAC_u8(2)
    , m_ScalingMonomCD_SA2SWAL_u8(2)
    , m_ScalingMonomCD_SA2SWAR_u8(2)
  {
  }

  SERIALIZABLE(VhmMechanicalSettingData)
  {
    ADD_INT_MEMBER(oneCyclePulseValue);
    ADD_INT_MEMBER(WICTeethCount);
    ADD_INT_MEMBER(WICCycleTime_MS);
    ADD_FLOAT_MEMBER(wheelRadius);
    ADD_INT_MEMBER(pulseMaxValue);
    ADD_UINT16_MEMBER(WICLength_u16);
    ADD_INT_MEMBER(MaxWICValue);
    ADD_INT_MEMBER(SWAMax);
    ADD_INT_MEMBER(SteeringRatio);
    ADD_INT_MEMBER(MultMonomA_ForwSWA2SA);
    ADD_INT_MEMBER(MultMonomB_ForwSWA2SA);
    ADD_INT_MEMBER(CoeffC_ForwSWA2SA);
    ADD_INT_MEMBER(CoeffD_ForwSWA2SA);
    ADD_INT_MEMBER(MultMonomA_BackSWA2SA);
    ADD_INT_MEMBER(MultMonomB_BackSWA2SA);
    ADD_INT_MEMBER(CoeffC_BackSWA2SA);
    ADD_INT_MEMBER(CoeffD_BackSWA2SA);
    ADD_INT_MEMBER(ScalingMonomA_SWA2SA);
    ADD_INT_MEMBER(ScalingMonomB_SWA2SA);
    ADD_INT_MEMBER(ScalingMonomCD_SWA2SA);
    ADD_FLOAT_MEMBER(Tuningfactor);
    ADD_INT_MEMBER(ClippingAngle1_s16);
    ADD_INT_MEMBER(ClippingAngle2_s16);
    ADD_INT_MEMBER(CoeffC_BackSA2SWAC_s32);
    ADD_INT_MEMBER(CoeffC_BackSA2SWAL_s32);
    ADD_INT_MEMBER(CoeffC_BackSA2SWAR_s32);
    ADD_INT_MEMBER(CoeffC_ForwSA2SWAC_s32);
    ADD_INT_MEMBER(CoeffC_ForwSA2SWAL_s32);
    ADD_INT_MEMBER(CoeffC_ForwSA2SWAR_s32);
    ADD_INT_MEMBER(CoeffD_BackSA2SWAC_s32);
    ADD_INT_MEMBER(CoeffD_BackSA2SWAL_s32);
    ADD_INT_MEMBER(CoeffD_BackSA2SWAR_s32);
    ADD_INT_MEMBER(CoeffD_ForwSA2SWAC_s32);
    ADD_INT_MEMBER(CoeffD_ForwSA2SWAL_s32);
    ADD_INT_MEMBER(CoeffD_ForwSA2SWAR_s32);
    ADD_MEMBER(DifFrontWheelRadiiTableSettings, DifFrontWheelRadiiTable_pu16);
    ADD_UINT32_MEMBER(DrivenAxle_en);
    ADD_INT_MEMBER(MultMonomA_BackSA2SWAC_s16);
    ADD_INT_MEMBER(MultMonomA_BackSA2SWAL_s16);
    ADD_INT_MEMBER(MultMonomA_BackSA2SWAR_s16);
    ADD_INT_MEMBER(MultMonomA_ForwSA2SWAC_s16);
    ADD_INT_MEMBER(MultMonomA_ForwSA2SWAL_s16);
    ADD_INT_MEMBER(MultMonomA_ForwSA2SWAR_s16);
    ADD_INT_MEMBER(MultMonomB_BackSA2SWAC_s16);
    ADD_INT_MEMBER(MultMonomB_BackSA2SWAL_s16);
    ADD_INT_MEMBER(MultMonomB_BackSA2SWAR_s16);
    ADD_INT_MEMBER(MultMonomB_ForwSA2SWAC_s16);
    ADD_INT_MEMBER(MultMonomB_ForwSA2SWAL_s16);
    ADD_INT_MEMBER(MultMonomB_ForwSA2SWAR_s16);
    ADD_INT_MEMBER(ScalingMonomA_SA2SWAC_u8);
    ADD_INT_MEMBER(ScalingMonomA_SA2SWAL_u8);
    ADD_INT_MEMBER(ScalingMonomA_SA2SWAR_u8);
    ADD_INT_MEMBER(ScalingMonomB_SA2SWAC_u8);
    ADD_INT_MEMBER(ScalingMonomB_SA2SWAL_u8);
    ADD_INT_MEMBER(ScalingMonomB_SA2SWAR_u8);
    ADD_INT_MEMBER(ScalingMonomCD_SA2SWAC_u8);
    ADD_INT_MEMBER(ScalingMonomCD_SA2SWAL_u8);
    ADD_INT_MEMBER(ScalingMonomCD_SA2SWAR_u8);
  }

  vfc::int32_t m_oneCyclePulseValue;  //A circle wic number
  vfc::int32_t m_WICTeethCount;
  vfc::int32_t m_WICCycleTime_MS;
  vfc::float32_t m_wheelRadius;
  vfc::int32_t m_pulseMaxValue;
  vfc::uint16_t m_WICLength_u16;
  vfc::int32_t m_MaxWICValue;
  vfc::int32_t m_SWAMax;
  vfc::int32_t m_SteeringRatio;
  vfc::int32_t m_MultMonomA_ForwSWA2SA;
  vfc::int32_t m_MultMonomB_ForwSWA2SA;
  vfc::int32_t m_CoeffC_ForwSWA2SA;
  vfc::int32_t m_CoeffD_ForwSWA2SA;
  vfc::int32_t m_MultMonomA_BackSWA2SA;
  vfc::int32_t m_MultMonomB_BackSWA2SA;
  vfc::int32_t m_CoeffC_BackSWA2SA;
  vfc::int32_t m_CoeffD_BackSWA2SA;
  vfc::int32_t m_ScalingMonomA_SWA2SA;
  vfc::int32_t m_ScalingMonomB_SWA2SA;
  vfc::int32_t m_ScalingMonomCD_SWA2SA;
  vfc::float32_t m_Tuningfactor;
  vfc::int32_t m_ClippingAngle1_s16;
  vfc::int32_t m_ClippingAngle2_s16;
  vfc::int32_t m_CoeffC_BackSA2SWAC_s32;
  vfc::int32_t m_CoeffC_BackSA2SWAL_s32;
  vfc::int32_t m_CoeffC_BackSA2SWAR_s32;
  vfc::int32_t m_CoeffC_ForwSA2SWAC_s32;
  vfc::int32_t m_CoeffC_ForwSA2SWAL_s32;
  vfc::int32_t m_CoeffC_ForwSA2SWAR_s32;
  vfc::int32_t m_CoeffD_BackSA2SWAC_s32;
  vfc::int32_t m_CoeffD_BackSA2SWAL_s32;
  vfc::int32_t m_CoeffD_BackSA2SWAR_s32;
  vfc::int32_t m_CoeffD_ForwSA2SWAC_s32;
  vfc::int32_t m_CoeffD_ForwSA2SWAL_s32;
  vfc::int32_t m_CoeffD_ForwSA2SWAR_s32;
  DifFrontWheelRadiiTableSettings m_DifFrontWheelRadiiTable_pu16;
  vfc::uint32_t m_DrivenAxle_en;
  vfc::int32_t m_MultMonomA_BackSA2SWAC_s16;
  vfc::int32_t m_MultMonomA_BackSA2SWAL_s16;
  vfc::int32_t  m_MultMonomA_BackSA2SWAR_s16;
  vfc::int32_t  m_MultMonomA_ForwSA2SWAC_s16;
  vfc::int32_t  m_MultMonomA_ForwSA2SWAL_s16;
  vfc::int32_t  m_MultMonomA_ForwSA2SWAR_s16;
  vfc::int32_t  m_MultMonomB_BackSA2SWAC_s16;
  vfc::int32_t  m_MultMonomB_BackSA2SWAL_s16;
  vfc::int32_t  m_MultMonomB_BackSA2SWAR_s16;
  vfc::int32_t  m_MultMonomB_ForwSA2SWAC_s16;
  vfc::int32_t  m_MultMonomB_ForwSA2SWAL_s16;
  vfc::int32_t  m_MultMonomB_ForwSA2SWAR_s16;
  vfc::int32_t   m_ScalingMonomA_SA2SWAC_u8;
  vfc::int32_t   m_ScalingMonomA_SA2SWAL_u8;
  vfc::int32_t   m_ScalingMonomA_SA2SWAR_u8;
  vfc::int32_t   m_ScalingMonomB_SA2SWAC_u8;
  vfc::int32_t   m_ScalingMonomB_SA2SWAL_u8;
  vfc::int32_t   m_ScalingMonomB_SA2SWAR_u8;
  vfc::int32_t   m_ScalingMonomCD_SA2SWAC_u8;
  vfc::int32_t   m_ScalingMonomCD_SA2SWAL_u8;
  vfc::int32_t   m_ScalingMonomCD_SA2SWAR_u8;
};

extern pc::util::coding::Item<VhmMechanicalSettingData> g_vhmConfig;

///
/// @brief VhmManager
///
class VhmManager : public pc::core::ICyclicRunnable
{
  public:
    VhmManager();

    void OnInit() override;

    void OnIterate() override;

    void OnShutdown() override;

  private:
};

} // namespace fdbus
} // namespace cc

#endif // CC_VHM_HPP
