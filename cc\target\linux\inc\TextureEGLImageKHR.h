//-------------------------------------------------------------------------------
// Copyright (c) 2017 by <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#ifndef SV3D_TEXTUREEGLIMAGEKHR_H
#define SV3D_TEXTUREEGLIMAGEKHR_H


#include <EGL/eglplatform.h>
#include <EGL/egl.h>
#ifndef EGL_EGLEXT_PROTOTYPES
#define EGL_EGLEXT_PROTOTYPES
#endif // EGL_EGLEXT_PROTOTYPES
#include <EGL/eglext.h>

#ifndef GL_GLEXT_PROTOTYPES
#define GL_GLEXT_PROTOTYPES
#endif // GL_GLEXT_PROTOTYPES
#include <GLES2/gl2.h>  /* use OpenGL ES 2.x */
#include <GLES2/gl2ext.h>  /* use OpenGL ES 2.x extension */

#include <osg/StateAttribute>
#include <osg/Texture>

namespace pc
{
namespace target
{
namespace qualcomm
{

/**
 * \brief Implementation of an osg::StateAttribute to implement textures backed by EGLImage.
 *
 * Limitations:
 * - supports only single GL context
 * - GL texture objects will never be released, so be careful about TextureEGLImageKHR
 *   lifecycle - i.e. update rather than recreate
 */
class TextureEGLImageKHR: public osg::StateAttribute
{
public:
  TextureEGLImageKHR();

  //! Construct texture from given EGL image handle
  TextureEGLImageKHR(EGLImageKHR f_eglImage, osg::Texture::FilterMode f_minificationFilter = osg::Texture::NEAREST, osg::Texture::FilterMode f_magnificationFilter = osg::Texture::LINEAR);
  TextureEGLImageKHR(const TextureEGLImageKHR& f_other, const osg::CopyOp& f_copyop);

  META_StateAttribute(sv3d, TextureEGLImageKHR, TEXTURE);

  virtual void apply(osg::State& state) const override;

  //must be called in render thread
  virtual int deleteGLTexture();

  virtual bool isTextureAttribute() const override;

  virtual int compare(const osg::StateAttribute&) const override;

  //! Assigns a new EGLImage to this EGLImageTexture
  void setEGLImage(EGLImageKHR f_eglImage);

  void setAndroidTextureID(GLuint f_textureId);

  void setMinificationFilter(osg::Texture::FilterMode f_minificationFilter);
  void setMagnificationFilter(osg::Texture::FilterMode f_magnificationFilter);
  osg::Texture::FilterMode getMinificationFilter() const;
  osg::Texture::FilterMode getMagnificationFilter() const;

  bool valid() const;

protected:
  mutable GLuint m_textureId = 0;
  EGLImageKHR m_eglImage;
  osg::Texture::FilterMode m_minificationFilter;
  osg::Texture::FilterMode m_magnificationFilter;
};

} // namespace ultrascale
} // namespace target
} // namespace pc

#endif //SV3D_TEXTUREEGLIMAGEKHR_H
