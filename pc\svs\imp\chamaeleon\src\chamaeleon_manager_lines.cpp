/// @copyright (C) 2023 Robert <PERSON> GmbH.
/// The reproduction, distribution and utilization of this file as well as the
/// communication of its contents to others without express authorization is
/// prohibited. Offenders will be held liable for the payment of damages. All
/// rights reserved in the event of the grant of a patent, utility model or
/// design.
/// @file

#include "pc/svs/imp/chamaeleon/inc/chamaeleon_manager_lines.hpp"

namespace rbp
{
namespace vis
{
namespace imp
{
namespace chamaeleon
{

ChamaeleonManagerLines::ChamaeleonManagerLines()
    : m_origin{}
    , m_lineDirection{}
    , m_lineBaseDirection{}
{
    for (const auto worldOverlap : CWorldOverlapIndex::ALL)
    {
        const ETabImageOverlapROI l_imageRoi1{getOverlapROI1(worldOverlap.asEnum())};
        const ETabImageOverlapROI l_imageRoi2{getOverlapROI2(worldOverlap.asEnum())};

        m_origin[worldOverlap.asEnum()]  = osg::Vec3f(-1.F, -1.F, -1.F);
        m_lineDirection[l_imageRoi1]     = osg::Vec2f(-1.F, -1.F);
        m_lineDirection[l_imageRoi2]     = osg::Vec2f(-1.F, -1.F);
        m_lineBaseDirection[l_imageRoi1] = osg::Vec2f(-1.F, -1.F);
        m_lineBaseDirection[l_imageRoi2] = osg::Vec2f(-1.F, -1.F);
    }
}

static osg::Vec2 calcChamaeleonLines(const osg::Vec2f& f_stitchingLine, const osg::Vec2f& f_baseLine)
{
    if (vfc::notZero(f_baseLine.length()))
    {
        return (f_stitchingLine + f_baseLine) / 2.F;
    }
    else
    {
        return f_stitchingLine;
    }
}

void ChamaeleonManagerLines::update(const pc::factory::StitchingLinesManager* const f_stitchLineMng)
{
    if (nullptr == f_stitchLineMng)
    {
        return;
    }

    // Update chamaeleonLineBaseDirection
    for (const auto twoCamArea : common::CTwoCamAreaIndex::ALL)
    {
        const ETabImageOverlapROI l_imageRoi1{getOverlapROI1(getWorldOverlap(twoCamArea.asEnum()))};
        const ETabImageOverlapROI l_imageRoi2{getOverlapROI2(getWorldOverlap(twoCamArea.asEnum()))};

        const pc::factory::TwoCamArea l_twoCamLeft{
            getTwoCamArea(getWorldOverlap(getOverlapROILeft(getSingleCamLeft(twoCamArea.asEnum()))))};
        const pc::factory::TwoCamArea l_twoCamRight{
            getTwoCamArea(getWorldOverlap(getOverlapROIRight(getSingleCamRight(twoCamArea.asEnum()))))};

        const osg::Vec3 l_lineBaseDirection1{
            f_stitchLineMng->getStitchingLineOrigin(twoCamArea.asEnum()) -
            f_stitchLineMng->getStitchingLineOrigin(l_twoCamRight)};
        const osg::Vec3 l_lineBaseDirection2{
            f_stitchLineMng->getStitchingLineOrigin(twoCamArea.asEnum()) -
            f_stitchLineMng->getStitchingLineOrigin(l_twoCamLeft)};

        m_lineBaseDirection[l_imageRoi1] = osg::Vec2f{l_lineBaseDirection1.x(), l_lineBaseDirection1.y()};
        m_lineBaseDirection[l_imageRoi1].normalize();
        m_lineBaseDirection[l_imageRoi2] = osg::Vec2f{l_lineBaseDirection2.x(), l_lineBaseDirection2.y()};
        m_lineBaseDirection[l_imageRoi2].normalize();
    }

    // Update chamaeleonLinesDirection and chamaeleonOrigin
    for (const auto worldOverlap : CWorldOverlapIndex::ALL)
    {
        const ETabImageOverlapROI l_imageRoi1{getOverlapROI1(worldOverlap.asEnum())};
        const ETabImageOverlapROI l_imageRoi2{getOverlapROI2(worldOverlap.asEnum())};

        m_origin[worldOverlap.asEnum()] = f_stitchLineMng->getStitchingLineOrigin(getTwoCamArea(worldOverlap.asEnum()));
        const osg::Vec2f l_stitchingLine{
            f_stitchLineMng->getStitchingLine(getTwoCamArea(worldOverlap.asEnum())).x(),
            f_stitchLineMng->getStitchingLine(getTwoCamArea(worldOverlap.asEnum())).y()};
        m_lineDirection[l_imageRoi1] = calcChamaeleonLines(l_stitchingLine, m_lineBaseDirection[l_imageRoi1]);
        m_lineDirection[l_imageRoi1].normalize();
        m_lineDirection[l_imageRoi2] = calcChamaeleonLines(l_stitchingLine, m_lineBaseDirection[l_imageRoi2]);
        m_lineDirection[l_imageRoi2].normalize();
    }
}
} // namespace chamaeleon
} // namespace imp
} // namespace vis
} // namespace rbp
