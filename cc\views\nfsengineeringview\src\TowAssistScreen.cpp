//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: VUJ1LR Vujicic Milica (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  TowAssistScreen.cpp
/// @brief
//=============================================================================

#include "pc/svs/core/inc/Framework.h"
#include "pc/svs/core/inc/SystemConf.h"
#include "pc/svs/core/inc/ShaderManager.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"

#include "cc/views/nfsengineeringview/inc/TowAssistScreen.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"

#include "osg/LineWidth"
#include "osg/Geometry"

namespace cc
{
namespace views
{
namespace nfsengineeringview
{

constexpr vfc::float32_t g_LineLength = 100.0f;
constexpr vfc::float32_t g_LineWidth  = 5.0f;

//!
//! TowAssistScreen
//!
TowAssistScreen::TowAssistScreen()
  : pc::views::engineeringview::EngineeringScreen("TowAssist screen")
  , m_counterCurrentTA(0)
  , m_counterCurrentTC(0)
  , m_counterCurrentSA(0)
{
    osg::StateSet* const l_stateSet = getOrCreateStateSet();
    l_stateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF); //PRQA S 3143
    pc::core::TextureShaderProgramDescriptor l_basicTexShader("colorText");
    l_basicTexShader.apply(l_stateSet);  // PRQA S 3803

    // set the positions based on viewport size
    const vfc::float32_t l_mainViewStartX = static_cast<vfc::float32_t>(cc::core::g_views->m_mainViewport.m_origin.x());
    const vfc::float32_t l_mainViewWidth  = static_cast<vfc::float32_t>(cc::core::g_views->m_mainViewport.m_size.x());
    const vfc::float32_t l_mainViewHeight = static_cast<vfc::float32_t>(cc::core::g_views->m_mainViewport.m_size.y());
    const vfc::float32_t l_sysMainHeight  = static_cast<vfc::float32_t>(pc::core::g_systemConf->m_mainViewport.m_size.y());

    // default values: based on left hand drive
    constexpr vfc::float32_t l_charSize = 20.0f;
    constexpr vfc::uint32_t l_keyWidth = 24u;
    const osg::Vec4f l_colorText(0.0f, 0.0f, 0.0f, 1.0f);
    osg::Vec3f l_posText(l_mainViewStartX + 24.0f, l_sysMainHeight - 60.0f, 0.0f);

    const osg::Vec4f l_colorLineReq(1.0f, 0.0f, 0.0f, 1.0f);   // red
    const osg::Vec4f l_colorLineCur(0.0f, 0.0f, 1.0f, 1.0f);   // blue
    osg::Vec3f l_posLineStart(l_mainViewStartX + l_mainViewWidth / 2.0f, l_sysMainHeight - l_mainViewHeight / 2.0f, 0.0f);
    osg::Vec3f l_posLineEnd(l_mainViewStartX + l_mainViewWidth / 2.0f, l_sysMainHeight - l_mainViewHeight / 2.0f + g_LineLength, 0.0f);

    // change the positions depends on the right hand drive
    const bool lhd = pc::vehicle::g_mechanicalData->m_leftHandDrive;
    if (!lhd)
    {
      //The car is right hand drive
      l_posText.x() -= l_mainViewStartX;

      l_posLineStart.x() -= l_mainViewStartX;
      l_posLineEnd.x() -= l_mainViewStartX;
    }

    pc::views::engineeringview::KeyValueTextBox * const l_pKeyValueTB
      = new pc::views::engineeringview::KeyValueTextBox(l_posText, l_colorText, l_charSize, l_keyWidth);
    addTextBox(l_pKeyValueTB);

    l_pKeyValueTB->getOrCreateEntry("TrID: ")->setValue("N/A");
    l_pKeyValueTB->getOrCreateEntry("TrLength: ")->setValue("N/A");
    l_pKeyValueTB->getOrCreateEntry("TrWidth: ")->setValue("N/A");
    l_pKeyValueTB->getOrCreateEntry("TrAxels: ")->setValue("N/A");
    l_pKeyValueTB->getOrCreateEntry("TrStat: ")->setValue("N/A");
    l_pKeyValueTB->getOrCreateEntry("TrCon: ")->setValue("N/A");
    l_pKeyValueTB->getOrCreateEntry("TrDetect: ")->setValue("N/A");

    l_pKeyValueTB->getOrCreateEntry("Yaw: ")->setValue("N/A");
    l_pKeyValueTB->getOrCreateEntry("Pitch: ")->setValue("N/A");
    l_pKeyValueTB->getOrCreateEntry("Roll: ")->setValue("N/A");
    l_pKeyValueTB->getOrCreateEntry("TA2CurrAng: ")->setValue("N/A");
    l_pKeyValueTB->getOrCreateEntry("TA2ReqAng: ")->setValue("N/A");
    l_pKeyValueTB->getOrCreateEntry("JackKnife Ang: ")->setValue("N/A");
    l_pKeyValueTB->getOrCreateEntry("RdWhlAgReq: ")->setValue("N/A");
    l_pKeyValueTB->getOrCreateEntry("ActRdWhlAng: ")->setValue("N/A");

    // Create a new stateset for lines
    osg::StateSet* const l_stateSetLine = new osg::StateSet;
    l_stateSetLine->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF); //PRQA S 3143
    pc::core::BasicShaderProgramDescriptor l_basicTexShaderLine("basicColor");
    l_basicTexShaderLine.apply(l_stateSetLine);  // PRQA S 3803

    //Add the requested trailer angle line
    pc::views::engineeringview::Line * const l_pLineReq
      = new pc::views::engineeringview::Line(l_posLineStart, l_posLineEnd, l_colorLineReq, g_LineWidth);
    l_pLineReq->setStateSet(l_stateSetLine);
    addLine(l_pLineReq);
    //Add the current trailer angle line
    pc::views::engineeringview::Line * const l_pLineCur
      = new pc::views::engineeringview::Line(l_posLineStart, l_posLineEnd, l_colorLineCur, g_LineWidth);
    l_pLineCur->setStateSet(l_stateSetLine);
    addLine(l_pLineCur);
}

TowAssistScreen::~TowAssistScreen() = default;

std::string TowAssistScreen::getTrailerStatusString(vfc::int32_t f_value)
{
  // map the m_trailerStatus value to corresponding strings
  std::string l_retString;
  switch(f_value)
  {
    case 0:
    {
      l_retString = "0 NO_DATA";
      break;
    }
    case 1:
    {
      l_retString = "1 STORED";
      break;
    }
    case 2:
    {
      l_retString = "2 DETECTING";
      break;
    }
    case 3:
    {
      l_retString = "3 CAL_IN_PROG 0";
      break;
    }
    case 4:
    {
      l_retString = "4 CAL_IN_PROG 25";
      break;
    }
    case 5:
    {
      l_retString = "5 CAL_IN_PROG 50";
      break;
    }
    case 6:
    {
      l_retString = "6 CAL_IN_PROG 75";
      break;
    }
    case 7:
    {
      l_retString = "7 CAL_COMP 100";
      break;
    }
    case 8:
    {
      l_retString = "8 STOR & CALIB";
      break;
    }
    case 9:
    {
      l_retString = "9 TA2PRE_TAIL OP";
      break;
    }
    case 17:
    {
      l_retString = "11 TA2ACT_Norm";         // 17 is HEX 11
      break;
    }
    case 18:
    {
      l_retString = "12 TA2ACT_SpeAppro";     // 18 is HEX 12
      break;
    }
    case 31:
    {
      l_retString = "1F TA2CAN_Steering";     // 31 is HEX 1F
      break;
    }
    default:
    {
      l_retString = "N/A";
      break;
    }
  }

  return l_retString;
}

bool TowAssistScreen::update(pc::core::Framework* f_framework)
{
  if (nullptr == f_framework)
  {
    return false;
  }
  const cc::daddy::TrailerSvsDaddy* const l_pTowAssist = f_framework->asCustomFramework()->m_trailerSvsReceiver.getData();
  const cc::daddy::TrailerConnectedDaddy_t* const l_pTrailerConn = f_framework->m_trailerConnectionReceiver.getData();
  const pc::daddy::SteeringAngleDaddy* const l_pSteeringAngle = f_framework->m_steeringAngleFrontReceiver.getData();
  bool l_changed = false;
  pc::views::engineeringview::TextBox *const l_pTextBox = getTextBox(0u);

  if ((nullptr != l_pTowAssist) && (static_cast<vfc::int32_t>(l_pTowAssist->m_sequenceNumber) != m_counterCurrentTA))
  {
    m_counterCurrentTA = static_cast<vfc::int32_t>(l_pTowAssist->m_sequenceNumber);

    pc::views::engineeringview::TextBox::Entry *const l_pEntryTrailerID = l_pTextBox->getOrCreateEntry("TrID: ");
    l_pEntryTrailerID->setValue(static_cast<vfc::int32_t>(l_pTowAssist->m_Data.m_trailerID));

    pc::views::engineeringview::TextBox::Entry *const l_pEntryTrailerLength = l_pTextBox->getOrCreateEntry("TrLength: ");
    l_pEntryTrailerLength->setValue(static_cast<vfc::float32_t>(l_pTowAssist->m_Data.m_trailerLength.value()));

    pc::views::engineeringview::TextBox::Entry *const l_pEntryTrailerWidth = l_pTextBox->getOrCreateEntry("TrWidth: ");
    l_pEntryTrailerWidth->setValue(static_cast<vfc::float32_t>(l_pTowAssist->m_Data.m_trailerWidth.value()));

    pc::views::engineeringview::TextBox::Entry *const l_pEntryAxelNum = l_pTextBox->getOrCreateEntry("TrAxels: ");
    l_pEntryAxelNum->setValue(static_cast<vfc::int32_t>(l_pTowAssist->m_Data.m_numAxels));

    pc::views::engineeringview::TextBox::Entry *const l_pEntryTrailerStatus = l_pTextBox->getOrCreateEntry("TrStat: ");
    const std::string l_sTrailerStatus = getTrailerStatusString(static_cast<vfc::int32_t>(l_pTowAssist->m_Data.m_trailerStatus));
    l_pEntryTrailerStatus->setValue(l_sTrailerStatus);

    pc::views::engineeringview::TextBox::Entry *const l_pEntryTrailerYaw = l_pTextBox->getOrCreateEntry("Yaw: ");
    l_pEntryTrailerYaw->setValue(static_cast<vfc::float32_t>(l_pTowAssist->m_Data.m_trailerYaw.value()));

    pc::views::engineeringview::TextBox::Entry *const l_pEntryTrailerPitch = l_pTextBox->getOrCreateEntry("Pitch: ");
    l_pEntryTrailerPitch->setValue(static_cast<vfc::float32_t>(l_pTowAssist->m_Data.m_trailerPitch.value()));

    pc::views::engineeringview::TextBox::Entry *const l_pEntryTrailerRoll = l_pTextBox->getOrCreateEntry("Roll: ");
    l_pEntryTrailerRoll->setValue(static_cast<vfc::float32_t>(l_pTowAssist->m_Data.m_trailerRoll.value()));

    pc::views::engineeringview::TextBox::Entry *const l_pEntryTrailerHeadDet = l_pTextBox->getOrCreateEntry("TrDetect: ");
    l_pEntryTrailerHeadDet->setValue(static_cast<vfc::int32_t>(l_pTowAssist->m_Data.m_trailerHeadDetected));

    pc::views::engineeringview::TextBox::Entry *const l_pEntryCurr = l_pTextBox->getOrCreateEntry("TA2CurrAng: ");
    l_pEntryCurr->setValue(static_cast<vfc::float32_t>(l_pTowAssist->m_Data.m_currentTrailerAngleToMaxAngle));

    pc::views::engineeringview::TextBox::Entry *const l_pEntryReq = l_pTextBox->getOrCreateEntry("TA2ReqAng: ");
    l_pEntryReq->setValue(static_cast<vfc::float32_t>(l_pTowAssist->m_Data.m_requestedTrailerAngleToMaxAngle));

    pc::views::engineeringview::TextBox::Entry *const l_pEntryTrailerJackKAng = l_pTextBox->getOrCreateEntry("JackKnife Ang: ");
    l_pEntryTrailerJackKAng->setValue(static_cast<vfc::float32_t>(l_pTowAssist->m_Data.m_trailerJackKnifeAngle_deg.value()));

    pc::views::engineeringview::TextBox::Entry *const l_pEntryTrailerReqSteer = l_pTextBox->getOrCreateEntry("RdWhlAgReq: ");
    l_pEntryTrailerReqSteer->setValue(static_cast<vfc::float32_t>(l_pTowAssist->m_Data.m_setRoadWheelAngle.value()));

    // update line Position
    //get the requested trailer angle line from engineerngview's array of lines and update its position
    pc::views::engineeringview::Line *const l_RequestedTrailerAngle = getLine(0u);
    l_RequestedTrailerAngle->setEnd(osg::Vec3f(
      l_RequestedTrailerAngle->getStart().x()
      + (std::sin((l_pTowAssist->m_Data.m_requestedAngle.value() * static_cast<vfc::float32_t>(osg::PI)) / 180.0f) * g_LineLength),
      l_RequestedTrailerAngle->getStart().y()
      + (std::abs(std::cos((l_pTowAssist->m_Data.m_requestedAngle.value() * static_cast<vfc::float32_t>(osg::PI)) / 180.0f) * g_LineLength)),
      l_RequestedTrailerAngle->getStart().z())
      );

    //get the current trailer angle line from engineerngview's array of lines and update its position
    pc::views::engineeringview::Line *const l_CurrentTrailerAngle = getLine(1u);
    l_CurrentTrailerAngle->setEnd(osg::Vec3f(
      l_CurrentTrailerAngle->getStart().x()
      + (std::sin((l_pTowAssist->m_Data.m_trailerAngle.value() * static_cast<vfc::float32_t>(osg::PI)) / 180.0f) * g_LineLength),
      l_CurrentTrailerAngle->getStart().y()
      + (std::abs(std::cos((l_pTowAssist->m_Data.m_trailerAngle.value() * static_cast<vfc::float32_t>(osg::PI)) / 180.0f) * g_LineLength)),
      l_CurrentTrailerAngle->getStart().z())
      );

    l_changed = true;
  }

  if ((nullptr != l_pTrailerConn) && ( 0 == l_pTrailerConn->m_Data))
  {
    // Reset trailer length after disconnect
    pc::views::engineeringview::TextBox::Entry *const l_pEntryTrailerLength = l_pTextBox->getOrCreateEntry("TrLength: ");
    l_pEntryTrailerLength->setValue(0.0f);
    // Reset trailer width after disconnect
    pc::views::engineeringview::TextBox::Entry *const l_pEntryTrailerWidth = l_pTextBox->getOrCreateEntry("TrWidth: ");
    l_pEntryTrailerWidth->setValue(0.0f);

    l_changed = true;
  }

  if ((nullptr != l_pTrailerConn) && (static_cast<vfc::int32_t>(l_pTrailerConn->m_sequenceNumber) != m_counterCurrentTC))
  {
    m_counterCurrentTC = static_cast<vfc::int32_t>(l_pTrailerConn->m_sequenceNumber);
    pc::views::engineeringview::TextBox::Entry *const l_pEntryTrailerConn = l_pTextBox->getOrCreateEntry("TrCon: ");
    l_pEntryTrailerConn->setValue(static_cast<vfc::int32_t>(l_pTrailerConn->m_Data));

    l_changed = true;
  }

  if ((nullptr != l_pSteeringAngle) && (static_cast<vfc::int32_t>(l_pSteeringAngle->m_sequenceNumber) != m_counterCurrentSA))
  {
    m_counterCurrentSA = static_cast<vfc::int32_t>(l_pSteeringAngle->m_sequenceNumber);
    pc::views::engineeringview::TextBox::Entry *const l_pEntrySteerAng = l_pTextBox->getOrCreateEntry("ActRdWhlAng: ");
    l_pEntrySteerAng->setValue(static_cast<vfc::float32_t>(l_pSteeringAngle->m_Data.value()));

    l_changed = true;
  }

  return l_changed;

}

} // namespace nfsengineeringview
} // namespace views
} // namespace cc

