/// @copyright (C) 2025 Robert <PERSON>.
/// The reproduction, distribution and utilization of this file as well as the
/// communication of its contents to others without express authorization is
/// prohibited. Offenders will be held liable for the payment of damages. All
/// rights reserved in the event of the grant of a patent, utility model or
/// design.
/// @file

#include "pc/svs/imp/tnf/inc/temporal_noise_filter_data.hpp"

namespace rbp
{
namespace vis
{
namespace imp
{
namespace tnf
{

TemporalNoiseFilterData::TemporalNoiseFilterData()
    : pc::util::coding::ISerializable{}
    , m_filteredImageWeight{0.65F}
    , m_motionDetectionThreshold{13U}
    , m_combineMotionMap{false}
    , m_setToPassThrough{false}
    , m_outputMotionMap{false}
    , m_enable{false}
{
}

TemporalNoiseFilterData::TemporalNoiseFilterData(
    vfc::float32_t f_filteredImageWeight,
    vfc::uint32_t  f_motionDetectionThreshold,
    bool           f_combineMotionMap,
    bool           f_setToPassThrough,
    bool           f_outputMotionMap,
    bool           f_enable)
    : pc::util::coding::ISerializable{}
    , m_filteredImageWeight{f_filteredImageWeight}
    , m_motionDetectionThreshold{f_motionDetectionThreshold}
    , m_combineMotionMap{f_combineMotionMap}
    , m_setToPassThrough{f_setToPassThrough}
    , m_outputMotionMap{f_outputMotionMap}
    , m_enable{f_enable}
{
}

pc::util::coding::Item<TemporalNoiseFilterData> g_temporalNoiseFilterData("IQ_TemporalNoiseFilter");

vfc::TCArray<std::size_t, pc::core::sysconf::E_SYSTEM_CONFIG_NUMBER_OF_CAMERAS> g_tnfBufferIndex = {{0U, 0U, 0U, 0U}};

} // namespace tnf
} // namespace imp
} // namespace vis
} // namespace rbp
