//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
// Copyright (c) 2013 by <PERSON>. All rights reserved.
//
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//=============================================================================
// R E V I S I O N I N F O R M A T I O N
//-----------------------------------------------------------------------------
// $Revision: 13956 $
// $Author: ca82lr $
// $Date: 2016-10-17 12:49:54 +0200 (Mo, 17 Okt 2016) $
//=============================================================================

#ifndef GRAPHICSCONTEXT_H
#define GRAPHICSCONTEXT_H

#include <osg/GraphicsContext>

#include <EGL/egl.h>
#include <EGL/eglext.h>
#include <GLES2/gl2.h>
#include <GLES2/gl2ext.h>

#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h"
using pc::util::logging::g_AppContext;

//#include "cc/imc/qualcomm/qcengine/inc/QcarCamCommon.h"

namespace osg
{

//!
//! Implementation of an osg::GraphicsContext for a Pixmap surface.
//!
//! @note This implementation is qualcomm / QNX specific!!!
//!
class QcGraphicsContext: public osg::GraphicsContext
{
public:
//  QcGraphicsContext(osg::GraphicsContext::Traits* f_traits, unsigned char f_nbSurfaces);
  QcGraphicsContext(osg::GraphicsContext::Traits* f_traits);

  virtual bool isSameKindAs(const Object* f_object) const override
  {
    return dynamic_cast<const QcGraphicsContext*>(f_object) != nullptr;
  }

  virtual const char* libraryName() const override
  {
    return "osgViewer";
  }

  virtual const char* className() const override
  {
    return "QcGraphicsContext";
  }

  //! avoid osg to do a glclear just after the swap buffer in the viewerbase implementation
  //! because this is is an issue in our implementation witht the fence sync
  //! the custom clear will be then called by our application at the beginning of each frame
  virtual void clear() override
  {
#if 0 // See customClear()

    _lastClearTick = osg::Timer::instance()->tick();

    if (_clearMask==0 || !_traits) return;

    glViewport(0, 0, _traits->width, _traits->height);
    glScissor(0, 0, _traits->width, _traits->height);

    glClearColor( _clearColor[0], _clearColor[1], _clearColor[2], _clearColor[3]);

    glClear( _clearMask );
#endif
  }
  virtual void customClear()
  {
    _lastClearTick = osg::Timer::instance()->tick();

    if (_clearMask==0 || !_traits) return;

    glViewport(0, 0, _traits->width, _traits->height);
    glScissor(0, 0, _traits->width, _traits->height);

    //glClearColor( _clearColor[0], _clearColor[1], _clearColor[2], _clearColor[3]);
    //glClearColor(0.0f, 0.0f, 1.0f, 0.0f);

    //byd avm background
    glClearColor(0.0f/255.0f, 0.0f/255.0f, 0.0f/255.0f, 0.0f/255.0f);

    glClear( _clearMask );
  }

  virtual bool valid() const override
  {
    return m_valid;
  }

  //! Realize the GraphicsContext.
  virtual bool realizeImplementation() override;

  //! Return true if the graphics context has been realized and is ready to use.
  virtual bool isRealizedImplementation() const override;

  //! Close the graphics context.
  virtual void closeImplementation() override;

  //! Make this graphics context current.
  virtual bool makeCurrentImplementation() override;

  //! Make this graphics context current with specified read context implementation.
  virtual bool makeContextCurrentImplementation(GraphicsContext* f_gc) override;

  //! Release the graphics context.*/
  virtual bool releaseContextImplementation() override;

  //! Pure virtual, Bind the graphics context to associated texture implementation.
  virtual void bindPBufferToTextureImplementation(GLenum buffer) override;

  //! Swap the front and back buffers.
  virtual void swapBuffersImplementation() override;

  virtual void checkEvents();

  int intEGLOfflineEnv();

  int setExternalDefaultFBOId(int fboID);

protected:

  virtual ~QcGraphicsContext();

  bool m_valid;
  bool m_initialized;
  bool m_realized;

public:

  int m_FBOForDefaultRendering = 0;

  int screen_win_width;
  int screen_win_height;

  EGLDisplay egl_display;
  EGLContext egl_ctx;
  EGLSurface egl_surface;
  EGLConfig egl_conf = (EGLConfig)0;

};

}

#endif /* GRAPHICSCONTEXT_H */
