/// @copyright (C) 2025 Robert <PERSON> GmbH.
/// The reproduction, distribution and utilization of this file as well as the
/// communication of its contents to others without express authorization is
/// prohibited. Offenders will be held liable for the payment of damages. All
/// rights reserved in the event of the grant of a patent, utility model or
/// design.
/// @file

#include "pc/svs/imp/chamaeleon/inc/chamaeleon_estimator.hpp"
#include "pc/svs/core/inc/ShaderManager.h"
#include "pc/svs/factory/inc/StitchingLinesManager.h"
#include "pc/svs/imp/common/inc/utils.hpp"
#include "pc/svs/util/osgx/inc/Utils.h"

namespace rbp
{
namespace vis
{
namespace imp
{
namespace chamaeleon
{

ChamaeleonEstimator::ChamaeleonEstimator()
    : m_rampFac{0.5F}
    , m_useWbGainsAsFallback{false}
    , m_callUpdateEstiFirstTime{true}
    , m_lowerUpperThresholdDeltaL{25.F, 26.F}
    , m_lowerUpperThresholdDeltaAB{3.F, 4.F}
    , m_minMaxOutputGain{0.6F, 1.4F}
    , m_chamaeleonPingPongEstimator{}
    , m_pingPongCounter{0}
{
    constexpr vfc::int32_t l_numSamples{0};
    constexpr vfc::int32_t l_textureWidth{static_cast<vfc::int32_t>(ETabImageOverlapROI::NUM_IMAGE_ROIS)};

    for (auto& l_esti : m_chamaeleonPingPongEstimator)
    {
        l_esti.m_estiAsTexture = osg_ext::make_ref<osg::Texture2D>();
        l_esti.m_estiAsTexture->setTextureSize(l_textureWidth, 1);
        l_esti.m_estiAsTexture->setSourceFormat(GL_RGBA);
        l_esti.m_estiAsTexture->setDataVariance(osg::Object::DYNAMIC);
        l_esti.m_estiAsTexture->setInternalFormat(GL_RGBA);
        l_esti.m_estiAsTexture->setSourceType(GL_UNSIGNED_BYTE);
        l_esti.m_estiAsTexture->setResizeNonPowerOfTwoHint(false);
        l_esti.m_estiAsTexture->setFilter(osg::Texture::MIN_FILTER, osg::Texture::NEAREST);
        l_esti.m_estiAsTexture->setFilter(osg::Texture::MAG_FILTER, osg::Texture::NEAREST);
        l_esti.m_estiAsTexture->setWrap(osg::Texture::WRAP_S, osg::Texture::CLAMP_TO_EDGE);
        l_esti.m_estiAsTexture->setWrap(osg::Texture::WRAP_T, osg::Texture::CLAMP_TO_EDGE);

        l_esti.m_cam = osg_ext::make_ref<::pc::util::osgx::RenderToTextureCamera>(l_esti.m_estiAsTexture, l_numSamples);

        l_esti.m_geode              = osg_ext::make_ref<osg::Geode>();
        osg::Geometry* const l_quad = osg::createTexturedQuadGeometry(
            osg::Vec3(0.F, 0.F, 0.F), osg::Vec3(1.F, 0.F, 0.F), osg::Vec3(0.F, 1.F, 0.F), 0.F, 0.F, 1.F, 1.F);
        l_esti.m_geode->addDrawable(l_quad);
        pc::core::TextureShaderProgramDescriptor l_chamaeleonColorShader("chamaeleonEstimator");
        osg::StateSet* const                     l_stateSet = l_esti.m_geode->getOrCreateStateSet();
        l_chamaeleonColorShader.apply(l_stateSet);
        l_esti.m_cam->addChild(l_esti.m_geode.get());
    }

    setParameters();
}

void ChamaeleonEstimator::setParameters(const ChamaeleonEstimatorData& f_params)
{
    m_rampFac                    = f_params.m_rampFac;
    m_useWbGainsAsFallback       = f_params.m_useWbGainsAsFallback;
    m_lowerUpperThresholdDeltaL  = osg::Vec2f{f_params.m_thresholdLowerDeltaL, f_params.m_thresholdUpperDeltaL};
    m_lowerUpperThresholdDeltaAB = osg::Vec2f{f_params.m_thresholdLowerDeltaAB, f_params.m_thresholdUpperDeltaAB};
    m_minMaxOutputGain           = osg::Vec2f{f_params.m_minOutputGain, f_params.m_maxOutputGain};
}

void ChamaeleonEstimator::updateEstimator(
    osg::Texture2D*       f_roisAsTexture,
    const osg::Vec4&      f_worldRoiDegradationMask,
    const osg::Matrix4x3& f_wbgRawGains,
    osg::NodeVisitor&     f_nv)
{
    const vfc::int32_t l_pingPongCounterNext = (m_pingPongCounter + 1) % 2;
    auto&              l_estiCurr            = m_chamaeleonPingPongEstimator[m_pingPongCounter];
    auto&              l_estiNext            = m_chamaeleonPingPongEstimator[l_pingPongCounterNext];

    osg::StateSet* const l_stateSet = l_estiCurr.m_geode->getOrCreateStateSet();

    // world roi validity mask
    osg::Uniform* const l_roiLod{l_stateSet->getOrCreateUniform("u_roiLod", osg::Uniform::INT)};
    l_roiLod->set(ROI_DIM_LOD);

    // world roi validity mask
    osg::Uniform* const l_worldRoiDegradationMask{
        l_stateSet->getOrCreateUniform("u_worldRoiDegradationMask", osg::Uniform::FLOAT_VEC4)};
    l_worldRoiDegradationMask->set(f_worldRoiDegradationMask);

    // ramping
    osg::Uniform* const l_rampingFactor{l_stateSet->getOrCreateUniform("u_rampingFactor", osg::Uniform::FLOAT)};
    l_rampingFactor->set(m_callUpdateEstiFirstTime ? 0.F : m_rampFac);

    // degradedGains
    osg::Uniform* const l_wbgRawGains{l_stateSet->getOrCreateUniform("u_wbgRawGains", osg::Uniform::FLOAT_MAT4x3)};
    l_wbgRawGains->set(m_useWbGainsAsFallback ? f_wbgRawGains : m_neutralGains);

    // delta_l threshold
    osg::Uniform* const l_lowerUpperThresholdDeltaL{
        l_stateSet->getOrCreateUniform("u_lowerUpperThresholdDeltaL", osg::Uniform::FLOAT_VEC2)};
    l_lowerUpperThresholdDeltaL->set(m_lowerUpperThresholdDeltaL);

    // delta_ab threshold
    osg::Uniform* const l_lowerUpperThresholdDeltaAB{
        l_stateSet->getOrCreateUniform("u_lowerUpperThresholdDeltaAB", osg::Uniform::FLOAT_VEC2)};
    l_lowerUpperThresholdDeltaAB->set(m_lowerUpperThresholdDeltaAB);

    // min max output gain
    osg::Uniform* const l_minMaxOutputGain{
        l_stateSet->getOrCreateUniform("u_minMaxOutputGain", osg::Uniform::FLOAT_VEC2)};
    l_minMaxOutputGain->set(m_minMaxOutputGain);

    // add chamaeleon rois as texture
    l_stateSet->setTextureAttribute(
        0U,
        f_roisAsTexture,
        static_cast<osg::StateAttribute::OverrideValue>(osg::StateAttribute::ON) |
            static_cast<osg::StateAttribute::OverrideValue>(osg::StateAttribute::OVERRIDE));
    osg::Uniform* const l_samplerUniformRoiAsTexture =
        l_stateSet->getOrCreateUniform("s_chamRoiAsTexture", osg::Uniform::SAMPLER_2D);
    l_samplerUniformRoiAsTexture->set(0);

    // add chamaeleon esti as texture
    l_stateSet->setTextureAttribute(
        1U,
        l_estiNext.m_estiAsTexture,
        static_cast<osg::StateAttribute::OverrideValue>(osg::StateAttribute::ON) |
            static_cast<osg::StateAttribute::OverrideValue>(osg::StateAttribute::OVERRIDE));
    osg::Uniform* const l_samplerUniformEstiAsTexture =
        l_stateSet->getOrCreateUniform("s_chamEstiAsTexture", osg::Uniform::SAMPLER_2D);
    l_samplerUniformEstiAsTexture->set(1);

    l_estiCurr.m_cam->accept(f_nv);

    m_pingPongCounter         = l_pingPongCounterNext;
    m_callUpdateEstiFirstTime = false;
}

} // namespace chamaeleon
} // namespace imp
} // namespace vis
} // namespace rbp
