//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: ESJ1LR Esparza Jose (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  BackchannelScreen.cpp
/// @brief
//=============================================================================

#include "pc/svs/core/inc/Framework.h"
#include "pc/svs/core/inc/SystemConf.h"
#include "pc/svs/core/inc/ShaderManager.h"

#include "cc/views/nfsengineeringview/inc/BackchannelScreen.h"

#include <iostream>
#include <sstream>

namespace cc
{
namespace views
{
namespace nfsengineeringview
{

//!
//! BackchannelScreen
//!

BackchannelScreen::BackchannelScreen()
  : pc::views::engineeringview::EngineeringScreen("Backchannel screen")
{
    addTextBoxes(1u);

    osg::StateSet* const l_stateSet = getOrCreateStateSet();
    l_stateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF); // PRQA S 3143
    pc::core::TextureShaderProgramDescriptor l_basicTexShader("colorText");
    l_basicTexShader.apply(l_stateSet);  // PRQA S 3803

    osgText::Text* const l_pText = dynamic_cast<osgText::Text*>(dynamic_cast<osg::Geode*>(getTextBox(0u))->getDrawable(0u)); // PRQA S 3077  // PRQA S 3400
    l_pText->setColor(osg::Vec4f(1.0f, 0.0f, 0.0f, 1.0f));
}

BackchannelScreen::~BackchannelScreen() = default;

bool BackchannelScreen::update(pc::core::Framework* f_framework)
{
  if (nullptr != f_framework)
  {
    pc::views::engineeringview::TextBox *const l_pTextBox = getTextBox(0u);
    pc::views::engineeringview::TextBox::Entry *const l_pEntry = l_pTextBox->getOrCreateEntry("Screen ID: 0x");
    std::stringstream l_ss;
    l_ss << std::hex << static_cast<vfc::int32_t>(f_framework->getCurrentScreenId());  // PRQA S 3803
    l_pEntry->setValue(l_ss.str());
  }
  return true;
}

} // namespace nfsengineeringview
} // namespace views
} // namespace cc

