//-------------------------------------------------------------------------------
// Copyright (c) 2017 by <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

//#include "cc/imc/qualcomm/qcengine/inc/QcEngine.h"
#include "cc/target/linux/inc/QcEngine.h"



#include <cassert>
#include <iomanip> //std::setprecision
#include <unistd.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <malloc.h>
#include <fcntl.h>

#ifndef EGL_EGLEXT_PROTOTYPES
#define EGL_EGLEXT_PROTOTYPES
#endif // EGL_EGLEXT_PROTOTYPES
#ifndef GL_GLEXT_PROTOTYPES
#define GL_GLEXT_PROTOTYPES
#endif // GL_GLEXT_PROTOTYPES
#include <EGL/egl.h>
#include <EGL/eglext.h>
//#include <EGL/eglextQCOM.h>
#include <GLES2/gl2.h>
#include <GLES2/gl2ext.h>

#include <osgGA/TrackballManipulator>
#include <osgViewer/Viewer>

#include "pc/generic/util/chrono/inc/chrono.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h"
// #include "pc/generic/util/logging/inc/Logging.h"

#include "pc/svs/core/inc/SvsState.h"
#include "pc/svs/factory/inc/RenderManager.h" //addEventHandler
//#include "cc/imc/qualcomm/qcengine/inc/QcarCamDaddyPorts.h"
//#include "cc/imc/qualcomm/qcengine/inc/VideoTexture.h"
//#include "cc/imc/qualcomm/qcengine/inc/ChamaeleonEGLRTTCam.h"


#include "cc/target/linux/inc/CustomSystemConf.h" //target+cc specific

//For IDC signal interface
#include "cc/core/inc/CustomScene.h"
#include "cc/daddy/inc/CustomDaddyPorts.h"
#include "cc/daddy/inc/CustomDaddyTypes.h"
#include "cc/sm/viewmode/inc/ViewModeNames.h"
#include "cc/sm/viewmode/inc/ViewModexViewStateMachine_R2015b_types.h"
//#include "cc/target/ultrascale/simplerpc/inc/RpcInterface.hpp"
#include "cc/views/panoramaview/inc/PanoramaView.h"
#include "cc/core/src/ViewModeToggle.h"


using pc::util::logging::g_EngineContext;
using pc::util::logging::g_StartupContext;




///
// Create a shader object, load the shader source, and
// compile the shader.
//
static GLuint LoadShader ( EGLenum type, const char *shaderSrc )
{
    GLuint shader = 0;
    GLint compiled = 0;

    // Create the shader object
    shader = glCreateShader ( type );

    if ( shader == 0 ) {
        return 0;
    }

    // Load the shader source
    glShaderSource( shader, 1, &shaderSrc, nullptr );

    // Compile the shader
    glCompileShader( shader );

    // Check the compile status
    glGetShaderiv( shader, GL_COMPILE_STATUS, &compiled );

    if ( compiled == 0 ) {
        GLint infoLen = 0;

        glGetShaderiv ( shader, GL_INFO_LOG_LENGTH, &infoLen );

        if ( infoLen > 1 ) {
            char *const infoLog = (char*)malloc ( sizeof ( char ) * infoLen );
            glGetShaderInfoLog ( shader, infoLen, nullptr, infoLog );
            //fprintf(stdout, "%s\n", infoLog);
            XLOG_ERROR(g_EngineContext, infoLog)
            free ( infoLog );
        }

        glDeleteShader ( shader );
        return 0;
    }

    return shader;

}

static void dumpFile(const char *path, void *data, int len)
{
    if (nullptr == path || nullptr == data)
    {
        return;
    }
    FILE *const fp = fopen(path, "wb");
    if(nullptr != fp)
    {
        fwrite(data, len, 1, fp);
        fclose(fp);
        //printf("Succed to open dump file:%s \n", path);
        XLOG_INFO(g_EngineContext, "succeed to dump to file");
    }
    else
    {
        //printf("Fail to open dump file:%s \n", path);
        XLOG_ERROR(g_EngineContext, "fail to open dump file");
    }
}


#define POCTEST 0

static int  l_tick = 0;
static bool l_flag = true;



// #define XLOG_FATAL_OS(A)   std::cout
// #define XLOG_ERROR_OS(A)   std::cout
// #define XLOG_WARN_OS(A)    std::cout
// #define XLOG_INFO_OS(A)    std::cout
// #define XLOG_DEBUG_OS(A)   std::cout
// #define XLOG_VERBOSE_OS(A) std::cout
// #define XLOG_ENDL          std::endl

#if POCTEST
static GLint samplerLoc;
static GLint positionLoc;
static GLint texcoordLoc;
static GLuint vertexID;
static GLuint texcoordID;
static GLuint programObject;

static GLuint khrTextureId;

static GLfloat vVertices[] =
{
    -1.0f,  1.0f, 0.0f, // Position 0
    -1.0f, -1.0f, 0.0f, // Position 1
    1.0f, -1.0f, 0.0f,  // Position 2
    1.0f, -1.0f, 0.0f,  // Position 3
    1.0f,  1.0f, 0.0f,  // Position 4
    -1.0f,  1.0f, 0.0f	// Position 6
};

static GLfloat texcoords[] =
{
    0.0f,  0.0f,        // TexCoord 0
    0.0f,  1.0f,        // TexCoord 1
    1.0f,  1.0f,        // TexCoord 2
    1.0f,  1.0f,        // TexCoord 2
    1.0f,  0.0f,        // TexCoord 3
    0.0f,  0.0f         // TexCoord 0
};


static void checkGLError(const char *tag)
{
    int errCode;
    errCode = glGetError();
    if(GL_NO_ERROR != errCode)
    {
        printf("GL Error, Code = %0x, %s \n", errCode, tag);
    }
}

static void checkEGLError(const char *tag) {
	int errCode;
	errCode = eglGetError();
	if (EGL_SUCCESS != errCode) {
		printf("EGL Error, Code = %0x %s\n", errCode, tag);
	}
}

static int initOpenGL(void)
{
    char vShaderStr[] =
        "#version 310 es \n"
        "precision mediump float;\n"
        "layout(location=0) in vec4 a_position;\n"
        "layout(location=1) in  vec2 a_texCoord;\n"
        "out vec2 v_texCoord;\n"
        "void main()\n"
        "{\n"
        "   gl_Position = a_position;\n"
        "	v_texCoord = a_texCoord;\n"
        "}\n";
//"uniform  samplerExternalOES s_texture;\n"
//"uniform  samplerExternal2DY2YEXT s_texture;\n"
//"   gl_FragColor = texture2D(s_texture, v_texCoord);\n"
    char fShaderStr[] =
        "#version 310 es \n"
        "#extension GL_OES_EGL_image_external_essl3 : require\n"
        "// #extension GL_EXT_YUV_target : require \n"
        "precision mediump float;\n"
        " //uniform  samplerExternal2DY2YEXT s_texture;\n"
        " uniform samplerExternalOES s_texture; \n"
        "in vec2 v_texCoord;\n"
        "out vec4 fragcolor;\n"
        "void main()\n"
        "{\n"
        "   //yuvCscStandardEXT conv_standard = itu_601_full_range; \n"
        "   //fragcolor = vec4(1.0);\n"
        "   //fragcolor.rgb = yuv_2_rgb(texture(s_texture, v_texCoord).rgb, conv_standard); \n"
        "   fragcolor = texture(s_texture, v_texCoord); \n"
        "}\n";

    GLuint vertexShader;
    GLuint fragmentShader;
    GLint linked;

    // Load the vertex/fragment shaders
    vertexShader = LoadShader ( GL_VERTEX_SHADER, vShaderStr );
    fragmentShader = LoadShader ( GL_FRAGMENT_SHADER, fShaderStr );

    // Create the program object
    programObject = glCreateProgram ( );

    if ( programObject == 0 ) {
        return 0;
    }

    glAttachShader ( programObject, vertexShader );
    glAttachShader ( programObject, fragmentShader );

    // Link the program
    glLinkProgram ( programObject );

    // Check the link status
    glGetProgramiv ( programObject, GL_LINK_STATUS, &linked );

    if ( !linked ) {
        GLint infoLen = 0;

        glGetProgramiv ( programObject, GL_INFO_LOG_LENGTH, &infoLen );

        if ( infoLen > 1 ) {
            char *infoLog = (char*)malloc ( sizeof ( char ) * infoLen );
            glGetProgramInfoLog ( programObject, infoLen, NULL, infoLog );
            fprintf(stdout, "%s\n", infoLog);
            free ( infoLog );
        }

        glDeleteProgram ( programObject );

        return EXIT_FAILURE;
    }

    // Get the sampler location
    samplerLoc = glGetUniformLocation(programObject, "s_texture");

    positionLoc = glGetAttribLocation(programObject, "a_position");
    texcoordLoc = glGetAttribLocation(programObject, "a_texCoord");

    glGenBuffers(1, &vertexID);
    glBindBuffer(GL_ARRAY_BUFFER, vertexID);
    glBufferData(GL_ARRAY_BUFFER, sizeof(vVertices), vVertices, GL_STATIC_DRAW);

    glGenBuffers(1, &texcoordID);
    glBindBuffer(GL_ARRAY_BUFFER, texcoordID);
    glBufferData(GL_ARRAY_BUFFER, sizeof(texcoords), texcoords, GL_STATIC_DRAW);

    //glClearColor(1.0f, 1.0f, 1.0f, 1.0f);

    // We don't need the shaders anymore
    glDeleteShader(fragmentShader);
    glDeleteShader(vertexShader);

    return EXIT_SUCCESS;
}
#endif

#define OSG_TRAVERSALS_DEBUG 0
namespace pc
{
namespace target
{
namespace qualcomm
{

//#define ULTRA_ENG_DEBUG

static void CheckError(int line)
{
  const GLenum err = glGetError();
  if (err != 0)
  {
    XLOG_ERROR(g_EngineContext, "GL Error 0x" << std::hex << err << std::dec << "at line " << line);
  }
  const EGLint eerr = eglGetError();
  if (eerr != EGL_SUCCESS)
  {
    XLOG_ERROR(g_EngineContext, "EGL Error 0x" << std::hex << eerr << std::dec  << "at line " << line);
  }
}

enum class EFrameDrop : vfc::uint32_t
{
  SUPERFRAME_DROP = 254U,
  PIXMAP_DROP = 255U
};


//!
//! QcEngine
//!
QcEngine::QcEngine(pc::core::Framework* f_pFramework,
                                   const unsigned int f_CycleTimeOut_ms)
  : pc::core::Engine(f_pFramework)
  , pc::core::IEventDrivenRunnable(std::chrono::milliseconds(f_CycleTimeOut_ms))
  , m_bEngineIsBusy(true)
  , m_eglKHRImageBuffer()
  , m_currentOESTextureNum{}
  , m_currentOESTextureIDArray{}
// hnd2hi  , m_frameDataInfoReceiver()
// hnd2hi  , m_releaseVidoutBufferReceiver()
// hnd2hi  , m_pixmapReleaseReceiver()
  , m_CurrMode(1000)
  , m_lastHandledFrameTime()
  , m_engineMode(EDO_FIRST_FRAME)
  , m_latencyMode(ELATENCY_MODE_NORMAL)
  , m_lastSequenceNumber(0)
  , m_pixmapFenceQueue{}
  , m_currentPixmap(0)
  , m_numberOfLatentFrames(0)
  , m_eglClientWaitThread(nullptr)
  , m_eglDisplay(nullptr)
  , m_cur_pos{}
  , m_view(FRONTVIEW)
  , m_cpcToSvsOverlaySMReceiver{}
  , m_HUDislayModeSwitchSMReceiver{}
{
  //Create the graphic context for QNX
  m_graphicsContext = QcEngine::createGraphicsContext();
}

QcEngine::~QcEngine() = default;
// {
// hdn2hi  delete (m_eglClientWaitPolling);
//  delete (m_eglClientWaitThread);
// }


void QcEngine::OnShutdown()
{
  XLOG_INFO(g_EngineContext, "QcEngine OnShutdown");
  //m_eglClientWaitThread->cancel();  // PRQA S 3803
  //m_eglClientWaitThread->join();  // PRQA S 3803

  //pc::svs::qualcomm::QCarcamDaddyPorts::sm_frameDataInSenderPort.disconnect( m_frameDataInReceiver );


  //destroy the gl texture id created by  m_videoTexture = createVideoTexture(); apply
  VideoTextureQc *const m_videoTextureQC = static_cast<VideoTextureQc*>(m_videoTexture.get());
  m_videoTextureQC->deleteGLTextures();

  // //need destroy the eglimage buffer
  // for(size_t idx = 0; idx < m_eglKHRImageBuffer.size(); idx++)
  // {
  //   //eglDestroy the eglimage
  //   if(m_eglKHRImageBuffer[idx].m_eglImage)
  //   {
  //     eglDestroyImageKHR(eglGetDisplay(EGL_DEFAULT_DISPLAY), m_eglKHRImageBuffer[idx].m_eglImage);
  //     m_eglKHRImageBuffer[idx].m_eglImage = nullptr;
  //   }
  // }

  doDeinitForRenderingFisheyeImg();
  cc::daddy::CustomDaddyPorts::sm_HUDislayModeSwitchDaddy_SenderPort.disconnect( m_HUDislayModeSwitchSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_cpcToSvsOverlay_SenderPort.disconnect( m_cpcToSvsOverlaySMReceiver ) ;

  XLOG_INFO(g_EngineContext, "QcEngine OnShutdown finished");
}


void QcEngine::OnTimeout()
{
  if(m_isCameraStreamOn)
  {
    XLOG_ERROR(g_EngineContext, "QcEngine timed out: no image was received after " << m_cycleTimeout.count() << " ms!");
  }
}


/// @deviation NRCS2_005
/// Rule QACPP-4.3.0-6040
/// message: "Do not write functions with an excessive MCCABE Cyclomatic Complexity."
void QcEngine::OnInit()  // PRQA S 6040
{

  cc::daddy::CustomDaddyPorts::sm_HUDislayModeSwitchDaddy_SenderPort.connect( m_HUDislayModeSwitchSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_cpcToSvsOverlay_SenderPort.connect( m_cpcToSvsOverlaySMReceiver ) ;

  // osg::QcGraphicsContext* l_pGraphicsCtx = static_cast<osg::QcGraphicsContext*>(m_graphicsContext.get());
  // l_pGraphicsCtx->intEGLOfflineEnv();

  #if POCTEST
  initOpenGL();
  #endif

  //pc::svs::qualcomm::QCarcamDaddyPorts::sm_frameDataInSenderPort.connect( m_frameDataInReceiver );

  //create the buffer for qcarcam via the qnx pix buffer stack
  //map this buffer handle to a texture ID via an eglcreate

  XLOG_INFO(g_EngineContext, " QcEngine Connect successful");

  m_videoTexture = createVideoTexture();
  XLOG_INFO(g_StartupContext, "Create Video Texture End Time [ms]: " << chrono_ms());

  osg::State* const l_pState = m_graphicsContext->getState();
  l_pState->setGlobalDefaultModeValue(GL_SCISSOR_TEST, pc::core::g_systemConf->m_defaultModeScissorTest);

  //XLOG_INFO_OS(g_StartupContext) << "m_framework->init(m_graphicsContext.get());  start"  << XLOG_ENDL;
  m_framework->init(m_graphicsContext.get());
  //XLOG_INFO_OS(g_StartupContext) << "m_framework->init(m_graphicsContext.get());  end "  << XLOG_ENDL;


  m_viewer = m_framework->getViewerBase();
  m_viewer->setThreadingModel(osgViewer::ViewerBase::SingleThreaded);   // hnd2hi ??????
  m_viewer->setReleaseContextAtEndOfFrameHint(false);
  if (!m_viewer->isRealized())
  {
    m_viewer->realize();
  }


  const osg::GraphicsContext::Traits* const l_traits = m_graphicsContext->getTraits();
  if (l_traits != nullptr)
  {
    //! in case of Ultrascale we might fake mouse pointer movement via command line inputs (remote controller).
    //! However we do not have a graphics window (offscreen render context only), so OSG is unable to normalize
    //! the mouse coursor position correctly. This can be fixed by setting the mouse input range manually.
    m_framework->getView()->getEventQueue()->setMouseInputRange(l_traits->x, l_traits->y, l_traits->width, l_traits->height);
  }

  //! everything was initialized properly
  // m_initialized = true;
  // m_bEngineIsBusy = false;


  // pc::svs::qualcomm::QcengineFlagInfoDaddy& l_engineFlag = 
  //                                           pc::svs::qualcomm::QCarcamDaddyPorts::sm_engineFlagSenderPort.reserve();

  //               l_engineFlag.m_Data.m_bEngineIsBusyDaddy        = m_bEngineIsBusy;
  //               l_engineFlag.m_Data.m_bEngineIsInitializedDaddy = m_initialized;

  // pc::svs::qualcomm::QCarcamDaddyPorts::sm_engineFlagSenderPort.deliver();

  //Using daddy to avoid the threads conflict issue

  //For set normal mode to svs_dai
  //std::cout<< " ========= set normal mode ========= "<< l_tick <<std::endl;
  //cc::daddy::PMA_OpModeDaddy& l_opMode = cc::daddy::CustomDaddyPorts::sm_PMA_OpModeSenderPort.reserve();
  //                     l_opMode.m_Data = static_cast<hmism::EVisuOpMd> (3);  // PRQA S 3013
  //cc::daddy::CustomDaddyPorts::sm_PMA_OpModeSenderPort.deliver();

  XLOG_INFO(g_EngineContext, " QcEngine OnInit leave");
}

osg::GraphicsContext* QcEngine::createGraphicsContext()
{
  XLOG_DEBUG(g_EngineContext, "createGraphicsContext");
  const osg::ref_ptr< osg::GraphicsContext::Traits > l_traits = new osg::GraphicsContext::Traits;

  pc::core::g_systemConf->apply(l_traits.get());
  l_traits->sharedContext = nullptr;

  osg::QcGraphicsContext* const l_pContext = new osg::QcGraphicsContext(l_traits);

  return l_pContext;
}


VideoTextureQc* QcEngine::createVideoTexture()
{
  XLOG_INFO(g_EngineContext, "createVideoTexture");
  //create the video texture used by qcarcam later

  // eglKHRImageBuffer l_eglKHRImageBuffer;
  //make the GC current before generating the tex IDs

  //m_graphicsContext->makeCurrentImplementation();

  XLOG_INFO(g_EngineContext, "QcEngine - EGLDisplay = " << eglGetDisplay(EGL_DEFAULT_DISPLAY));

  // XLOG_INFO_OS(g_EngineContext) << "pixmap list size: " << mNativePixmapList->size() << XLOG_ENDL;
  // for (unsigned int i = 0; i < mNativePixmapList->size() ; ++i)
  // {
  //   //create the eglimage from the QNX pixmap
  //   l_eglKHRImageBuffer.m_eglImage = eglCreateImageKHR(eglGetDisplay(EGL_DEFAULT_DISPLAY),
  //       EGL_NO_CONTEXT, EGL_NATIVE_PIXMAP_KHR,
  //       (EGLNativePixmapType) (((screen_pixmap_t)mNativePixmapList->at(i).pixmap)), 0);

  //   CheckError(__LINE__);
  //   m_eglKHRImageBuffer.push_back(l_eglKHRImageBuffer);

  //   // XLOG_INFO_OS(g_EngineContext)
  //   //   << "HKR image buffer #" << i
  //   //   << " eglImage: " << static_cast<void*>(m_eglKHRImageBuffer.at(i).m_eglImage)
  //   //   << " from screen pixmap: " << m_screenPixmapCamList->at(i)
  //   //   << XLOG_ENDL;
  // }
  // XLOG_INFO_OS(g_EngineContext) << "eglCreateImages done" << XLOG_ENDL;

  VideoTextureType* const l_videoTexture = new VideoTextureType(
      pc::core::sysconf::toFilterMode(cc::target::sysconf::E_TEXTURE_FILTER_MIN),
      pc::core::sysconf::toFilterMode(cc::target::sysconf::E_TEXTURE_FILTER_MAG)
  );
  XLOG_INFO(g_EngineContext, "createVideoTexture leave");
  return l_videoTexture;
}

int QcEngine::setDefaulFBOId(int fboID)
{
  osg::QcGraphicsContext* const l_pGraphicsCtx = static_cast<osg::QcGraphicsContext*>(m_graphicsContext.get());
  if(nullptr != l_pGraphicsCtx)
  {
    l_pGraphicsCtx->setExternalDefaultFBOId(fboID);
    XLOG_WARN(g_EngineContext, "engine try to set default fbo id = " << fboID);
  }
  else
  {
    XLOG_WARN(g_EngineContext, "engine try to set default fbo id to" << fboID <<"but graphic context is nullptr");
  }

  return 0;
}

int QcEngine::setOESTextureIds(vfc::int32_t f_num, vfc::int32_t *f_oesTextureIds)
{
  if(f_oesTextureIds==nullptr || f_num<cc::target::sysconf::E_SYSTEM_CONFIG_NUMBER_OF_CAMERAS_TEXTURE) // f_num<0
  {
    m_currentOESTextureNum = 0;
    return -1;
  }

  //check isAllOESTextureID vaild
  for(vfc::uint32_t idx = 0; idx < f_num; idx++)
  {
    if(f_oesTextureIds[idx] <= 0)
    {
      m_currentOESTextureNum = 0;
      return -1;
    }
  }

  VideoTextureType* const l_VideoTexture = static_cast<VideoTextureType*>(m_videoTexture.get());
  if(nullptr != l_VideoTexture)
  {
    for (unsigned int l_cam = 0; l_cam < cc::target::sysconf::E_SYSTEM_CONFIG_NUMBER_OF_CAMERAS_TEXTURE; ++l_cam)
    {
      l_VideoTexture->updateAndroidTextureId(l_cam, f_oesTextureIds[l_cam]);
    }
  }

  m_currentOESTextureNum = (f_num <= 4)? f_num:4;
  for(vfc::int32_t idx = 0; idx < f_num; idx++)
  {
    m_currentOESTextureIDArray[idx] = f_oesTextureIds[idx];
  }

  return 0;
}

void QcEngine::toggleVideoTextures()
{
  //XLOG_INFO_OS(g_EngineContext) << "QcEngine: toggleVideoTextures" << XLOG_ENDL;

  //create the degradation mask
  static pc::daddy::daddy4bitfield l_CameraDegradationMask = 0;


#if 0  //test--------test

  pc::daddy::setBit( pc::core::sysconf::FRONT_CAMERA, l_CameraDegradationMask);
  pc::daddy::setBit( pc::core::sysconf::RIGHT_CAMERA, l_CameraDegradationMask);
  pc::daddy::setBit( pc::core::sysconf::REAR_CAMERA, l_CameraDegradationMask);
  pc::daddy::setBit( pc::core::sysconf::LEFT_CAMERA, l_CameraDegradationMask);

  //give the degradationMask to daddy
  pc::daddy::CameraDegradationMaskDaddy & l_refCamDegMsk =
    pc::daddy::BaseDaddyPorts::sm_CameraDegradationMaskDaddySenderPort.reserve();
  l_refCamDegMsk.m_Data = l_CameraDegradationMask;
  pc::daddy::BaseDaddyPorts::sm_CameraDegradationMaskDaddySenderPort.deliver();

  return;

#endif

  VideoTextureType* const l_VideoTexture = static_cast<VideoTextureType*>(m_videoTexture.get());

  if (cc::target::sysconf::E_SYSTEM_CONFIG_NUMBER_OF_CAMERAS_TEXTURE == 4)
  {
    // implementation for the 4 camera configuration
    //iterate over the 4 cameras
    for (unsigned int l_cam = 0; l_cam < cc::target::sysconf::E_SYSTEM_CONFIG_NUMBER_OF_CAMERAS_TEXTURE; ++l_cam)
    {
      constexpr unsigned int l_currIndex     = cc::target::sysconf::E_SYSTEM_CONFIG_INPUT_BUFFERS_PER_CAM;
      // std::cout<< " l_currIndex " << l_currIndex <<std::endl;
      // XLOG_INFO_OS(g_EngineContext) << "QcEngine: l_cam: " << l_cam << " currIndex: " << f_currBufferIndex[l_cam].idx << XLOG_ENDL;

      unsigned int l_currIndex4Tex = cc::target::sysconf::E_SYSTEM_CONFIG_INPUT_BUFFERS_PER_CAM;
      // if we get an error(index==E_SYSTEM_CONFIG_INPUT_BUFFERS_PER_CAM) the blackbuffer has to be used
      if (l_currIndex == cc::target::sysconf::E_SYSTEM_CONFIG_INPUT_BUFFERS_PER_CAM)
      {
          //we put the black texture buffer in the sm_CameraDegradationMaskDaddySenderPort so the buffer is deactivated and just shows black
        l_currIndex4Tex = cc::target::sysconf::E_SYSTEM_CONFIG_INPUT_BUFFERS_PER_CAM * cc::target::sysconf::E_SYSTEM_CONFIG_NUMBER_OF_CAMERAS_TEXTURE;
        XLOG_WARN(g_EngineContext, "black texture will be used for cam");

        //calculate the degradation mask
        switch (l_cam)
        {
          case pc::core::sysconf::FRONT_CAMERA:
          {
            pc::daddy::setBit( pc::core::sysconf::FRONT_CAMERA, l_CameraDegradationMask);
            break;
          }
          case pc::core::sysconf::RIGHT_CAMERA:
          {
            pc::daddy::setBit( pc::core::sysconf::RIGHT_CAMERA, l_CameraDegradationMask);
            break;
          }
          case pc::core::sysconf::REAR_CAMERA:
          {
            pc::daddy::setBit( pc::core::sysconf::REAR_CAMERA, l_CameraDegradationMask);
            break;
          }
          case pc::core::sysconf::LEFT_CAMERA:
          {
            pc::daddy::setBit( pc::core::sysconf::LEFT_CAMERA, l_CameraDegradationMask);
            break;
          }
          default:
          {
            break;
          }
        }
      }
      else
      {
        // calculation of the offset of the buffer
        // cam0 [buf0, buf1, buf2, buf3] cam1 [buf0, buf1, buf2, buf3] cam2 [buf0, buf1, buf2, buf3] cam3 [buf0, buf1, buf2, buf3]
        // cam0 [buf0, buf1, buf2, buf3, buf4] cam1 [buf0, buf1, buf2, buf3, buf4] cam2 [buf0, buf1, buf2, buf3, buf4] cam3 [buf0, buf1, buf2, buf3, buf4]
        l_currIndex4Tex = (l_cam*cc::target::sysconf::E_SYSTEM_CONFIG_INPUT_BUFFERS_PER_CAM) + l_currIndex;

        switch (l_cam)
        {
          case pc::core::sysconf::FRONT_CAMERA:
          {
            pc::daddy::unsetBit( pc::core::sysconf::FRONT_CAMERA, l_CameraDegradationMask);
            break;
          }
          case pc::core::sysconf::RIGHT_CAMERA:
          {
            pc::daddy::unsetBit( pc::core::sysconf::RIGHT_CAMERA, l_CameraDegradationMask);
            break;
          }
          case pc::core::sysconf::REAR_CAMERA:
          {
            pc::daddy::unsetBit( pc::core::sysconf::REAR_CAMERA, l_CameraDegradationMask);
            break;
          }
          case pc::core::sysconf::LEFT_CAMERA:
          {
            pc::daddy::unsetBit( pc::core::sysconf::LEFT_CAMERA, l_CameraDegradationMask);
            break;
          }
          default:
          {
            break;
          }
        }
        //toggle the texture ids for the application
        //std::cout<< " l_currIndex4Tex " << l_currIndex4Tex <<std::endl;
        //printf("camera: %d  will use eglimage %d \n", l_cam,  l_currIndex4Tex);
        l_VideoTexture->updateEGLImageHandle(l_cam, m_eglKHRImageBuffer.at(l_currIndex4Tex).m_eglImage);

      }

#if 0
      {
          int l_rc = 0;
          char     File_Name[30];
          FILE     *File_Handle;

          sprintf(File_Name, "engine_ptr_%01d_.raw", l_currIndex4Tex);

          void* l_pixBuffer;
          l_rc = screen_get_pixmap_property_pv(*(m_screenPixmapCamList->at(l_currIndex4Tex)), SCREEN_PROPERTY_BUFFERS, &l_pixBuffer);
          if (!l_rc)
          {
            unsigned char *ptr = NULL;

            l_rc = screen_get_buffer_property_pv((screen_buffer_t)l_pixBuffer, SCREEN_PROPERTY_POINTER, (void **)&ptr);
            File_Handle=fopen(File_Name, "wb");
            if (NULL != File_Handle)
            {
               fwrite(ptr, 2, 1340 * 1020, File_Handle);
               fclose(File_Handle);
            }
          }
      }
#endif

      //give the degradationMask to daddy
      pc::daddy::CameraDegradationMaskDaddy & l_refCamDegMsk =
        pc::daddy::BaseDaddyPorts::sm_CameraDegradationMaskDaddySenderPort.reserve();
      l_refCamDegMsk.m_Data = l_CameraDegradationMask;
      pc::daddy::BaseDaddyPorts::sm_CameraDegradationMaskDaddySenderPort.deliver();
    }
  }
  else if (cc::target::sysconf::E_SYSTEM_CONFIG_NUMBER_OF_CAMERAS_TEXTURE == 1)
  {
    unsigned int l_currIndex     = cc::target::sysconf::E_SYSTEM_CONFIG_INPUT_BUFFERS_PER_CAM;
    unsigned int l_currIndex4Tex = 0;
    // if we get an error(index==E_SYSTEM_CONFIG_INPUT_BUFFERS_PER_CAM) the blackbuffer has to be used
    if (l_currIndex == cc::target::sysconf::E_SYSTEM_CONFIG_INPUT_BUFFERS_PER_CAM)
    {
      //we put the black texture buffer in the sm_CameraDegradationMaskDaddySenderPort so the buffer is deactivated and just shows black
      l_currIndex4Tex = cc::target::sysconf::E_SYSTEM_CONFIG_INPUT_BUFFERS_PER_CAM;
      XLOG_WARN(g_EngineContext, "black texture will be used for all cam");
      // degrade all the Cameras
      pc::daddy::setBit( pc::core::sysconf::FRONT_CAMERA, l_CameraDegradationMask);
      pc::daddy::setBit( pc::core::sysconf::RIGHT_CAMERA, l_CameraDegradationMask);
      pc::daddy::setBit( pc::core::sysconf::REAR_CAMERA, l_CameraDegradationMask);
      pc::daddy::setBit( pc::core::sysconf::LEFT_CAMERA, l_CameraDegradationMask);
    }
    else
    {
      // calculation of the offset of the buffer
      // cam0 [buf0, buf1, buf2, buf3] cam1 [buf0, buf1, buf2, buf3] cam2 [buf0, buf1, buf2, buf3] cam3 [buf0, buf1, buf2, buf3]
      // cam0 [buf0, buf1, buf2, buf3, buf4] cam1 [buf0, buf1, buf2, buf3, buf4] cam2 [buf0, buf1, buf2, buf3, buf4] cam3 [buf0, buf1, buf2, buf3, buf4]
      l_currIndex4Tex = l_currIndex;
      pc::daddy::unsetBit( pc::core::sysconf::FRONT_CAMERA, l_CameraDegradationMask);
      pc::daddy::unsetBit( pc::core::sysconf::RIGHT_CAMERA, l_CameraDegradationMask);
      pc::daddy::unsetBit( pc::core::sysconf::REAR_CAMERA, l_CameraDegradationMask);
      pc::daddy::unsetBit( pc::core::sysconf::LEFT_CAMERA, l_CameraDegradationMask);

      //toggle the texture ids for the application
      l_VideoTexture->updateEGLImageHandle(0, m_eglKHRImageBuffer.at(l_currIndex4Tex).m_eglImage);
    }

    //give the degradationMask to daddy
    pc::daddy::CameraDegradationMaskDaddy & l_refCamDegMsk = pc::daddy::BaseDaddyPorts::sm_CameraDegradationMaskDaddySenderPort.reserve();
    l_refCamDegMsk.m_Data = l_CameraDegradationMask;
    pc::daddy::BaseDaddyPorts::sm_CameraDegradationMaskDaddySenderPort.deliver();
  }
  else
  {
     XLOG_ERROR_OS(g_EngineContext) << "other use case than 4 cameras not yet supported for qualcomm" << XLOG_ENDL;
    // degrade all the Cameras
    pc::daddy::setBit( pc::core::sysconf::FRONT_CAMERA, l_CameraDegradationMask);
    pc::daddy::setBit( pc::core::sysconf::RIGHT_CAMERA, l_CameraDegradationMask);
    pc::daddy::setBit( pc::core::sysconf::REAR_CAMERA, l_CameraDegradationMask);
    pc::daddy::setBit( pc::core::sysconf::LEFT_CAMERA, l_CameraDegradationMask);

    //give the degradationMask to daddy
    pc::daddy::CameraDegradationMaskDaddy & l_refCamDegMsk =
      pc::daddy::BaseDaddyPorts::sm_CameraDegradationMaskDaddySenderPort.reserve();
    l_refCamDegMsk.m_Data = l_CameraDegradationMask;
    pc::daddy::BaseDaddyPorts::sm_CameraDegradationMaskDaddySenderPort.deliver();
  }
}


/// @deviation NRCS2_005
/// Rule QACPP-4.3.0-6040
/// message: "Do not write functions with an excessive MCCABE Cyclomatic Complexity."
void QcEngine::OnIterate()  // PRQA S 6040
{
#ifdef LONGTIMRTEST
  l_tick ++;

  if(l_tick % 10 == 0)
  {
    std::cout<< " ========= start to send steering && gear signal ========= "<< l_tick <<std::endl;
  }

  if(l_tick % 100 == 0)
  {
    l_flag = ! l_flag;
  }

  if(l_flag)
  {
    //Rear for view
    cc::daddy::HmiData_Daddy& l_vmc = cc::daddy::CustomDaddyPorts::sm_HmiData_SenderPort.reserve();
              l_vmc.m_Data.m_modeRq = 2;
           l_vmc.m_Data.m_hardKeyRq = valin_cfg::PARK_CTRL_HARD_KEY_NPSD;

    cc::daddy::CustomDaddyPorts::sm_HmiData_SenderPort.deliver();

    //Rear for gear
    pc::daddy::GearDaddy& l_gear = pc::daddy::BaseDaddyPorts::sm_gearSenderPort.reserve();
                   l_gear.m_Data = 11;

    pc::daddy::BaseDaddyPorts::sm_gearSenderPort.deliver();

    //Rear for steering angle
    pc::daddy::SteeringAngleDaddy& l_steeringAngleDaddy = \
                                    pc::daddy::BaseDaddyPorts::sm_SteeringAngleRearDaddySenderPort.reserve();
                            l_steeringAngleDaddy.m_Data = \
                                    vfc::CSI::si_degree_f32_t(10);

    pc::daddy::BaseDaddyPorts::sm_SteeringAngleRearDaddySenderPort.deliver();
  }
  else
  {
    //Front for view
    cc::daddy::HmiData_Daddy& l_vmc = cc::daddy::CustomDaddyPorts::sm_HmiData_SenderPort.reserve();
              l_vmc.m_Data.m_modeRq = 1;
           l_vmc.m_Data.m_hardKeyRq = valin_cfg::PARK_CTRL_HARD_KEY_NPSD;

    cc::daddy::CustomDaddyPorts::sm_HmiData_SenderPort.deliver();

    //Front for gear
    pc::daddy::GearDaddy& l_gear = pc::daddy::BaseDaddyPorts::sm_gearSenderPort.reserve();
                   l_gear.m_Data = 13;

    pc::daddy::BaseDaddyPorts::sm_gearSenderPort.deliver();

    //Front for steering angle
    pc::daddy::SteeringAngleDaddy& l_steeringAngleDaddy = \
                                    pc::daddy::BaseDaddyPorts::sm_SteeringAngleFrontDaddySenderPort.reserve();
                            l_steeringAngleDaddy.m_Data = \
                                    vfc::CSI::si_degree_f32_t(15);

    pc::daddy::BaseDaddyPorts::sm_SteeringAngleFrontDaddySenderPort.deliver();
  }
#endif

#ifdef HUCOOR

  //Get HU coor based on QNX Screen interface API
  m_screen_ctx = getQnxScreenContext();

  screen_create_event(&m_screen_ev);

  screen_get_event(m_screen_ctx, m_screen_ev, 5000000);//wait for 10ms

  int l_type;

  screen_get_event_property_iv(m_screen_ev, SCREEN_PROPERTY_TYPE, &l_type);

  if(SCREEN_EVENT_MTOUCH_TOUCH==l_type)
  {
    screen_get_event_property_iv(m_screen_ev, SCREEN_PROPERTY_POSITION, m_cur_pos);

    std::cout<<"Current X position is :"<<m_cur_pos[0]<<std::endl;
    std::cout<<"Current Y position is :"<<m_cur_pos[1]<<std::endl;

    m_view = static_cast<eVIEW>(setViewBasedonHuCoor(m_cur_pos[0],m_cur_pos[1]));

    std::cout<<"Current view is :"      <<m_view<<std::endl;
  }

  cc::daddy::HmiData_Daddy& l_vmc = cc::daddy::CustomDaddyPorts::sm_HmiData_SenderPort.reserve();

  switch (m_view)
  {
  case FRONTVIEW:
  {
    //Front view
    l_vmc.m_Data.m_modeRq = 1;
    break;
  }
  case REARVIEW:
  {
    //Rear view
    l_vmc.m_Data.m_modeRq = 2;
    break;
  }
  default:
  {
    break;
  }
  }

  l_vmc.m_Data.m_hardKeyRq = valin_cfg::PARK_CTRL_HARD_KEY_NPSD;

  cc::daddy::CustomDaddyPorts::sm_HmiData_SenderPort.deliver();

#endif

  // // XLOG_DEBUG_OS(g_EngineContext) << "QcEngine " << "OnIterate" << " start " << XLOG_ENDL;
  // pc::svs::qualcomm::QcarCamFrameDataInfoDaddy l_frameInfocpy;
  // //! thread has been activated from the grabber thread
  // //! get the current texture ID from the grabber
  // assert(m_frameDataInReceiver.isConnected());

  // m_bEngineIsBusy = true;

  // pc::svs::qualcomm::QcengineFlagInfoDaddy& l_engineFlag = \
  //                                           pc::svs::qualcomm::QCarcamDaddyPorts::sm_engineFlagSenderPort.reserve();

  //               l_engineFlag.m_Data.m_bEngineIsBusyDaddy        = m_bEngineIsBusy;
  //               l_engineFlag.m_Data.m_bEngineIsInitializedDaddy = m_initialized;

  // pc::svs::qualcomm::QCarcamDaddyPorts::sm_engineFlagSenderPort.deliver();

  //get the graphic context
  osg::QcGraphicsContext* const l_pGC = dynamic_cast<osg::QcGraphicsContext*> (getGraphicsContext());

/***********hnd2hi***************************/
  l_pGC->makeCurrentImplementation();
/********************************************/

#if POCTEST
   static int loopIndex = 0;
   //if(loopIndex < 50)
   {
      loopIndex++;
      glViewport(0, 0, 1920, 1080);
      //glDisable(GL_SCISSOR_TEST);
      glClearColor(0.0f, 1.0f, 0.0f, 1.0f);
      glClear(GL_COLOR_BUFFER_BIT);
      XLOG_ERROR_OS(g_EngineContext)  << "QcEngine::OnIterate() -------------------------------------  Rendering in Loop---1st---frame---render--a--green--screen---before !" << XLOG_ENDL;
      //l_pGC->swapBuffers();
      XLOG_ERROR_OS(g_EngineContext)  << "QcEngine::OnIterate() -------------------------------------  Rendering in Loop---1st---frame---render--a--green--screen !" << XLOG_ENDL;
      //usleep(500*1000);

        glGenTextures(1, &khrTextureId);
        glBindTexture(GL_TEXTURE_EXTERNAL_OES, khrTextureId);
        glTexParameteri(GL_TEXTURE_EXTERNAL_OES, GL_TEXTURE_MIN_FILTER, GL_LINEAR);
        glTexParameteri(GL_TEXTURE_EXTERNAL_OES, GL_TEXTURE_MAG_FILTER, GL_LINEAR);
        glEGLImageTargetTexture2DOES(GL_TEXTURE_EXTERNAL_OES, (GLeglImageOES)m_eglKHRImageBuffer[loopIndex%4].m_eglImage);


        //draw the eglimage
        glUseProgram(programObject);

        glBindTexture(GL_TEXTURE_EXTERNAL_OES, khrTextureId);
        glActiveTexture(GL_TEXTURE0);
        glUniform1i(0,0);

        glBindBuffer(GL_ARRAY_BUFFER, vertexID);
        glVertexAttribPointer( positionLoc, 3, GL_FLOAT, GL_FALSE, 3 * sizeof(GLfloat), 0 );
        glEnableVertexAttribArray(positionLoc);

        // Load the texture coordinate
        glBindBuffer(GL_ARRAY_BUFFER, texcoordID);
        glVertexAttribPointer( texcoordLoc, 2, GL_FLOAT, GL_FALSE, 2 * sizeof(GLfloat), 0 );
        glEnableVertexAttribArray(texcoordLoc);

        glDrawArrays( GL_TRIANGLES, 0, 6 );

        l_pGC->swapBuffers();
        //usleep(500*1000);

        glDisableVertexAttribArray(positionLoc);
        glDisableVertexAttribArray(texcoordLoc);
        glDeleteTextures(1, &khrTextureId);
   }
#endif

  // if (l_pGC == nullptr)
  // {
  //   XLOG_FATAL_OS(g_EngineContext) << "No valid graphic context" << XLOG_ENDL;
  //   return;
  // }

  // m_frameDataInReceiver.update();

  // const FrameDataReceiver_t::PortDataContainer_t l_pFrameDataInContainer = m_frameDataInReceiver.getData();
  // //for (int i=0; i<l_pFrameDataInContainer.size(); i++)
  // //{
  // //  XLOG_INFO_OS(g_EngineContext) << "Received new frame data seq. no:  " << l_pFrameDataInContainer[i]->m_sequenceNumber << XLOG_ENDL;
  // //}
  // //sanity checking
  // if ( 0 == l_pFrameDataInContainer.size() )
  // {
  //   XLOG_FATAL_OS(g_EngineContext) << "FrameDataInfo unavailable" << XLOG_ENDL;
  //   assert(l_pFrameDataInContainer.size() == 0);
  //   exit(-1);
  // }
  // // XLOG_INFO_OS(g_EngineContext) << "FrameDataInfo available" << XLOG_ENDL;
  // const pc::svs::qualcomm::QcarCamFrameDataInfoDaddy* l_pFrameDataInfo = l_pFrameDataInContainer.back();

  // if ( l_pFrameDataInfo->m_sequenceNumber == m_lastSequenceNumber )
  // {
  //   XLOG_ERROR_OS(g_EngineContext)  << "Got same Daddy FrameDataInfo container from FPGA Grabber !"
  //                                   << "\n Render frame seq. number: " << l_pFrameDataInfo->m_sequenceNumber
  //                                   // << "\n Frame time: "  << (l_pFrameDataInfo->m_Data.m_frameInTime)
  //                                   // << "\n Engine time: " << chrono_ms()
  //                                   << XLOG_ENDL;
  // }
  // else
  // {
  //   if ( l_pFrameDataInfo->m_sequenceNumber > m_lastSequenceNumber + 1 )
  //   {
  //     const int l_missedContainers = l_pFrameDataInfo->m_sequenceNumber - ( m_lastSequenceNumber + 1 ) ;

  //     XLOG_WARN_OS(g_EngineContext) << "Missed " << l_missedContainers
  //       << " frame(s) from qcarcam muxer!" << XLOG_ENDL;
  //   }

  //   //if vidout buffer free, then render the frame
  //   toggleVideoTextures(l_pFrameDataInfo->m_Data.m_vidInBufferData);
  //   // XLOG_DEBUG_OS(g_EngineContext) << "Render frame seq. number: " << l_pFrameDataInfo->m_sequenceNumber
  //   //                               << "\n Frame time [ms]: "        << l_pFrameDataInfo->m_Data.m_frameInTime
  //   //                               << "\n Engine begin time [ms]: " << chrono_ms()
  //   //                               << XLOG_ENDL;
  // }

  // for (unsigned int l_cam = 0; l_cam < cc::target::sysconf::E_SYSTEM_CONFIG_NUMBER_OF_CAMERAS_TEXTURE; l_cam++)
  // {
  //   // XLOG_DEBUG_OS(g_EngineContext) << "input img. seq. number " << l_pFrameDataInfo->m_Data.m_vidInBufferData[l_cam].seq_no << XLOG_ENDL;
  //   //copy the daddy content for frame release
  //   l_frameInfocpy.m_Data.m_vidInBufferData[l_cam] = l_pFrameDataInfo->m_Data.m_vidInBufferData[l_cam];
  // }

  // m_frameDataInReceiver.cleanup();

  //toggleVideoTextures(l_pFrameDataInfo->m_Data.m_vidInBufferData);

  // static int loop = 0;
  // loop++;
  // glClearColor((loop%200)/200.0f, (loop%200)/200.0f, 0.0f, 1.0f);
  // glClear(GL_COLOR_BUFFER_BIT);

#if 1
  //XLOG_FATAL_OS(g_EngineContext) << "toggleVideoTextures ------------ start" << XLOG_ENDL;
  //toggleVideoTextures();
  //XLOG_FATAL_OS(g_EngineContext) << "toggleVideoTextures ------------ end" << XLOG_ENDL;

  //View ID process
  {
    m_HUDislayModeSwitchSMReceiver.update();
    m_cpcToSvsOverlaySMReceiver.update();
    cc::daddy::PlanViewEnlargeStatusDaddy& l_planViewEnlargeStatusContainer = cc::daddy::CustomDaddyPorts::sm_planViewEnlargeStatus_SenderPort.reserve();
    cc::daddy::SVSDisplayedViewDaddy_t& l_rSVSDisplayedViewContainer = cc::daddy::CustomDaddyPorts::sm_SVSDisplayedViewDaddy_SenderPort.reserve() ;

    static vfc::int32_t l_NewMode = EScreenID_NO_CHANGE;

    if (m_HUDislayModeSwitchSMReceiver.isConnected() && m_HUDislayModeSwitchSMReceiver.hasData())
    {
        const cc::daddy::HUDislayModeSwitchDaddy_t* const l_pData = m_HUDislayModeSwitchSMReceiver.getData();
        l_NewMode = (EScreenID)l_pData->m_Data;
        // XLOG_INFO_OS( g_AppContext ) << "Setting DislayModeSwitch to: !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!"<< static_cast<vfc::int32_t> (l_pData->m_Data) << XLOG_ENDL;//PRQA S 4060
    }

    if (m_cpcToSvsOverlaySMReceiver.isConnected() && m_cpcToSvsOverlaySMReceiver.hasData())
    {
        const cc::daddy::CpcToSvsOverlay_t* const l_pData = m_cpcToSvsOverlaySMReceiver.getData();
        if (false == l_pData->m_Data.m_isQuit)
        {
            l_NewMode = EScreenID_QUAD_RAW;
        }
    }

    l_rSVSDisplayedViewContainer.m_Data = static_cast<EScreenID>(l_NewMode);

    // IMGUI_LOG("ViewModeStateMachine", "CurrentScreenID", std::to_string(l_NewMode) + " - " + cc::sm::getViewName(static_cast<EScreenID>(l_NewMode)));

    switch (l_NewMode)
    {
        case EScreenID_VERT_PLAN_FRONT_ENLARGE__FRONT_MAIN__LEFT_SIDE:
        case EScreenID_VERT_PLAN_FRONT_ENLARGE__FRONT_MAIN__RIGHT_SIDE:
        case EScreenID_VERT_FULLSCREEN_FRONT_ENLARGE:
        case EScreenID_MODEL_F_VIEW_ENLARGEMENT:
        case EScreenID_FULLSCREEN_FRONT_ENLARGE:
        case EScreenID_IMAGE_IN_IMAGE_PLANVIEW_FRONT_ENLARGE:
        {
            l_planViewEnlargeStatusContainer.m_Data = cc::daddy::ENLARGE_FRONT; break;
        }
        case EScreenID_VERT_PLAN_REAR_ENLARGE__REAR_MAIN__LEFT_SIDE:
        case EScreenID_VERT_PLAN_REAR_ENLARGE__REAR_MAIN__RIGHT_SIDE:
        case EScreenID_VERT_FULLSCREEN_REAR_ENLARGE:
        case EScreenID_MODEL_B_VIEW_ENLARGEMENT:
        case EScreenID_FULLSCREEN_REAR_ENLARGE:
        case EScreenID_IMAGE_IN_IMAGE_PLANVIEW_REAR_ENLARGE:
        {
            l_planViewEnlargeStatusContainer.m_Data = cc::daddy::ENLARGE_REAR; break;
        }
        default:
        {
            l_planViewEnlargeStatusContainer.m_Data = cc::daddy::NO_ENLARGE; break;
        }
    }

    if ((l_NewMode != m_CurrMode) && (l_NewMode != EScreenID_NO_CHANGE))
    {
        pc::daddy::ViewModeDaddy& l_vm = pc::daddy::BaseDaddyPorts::sm_viewModeSenderPort.reserve();
        l_vm.m_Data = pc::daddy::ViewMode(m_CurrMode, l_NewMode);
        pc::daddy::BaseDaddyPorts::sm_viewModeSenderPort.deliver();
        m_CurrMode = l_NewMode;
    }
    else if (m_HUDislayModeSwitchSMReceiver.hasNewData())
    {
        if (cc::core::isSurroundView(l_NewMode) && !cc::core::isFullscreenSurroundView(l_NewMode) && (l_NewMode != EScreenID_NORMAL3D_KEEP))
        {
            pc::daddy::ViewModeDaddy& l_vm = pc::daddy::BaseDaddyPorts::sm_viewModeSenderPort.reserve();
            l_vm.m_Data = pc::daddy::ViewMode(m_CurrMode, l_NewMode);
            pc::daddy::BaseDaddyPorts::sm_viewModeSenderPort.deliver();
        }
    }
    else
    {
    }

    cc::daddy::CustomDaddyPorts::sm_planViewEnlargeStatus_SenderPort.deliver();
    cc::daddy::CustomDaddyPorts::sm_SVSDisplayedViewDaddy_SenderPort.deliver();
    m_HUDislayModeSwitchSMReceiver.cleanup();
    m_cpcToSvsOverlaySMReceiver.cleanup();
  }

//   XLOG_INFO(g_AppContext, "DEBUG MEMPOOL beginTimeEngine is " << static_cast<vfc::int32_t>(l_beginTimeEngine));
  #if OSG_TRAVERSALS_DEBUG
  int64_t l_beginTime = chrono_ms();
  int64_t l_beginTimeEngine= chrono_ms();
  XLOG_INFO_OS(g_AppContext) <<"[svs]: l_beginTimeEngine is " <<static_cast<vfc::int32_t>(l_beginTimeEngine) << XLOG_ENDL;
  #endif
  l_pGC->customClear();
  m_framework->preFrame();
  m_viewer->advance();
  m_viewer->eventTraversal(); // makes sure framework is aware of current active screen id
  m_viewer->updateTraversal();
  m_viewer->renderingTraversals();  //flush
  #if OSG_TRAVERSALS_DEBUG
  XLOG_WARN_OS(g_EngineContext) << "rendering CPU duration: " << chrono_ms() - l_beginTime << " ms!" << XLOG_ENDL;
  #endif
  m_framework->postFrame();

  #if OSG_TRAVERSALS_DEBUG
  int64_t l_afterEngine = chrono_ms();
  XLOG_INFO_OS(g_AppContext) <<"[svs]: l_afterEngine is " <<static_cast<vfc::int32_t>(l_afterEngine) << XLOG_ENDL;
  #endif
#endif

  //CheckError(__LINE__);

  // for debug------------------------ dump pixels
  // int l_currentFBOId = -1;
  // glGetIntegerv(GL_FRAMEBUFFER_BINDING, &l_currentFBOId);
  // XLOG_INFO_OS(g_EngineContext) << "avm calibration data dump, current framebuffer index =  "<< l_currentFBOId << XLOG_ENDL;
  //   static int dumploop = 0;
  //   dumploop++;
  //   if(loop%100 == 0)
  //   {
  //       //dump to file
  //       int l_bufferWidth = 720;
  //       int l_bufferHeight = 1920;
  //       GLubyte *l_pixels = (GLubyte *)malloc(l_bufferWidth*l_bufferHeight*4);//rgb888
  //       if(l_pixels)
  //       {
  //           glFinish();
  //           char l_filename[128];
  //           glPixelStorei(GL_PACK_ALIGNMENT, 1);
  //           glReadPixels(0, 0, l_bufferWidth, l_bufferHeight, GL_RGBA, GL_UNSIGNED_BYTE, l_pixels);
  //           glPixelStorei(GL_PACK_ALIGNMENT, 4);
  //           sprintf(l_filename, "%s/osg_%dx%d_rgba888_%d.raw", "./",l_bufferWidth, l_bufferHeight, dumploop);
  //           dumpFile(l_filename, l_pixels, l_bufferWidth*l_bufferHeight*4);
  //           free(l_pixels);
  //       }
  //   }



  // XLOG_INFO_OS(g_EngineContext) << "postframe-----------------postframe" << XLOG_ENDL;

  CheckError(__LINE__);
  //m_lastSequenceNumber = l_pFrameDataInfo->m_sequenceNumber;


  //glFinish();
  // static int frameindex = 0;
  // frameindex++;
  // if(frameindex%90 == 0  && frameindex <= 500)
  // {
  //   std::vector<unsigned char> l_pixelVec;
  //   l_pixelVec.resize(2560*1320*4);
  //   glReadPixels(0, 0, 2560, 1320, GL_RGBA, GL_UNSIGNED_BYTE, &l_pixelVec[0]);
  //   char filename[256];
  //   //sprintf(filename, "/data/data/com.myosg.osgcarpaintviewer/avm_pbufsurface_2560x1320_rgba8888_%d.raw", frameindex);
  //   sprintf(filename, "/data/data/com.bosch.svsapp/avm_pbufsurface_2560x1320_rgba8888_%d.raw", frameindex);
  //   FILE *fp = fopen(filename, "wb");
  //   if(fp)
  //   {
  //     fwrite(&l_pixelVec[0], 1, 2560*1320*4, fp);
  //     fclose(fp);
  //     XLOG_FATAL_OS(g_EngineContext) << "postframe-----------------succeed to dump to:"<< filename << XLOG_ENDL;
  //   }
  //   else
  //   {
  //     XLOG_FATAL_OS(g_EngineContext) << "postframe-----------------fail to open: "<<filename << XLOG_ENDL;
  //   }
  // }


  if(m_dumpCameraBuffer)
  {
    if(m_currentOESTextureNum > 0)
    {
      //mutex is not need
      m_dumpCameraBuffer = 0;

      XLOG_INFO_OS(g_EngineContext) << "try to dump avm cpc " << XLOG_ENDL;
      doGenerateCPCBufferFromOESTexture(m_currentOESTextureIDArray[0]);
    }
    else
    {
      XLOG_FATAL_OS(g_EngineContext) << "try to dump avm cpc, but not a vaild texutre 2d id " << XLOG_ENDL;
    }
  }


  // if(m_dumpCameraBuffer)
  // {
  //   //mutex is not need
  //   m_dumpCameraBuffer = 0;

  //   for (unsigned int l_cam = 0; l_cam < cc::target::sysconf::E_SYSTEM_CONFIG_NUMBER_OF_CAMERAS_TEXTURE; ++l_cam)
  //   {
  //     char l_filename[128];
  //     uint32_t l_bufferWidth = l_frameInfocpy.m_Data.m_vidInBufferData[l_cam].bufferInfo->bufferWidth;
  //     uint32_t l_bufferHeight = l_frameInfocpy.m_Data.m_vidInBufferData[l_cam].bufferInfo->bufferHeight;
  //     void *l_bufferData = l_frameInfocpy.m_Data.m_vidInBufferData[l_cam].bufferInfo->bufferVaddr;
  //     uint32_t l_bufferSize = l_bufferWidth*l_bufferHeight*2;
  //     sprintf(l_filename, "%s/avmsv3d_cal_dump_cam_%u_%ux%u_uyvy.raw", cc::target::sysconf::g_AVMSv3dDataFolder, l_cam, l_bufferWidth, l_bufferHeight);
  //     FILE *l_fp = fopen(l_filename, "wb");
  //     if(l_fp)
  //     {
  //       uint32_t l_outputByteNum = 0;
  //       l_outputByteNum = fwrite(l_bufferData, 1, l_bufferSize, l_fp);
  //       fclose(l_fp);
  //       if(l_outputByteNum == l_bufferSize)
  //       {
  //         XLOG_INFO_OS(g_EngineContext) << "Succeed to dump cal_camera_buffer to file: "<< l_filename << XLOG_ENDL;
  //       }
  //       else
  //       {
  //         XLOG_WARN_OS(g_EngineContext) << "Succeed to dump cal_camera_buffer to file: "<< l_filename <<" ,but buf-size:" <<l_bufferSize<<" ,write-size:"<< l_outputByteNum << XLOG_ENDL;
  //       }
  //     }
  //     else
  //     {
  //       XLOG_ERROR_OS(g_EngineContext) << "Fail to create the cal_camera_buffer dump file: "<< l_filename << XLOG_ENDL;
  //     }
  //   }
  // }

  // //send the buffer back to the mux for release
  // for (unsigned int l_cam = 0; l_cam < cc::target::sysconf::E_SYSTEM_CONFIG_NUMBER_OF_CAMERAS_TEXTURE; ++l_cam)
  // {
  //   if ( true == pc::svs::qualcomm::QCarcamDaddyPorts::sm_frameDataOutSenderPort[l_cam].isConnected() )
  //   {
  //     pc::svs::qualcomm::QcarCamFrameDataInfoDaddy &l_frameInfo = pc::svs::qualcomm::QCarcamDaddyPorts::sm_frameDataOutSenderPort[l_cam].reserve();
  //     l_frameInfo.m_Data.m_vidInBufferData[0] = l_frameInfocpy.m_Data.m_vidInBufferData[l_cam];
  //     pc::svs::qualcomm::QCarcamDaddyPorts::sm_frameDataOutSenderPort[l_cam].deliver();
  //     XLOG_DEBUG_OS(g_EngineContext) << "sm_frameDataOutSenderPort " << l_cam << " to be freed" << XLOG_ENDL;
  //   } else {
  //     XLOG_ERROR_OS(g_EngineContext) << "sm_frameDataOutSenderPort " << l_cam << " is not connected!" << XLOG_ENDL;
  //   }
  // }
  // m_bEngineIsBusy = false;

  // pc::svs::qualcomm::QcengineFlagInfoDaddy& l_engineFlag_end = \
  //                                         pc::svs::qualcomm::QCarcamDaddyPorts::sm_engineFlagSenderPort.reserve();

  //             l_engineFlag_end.m_Data.m_bEngineIsBusyDaddy        = m_bEngineIsBusy;
  //             l_engineFlag_end.m_Data.m_bEngineIsInitializedDaddy = m_initialized;

  // pc::svs::qualcomm::QCarcamDaddyPorts::sm_engineFlagSenderPort.deliver();

  // XLOG_INFO_OS(g_EngineContext) << "OnIterate" << " leave" << XLOG_ENDL;
}


bool QcEngine::isNeedShowFisheyeImg()
{
  bool l_showFisheyeImg = false;
  int l_ret = 0;

  l_ret = access(m_flagForShowFisheyeImg, F_OK);
  if(0 == l_ret)
  {
    l_showFisheyeImg = true;
  }

  return l_showFisheyeImg;
}

int QcEngine::doInitForRenderingFisheyeImg()
{
    char vShaderStr[] = "#version 310 es \n"
                        "precision highp float;\n"
                        "layout(location=0) in vec4 a_position;\n"
                        "layout(location=1) in vec2 a_texCoord;\n"
                        "out vec2 v_texCoord;\n"
                        "void main()\n"
                        "{\n"
                        "     gl_Position = a_position;\n"
                        "     v_texCoord = a_texCoord;\n"
                        "}\n";

    //"uniform  samplerExternalOES s_texture;\n"
    //"uniform  __samplerExternal2DY2YEXT s_texture;\n"
    //"   gl_FragColor = texture2D(s_texture, v_texCoord);\n"
    char fShaderStr[] = "#version 310 es \n"
                        "#extension GL_OES_EGL_image_external_essl3 : require\n"
                        "// #extension GL_EXT_YUV_target : require \n"
                        "precision highp float;\n"
                        " //uniform  __samplerExternal2DY2YEXT s_texture;\n"
                        "layout(binding=0) uniform samplerExternalOES s_texture; \n"
                        "in vec2 v_texCoord;\n"
                        "out vec4 fragcolor;\n"
                        "void main()\n"
                        "{\n"
                        "   //yuvCscStandardEXT conv_standard = itu_601_full_range; \n"
                        "   //fragcolor = vec4(1.0);\n"
                        "   //fragcolor.rgb = yuv_2_rgb(texture(s_texture, v_texCoord).rgb, conv_standard); \n"
                        "   fragcolor = texture(s_texture, v_texCoord); \n"
                        "}\n";

    GLuint vertexShader = 0;
    GLuint fragmentShader = 0;
    GLint  linked = 0;

    // Load the vertex/fragment shaders
    vertexShader   = LoadShader(GL_VERTEX_SHADER, vShaderStr);
    fragmentShader = LoadShader(GL_FRAGMENT_SHADER, fShaderStr);

    // Create the program object
    m_ProgramForRenderFisheye = glCreateProgram();

    if (m_ProgramForRenderFisheye == 0)
    {
        return 0;
    }

    glAttachShader(m_ProgramForRenderFisheye, vertexShader);
    glAttachShader(m_ProgramForRenderFisheye, fragmentShader);

    // Link the program
    glLinkProgram(m_ProgramForRenderFisheye);

    // Check the link status
    glGetProgramiv(m_ProgramForRenderFisheye, GL_LINK_STATUS, &linked);

    if (linked == 0)
    {
        GLint infoLen = 0;

        glGetProgramiv(m_ProgramForRenderFisheye, GL_INFO_LOG_LENGTH, &infoLen);

        if (infoLen > 1)
        {
            char* const infoLog = (char*)malloc(sizeof(char) * infoLen);
            glGetProgramInfoLog(m_ProgramForRenderFisheye, infoLen, nullptr, infoLog);
            //fprintf(stdout, "%s\n", infoLog);
            XLOG_ERROR(g_EngineContext, infoLog);
            free(infoLog);
        }

        glDeleteProgram(m_ProgramForRenderFisheye);

        return EXIT_FAILURE;
    }

    // static GLint samplerLoc;
    // static GLint positionLoc;
    // static GLint texcoordLoc;
    // // Get the sampler location
    // samplerLoc = glGetUniformLocation(m_ProgramForRenderFisheye, "s_texture");
    // positionLoc = glGetAttribLocation(m_ProgramForRenderFisheye, "a_position");
    // texcoordLoc = glGetAttribLocation(m_ProgramForRenderFisheye, "a_texCoord");

    glGenBuffers(1, &m_VertexBufferIDForRenderFisheyeImg);
    glBindBuffer(GL_ARRAY_BUFFER, m_VertexBufferIDForRenderFisheyeImg);
    glBufferData(GL_ARRAY_BUFFER, sizeof(m_VertCoordForRenderFisheyeImg), m_VertCoordForRenderFisheyeImg, GL_STATIC_DRAW);

    glGenBuffers(1, &m_TextureBufferIDForRenderFisheyeImg);
    glBindBuffer(GL_ARRAY_BUFFER, m_TextureBufferIDForRenderFisheyeImg);
    glBufferData(GL_ARRAY_BUFFER, sizeof(m_TexCoordForRenderFisheyeImg), m_TexCoordForRenderFisheyeImg, GL_STATIC_DRAW);

    // We don't need the shaders anymore
    glDeleteShader(fragmentShader);
    glDeleteShader(vertexShader);

    m_isFisheyeRenderInited = true;

    return EXIT_SUCCESS;
}

int QcEngine::doDeinitForRenderingFisheyeImg()
{
    if(m_ProgramForRenderFisheye > 0)
    {
        glDeleteProgram(m_ProgramForRenderFisheye);
        m_ProgramForRenderFisheye = 0;
    }

    if(m_VertexBufferIDForRenderFisheyeImg > 0)
    {
        glDeleteBuffers(1, &m_VertexBufferIDForRenderFisheyeImg);
        m_VertexBufferIDForRenderFisheyeImg = 0;
    }

    if(m_TextureBufferIDForRenderFisheyeImg > 0)
    {
        glDeleteBuffers(1, &m_TextureBufferIDForRenderFisheyeImg);
        m_TextureBufferIDForRenderFisheyeImg = 0;
    }

    return 0;
}

int QcEngine::doGenerateCPCBufferFromOESTexture(GLuint f_textureId)
{
  GLint l_currentFBOId = -1;

  GLuint l_cpcFBOId = 0;
  GLuint l_cpcFBOTextureId = 0;
  constexpr int l_bufferWidth  = static_cast<unsigned int>(cc::target::sysconf::E_IMAGE_FORMAT_TEXTURE_WIDTH)*4;
  constexpr int l_bufferHeight = cc::target::sysconf::E_IMAGE_FORMAT_TEXTURE_HEIGHT;

  GLint status = -1;

  if(false == m_isFisheyeRenderInited)
  {
    doInitForRenderingFisheyeImg();
  }

  glGetIntegerv(GL_FRAMEBUFFER_BINDING, &l_currentFBOId);
  XLOG_INFO(g_EngineContext, "avm calibration data dump, current framebuffer index =  "<< l_currentFBOId);


  glGenFramebuffers(1, &l_cpcFBOId);
  glBindFramebuffer(GL_FRAMEBUFFER, l_cpcFBOId);

  glGenTextures(1, &l_cpcFBOTextureId);
  glBindTexture(GL_TEXTURE_2D, l_cpcFBOTextureId);
  glTexImage2D(GL_TEXTURE_2D, 0, GL_RGB, l_bufferWidth, l_bufferHeight, 0, GL_RGB, GL_UNSIGNED_BYTE, nullptr);
  glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_LINEAR);
  glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_LINEAR);
  glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_S, GL_CLAMP_TO_EDGE);
  glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_T, GL_CLAMP_TO_EDGE);
  glFramebufferTexture2D(GL_FRAMEBUFFER, GL_COLOR_ATTACHMENT0, GL_TEXTURE_2D, l_cpcFBOTextureId, 0);

  status = glCheckFramebufferStatus(GL_FRAMEBUFFER);
  if(GL_FRAMEBUFFER_COMPLETE != status)
  {
      XLOG_ERROR(g_EngineContext, "avm calibration data dump, fail to create the fbo for cpc dump");
  }
  else
  {
      XLOG_INFO(g_EngineContext, "avm calibration data dump, succeed to create the fbo for cpc dump");
  }

  glViewport(0, 0, l_bufferWidth, l_bufferHeight);
  glScissor(0, 0, l_bufferWidth, l_bufferHeight);

  glClearColor(0.0f, 1.0f, 0.0f, 1.0f);
  glClear(GL_COLOR_BUFFER_BIT); //GL_DEPTH_BUFFER_BIT

  //draw the eglimage
  glUseProgram(m_ProgramForRenderFisheye);

  glActiveTexture(GL_TEXTURE0);
  glBindTexture(GL_TEXTURE_EXTERNAL_OES, f_textureId);
  glUniform1i(0,0);

  glBindBuffer(GL_ARRAY_BUFFER, m_VertexBufferIDForRenderFisheyeImg);
  glEnableVertexAttribArray(0);//positionLoc
  glVertexAttribPointer(0, 4, GL_FLOAT, GL_FALSE, 0, nullptr);

  // Load the texture coordinate
  glBindBuffer(GL_ARRAY_BUFFER, m_TextureBufferIDForRenderFisheyeImg);
  glEnableVertexAttribArray(1);//texcoordLoc
  glVertexAttribPointer(1, 2, GL_FLOAT, GL_FALSE, 0, nullptr);

  glDrawArrays(GL_TRIANGLES, 0, 6);

  //dump to file
  GLubyte *const l_pixels = (GLubyte *)malloc(l_bufferWidth*l_bufferHeight*3);//rgb888
  if(l_pixels != nullptr)
  {
    std::array<char, 128> l_filename;
    glPixelStorei(GL_PACK_ALIGNMENT, 1);
    glReadPixels(0, 0, l_bufferWidth, l_bufferHeight, GL_RGB, GL_UNSIGNED_BYTE, l_pixels);
    glPixelStorei(GL_PACK_ALIGNMENT, 4);
    sprintf(l_filename.data(), "%s/avm_cpc_%dx%d_rgb888.raw", g_dataContainerToSvs.m_StrippedCpjValData.m_cpcDumpImagePath.c_str(),l_bufferWidth, l_bufferHeight);
    // sprintf(l_filename, "%s/avm_cpc_%dx%d_rgb888.raw", "/data/data/com.bosch.svsapp/tcc",l_bufferWidth, l_bufferHeight);

    dumpFile(l_filename.data(), l_pixels, l_bufferWidth*l_bufferHeight*3);
    XLOG_INFO(g_EngineContext, "avm calibration data dump, succeed to dump pixel data to file:"<< l_filename.data());
  }
  else
  {
    XLOG_ERROR(g_EngineContext, "avm calibration data dump, fail to allocate memory to save pixel data, won't dump");
  }

  glBindFramebuffer(GL_FRAMEBUFFER, l_currentFBOId);
  glDeleteFramebuffers(1, &l_cpcFBOId);
  glDeleteTextures(1, &l_cpcFBOTextureId);

  free(l_pixels);

  CheckError(__LINE__);

  return 0;
}


bool QcEngine::dumpPixmap(int f_pixmap, const char* f_filename) const
{
  const char* const l_isJPEG = std::strstr(f_filename,".jpg");

  const osg::QcGraphicsContext* const l_pGC = dynamic_cast<const osg::QcGraphicsContext*>(m_graphicsContext.get());
  if (nullptr != l_pGC)
  {
    XLOG_INFO(g_EngineContext, "dumping pixmap "<< f_pixmap);
    if (nullptr == l_isJPEG)
    {
// hnd2hi:      return l_pGC->dump_pixmap_raw(f_pixmap, f_filename);
    }
    else
    {
// hnd2hi:      return l_pGC->dump_pixmap_jpeg(f_pixmap, f_filename);
    }
  }

  return false;
}



void QcEngine::dumpCameras() const
{
#if 0 // hnd2hi
  assert(nullptr != m_fpgaGrabber);
  XLOG_INFO(g_EngineContext, "Requesting buffers dump");
  m_fpgaGrabber->dumpBuffer(-1);
#endif

  //the real dump operation will be done in QcEngine::OnIterate()
  //mutex is not need
  m_dumpCameraBuffer = 1;
}


void QcEngine::dumpCamera(int /*f_id*/) const
{
#if 0 // hnd2hi
  assert(nullptr != m_fpgaGrabber);
  XLOG_INFO(g_EngineContext, "Requesting buffer #" << f_id << " dump");
  m_fpgaGrabber->dumpBuffer(f_id);
#endif
}

bool QcEngine::dumpCustomWindow(
    const char*                                  f_filename,
    const pc::util::osgx::ReadPixelsCoordinates& f_readPixelsCoord) const
{
    return createDefaultDumpCustomWindow(f_filename, f_readPixelsCoord);
}


#if 0

pc::core::IOpenGLPreDrawCallCallback* QcEngine::createCustomGLPreRenderCallback(unsigned int f_width, unsigned int f_height) const
{
#if 0 // hnd2hi
  //create the additional zero copy tex and the callback for the openGL extension
  return new pc::target::qualcomm::OpenGLPreDrawCallCallback(m_framework.get(), f_width, f_height);
#else
  return NULL;
#endif
}

pc::core::IOpenGLPostDrawCallCallback* QcEngine::createCustomGLPostRenderCallback(pc::core::IOpenGLPostDrawCallCallback::ExportType f_formatType, unsigned int f_width, unsigned int f_height) const
{
#if 0 // hnd2hi
  return new pc::target::qualcomm::OpenGLPostDrawCallCallback(m_framework.get(), f_formatType, f_width, f_height);
#endif
  return NULL;
}

#endif

// pc::core::IChamaeleonPreDrawCallBack*  QcEngine::createChamaeleonPreRenderCallback(int f_width, int f_height) const
// {
//   return new ChamaeleonEGLPreDrawCallBack(m_framework.get(), f_width, f_height);
// }
//
// pc::core::IChamaeleonPostDrawCallBack* QcEngine::createChamaeleonPostRenderCallback() const
// {
//   return new ChamaeleonEGLPostDrawCallBack(m_framework.get());
// }

int QcEngine::setViewBasedonHuCoor(int /*f_coor_x*/, int f_coor_y)
{
  //Solution for svs with leftview and right view
  // if(f_coor_x>=5 && f_coor_x<=80)
  // {
  //   return LEFTVIEW;
  // }
  // else
  // {
  //   if(f_coor_x>100 && f_coor_x <=250)
  //   {
  //     if(f_coor_y>=5 && f_coor_y<=150)
  //     {
  //       return FRONTVIEW;
  //     }
  //     else if(f_coor_y>=500 && f_coor_y<=700)
  //     {
  //       return REARVIEW;
  //     }
  //     else
  //     {
  //       return 0;
  //     }
  //   }
  //   else if(f_coor_x >=300)
  //   {
  //     return RIGHTVIEW;
  //   }
  //   else
  //   {
  //     return 0;
  //   }
  // }

  //Solution for only have front and rear view
  if(f_coor_y>=5 && f_coor_y<=300)
  {
    return FRONTVIEW;
  }
  else if(f_coor_y>=400 && f_coor_y<=700)
  {
    return REARVIEW;
  }
  else
  {
    return 0;
  }
}
} // namespace qualcomm
} // namespace target
} // namespace pc
