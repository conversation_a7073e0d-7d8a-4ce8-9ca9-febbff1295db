#version 300 es


out vec2 v_alphaCam;

out vec2 v_texLeftCam;
out vec2 v_texRightCam;

#define CAM_ON 0
#define CAM_DISABLED 1
#define CAM_OFF 2

#if (LEFT_SOURCE == CAM_DISABLED) || (LEFT_TARGET == CAM_DISABLED) || (RIGHT_SOURCE == CAM_DISABLED) || (RIGHT_TARGET == CAM_DISABLED)
// CAM_DISABLEDing is currently not implemented, so no need for CAM_DISABLED setup
//#define ENABLE_CAM_DISABLED
#endif

#ifdef ENABLE_CAM_DISABLED
out float v_incrementXY;
#ifdef IS_WALL
out float v_incrementWall;
#endif
#endif

#ifdef ENABLE_STITCHING_LINE
uniform vec3 u_stitch_line;
#ifdef IS_WALL
uniform bool u_enableDynamicWallModelMatrix;
uniform mat4 u_dynamicWallModelMatrix;
#endif
#endif


in highp vec4 osg_Vertex;
in vec2 osg_MultiTexCoord0;
in vec2 osg_MultiTexCoord1;

in float a_stitchFactors;
uniform highp mat4 osg_ModelViewProjectionMatrix;

uniform vec3 u_ortho_line;
uniform float u_stitchBand;

#ifdef ENABLE_CHAMAELEON

out vec3 v_cham_correctionMidLeft;
out vec3 v_cham_correctionMidRight;
out vec3 v_cham_correctionLeft;
out vec3 v_cham_correctionRight;

out float v_cham_weightRightMid;
out float v_cham_weightMidLeft;

uniform vec2 u_cham_direction_mid_left;
uniform vec2 u_cham_direction_mid_right;
uniform vec2 u_cham_direction_left;
uniform vec2 u_cham_direction_right;

uniform vec3 u_cham_origin_mid;
uniform vec3 u_cham_origin_left;
uniform vec3 u_cham_origin_right;

uniform sampler2D s_chamGainsAsTexture;
uniform ivec4 u_cham_worldRoiIdx;

float getDistToLine(vec2 pt1, vec2 pt2, vec2 testPt)
{
  vec2 lineDir = pt2 - pt1;
  vec2 perpDir = vec2(lineDir.y, -lineDir.x);
  vec2 dirToPt1 = pt1 - testPt;
  return abs(dot(normalize(perpDir), dirToPt1));
}

float crossVec2(vec2 v, vec2 w)
{
  return (v.x * w.y) - (v.y * w.x);
}

float getColorCorrectionWeight(vec2 f_root0, vec2 f_line0,
                               vec2 f_root1, vec2 f_line1,
                               vec2 f_vertTemp)
{
  float u0 = crossVec2((f_vertTemp - f_root0), f_line0) / (crossVec2(f_line0, f_root1 - f_root0));
  float u1 = crossVec2((f_vertTemp - f_root1), f_line1) / (crossVec2(f_line1, f_root1 - f_root0));

  vec2 intersectionLine0 = f_vertTemp + u0 * (f_root1 - f_root0);
  vec2 intersectionLine1 = f_vertTemp + u1 * (f_root1 - f_root0);

  float distance0 = distance(intersectionLine0, f_vertTemp);
  float distance1 = distance(intersectionLine1, f_vertTemp);
  float distanceAll = distance(intersectionLine0, intersectionLine1);

  float diff = distance1 - min(distanceAll, distance1);

  return clamp(((distance0 - diff) / distanceAll), 0.0, 1.0);
}
#endif // ENABLE_CHAMAELEON

#ifdef ENABLE_SHARPNESS_HARMONIZATION
// currently no special content except OpenGL ES 3 conformant shader
#endif // ENABLE_SHARPNESS_HARMONIZATION

uniform float u_stitchHeight;
uniform float u_stitchParam;

uniform bool u_disableBorderBlending; // workaround for NRCSTWO-76310

float getStitchFactor(float f_blendingBandwidth, float f_distance)
{
  return f_distance*0.5/(f_blendingBandwidth+0.01) + 0.5;
}

void main()
{
  v_texLeftCam = osg_MultiTexCoord0;
  v_texRightCam = osg_MultiTexCoord1;

#ifdef ENABLE_CAM_DISABLED
  v_incrementXY = osg_Vertex.y - osg_Vertex.x;

#ifdef IS_WALL
  vec2 l_V = normalize (osg_Vertex.xy);
  vec2 l_XDirection = vec2 (1, 0);
  vec2 l_XDirectionNormalized = normalize (l_XDirection);
  float l_CosineAngle = dot (l_V, l_XDirectionNormalized);
  float l_Angle = acos (l_CosineAngle);
  v_incrementWall = float (l_Angle) + osg_Vertex.z;
#endif

#endif

#ifndef ENABLE_STITCHING_LINE
  v_alphaCam = vec2(a_stitchFactors, 1.0-a_stitchFactors);
#else
#ifdef IS_WALL
  vec3 l_vertLocal;
  if (u_enableDynamicWallModelMatrix)
  {
    l_vertLocal = (u_dynamicWallModelMatrix*osg_Vertex).xyz;
  }
  else
  {
    l_vertLocal = osg_Vertex.xyz;
  }
#else
  vec3 l_vertLocal = osg_Vertex.xyz;
#endif
  vec3 l_vertTemp = vec3(l_vertLocal.xy, 1.0);
  // calculate the distance from the line to the point
  float l_dist2line = dot(u_ortho_line, l_vertTemp);
  float l_dist2Origin = abs(dot(u_stitch_line, l_vertTemp))/2.;
  float l_stitchBandNew = ((l_dist2Origin + 0.01)*u_stitchBand);

#ifdef IS_WALL
  // stitch-line for wall (with border, height dependent stiching band width)
  // decide the stitch factor depending on the stitch line
  l_stitchBandNew += (u_stitchBand * u_stitchParam * (l_vertLocal.z / u_stitchHeight));

  if (u_disableBorderBlending)
  {
    float l_StitchFactor = getStitchFactor(l_stitchBandNew, l_dist2line);
    v_alphaCam.x = l_StitchFactor;
    v_alphaCam.y = 1.0-l_StitchFactor;
  }
  else
  {
    float l_StitchFactor = clamp(getStitchFactor(l_stitchBandNew, l_dist2line), 0.0, 1.0);
    vec2 l_border = smoothstep(0.0, 0.2, vec2(a_stitchFactors, 1.0-a_stitchFactors));
    v_alphaCam = min(l_border, vec2(l_StitchFactor, (1.0-l_StitchFactor)));
    v_alphaCam/= (v_alphaCam.x + v_alphaCam.y);
  }
#else
  float l_StitchFactor = getStitchFactor(l_stitchBandNew, l_dist2line);
  v_alphaCam.x = l_StitchFactor;
  v_alphaCam.y = 1.0-l_StitchFactor;
#endif // IS_WALL


#endif // ENABLE_STITCHING_LINE

#ifdef ENABLE_CHAMAELEON
    v_cham_correctionLeft = texelFetch(s_chamGainsAsTexture, ivec2(u_cham_worldRoiIdx[0],0), 0).rgb + vec3(0.5);
    v_cham_correctionMidRight = texelFetch(s_chamGainsAsTexture, ivec2(u_cham_worldRoiIdx[1],0), 0).rgb + vec3(0.5);
    v_cham_correctionMidLeft = texelFetch(s_chamGainsAsTexture, ivec2(u_cham_worldRoiIdx[2],0), 0).rgb + vec3(0.5);
    v_cham_correctionRight = texelFetch(s_chamGainsAsTexture, ivec2(u_cham_worldRoiIdx[3],0), 0).rgb + vec3(0.5);

    v_cham_weightRightMid = getColorCorrectionWeight(u_cham_origin_mid.xy, u_cham_direction_mid_right, u_cham_origin_right.xy, u_cham_direction_right, osg_Vertex.xy);
    v_cham_weightMidLeft  = getColorCorrectionWeight(u_cham_origin_left.xy , u_cham_direction_left, u_cham_origin_mid.xy, u_cham_direction_mid_left, osg_Vertex.xy);
#endif // ENABLE_CHAMAELEON

  gl_Position = osg_ModelViewProjectionMatrix * osg_Vertex;
}
