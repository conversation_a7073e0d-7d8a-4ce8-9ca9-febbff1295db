//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON> GmbH. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include "pc/svs/factory/inc/ShaderSelectionLogic.h"
#include "vfc/core/vfc_types.hpp"

namespace pc
{
namespace factory
{

ShaderSelectionLogic::ShaderSelectionLogic()
{
  for(unsigned int nCam = 0u; nCam < 4u; ++nCam)
  {
    m_globalShaderConfig.m_singleCamFloorShaders[nCam].m_SourceInput = SHADER_CAM_ON;
    m_globalShaderConfig.m_singleCamFloorShaders[nCam].m_TargetInput = SHADER_CAM_ON;
    m_globalShaderConfig.m_singleCamFloorShaders[nCam].m_IsWall = false;
    m_globalShaderConfig.m_singleCamFloorShaders[nCam].m_EnableChamaeleon = false;
    m_globalShaderConfig.m_singleCamFloorShaders[nCam].m_EnableSharpnessHarmonization = false;
    m_globalShaderConfig.m_singleCamFloorShaders[nCam].m_DebugSharpnessHarmonization = false;
    m_globalShaderConfig.m_singleCamFloorShaders[nCam].m_EnableTemporalNoiseFilter    = false;

    m_globalShaderConfig.m_singleCamWallShaders[nCam].m_SourceInput = SHADER_CAM_ON;
    m_globalShaderConfig.m_singleCamWallShaders[nCam].m_TargetInput = SHADER_CAM_ON;
    m_globalShaderConfig.m_singleCamWallShaders[nCam].m_IsWall = true;
    m_globalShaderConfig.m_singleCamWallShaders[nCam].m_EnableChamaeleon = false;
    m_globalShaderConfig.m_singleCamWallShaders[nCam].m_EnableSharpnessHarmonization = false;
    m_globalShaderConfig.m_singleCamWallShaders[nCam].m_DebugSharpnessHarmonization = false;
    m_globalShaderConfig.m_singleCamWallShaders[nCam].m_EnableTemporalNoiseFilter    = false;
  }

  for(unsigned int nCam = 0u; nCam < 4u; ++nCam)
  {
    m_globalShaderConfig.m_twoCamFloorShaders[nCam] = computeTwoCamConfiguration(false, nCam, (nCam+1)%4);
    m_globalShaderConfig.m_twoCamWallShaders[nCam] =  computeTwoCamConfiguration(true , nCam, (nCam+1)%4);
  }
}


SingleCamShaderParameters ShaderSelectionLogic::computeSingleCamTransitionConfiguration(
  bool f_isWall,
  unsigned int f_degradationMask,
  unsigned int f_deactivationBitMask,
  unsigned int f_camNumber)
{

  SingleCamShaderParameters l_shaderParams = f_isWall ? m_globalShaderConfig.m_singleCamWallShaders[f_camNumber]
                                                      : m_globalShaderConfig.m_singleCamFloorShaders[f_camNumber];

  if( (f_degradationMask >> f_camNumber) & 0x01u )
  {
    l_shaderParams.m_TargetInput = SHADER_CAM_OFF;
  }
  else
  {
    if( (f_deactivationBitMask >> f_camNumber) & 0x01u )
    {
      l_shaderParams.m_TargetInput = SHADER_CAM_DISABLED;
    }
    else
    {
      l_shaderParams.m_TargetInput = SHADER_CAM_ON;
    }
  }
  return l_shaderParams;
 }


SingleCamShaderParameters ShaderSelectionLogic::computeSingleCamFinalConfiguration (bool f_isWall, unsigned int f_camNumber)
{
  SingleCamShaderParameters l_shaderParams = f_isWall ? m_globalShaderConfig.m_singleCamWallShaders[f_camNumber]
                                                      : m_globalShaderConfig.m_singleCamFloorShaders[f_camNumber];

  l_shaderParams.m_SourceInput = l_shaderParams.m_TargetInput;

  return l_shaderParams;
}


void ShaderSelectionLogic::setAllShaderTransitionConfigurations(unsigned int f_degradationMask, unsigned int f_deactivationBitMask)
{
  for (unsigned int nCam = 0u; nCam < 4u; ++nCam)
  {
    m_globalShaderConfig.m_singleCamFloorShaders[nCam] = computeSingleCamTransitionConfiguration(
        false,
        f_degradationMask,
        f_deactivationBitMask,
        nCam);

    m_globalShaderConfig.m_singleCamWallShaders[nCam]  = computeSingleCamTransitionConfiguration(
        true,
        f_degradationMask,
        f_deactivationBitMask,
        nCam);
  }

  for (unsigned int nCam = 0u; nCam < 4u; ++nCam)
  {
    m_globalShaderConfig.m_twoCamFloorShaders[nCam] = computeTwoCamConfiguration(false, nCam, (nCam + 1u) % 4u);
    m_globalShaderConfig.m_twoCamWallShaders[nCam] =  computeTwoCamConfiguration(true , nCam, (nCam + 1u) % 4u);
  }
}


void ShaderSelectionLogic::setAllShaderFinalConfigurations()
{
  for(unsigned int nCam = 0u; nCam < 4u; ++nCam)
  {
    m_globalShaderConfig.m_singleCamFloorShaders[nCam] = computeSingleCamFinalConfiguration(false, nCam);
    m_globalShaderConfig.m_singleCamWallShaders[nCam]  = computeSingleCamFinalConfiguration(true, nCam);
  }

  for(unsigned int nCam = 0u; nCam < 4u; ++nCam)
  {
    m_globalShaderConfig.m_twoCamFloorShaders[nCam] = computeTwoCamConfiguration(false, nCam, (nCam + 1u) % 4u);
    m_globalShaderConfig.m_twoCamWallShaders[nCam] =  computeTwoCamConfiguration(true , nCam, (nCam + 1u) % 4u);
  }
}


void ShaderSelectionLogic::setEnableChamaeleon(bool f_isEnabled)
{
  for (vfc::uint32_t nCam = 0U; nCam < 4U; ++nCam)
  {
    m_globalShaderConfig.m_singleCamFloorShaders[nCam].m_EnableChamaeleon = f_isEnabled;
    m_globalShaderConfig.m_singleCamWallShaders[nCam].m_EnableChamaeleon  = f_isEnabled;
  }

  for (vfc::uint32_t nCam = 0U; nCam < 4U; ++nCam)
  {
    m_globalShaderConfig.m_twoCamFloorShaders[nCam].m_EnableChamaeleon = f_isEnabled;
    m_globalShaderConfig.m_twoCamWallShaders[nCam].m_EnableChamaeleon  = f_isEnabled;
  }
}


void ShaderSelectionLogic::setEnableSharpnessHarmonization(bool f_isEnabled, bool f_isDebug)
{
  for (vfc::uint32_t nCam = 0U; nCam < 4U; ++nCam)
  {
    m_globalShaderConfig.m_singleCamFloorShaders[nCam].m_EnableSharpnessHarmonization = f_isEnabled;
    m_globalShaderConfig.m_singleCamWallShaders[nCam].m_EnableSharpnessHarmonization  = f_isEnabled;
    m_globalShaderConfig.m_singleCamFloorShaders[nCam].m_DebugSharpnessHarmonization  = f_isDebug;
    m_globalShaderConfig.m_singleCamWallShaders[nCam].m_DebugSharpnessHarmonization   = f_isDebug;
  }

  for (vfc::uint32_t nCam = 0U; nCam < 4U; ++nCam)
  {
    m_globalShaderConfig.m_twoCamFloorShaders[nCam].m_EnableSharpnessHarmonization = f_isEnabled;
    m_globalShaderConfig.m_twoCamWallShaders[nCam].m_EnableSharpnessHarmonization  = f_isEnabled;
    m_globalShaderConfig.m_twoCamFloorShaders[nCam].m_DebugSharpnessHarmonization  = f_isDebug;
    m_globalShaderConfig.m_twoCamWallShaders[nCam].m_DebugSharpnessHarmonization   = f_isDebug;
  }
}

void ShaderSelectionLogic::setEnableTNF(bool f_isEnabled)
{
  for (vfc::uint32_t nCam = 0U; nCam < 4U; ++nCam)
  {
    m_globalShaderConfig.m_singleCamFloorShaders[nCam].m_EnableTemporalNoiseFilter = f_isEnabled;
    m_globalShaderConfig.m_singleCamWallShaders[nCam].m_EnableTemporalNoiseFilter  = f_isEnabled;
  }

  for (vfc::uint32_t nCam = 0U; nCam < 4U; ++nCam)
  {
    m_globalShaderConfig.m_twoCamFloorShaders[nCam].m_EnableTemporalNoiseFilter = f_isEnabled;
    m_globalShaderConfig.m_twoCamWallShaders[nCam].m_EnableTemporalNoiseFilter  = f_isEnabled;
  }
}

TwoCamShaderParameters ShaderSelectionLogic::computeTwoCamConfiguration(bool f_isWall, unsigned int f_firstCam, unsigned int f_secondCam)
{
  TwoCamShaderParameters l_shaderParams = f_isWall ? m_globalShaderConfig.m_twoCamWallShaders[f_firstCam]
                                                   : m_globalShaderConfig.m_twoCamFloorShaders[f_firstCam];

  const SingleCamShaderParameters * const l_firstCamParams = f_isWall ? &(m_globalShaderConfig.m_singleCamWallShaders[f_firstCam])
                                                                      : &(m_globalShaderConfig.m_singleCamFloorShaders[f_firstCam]);
  const SingleCamShaderParameters * const l_secondCamParams = f_isWall ? &(m_globalShaderConfig.m_singleCamWallShaders[f_secondCam])
                                                                       : &(m_globalShaderConfig.m_singleCamFloorShaders[f_secondCam]);

  l_shaderParams.m_SourceInputLeft = l_firstCamParams->m_SourceInput;
  l_shaderParams.m_TargetInputLeft = l_firstCamParams->m_TargetInput;
  l_shaderParams.m_SourceInputRight = l_secondCamParams->m_SourceInput;
  l_shaderParams.m_TargetInputRight = l_secondCamParams->m_TargetInput;
  l_shaderParams.m_IsWall = f_isWall;
  l_shaderParams.m_EnableStitching = true;

  return l_shaderParams;
}


SingleCamShaderParameters ShaderSelectionLogic::getSingleCamShaderParameters(bool f_isWall, unsigned int f_camNumber)
{
  return ( (f_isWall)? m_globalShaderConfig.m_singleCamWallShaders[f_camNumber] : m_globalShaderConfig.m_singleCamFloorShaders[f_camNumber] );
}


TwoCamShaderParameters ShaderSelectionLogic::getTwoCamShaderParameters(bool f_isWall, unsigned int f_camNumber)
{
  return ( (f_isWall)? m_globalShaderConfig.m_twoCamWallShaders[f_camNumber] : m_globalShaderConfig.m_twoCamFloorShaders[f_camNumber] );
}


} // namespace factory
} // namespace pc
