//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: EXTERNAL Castellane Florian (Altran, CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  Vehicle.cpp
/// @brief
//=============================================================================
#include "cc/assets/common/inc/Vehicle.h"
#include "cc/core/inc/CustomMechanicalData.h"
#include "cc/target/common/inc/commonInterface.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h"
#include "pc/svs/assets/vehiclemodel/inc/BodyAnimation.h"
#include "pc/svs/assets/vehiclemodel/inc/DoorAnimation.h"
#include "pc/svs/assets/vehiclemodel/inc/RenderBinSetter.h"
#include "pc/svs/assets/vehiclemodel/inc/Utils.h"
#include "pc/svs/assets/vehiclemodel/inc/VehicleModel.h"
#include "pc/svs/assets/vehiclemodel/inc/WheelAnimation.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"

#include "pc/generic/util/cli/inc/CommandLineInterface.h"
#include "pc/svs/util/cli/inc/BaseCommands.h"

#include "cc/assets/common/inc/LightStateClasses.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/sm/viewmode/inc/ViewModeStateMachine.h"

#include "cc/imgui/inc/imgui_manager.h"
#include "pc/svs/util/math/inc/FloatComp.h"

#include "osg/StateSet"
#include "osg/Uniform"

#include <osg/CullFace>
#include <osg/Texture2D>
#include <osg/TextureCubeMap>
#include <osg/Image>
#include <osg/Vec4>
#include <osg/Geode>
#include <osg/Group>
#include <osgDB/ReadFile>
#include <osgDB/WriteFile>

#include "osg/Depth"
#include "osg/BlendFunc"

// #include "osg/GL"
// #include "osg/GLExtensions"
// #include "osg/GL2Extensions"

#include "cc/assets/common/inc/StateSetUpdater.h"

#include <array>
#include <memory>
#include <unordered_map>

using pc::util::logging::g_AppContext;
using pc::util::logging::g_EngineContext;

namespace cc
{
namespace assets
{
namespace common
{

pc::util::coding::Item<CustomVehicleModelSettings> g_modelSettings("CustomVehicleModel");
pc::util::coding::Item<CustomVehicleColorSettings> g_modelColorSettings("CustomVehicleColorSettings");
constexpr const char* const                              g_uniformNameDoorOpen = "u_openDoorColor";

float VehicleTransparencyUpdateCallback::sm_transparencyLevel = 1.0f;

static bool checkParkingActive(cc::target::common::EPARKStatusR2L f_parkStatus)
{
    using namespace cc::target::common;
    return f_parkStatus == PARK_Searching || f_parkStatus == PARK_Guidance_active ||
           f_parkStatus == PARK_Guidance_suspend || f_parkStatus == PARK_Terminated || f_parkStatus == PARK_Completed ||
           f_parkStatus == PARK_Failure || f_parkStatus == PARK_AssistStandby;
}

//!
//! \brief Custom implementation of OSG's StateSet getOrCreateUniform method
//! which allows setting the override value
//!
osg::Uniform* getOrCreateUniform(
    osg::StateSet* const f_stateSet,
    const std::string& f_uniformName,
    osg::Uniform::Type f_type,
    osg::StateAttribute::OverrideValue f_value)
{
    if (f_stateSet == nullptr)
    {
        return nullptr;
    }
    osg::Uniform* l_uniform = f_stateSet->getUniform(f_uniformName);
    if (((l_uniform != nullptr) && (l_uniform->getType() == f_type)))
    {
        return l_uniform;
    }
    l_uniform = new osg::Uniform(f_type, f_uniformName);
    f_stateSet->addUniform(l_uniform, f_value);
    return l_uniform;
}

//!
//! class VehicleModel
//!
CustomVehicleModel::CustomVehicleModel(pc::core::Framework* f_framework)
    : pc::vehiclemodel::VehicleModel(f_framework) // PRQA S 2323
    , m_customComponents{}
    , m_floorplateNodeMask{~0u}
    , m_prevBaseplateState{cc::daddy::SolidBasePlateState::deactivated}
    , m_newRefViewID{std::numeric_limits<vfc::uint32_t>::max()}
    , m_customFramework{nullptr}
{
    if (f_framework == nullptr)
    {
        XLOG_ERROR(g_AppContext, "Vehicle.cpp: CustomVehicleModel - f_framework is null");
    }
    else
    {
        m_customFramework = f_framework->asCustomFramework();
    }
    setNumChildrenRequiringUpdateTraversal(getNumChildrenRequiringUpdateTraversal() + 1u);
}

CustomVehicleModel::CustomVehicleModel(const CustomVehicleModel& f_other, const osg::CopyOp& f_copyOp)
    : pc::vehiclemodel::VehicleModel(f_other, f_copyOp) // PRQA S 2323
    , m_customComponents{f_other.m_customComponents}
    , m_floorplateNodeMask{f_other.m_floorplateNodeMask}
    , m_prevBaseplateState{cc::daddy::SolidBasePlateState::deactivated}
    , m_newRefViewID{std::numeric_limits<vfc::uint32_t>::max()}
    , m_customFramework{f_other.m_customFramework}
{
    setNumChildrenRequiringUpdateTraversal(getNumChildrenRequiringUpdateTraversal() + 1u);
}

void CustomVehicleModel::reset()
{
    m_customComponents.fill(nullptr);
    pc::vehiclemodel::VehicleModel::reset();
}

osg::Camera* findParentCameraWithAbsoluteReferenceFrame(const osg::NodePath& f_nodePath)
{
    osg::NodePath::const_reverse_iterator l_itr = f_nodePath.rbegin();
    while (l_itr != f_nodePath.rend())
    {
        osg::Camera* const l_cam = (*l_itr)->asCamera();
        if ((l_cam != nullptr) && (osg::Transform::ABSOLUTE_RF == l_cam->getReferenceFrame()))
        {
            return l_cam;
        }
        ++l_itr; // PRQA S 3803
    }
    return nullptr;
}

void updateCameraStateSet(osg::StateSet* const f_stateSet, const osg::Matrix& f_inverseViewMatrix)
{
    if (f_stateSet == nullptr)
    {
        XLOG_ERROR(g_AppContext, "Vehicle.cpp updateCameraStateSet() - f_stateSet is nullptr");
        return;
    }
    osg::Uniform* const l_viewMatrixInverseUniform =
        f_stateSet->getOrCreateUniform("osg_ViewMatrixInverse", osg::Uniform::FLOAT_MAT4);
    l_viewMatrixInverseUniform->set(f_inverseViewMatrix); // PRQA S 3803
}

void CustomVehicleModel::traverse(osg::NodeVisitor& f_nv)
{
    switch (f_nv.getVisitorType())
    {
    case osg::NodeVisitor::UPDATE_VISITOR:
    {
        bool                             l_parkActive   = false;
        cc::target::common::EThemeTypeHU l_currentTheme = cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI;
        pc::core::View*                  l_newRefView   = nullptr;

        vfc::uint32_t l_newRefViewID = std::numeric_limits<vfc::uint32_t>::max() - 1;
        auto const    l_scene        = getFramework()->asCustomFramework()->getScene();

        //! THEME
        if (getFramework()->asCustomFramework()->m_SVSRotateStatusDaddy_Receiver.hasData())
        {
            const cc::daddy::SVSRotateStatusDaddy_t* const l_themeType =
                getFramework()->asCustomFramework()->m_SVSRotateStatusDaddy_Receiver.getData();
            l_currentTheme = static_cast<cc::target::common::EThemeTypeHU>(l_themeType->m_Data); // PRQA S 3013
        }

        //! PARK ACTIVE
        if (getFramework()->asCustomFramework()->m_parkHmiParkingStatusReceiver.hasData())
        {
            const cc::daddy::ParkStatusDaddy_t* const l_parkStatus =
                getFramework()->asCustomFramework()->m_parkHmiParkingStatusReceiver.getData();
            l_parkActive = checkParkingActive(l_parkStatus->m_Data);
        }

        if (l_currentTheme == cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI && l_parkActive == true)
        {
            l_newRefView = l_scene->getView(cc::core::CustomViews::EView::HORI_PARKING_PLAN_VIEW);
        }

        if (l_currentTheme == cc::target::common::EThemeTypeHU::ETHEME_TYPE_VERT && l_parkActive == true)
        {
            l_newRefView = l_scene->getView(cc::core::CustomViews::EView::VERT_PARKING_PLAN_VIEW);
        }

        if (l_newRefView != nullptr)
        {
            if (l_newRefView->getNodeMask() != 0u)
            {
                // get view id from view
                l_newRefViewID = l_scene->getViewId(l_newRefView);
            }
        }

        osg::Node* const l_node =
            pc::vehiclemodel::VehicleModel::getComponent(pc::vehiclemodel::VehicleModel::FLOOR_PLANE);
        if (l_node != nullptr)
        {
            const auto transparencyLevel = VehicleTransparencyUpdateCallback::getTransparencyLevel();
            l_node->setNodeMask(
                (transparencyLevel >= 0.99f)
                    ? static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::FLOOR_PLANE_MASK)
                    : 0u);

            // l_node->setNodeMask(
            //     IMGUI_GET_CHECKBOX_BOOL("Settings", "Floor")
            //         ? static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::FLOOR_PLANE_MASK)
            //         : 0u);
        }

        if (m_newRefViewID != l_newRefViewID)
        {
            m_newRefViewID = l_newRefViewID;
            osg::Node* const l_node =
                pc::vehiclemodel::VehicleModel::getComponent(pc::vehiclemodel::VehicleModel::FLOOR_PLANE);

            if (l_node != nullptr)
            {
                m_floorplateNodeMask = l_node->getNodeMask();

                if ((l_newRefViewID == cc::core::CustomViews::EView::HORI_PARKING_PLAN_VIEW ||
                     l_newRefViewID == cc::core::CustomViews::EView::VERT_PARKING_PLAN_VIEW))
                {
                    l_node->setNodeMask(0u);
                }
                else
                {
                    l_node->setNodeMask(m_floorplateNodeMask);
                }
            }
        }

        const cc::daddy::SolidBasePlateStateDaddy* const l_solidBasePlateStateDaddy =
            getFramework()->asCustomFramework()->m_solidBaseplateState_ReceiverPort.getData();
        if (nullptr != l_solidBasePlateStateDaddy)
        {
            if (m_prevBaseplateState != l_solidBasePlateStateDaddy->m_Data)
            {
                m_prevBaseplateState = l_solidBasePlateStateDaddy->m_Data;

                osg::Node* const l_node =
                    pc::vehiclemodel::VehicleModel::getComponent(pc::vehiclemodel::VehicleModel::FLOOR_PLANE);

                if (l_node != nullptr)
                {
                    if (l_node->getNodeMask() != 0u)
                    {
                        m_floorplateNodeMask = l_node->getNodeMask();
                    }

                    switch (l_solidBasePlateStateDaddy->m_Data)
                    {
                    case cc::daddy::SolidBasePlateState::activated:
                    {
                        // disable vehicle shadow
                        l_node->setNodeMask(0u);
                        break;
                    }
                    case cc::daddy::SolidBasePlateState::deactivated:
                    {
                        // reenable vehicle shadow
                        l_node->setNodeMask(m_floorplateNodeMask);
                        break;
                    }
                    default:
                    {
                        // do nothing
                        break;
                    }
                    }
                }
            }
        }
        osg::StateSet* const l_stateSet             = getOrCreateStateSet();
        osg::Uniform*  const l_leftHandDriveUniform = getOrCreateUniform(
            l_stateSet, "u_leftHandDrive", osg::Uniform::BOOL, osg::StateAttribute::OVERRIDE); // PRQA S 3143
        l_leftHandDriveUniform->set(pc::vehicle::g_mechanicalData->m_leftHandDrive);            // PRQA S 3803
        pc::vehiclemodel::VehicleModel::traverse(f_nv);
    }
    break;

    case osg::NodeVisitor::CULL_VISITOR:
    {
        //! Super transparent mode
        // auto& l_superTransparentModeReceiverPort = m_customFramework->m_HUvehTransLevelReceiver;

        // if (l_superTransparentModeReceiverPort.hasData())
        // {
        //   const auto* l_pData = l_superTransparentModeReceiverPort.getData().front();
        //   if (l_pData != nullptr)
        //   {
        //     if (l_pData->m_Data == 2)
        //     {
        //       return;
        //     }
        //   }
        // }
        osgUtil::CullVisitor* const l_cv            = static_cast<osgUtil::CullVisitor*>(&f_nv);
        osg::Camera*          const l_currentCamera = l_cv->getCurrentCamera();
        osg::StateSet*        const l_stateSet      = l_currentCamera->getOrCreateStateSet();
        if (osg::Transform::RELATIVE_RF == l_currentCamera->getReferenceFrame())
        {
            osg::Camera* const l_absCam = findParentCameraWithAbsoluteReferenceFrame(f_nv.getNodePath());
            if (l_absCam != nullptr)
            {
                updateCameraStateSet(l_stateSet, l_absCam->getInverseViewMatrix());
            }
        }
        else
        {
            updateCameraStateSet(l_stateSet, l_currentCamera->getInverseViewMatrix());
        }
        pc::vehiclemodel::VehicleModel::traverse(f_nv);
    }
    break;
    default:
    {
        pc::vehiclemodel::VehicleModel::traverse(f_nv);
        break;
    }
    }
}

//!
//! FrontWheelsGetter
//!
class FrontWheelsGetter : public pc::vehiclemodel::IFinalizer
{
public:
    explicit FrontWheelsGetter(osg::Group* f_targetGroup)
        : m_targetGroup(f_targetGroup)
    {
    }

    void finalize(pc::vehiclemodel::VehicleModel* f_vehicleModel) override
    {
        if (f_vehicleModel == nullptr)
        {
            XLOG_ERROR(g_AppContext, "FrontWheelsGetter::finalize - f_vehicleModel is nullptr");
            return;
        }
        if (!m_targetGroup.valid())
        {
            return;
        }

        osg::Node* const l_frontLeftWheel  = f_vehicleModel->getComponent(pc::vehiclemodel::VehicleModel::FRONT_LEFT_WHEEL);
        osg::Node* const l_frontRightWheel = f_vehicleModel->getComponent(pc::vehiclemodel::VehicleModel::FRONT_RIGHT_WHEEL);

        if ((l_frontLeftWheel != nullptr) && (l_frontRightWheel != nullptr))
        {
            osg::Matrix l_translation;
            l_translation.makeTranslate(0.0f, 0.0f, 0.0f); // magic number. Positions car on ground level
            osg::MatrixTransform* const l_matrixTransform = new osg::MatrixTransform(l_translation);

            l_matrixTransform->addChild(l_frontLeftWheel);  // PRQA S 3803
            l_matrixTransform->addChild(l_frontRightWheel); // PRQA S 3803

            const osg::ref_ptr<osg::Uniform> l_brightnessUniform = getOrCreateUniform(
                l_matrixTransform->getOrCreateStateSet(),
                "brightness",
                osg::Uniform::FLOAT,
                osg::StateAttribute::OVERRIDE);
            l_brightnessUniform->set(1.0f); // PRQA S 3803

            m_targetGroup->addChild(l_matrixTransform); // PRQA S 3803
        }
    }

protected:
    ~FrontWheelsGetter() override = default;

private:
    osg::ref_ptr<osg::Group> m_targetGroup;

    // Private copy constructor and assignment operator
    FrontWheelsGetter(const FrontWheelsGetter&)            = delete;
    FrontWheelsGetter& operator=(const FrontWheelsGetter&) = delete;
};

//!
//! AllWheelsGetter
//!
class AllWheelsGetter : public pc::vehiclemodel::IFinalizer
{
public:
    explicit AllWheelsGetter(osg::Group* f_targetGroup, pc::core::Framework* f_framework)
        : m_targetGroup(f_targetGroup)
        , m_framework(f_framework)
    {
    }

    void finalize(pc::vehiclemodel::VehicleModel* f_vehicleModel) override
    {
        if (f_vehicleModel == nullptr)
        {
            XLOG_ERROR(g_AppContext, "AllWheelsGetter::finalize - f_vehicleModel is nullptr");
            return;
        }
        if (!m_targetGroup.valid())
        {
            return;
        }

        osg::Node* const l_frontLeftWheel  = f_vehicleModel->getComponent(pc::vehiclemodel::VehicleModel::FRONT_LEFT_WHEEL);
        osg::Node* const l_frontRightWheel = f_vehicleModel->getComponent(pc::vehiclemodel::VehicleModel::FRONT_RIGHT_WHEEL);
        osg::Node* const l_rearLeftWheel   = f_vehicleModel->getComponent(pc::vehiclemodel::VehicleModel::REAR_LEFT_WHEEL);
        osg::Node* const l_rearRightWheel  = f_vehicleModel->getComponent(pc::vehiclemodel::VehicleModel::REAR_RIGHT_WHEEL);

        if ((l_frontLeftWheel != nullptr) && (l_frontRightWheel != nullptr) && (l_rearLeftWheel != nullptr) &&
            (l_rearRightWheel != nullptr))
        {
            osg::Matrix l_translation;
            l_translation.makeTranslate(0.0f, 0.0f, 0.0f); // radius used for the STB overlays
            osg::MatrixTransform* const l_matrixTransform = new osg::MatrixTransform(l_translation);

            l_matrixTransform->addChild(l_frontLeftWheel);  // PRQA S 3803
            l_matrixTransform->addChild(l_frontRightWheel); // PRQA S 3803
            l_matrixTransform->addChild(l_rearLeftWheel);   // PRQA S 3803
            l_matrixTransform->addChild(l_rearRightWheel);  // PRQA S 3803

            const osg::ref_ptr<osg::Uniform> l_brightnessUniform = getOrCreateUniform(
                l_matrixTransform->getOrCreateStateSet(),
                "brightness",
                osg::Uniform::FLOAT,
                osg::StateAttribute::OVERRIDE);
            l_brightnessUniform->set(1.0f); // PRQA S 3803

            m_targetGroup->addChild(l_matrixTransform); // PRQA S 3803
            // osg::StateSet* l_stateSet = m_targetGroup->getOrCreateStateSet();
            //! set this wheel node explicitly opaque
            // l_stateSet->addUniform(new osg::Uniform("VehicleTransparency", 0.3f));
            // l_stateSet->setMode(GL_BLEND, osg::StateAttribute::ON);    // PRQA S 3143
        }
    }

protected:
    ~AllWheelsGetter() override = default;

private:
    //! Copy constructor is not permitted.
    AllWheelsGetter(const AllWheelsGetter& other) = delete;
    //! Copy assignment operator is not permitted.
    AllWheelsGetter& operator=(const AllWheelsGetter& other) = delete;

    osg::ref_ptr<osg::Group> m_targetGroup;

    pc::core::Framework* m_framework;
};

class WipingIndicatorSignal : public pc::util::coding::ISerializable
{
public:
    WipingIndicatorSignal()
        : m_period(0.3f)
        , m_sustain(0.2f)
        , m_delay(0.3f)
        , m_offset(0.0f)
    {
    }

    SERIALIZABLE(WipingIndicatorSignal) // PRQA S 2428
    {
        ADD_MEMBER(vfc::float32_t, period);
        ADD_MEMBER(vfc::float32_t, sustain);
        ADD_MEMBER(vfc::float32_t, delay);
        ADD_MEMBER(vfc::float32_t, offset);
    }

    vfc::float32_t getDuration() const
    {
        return m_period + m_sustain + m_delay;
    }

    vfc::float32_t getValue(vfc::float32_t f_t) const
    {
        f_t += m_offset;
        f_t = std::fmod(f_t, getDuration());
        if (f_t <= m_period)
        {
            // add epsilon to ensure begin of period is always larger than exact 0.0
            return (f_t / m_period) + std::numeric_limits<vfc::float32_t>::epsilon();
        }
        else if (f_t <= (m_period + m_sustain))
        {
            return 1.0f;
        }
        else
        {
            // do nothing
        }
        return 0.0f;
    }

    vfc::float32_t m_period;
    vfc::float32_t m_sustain;
    vfc::float32_t m_delay;
    vfc::float32_t m_offset;
};

pc::util::coding::Item<WipingIndicatorSignal> g_wipingIndicatorSettings("WipingIndicator");

///
/// WipingIndicatorAnimation
///
class WipingIndicatorAnimation : public osg::NodeCallback
{
public:
    typedef pc::daddy::IndicatorStateDaddy::value_type IndicatorState;

    explicit WipingIndicatorAnimation(pc::core::Framework& f_framework)
        : m_framework(f_framework) // PRQA S 2323 // PRQA S 4052
        , m_lastUpdate(0u) // PRQA S 2323
        , m_previousState(pc::daddy::INDICATOR_OFF) // PRQA S 2323
        , m_startTime(-1.0f) // PRQA S 2323
        , m_progress(0.0f) // PRQA S 2323

    {
    }

    void operator()(osg::Node* f_node, osg::NodeVisitor* f_nv) override
    {
        if (f_node == nullptr || f_nv == nullptr)
        {
            XLOG_ERROR(g_AppContext, "WipingIndicatorAnimation::operator() - f_node or f_nv is nullptr");
            return;
        }
        const osg::FrameStamp* const l_frameStamp = f_nv->getFrameStamp();
        if (m_lastUpdate != l_frameStamp->getFrameNumber())
        {
            update(static_cast<vfc::float32_t>(l_frameStamp->getReferenceTime()));
            m_lastUpdate = l_frameStamp->getFrameNumber();
        }
        else
        {
            // Do nothing
        }
        traverse(f_node, f_nv);
    }

    vfc::float32_t getProgress() const
    {
        return m_progress;
    }

private:
    void update(vfc::float32_t f_referenceTime)
    {
        const IndicatorState l_indicatorState = getIndicatorState();
        if (hasIndicatorStateChanged(l_indicatorState))
        {
            m_startTime = -1.0f;
        }
        if (isWipingEnabled() && isIndicatorActive(l_indicatorState))
        {
            if (0.0f > m_startTime)
            {
                m_startTime = f_referenceTime; // begin new animation cycle
            }
            m_progress = 1.0f - g_wipingIndicatorSettings->getValue(f_referenceTime - m_startTime);
        }
        else
        {
            m_progress = 0.0f;
        }
    }

    IndicatorState getIndicatorState() const
    {
        const auto l_pCustomFramework = m_framework.asCustomFramework();
        if (l_pCustomFramework == nullptr)
        {
            XLOG_ERROR(g_AppContext, "WipingIndicatorAnimation::getIndicatorState - l_pCustomFramework is nullptr");
            return pc::daddy::INDICATOR_OFF;
        }
        if (l_pCustomFramework->m_VehicleLightsReceiver.isConnected())
        {
            const cc::daddy::CustomVehicleLightsDaddy* const l_pCustomData =
                l_pCustomFramework->m_VehicleLightsReceiver.getData();

            if (nullptr != l_pCustomData)
            {
                if (cc::target::common::EturnSignalstatus_::Turn_Signal_DangerWarningSignal == static_cast<cc::target::common::EturnSignalstatus_>(l_pCustomData->m_Data.m_turnSignalStatus)) // PRQA S 4899
                {
                    return pc::daddy::INDICATOR_WARN;
                }
                else if (
                    cc::target::common::EturnSignalstatus_::Turn_Signal_LeftTurnSignalLightNormalFlashing ==
                    static_cast<cc::target::common::EturnSignalstatus_>(l_pCustomData->m_Data.m_turnSignalStatus)) // PRQA S 4899
                {
                    return pc::daddy::INDICATOR_LEFT;
                }
                else if (
                    cc::target::common::EturnSignalstatus_::Turn_Signal_RightTurnSignalLightFlashingNormally ==
                    static_cast<cc::target::common::EturnSignalstatus_>(l_pCustomData->m_Data.m_turnSignalStatus)) // PRQA S 4899
                {
                    return pc::daddy::INDICATOR_RIGHT;
                }
                else if (cc::target::common::EturnSignalstatus_::Turn_Signal_NOTWORKING == static_cast<cc::target::common::EturnSignalstatus_>(l_pCustomData->m_Data.m_turnSignalStatus)) // PRQA S 4899
                {
                    return pc::daddy::INDICATOR_OFF;
                }
                else
                {
                    // do nothing
                }
            }
        }
        const pc::daddy::IndicatorStateDaddy* const l_state = l_pCustomFramework->m_indicatorStateReceiver.getData();
        if (l_state != nullptr)
        {
            return l_state->m_Data;
        }
        return pc::daddy::INDICATOR_OFF;
    }

    bool hasIndicatorStateChanged(const IndicatorState& f_state)
    {
        if (f_state != m_previousState)
        {
            m_previousState = f_state;
            return true;
        }
        return false;
    }

    static bool isIndicatorActive(const IndicatorState& f_state)
    {
        return pc::daddy::INDICATOR_OFF != f_state;
    }

    static bool isWipingEnabled()
    {
#if 0 // m_dynIndicatorEnable signal is not mapped in ValIn
    cc::core::CustomFramework* l_pCustomFramework = static_cast<cc::core::CustomFramework*>(m_framework);
    if (l_pCustomFramework->m_VehicleLightsReceiver.isConnected())
    {
        const cc::daddy::CustomVehicleLightsDaddy* l_pCustomData = l_pCustomFramework->m_VehicleLightsReceiver.getData();

        if (0 != l_pCustomData)
        {
            if (1u == l_pCustomData->m_Data.m_dynIndicatorEnable)
            {
              return true;
            }
            else
            {
              // Do nothing
            }
        }
        else
        {
          // Do nothing
        }
    }
    else
    {
      //Do nothing
    }
    return false;
#else
        return true;
#endif
    }

    pc::core::Framework& m_framework;
    vfc::uint32_t        m_lastUpdate;
    IndicatorState       m_previousState;
    vfc::float32_t       m_startTime;
    vfc::float32_t       m_progress;
};

static constexpr const char* ANIMATION_UNIFORM_NAME = "alpha"; // PRQA S 2428
///
/// WipingIndicatorUpdateCallback
///
class WipingIndicatorCallback : public osg::NodeCallback
{
public:
    explicit WipingIndicatorCallback(WipingIndicatorAnimation* f_wipingIndicatorAnimation)
        : m_wipingIndicatorAnimation(f_wipingIndicatorAnimation)
    {
    }

    static osg::Uniform* getOrCreateAnimationUnifrom(osg::StateSet* f_stateSet)
    {
        if (f_stateSet == nullptr)
        {
            XLOG_ERROR(g_AppContext, "WipingIndicatorCallback::getOrCreateAnimationUniform - f_stateSet is nullptr");
            return nullptr;
        }
        osg::Uniform* l_animationUniform = f_stateSet->getUniform(ANIMATION_UNIFORM_NAME);
        if (nullptr == l_animationUniform)
        {
            l_animationUniform = new osg::Uniform(osg::Uniform::FLOAT, ANIMATION_UNIFORM_NAME);
            f_stateSet->addUniform(l_animationUniform, osg::StateAttribute::OVERRIDE); // PRQA S 3143
        }
        return l_animationUniform;
    }

    void operator()(osg::Node* f_node, osg::NodeVisitor* f_nv) override
    {
        if (f_node == nullptr || f_nv == nullptr)
        {
            XLOG_ERROR(g_AppContext, "WipingIndicatorCallback::operator() - f_node or f_nv is nullptr");
            return;
        }
        osg::StateSet* const       l_stateSet    = f_node->getOrCreateStateSet();
        osg::Uniform* const        l_fadeUniform = getOrCreateAnimationUnifrom(l_stateSet);
        const vfc::float32_t l_progress    = m_wipingIndicatorAnimation->getProgress();
        l_fadeUniform->set(l_progress); // PRQA S 3803
        traverse(f_node, f_nv);
    }

private:
    osg::ref_ptr<WipingIndicatorAnimation> m_wipingIndicatorAnimation;
};

class LightStateParser : public lightstate::LightStateJsonParser
{
public:
    LightStateParser(
        const std::shared_ptr<lightstate::SignalWrapperRegistry>& f_registry,
        WipingIndicatorAnimation*                                 f_wipingIndicatorAnimation)
        : lightstate::LightStateJsonParser(f_registry)
        , m_wipingIndicatorAnimation(f_wipingIndicatorAnimation)
    {
    }

    void onHint(const std::string& f_hint, osg::Node* f_node) override
    {
        if (f_node == nullptr)
        {
            XLOG_ERROR(g_AppContext, "LightStateParser::onHint - f_node is nullptr");
            return;
        }
        if (f_hint == "wiping_indicator")
        {
            f_node->addUpdateCallback(new WipingIndicatorCallback(m_wipingIndicatorAnimation.get()));
        }
    }

private:
    osg::ref_ptr<WipingIndicatorAnimation> m_wipingIndicatorAnimation;
};

///
/// LightFinalizer
///
class LightFinalizer : public pc::vehiclemodel::IFinalizer
{
public:
    explicit LightFinalizer(pc::core::Framework* f_framework)
        : m_framework(f_framework)
        , m_registry(std::make_shared<lightstate::SignalWrapperRegistry>(f_framework))
    {
        m_registry->registerSignal("Light", std::make_shared<lightstate::HeadlightWrapper>());
        m_registry->registerSignal("Brake", std::make_shared<lightstate::BrakeLightWrapper>());
        m_registry->registerSignal("Reverse", std::make_shared<lightstate::ReverseGearWrapper>());
        m_registry->registerSignal("Indicator", std::make_shared<lightstate::IndicatorWrapper>());
        m_registry->registerSignal("SideIndicator", std::make_shared<lightstate::SideIndicatorWrapper>());
        m_registry->registerSignal("Day", std::make_shared<lightstate::DayWrapper>());
        m_registry->registerSignal("RearPos", std::make_shared<lightstate::RearPosWrapper>());
        m_registry->registerSignal("SmallLight", std::make_shared<lightstate::SmallLightWrapper>());
        m_registry->registerSignal("FogLight", std::make_shared<lightstate::FogLightWrapper>());
        m_registry->registerSignal("FrontClearanceLamp", std::make_shared<lightstate::FrontClearanceLampWrapper>());
        m_registry->registerSignal("RearClearanceLamp", std::make_shared<lightstate::RearClearanceLampWrapper>());
        m_registry->registerSignal("FrontCornerLeftLamp", std::make_shared<lightstate::FrontCornerLeftLampWrapper>());
        m_registry->registerSignal("FrontCornerRightLamp", std::make_shared<lightstate::FrontCornerRightLampWrapper>());
        m_registry->registerSignal("FrontPos", std::make_shared<lightstate::FrontPosWrapper>());
  }

    void finalize(pc::vehiclemodel::VehicleModel* f_vehicleModel) override
    {
        if (f_vehicleModel == nullptr)
        {
            return;
        }
        // install global vehicle model updated callbacks to nested root-node (actual root node of loaded OSG file),
        // so they will be properly removed in case of a model reset
        osg::Group* l_nestedRoot = nullptr;
        if (0u < f_vehicleModel->getNumChildren())
        {
            l_nestedRoot = dynamic_cast<osg::Group*>(f_vehicleModel->getChild(0u)); // PRQA S 3077  // PRQA S 3400
        }
        // parse light node json file
        const osg::ref_ptr<WipingIndicatorAnimation> l_wipingIndicatorAnimation = new WipingIndicatorAnimation(*m_framework);
        LightStateParser                       l_parser(m_registry, l_wipingIndicatorAnimation.get());
        // XLOG_INFO_OS(g_AppContext) <<"[Inside svs]: m_lightStateFilePath is !!" <<
        // CONCAT_PATH_VEHICLE_MODEL(g_modelSettings->m_lightStateFilePath) << XLOG_ENDL;
        lightstate::LightNodeUpdateCallback* const l_lightNodeUpdateCallback =
            l_parser.parse(CONCAT_PATH_VEHICLE_MODEL(g_modelSettings->m_lightStateFilePath), f_vehicleModel);
        if ((l_lightNodeUpdateCallback != nullptr) && (l_nestedRoot != nullptr))
        {
            l_nestedRoot->addUpdateCallback(l_lightNodeUpdateCallback);
            l_nestedRoot->addUpdateCallback(l_wipingIndicatorAnimation.get());
        }
    }

protected:
    ~LightFinalizer() override = default;

private:
    LightFinalizer(const LightFinalizer&)            = delete;
    LightFinalizer& operator=(const LightFinalizer&) = delete;

    pc::core::Framework*                               m_framework;
    std::shared_ptr<lightstate::SignalWrapperRegistry> m_registry;
};

//!
//! VehicleNodeMaskFinalizer
//!
class VehicleNodeMaskFinalizer : public pc::vehiclemodel::IFinalizer
{
public:
    explicit VehicleNodeMaskFinalizer(pc::core::Framework* f_pFramework)
        : m_pFramework(f_pFramework)
    {
    }

    void finalize(pc::vehiclemodel::VehicleModel* f_vehicleModel) override // PRQA S 6041
    {
        if (f_vehicleModel == nullptr)
        {
            XLOG_ERROR(g_AppContext, "VehicleNodeMaskFinalizer::finalize - f_vehicleModel is nullptr");
            return;
        }
        osg::Node* l_node = nullptr;

        // set all node cull setting to default
        constexpr vfc::uint32_t l_otherComponentsNodeMask =
            0xffffffu &
            ~(static_cast<vfc::uint32_t>(
                static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::FRONT_LEFT_WHEEL_MASK) |
                static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::FRONT_RIGHT_WHEEL_MASK) |
                static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::REAR_LEFT_WHEEL_MASK) |
                static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::REAR_RIGHT_WHEEL_MASK) |
                static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::FRONT_RIGHT_DOOR_INTERIOR_MASK) |
                static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::FRONT_RIGHT_DOOR_MASK) |
                static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::FRONT_LEFT_DOOR_INTERIOR_MASK) |
                static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::FRONT_LEFT_DOOR_MASK) |
                static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::REAR_LEFT_DOOR_MASK) |
                static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::REAR_RIGHT_DOOR_MASK) | static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::TRUNK_DOOR_MASK) |
                static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::HOOD_DOOR_MASK) | static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::INTERIOR_MASK) |
                static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::FLOOR_PLANE_MASK)));
        pc::util::osgx::NodeMaskSetter l_nodeMaskParser(l_otherComponentsNodeMask);
        f_vehicleModel->accept(l_nodeMaskParser);

        // const bool l_isOptional = ((cc::core::g_ccf->m_ccfDoors != cc::core::CCF_DOORS_4DOOR) &&
        // (cc::core::g_ccf->m_ccfDoors != cc::core::CCF_DOORS_5DOOR)); const bool l_isOptional = false;

        // Name does not exactly match "Scene Root", since vehicle variant is included -> use node name finder
        pc::util::osgx::NodeNameFinder l_nodeFinder("Scene Root");
        f_vehicleModel->accept(l_nodeFinder);
        l_node = l_nodeFinder.getFoundNode();
        if (l_node != nullptr)
        {
            l_node->setNodeMask(0xffffffu);

            // Publish actual root name
            cc::core::CustomScene* const l_scene = static_cast<cc::core::CustomScene*>(m_pFramework->getScene());
            if (l_scene != nullptr)
            {
                l_scene->setVehicleModelName(l_nodeFinder.getNodeName());
            }
        }
        f_vehicleModel->setNodeMask(0xffffffu);

        // Modify the cull mask for the important parts
        l_node = f_vehicleModel->getComponent(pc::vehiclemodel::VehicleModel::FLOOR_PLANE);
        if (l_node != nullptr)
        {
            l_node->setNodeMask(static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::FLOOR_PLANE_MASK));
        }

        l_node = f_vehicleModel->getComponent(pc::vehiclemodel::VehicleModel::INTERIOR);
        if (l_node != nullptr)
        {
            l_node->setNodeMask(static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::INTERIOR_MASK));
        }

        // Wheels masks
        l_node = f_vehicleModel->getComponent(pc::vehiclemodel::VehicleModel::FRONT_LEFT_WHEEL);
        if (l_node != nullptr)
        {
            l_node->setNodeMask(static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::FRONT_LEFT_WHEEL_MASK));
            // set the children with the right nodemask
            pc::util::osgx::NodeMaskSetter l_nodeMaskParser1(
                static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::FRONT_LEFT_WHEEL_MASK));
            l_node->accept(l_nodeMaskParser1);
        }
        // brake caliper
        l_node = f_vehicleModel->getComponent(pc::vehiclemodel::VehicleModel::FRONT_LEFT_BRAKE);
        if (l_node != nullptr)
        {
            l_node->setNodeMask(static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::FRONT_LEFT_WHEEL_MASK));
        }

        l_node = f_vehicleModel->getComponent(pc::vehiclemodel::VehicleModel::REAR_LEFT_WHEEL);
        if (l_node != nullptr)
        {
            l_node->setNodeMask(static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::REAR_LEFT_WHEEL_MASK));
            // set the children with the right nodemask
            pc::util::osgx::NodeMaskSetter l_nodeMaskParser2(
                static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::REAR_LEFT_WHEEL_MASK));
            l_node->accept(l_nodeMaskParser2);
        }
        // brake caliper
        l_node = f_vehicleModel->getComponent(pc::vehiclemodel::VehicleModel::REAR_LEFT_BRAKE);
        if (l_node != nullptr)
        {
            l_node->setNodeMask(static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::REAR_LEFT_WHEEL_MASK));
        }

        l_node = f_vehicleModel->getComponent(pc::vehiclemodel::VehicleModel::FRONT_RIGHT_WHEEL);
        if (l_node != nullptr)
        {
            l_node->setNodeMask(static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::FRONT_RIGHT_WHEEL_MASK));
            // set the children with the right nodemask
            pc::util::osgx::NodeMaskSetter l_nodeMaskParser3(
                static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::FRONT_RIGHT_WHEEL_MASK));
            l_node->accept(l_nodeMaskParser3);
        }
        // brake caliper
        l_node = f_vehicleModel->getComponent(pc::vehiclemodel::VehicleModel::FRONT_RIGHT_BRAKE);
        if (l_node != nullptr)
        {
            l_node->setNodeMask(static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::FRONT_RIGHT_WHEEL_MASK));
        }

        l_node = f_vehicleModel->getComponent(pc::vehiclemodel::VehicleModel::REAR_RIGHT_WHEEL);
        if (l_node != nullptr)
        {
            l_node->setNodeMask(static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::REAR_RIGHT_WHEEL_MASK));
            // set the children with the right nodemask
            pc::util::osgx::NodeMaskSetter l_nodeMaskParser4(
                static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::REAR_RIGHT_WHEEL_MASK));
            l_node->accept(l_nodeMaskParser4);
        }
        // brake caliper
        l_node = f_vehicleModel->getComponent(pc::vehiclemodel::VehicleModel::REAR_RIGHT_BRAKE);
        if (l_node != nullptr)
        {
            l_node->setNodeMask(static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::REAR_RIGHT_WHEEL_MASK));
        }

        // doors masks
        l_node = f_vehicleModel->getComponent(pc::vehiclemodel::VehicleModel::FRONT_LEFT_DOOR);
        if (l_node != nullptr)
        {
            l_node->setNodeMask(static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::FRONT_LEFT_DOOR_MASK));
            pc::util::osgx::NodeMaskSetter l_nodeMaskParser5(
                static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::FRONT_LEFT_DOOR_MASK));
            l_node->accept(l_nodeMaskParser5);
        }

        l_node = f_vehicleModel->getComponent(pc::vehiclemodel::VehicleModel::FRONT_RIGHT_DOOR);
        if (l_node != nullptr)
        {
            l_node->setNodeMask(static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::FRONT_RIGHT_DOOR_MASK));
            pc::util::osgx::NodeMaskSetter l_nodeMaskParser6(
                static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::FRONT_RIGHT_DOOR_MASK));
            l_node->accept(l_nodeMaskParser6);
        }

        // Load the rear doors if the car has 4 or 5 doors
        l_node = f_vehicleModel->getComponent(pc::vehiclemodel::VehicleModel::REAR_LEFT_DOOR);
        if (l_node != nullptr)
        {
            l_node->setNodeMask(static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::REAR_LEFT_DOOR_MASK));
            pc::util::osgx::NodeMaskSetter l_nodeMaskParser7(
                static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::REAR_LEFT_DOOR_MASK));
            l_node->accept(l_nodeMaskParser7);
        }

        l_node = f_vehicleModel->getComponent(pc::vehiclemodel::VehicleModel::REAR_RIGHT_DOOR);
        if (l_node != nullptr)
        {
            l_node->setNodeMask(static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::REAR_RIGHT_DOOR_MASK));
            pc::util::osgx::NodeMaskSetter l_nodeMaskParser8(
                static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::REAR_RIGHT_DOOR_MASK));
            l_node->accept(l_nodeMaskParser8);
        }

        l_node = f_vehicleModel->getComponent(pc::vehiclemodel::VehicleModel::TRUNK);
        if (l_node != nullptr)
        {
            l_node->setNodeMask(static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::TRUNK_DOOR_MASK));
            pc::util::osgx::NodeMaskSetter l_nodeMaskParser9(
                static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::TRUNK_DOOR_MASK));
            l_node->accept(l_nodeMaskParser9);
        }

        l_node = f_vehicleModel->getComponent(pc::vehiclemodel::VehicleModel::HOOD);
        if (l_node != nullptr)
        {
            l_node->setNodeMask(static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::HOOD_DOOR_MASK));
            pc::util::osgx::NodeMaskSetter l_nodeMaskParser10(
                static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::HOOD_DOOR_MASK));
            l_node->accept(l_nodeMaskParser10);
        }

        // osgDB::writeNodeFile(*f_vehicleModel, "D:/temp/VehcileCulling.osg");
    }

private:
    pc::core::Framework* m_pFramework;
};

//!
//! VehicleDoorsGetteretter
//!
class VehicleDoorsGetter : public pc::vehiclemodel::IFinalizer
{
public:
    explicit VehicleDoorsGetter(osg::Group* f_targetGroup)
        : m_targetGroup(f_targetGroup)
    {
    }

    void finalize(pc::vehiclemodel::VehicleModel* f_vehicleModel) override
    {
        if (!m_targetGroup.valid() || (f_vehicleModel == nullptr))
        {
            return;
        }

        osg::Node* const l_frontLeftDoor  = f_vehicleModel->getComponent(pc::vehiclemodel::VehicleModel::FRONT_LEFT_DOOR);
        osg::Node* const l_frontRightDoor = f_vehicleModel->getComponent(pc::vehiclemodel::VehicleModel::FRONT_RIGHT_DOOR);
        osg::Node* const l_rearLeftDoor   = f_vehicleModel->getComponent(pc::vehiclemodel::VehicleModel::REAR_LEFT_DOOR);
        osg::Node* const l_rearRightDoor  = f_vehicleModel->getComponent(pc::vehiclemodel::VehicleModel::REAR_RIGHT_DOOR);
        osg::Node* const l_trunkDoor      = f_vehicleModel->getComponent(pc::vehiclemodel::VehicleModel::TRUNK);
        osg::Node* const l_hoodDoor       = f_vehicleModel->getComponent(pc::vehiclemodel::VehicleModel::HOOD);

        if (l_frontLeftDoor != nullptr)
        {
            m_targetGroup->addChild(l_frontLeftDoor); // PRQA S 3803
        }

        if (l_frontRightDoor != nullptr)
        {
            m_targetGroup->addChild(l_frontRightDoor); // PRQA S 3803
        }

        if (l_rearLeftDoor != nullptr)
        {
            m_targetGroup->addChild(l_rearLeftDoor); // PRQA S 3803
        }

        if (l_rearRightDoor != nullptr)
        {
            m_targetGroup->addChild(l_rearRightDoor); // PRQA S 3803
        }

        if (l_trunkDoor != nullptr)
        {
            m_targetGroup->addChild(l_trunkDoor); // PRQA S 3803
        }

        if (l_hoodDoor != nullptr)
        {
            m_targetGroup->addChild(l_hoodDoor); // PRQA S 3803
        }
        if ((l_frontLeftDoor != nullptr) || (l_frontRightDoor != nullptr) || (l_rearLeftDoor != nullptr) ||
            (l_rearRightDoor != nullptr) || (l_trunkDoor != nullptr) || (l_hoodDoor != nullptr))
        {
            osg::StateSet* const l_stateSet = m_targetGroup->getOrCreateStateSet();
            //! set this door node explicitly opaque
            l_stateSet->addUniform(new osg::Uniform("VehicleTransparency", 1.0f));
            l_stateSet->setMode(GL_BLEND, osg::StateAttribute::ON); // PRQA S 3143
        }
    }

protected:
    ~VehicleDoorsGetter() override = default;

private:
    //! Copy constructor is not permitted.
    VehicleDoorsGetter(const VehicleDoorsGetter& other) = delete;
    //! Copy assignment operator is not permitted.
    VehicleDoorsGetter& operator=(const VehicleDoorsGetter& other) = delete;

    osg::ref_ptr<osg::Group> m_targetGroup;
};

ColorSettings getColorSettingFromIndex(vfc::uint8_t f_index, pc::core::Framework* f_framework)
{
    if (f_framework == nullptr)
    {
        XLOG_ERROR(g_AppContext, "Vehicle.cpp getColorSettingFromIndex() - f_framework is nullptr, using color TimeGray");
        return g_modelColorSettings->m_TimeGray;
    }

    bool                             l_parkActive   = false;
    cc::target::common::EThemeTypeHU l_currentTheme = cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI;
    pc::core::View*                  l_newRefView   = nullptr;

    vfc::uint32_t l_newRefViewID = std::numeric_limits<vfc::uint32_t>::max();
    auto const    l_scene        = f_framework->asCustomFramework()->getScene();

    //! THEME
    if (f_framework->asCustomFramework()->m_SVSRotateStatusDaddy_Receiver.hasData())
    {
        const cc::daddy::SVSRotateStatusDaddy_t* const l_themeType =
            f_framework->asCustomFramework()->m_SVSRotateStatusDaddy_Receiver.getData();
        l_currentTheme = static_cast<cc::target::common::EThemeTypeHU>(l_themeType->m_Data); // PRQA S 3013
    }

    //! PARK ACTIVE
    if (f_framework->asCustomFramework()->m_parkHmiParkingStatusReceiver.hasData())
    {
        const cc::daddy::ParkStatusDaddy_t* const l_parkStatus =
            f_framework->asCustomFramework()->m_parkHmiParkingStatusReceiver.getData();
        l_parkActive = checkParkingActive(l_parkStatus->m_Data);
    }

    if (l_currentTheme == cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI && l_parkActive == true)
    {
        l_newRefView = l_scene->getView(cc::core::CustomViews::EView::HORI_PARKING_PLAN_VIEW);
    }

    if (l_currentTheme == cc::target::common::EThemeTypeHU::ETHEME_TYPE_VERT && l_parkActive == true)
    {
        l_newRefView = l_scene->getView(cc::core::CustomViews::EView::VERT_PARKING_PLAN_VIEW);
    }

    if (l_newRefView != nullptr)
    {
        if (l_newRefView->getNodeMask() != 0u)
        {
            // get view id from view
            l_newRefViewID = l_scene->getViewId(l_newRefView);
        }
    }

    if ((l_newRefViewID == cc::core::CustomViews::EView::HORI_PARKING_PLAN_VIEW ||
         l_newRefViewID == cc::core::CustomViews::EView::VERT_PARKING_PLAN_VIEW))
    {
        return g_modelColorSettings->m_SnowyWhite;
    }

    ColorSettings         l_colorSetting = g_modelColorSettings->m_TimeGray;
    const cc::daddy::EColorCode l_Color_index  = cc::daddy::EColorCode_Convert_uint8ToEnum(f_index);
    switch (l_Color_index)
    {
    case cc::daddy::TIME_GRAY:
    case cc::daddy::WINTER_GREY:
    {
        l_colorSetting = g_modelColorSettings->m_TimeGray;
        break;
    }
    case cc::daddy::MOUNTAIN_ASH:
    {
        l_colorSetting = g_modelColorSettings->m_MountainAsh;
        break;
    }
    case cc::daddy::RED_EMPEROR:
    {
        l_colorSetting = g_modelColorSettings->m_RedEmperor;
        break;
    }
    case cc::daddy::SNOWY_WHITE:
    case cc::daddy::MOON_WHITE:
    case cc::daddy::SGHC_SNOW_WHITHE:
    {
        l_colorSetting = g_modelColorSettings->m_SnowyWhite;
        break;
    }
    case cc::daddy::SILVER_GLAZE_WHITE:
    {
        l_colorSetting = g_modelColorSettings->m_SilverGlazeWhite;
        break;
    }
    // case cc::daddy::JUN_WARE_GRAY:
    // {
    //   l_colorSetting = g_modelColorSettings->m_JunWareGray;
    //   break;
    // }
    case cc::daddy::DU_DU_WHITE:
    {
        l_colorSetting = g_modelColorSettings->m_DuDuWhite;
        break;
    }
    case cc::daddy::SILVERSAND_BLACK:
    case cc::daddy::XUANKONG_BLACK:
    case cc::daddy::YAO_SHI_BLACK:
    case cc::daddy::SGHC_OBSIDIAN_BLACK:
    {
        l_colorSetting = g_modelColorSettings->m_SilverSandBlack;
        break;
    }
    case cc::daddy::WISDOM_BLUE:
    {
        l_colorSetting = g_modelColorSettings->m_WisdomBlue;
        break;
    }
    case cc::daddy::QIANSHAN_CUI:
    {
        l_colorSetting = g_modelColorSettings->m_QianshanCui;
        break;
    }
    case cc::daddy::AZURE:
    {
        l_colorSetting = g_modelColorSettings->m_Azure;
        break;
    }
    case cc::daddy::MUSHAN_PINK:
    {
        l_colorSetting = g_modelColorSettings->m_MushanPink;
        break;
    }
    case cc::daddy::WU_SONG_GOLDEN:
    {
        l_colorSetting = g_modelColorSettings->m_Golden;
        break;
    }
    case cc::daddy::HERMES_GREEN:
    case cc::daddy::SGHC_HERMES_GREEN:
    {
        l_colorSetting = g_modelColorSettings->m_HermesGreen;
        break;
    }
    case cc::daddy::SUNRISE_GOLD:
    {
        l_colorSetting = g_modelColorSettings->m_SunriseGolden;
        break;
    }
    case cc::daddy::BLACK_GOLD:
    {
        l_colorSetting = g_modelColorSettings->m_BlackGolden;
        break;
    }
    default:
    {
        l_colorSetting = g_modelColorSettings->m_HermesGreen;
        break;
    }
    }
    return l_colorSetting;
}

//!
//! Vehicle Diffuse Color
//!
void CarPaintFinalizer::VehicleDiffuseColorUpdateCallback::update(
    osg::Uniform* uniform,
    osg::NodeVisitor* /* f_nv */) // PRQA S 6043
{
    if (uniform == nullptr)
    {
        XLOG_ERROR(g_AppContext, "CarPaintFinalizer::VehicleDiffuseColorUpdateCallback::update - uniform is nullptr");
        return;
    }
    cc::core::CustomFramework* const l_pCustomFramework = m_pFramework->asCustomFramework();
    if (l_pCustomFramework->m_vehColorSts_ReceiverPort.isConnected())
    {
        const cc::daddy::ColorIndexDaddy* const l_pColorStateDaddy = l_pCustomFramework->m_vehColorSts_ReceiverPort.getData();
        if (l_pColorStateDaddy == nullptr)
        {
            return;
        }
        ColorSettings l_colorSetting = getColorSettingFromIndex(l_pColorStateDaddy->m_Data, m_pFramework);
        uniform->set(osg::Vec4f(
            static_cast<vfc::float32_t>(l_colorSetting.m_diffuseColor.x()) / 255.f, // PRQA S 3803
            static_cast<vfc::float32_t>(l_colorSetting.m_diffuseColor.y()) / 255.f,
            static_cast<vfc::float32_t>(l_colorSetting.m_diffuseColor.z()) / 255.f,
            static_cast<vfc::float32_t>(l_colorSetting.m_diffuseColor.a()) / 255.f));
    }

    // m_referenceViewId = l_curRefViewId;
}

//!
//! specColor1 Update
//!
void CarPaintFinalizer::VehicleSpecColor1UpdateCallback::update(osg::Uniform* uniform, osg::NodeVisitor*)
{
    if (uniform == nullptr)
    {
        XLOG_ERROR(g_AppContext, "CarPaintFinalizer::VehicleSpecColor1UpdateCallback::update - uniform is nullptr");
        return;
    }
    cc::core::CustomFramework* const l_pCustomFramework = m_pFramework->asCustomFramework();
    if (l_pCustomFramework->m_vehColorSts_ReceiverPort.isConnected())
    {
        const cc::daddy::ColorIndexDaddy* const l_pColorStateDaddy = l_pCustomFramework->m_vehColorSts_ReceiverPort.getData();
        ColorSettings l_colorSetting = getColorSettingFromIndex(l_pColorStateDaddy->m_Data, m_pFramework);
        uniform->set(osg::Vec4f(
            static_cast<vfc::float32_t>(l_colorSetting.m_specColor1.x()) / 255.f, // PRQA S 3803
            static_cast<vfc::float32_t>(l_colorSetting.m_specColor1.y()) / 255.f,
            static_cast<vfc::float32_t>(l_colorSetting.m_specColor1.z()) / 255.f,
            static_cast<vfc::float32_t>(l_colorSetting.m_specColor1.a()) / 255.f));
    }
}

//!
//! VehicleSpecShininess1UpdateCallback
//!
void CarPaintFinalizer::VehicleSpecShininess1UpdateCallback::update(osg::Uniform* uniform, osg::NodeVisitor*)
{
    if (uniform == nullptr)
    {
        XLOG_ERROR(g_AppContext, "CarPaintFinalizer::VehicleSpecShininess1UpdateCallback::update - uniform is nullptr");
        return;
    }
    cc::core::CustomFramework* const l_pCustomFramework = m_pFramework->asCustomFramework();
    if (l_pCustomFramework->m_vehColorSts_ReceiverPort.isConnected())
    {
        const cc::daddy::ColorIndexDaddy* const l_pColorStateDaddy = l_pCustomFramework->m_vehColorSts_ReceiverPort.getData();
        const ColorSettings l_colorSetting = getColorSettingFromIndex(l_pColorStateDaddy->m_Data, m_pFramework);
        uniform->set(l_colorSetting.m_specShininess1); // PRQA S 3803
    }
}

//!
//! SpecColor2 Update
//!
void CarPaintFinalizer::VehicleSpecColor2UpdateCallback::update(osg::Uniform* uniform, osg::NodeVisitor*)
{
    if (uniform == nullptr)
    {
        XLOG_ERROR(g_AppContext, "CarPaintFinalizer::VehicleSpecColor2UpdateCallback::update - uniform is nullptr");
        return;
    }
    cc::core::CustomFramework* const l_pCustomFramework = m_pFramework->asCustomFramework();
    if (l_pCustomFramework->m_vehColorSts_ReceiverPort.isConnected())
    {
        const cc::daddy::ColorIndexDaddy* const l_pColorStateDaddy = l_pCustomFramework->m_vehColorSts_ReceiverPort.getData();
        ColorSettings l_colorSetting = getColorSettingFromIndex(l_pColorStateDaddy->m_Data, m_pFramework);
        uniform->set(osg::Vec4f(
            static_cast<vfc::float32_t>(l_colorSetting.m_specColor2.x()) / 255.f, // PRQA S 3803
            static_cast<vfc::float32_t>(l_colorSetting.m_specColor2.y()) / 255.f,
            static_cast<vfc::float32_t>(l_colorSetting.m_specColor2.z()) / 255.f,
            static_cast<vfc::float32_t>(l_colorSetting.m_specColor2.a()) / 255.f));
    }
}

//!
//! VehicleSpecShininess2UpdateCallback
//!
void CarPaintFinalizer::VehicleSpecShininess2UpdateCallback::update(osg::Uniform* uniform, osg::NodeVisitor*)
{
    if (uniform == nullptr)
    {
        XLOG_ERROR(g_AppContext, "CarPaintFinalizer::VehicleSpecShininess2UpdateCallback::update - uniform is nullptr");
        return;
    }
    cc::core::CustomFramework* const l_pCustomFramework = m_pFramework->asCustomFramework();
    if (l_pCustomFramework->m_vehColorSts_ReceiverPort.isConnected())
    {
        const cc::daddy::ColorIndexDaddy* const l_pColorStateDaddy = l_pCustomFramework->m_vehColorSts_ReceiverPort.getData();
        const ColorSettings l_colorSetting = getColorSettingFromIndex(l_pColorStateDaddy->m_Data, m_pFramework);
        uniform->set(l_colorSetting.m_specShininess2); // PRQA S 3803
    }
}

//!
//! ReflectionUpdateCallback
//!
void CarPaintFinalizer::ReflectionUpdateCallback::update(osg::Uniform* uniform, osg::NodeVisitor*)
{
    if (uniform == nullptr)
    {
        XLOG_ERROR(g_AppContext, "CarPaintFinalizer::ReflectionUpdateCallback::update - uniform is nullptr");
        return;
    }
    cc::core::CustomFramework* const l_pCustomFramework = m_pFramework->asCustomFramework();

    if (l_pCustomFramework->m_vehColorSts_ReceiverPort.isConnected())
    {
        const cc::daddy::ColorIndexDaddy* const l_pColorStateDaddy = l_pCustomFramework->m_vehColorSts_ReceiverPort.getData();
        const ColorSettings l_colorSetting = getColorSettingFromIndex(l_pColorStateDaddy->m_Data, m_pFramework);
        uniform->set(l_colorSetting.m_reflectionPower); // PRQA S 3803
    }
}

//!
//! VehicleFresnelUpdateCallback
//!
void CarPaintFinalizer::VehicleFresnelUpdateCallback::update(osg::Uniform* uniform, osg::NodeVisitor*)
{
    if (uniform == nullptr)
    {
        XLOG_ERROR(g_AppContext, "CarPaintFinalizer::VehicleFresnelUpdateCallback::update - uniform is nullptr");
        return;
    }
    cc::core::CustomFramework* const l_pCustomFramework = m_pFramework->asCustomFramework();
    if (l_pCustomFramework->m_vehColorSts_ReceiverPort.isConnected())
    {
        const cc::daddy::ColorIndexDaddy* const l_pColorStateDaddy = l_pCustomFramework->m_vehColorSts_ReceiverPort.getData();
        const ColorSettings l_colorSetting = getColorSettingFromIndex(l_pColorStateDaddy->m_Data, m_pFramework);
        uniform->set(l_colorSetting.m_fresnel); // PRQA S 3803
    }
}

//!
//! LightPositionUpdateCallback
//!
void CarPaintFinalizer::LightPositionUpdateCallback::update(osg::Uniform* uniform, osg::NodeVisitor*)
{
    if (uniform == nullptr)
    {
        XLOG_ERROR(g_AppContext, "CarPaintFinalizer::LightPositionUpdateCallback::update - uniform is nullptr");
        return;
    }
    cc::core::CustomFramework* const l_pCustomFramework = m_pFramework->asCustomFramework();

    if (l_pCustomFramework->m_SVSRotateStatusDaddy_Receiver.isConnected() &&
        l_pCustomFramework->m_vehColorSts_ReceiverPort.isConnected())
    {
        const cc::daddy::SVSRotateStatusDaddy_t* const l_themeType =
            l_pCustomFramework->m_SVSRotateStatusDaddy_Receiver.getData();
        const cc::daddy::ColorIndexDaddy* const l_pColorStateDaddy = l_pCustomFramework->m_vehColorSts_ReceiverPort.getData();
        ColorSettings l_colorSetting = getColorSettingFromIndex(l_pColorStateDaddy->m_Data, m_pFramework);
        osg::Vec3f    l_lightPos     = g_modelColorSettings->m_TimeGray.m_lightPos; // default color is Time gray
        if (nullptr != l_themeType)
        {
            if (static_cast<vfc::uint8_t>(cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI) == l_themeType->m_Data)
            {
                l_lightPos = l_colorSetting.m_lightPos;
            }
            else // cc::target::common::EThemeTypeHU::ETHEME_TYPE_VERT
            {
                l_lightPos = osg::Vec3f(
                    -l_colorSetting.m_lightPos.y(), l_colorSetting.m_lightPos.x(), l_colorSetting.m_lightPos.z());
            }
        }
        uniform->set(l_lightPos); // PRQA S 3803
    }
}

//!
//! BrightnessUpdateCallback
//!
void CarPaintFinalizer::BrightnessUpdateCallback::update(osg::Uniform* uniform, osg::NodeVisitor*)
{
    if (uniform == nullptr)
    {
        XLOG_ERROR(g_AppContext, "CarPaintFinalizer::BrightnessUpdateCallback::update - uniform is nullptr");
        return;
    }
    cc::core::CustomFramework* const l_pCustomFramework = m_pFramework->asCustomFramework();
    if (l_pCustomFramework->m_vehColorSts_ReceiverPort.isConnected())
    {
        const cc::daddy::ColorIndexDaddy* const l_pColorStateDaddy = l_pCustomFramework->m_vehColorSts_ReceiverPort.getData();
        const ColorSettings l_colorSetting = getColorSettingFromIndex(l_pColorStateDaddy->m_Data, m_pFramework);
        uniform->set(l_colorSetting.m_brightness); // PRQA S 3803
    }
}

//!
//! VehicleChromeDiffuseColorUpdateCallback
//!
void CarPaintFinalizer::VehicleChromeDiffuseColorUpdateCallback::update(
    osg::Uniform* uniform,
    osg::NodeVisitor* /* f_nv */) // PRQA S 6043
{
    if (uniform == nullptr)
    {
        XLOG_ERROR(g_AppContext, "CarPaintFinalizer::VehicleChromeDiffuseColorUpdateCallback::update - uniform is nullptr");
        return;
    }
    if (m_pFramework->asCustomFramework()->m_vehColorSts_ReceiverPort.isConnected())
    {
        const cc::daddy::ColorIndexDaddy* const l_pIndexDaddy =
            m_pFramework->asCustomFramework()->m_vehColorSts_ReceiverPort.getData();
        if (nullptr != l_pIndexDaddy)
        {
            ColorSettings l_colorSetting = getColorSettingFromIndex(l_pIndexDaddy->m_Data, m_pFramework);
            uniform->set(osg::Vec4f(
                (l_colorSetting.m_chromeDiffuseColor.x()) / 255.f, // PRQA S 3803
                (l_colorSetting.m_chromeDiffuseColor.y()) / 255.f,
                (l_colorSetting.m_chromeDiffuseColor.z()) / 255.f,
                (l_colorSetting.m_chromeDiffuseColor.a()) / 255.f));
        }
    }
}

//!
//! VehicleChromeSpecColorUpdateCallback
//!
void CarPaintFinalizer::VehicleChromeSpecColorUpdateCallback::update(osg::Uniform* uniform, osg::NodeVisitor*)
{
    if (uniform == nullptr)
    {
        XLOG_ERROR(g_AppContext, "CarPaintFinalizer::VehicleChromeDiffuseColorUpdateCallback::update - uniform is nullptr");
        return;
    }
    if (m_pFramework->asCustomFramework()->m_vehColorSts_ReceiverPort.isConnected())
    {
        const cc::daddy::ColorIndexDaddy* const l_pColorStateDaddy =
            m_pFramework->asCustomFramework()->m_vehColorSts_ReceiverPort.getData();
        ColorSettings l_colorSetting = getColorSettingFromIndex(l_pColorStateDaddy->m_Data, m_pFramework);
        uniform->set(osg::Vec4f(
            (l_colorSetting.m_chromeSpecColor.x()) / 255.f,
            (l_colorSetting.m_chromeSpecColor.y()) / 255.f,
            (l_colorSetting.m_chromeSpecColor.z()) / 255.f,
            (l_colorSetting.m_chromeSpecColor.a()) / 255.f));
    }
}

//!
//! VehicleChromeSpecShininessUpdateCallback
//!
void CarPaintFinalizer::VehicleChromeSpecShininessUpdateCallback::update(osg::Uniform* uniform, osg::NodeVisitor*)
{
    if (uniform == nullptr)
    {
        XLOG_ERROR(g_AppContext, "CarPaintFinalizer::VehicleChromeSpecShininessUpdateCallback::update - uniform is nullptr");
        return;
    }
    if (m_pFramework->asCustomFramework()->m_vehColorSts_ReceiverPort.isConnected())
    {
        const cc::daddy::ColorIndexDaddy* const l_pColorStateDaddy =
            m_pFramework->asCustomFramework()->m_vehColorSts_ReceiverPort.getData();
        const ColorSettings l_colorSetting = getColorSettingFromIndex(l_pColorStateDaddy->m_Data, m_pFramework);
        uniform->set(l_colorSetting.m_chromeSpecShininess); // PRQA S 3803
    }
}

//!
//! VehicleChromeBrightnessUpdateCallback
//!
void CarPaintFinalizer::VehicleChromeBrightnessUpdateCallback::update(osg::Uniform* uniform, osg::NodeVisitor*)
{
    if (uniform == nullptr)
    {
        XLOG_ERROR(g_AppContext, "CarPaintFinalizer::VehicleChromeBrightnessUpdateCallback::update - uniform is nullptr");
        return;
    }
    if (m_pFramework->asCustomFramework()->m_vehColorSts_ReceiverPort.isConnected())
    {
        const cc::daddy::ColorIndexDaddy* const l_pColorStateDaddy =
            m_pFramework->asCustomFramework()->m_vehColorSts_ReceiverPort.getData();
        const ColorSettings l_colorSetting = getColorSettingFromIndex(l_pColorStateDaddy->m_Data, m_pFramework);
        uniform->set(l_colorSetting.m_chromeBrightness); // PRQA S 3803
    }
}

//!
//! VehicleChrome2BrightnessUpdateCallback
//!
void CarPaintFinalizer::VehicleChrome2BrightnessUpdateCallback::update(osg::Uniform* uniform, osg::NodeVisitor*)
{
    if (uniform == nullptr)
    {
        XLOG_ERROR(g_AppContext, "CarPaintFinalizer::VehicleChrome2BrightnessUpdateCallback::update - uniform is nullptr");
        return;
    }
    if (m_pFramework->asCustomFramework()->m_vehColorSts_ReceiverPort.isConnected())
    {
        const cc::daddy::ColorIndexDaddy* const l_pColorStateDaddy =
            m_pFramework->asCustomFramework()->m_vehColorSts_ReceiverPort.getData();
        const ColorSettings l_colorSetting = getColorSettingFromIndex(l_pColorStateDaddy->m_Data, m_pFramework);
        uniform->set(l_colorSetting.m_chrome2Brightness); // PRQA S 3803
    }
}

//!
//! VehicleChrome3BrightnessUpdateCallback
//!
void CarPaintFinalizer::VehicleChrome3BrightnessUpdateCallback::update(osg::Uniform* uniform, osg::NodeVisitor*)
{
    if (nullptr == uniform)
    {
        return;
    }
#if 0
    if (m_pFramework->asCustomFramework()->m_vehColorSts_ReceiverPort.isConnected())
    {
        const cc::daddy::ColorIndexDaddy* const l_pColorStateDaddy =
            m_pFramework->asCustomFramework()->m_vehColorSts_ReceiverPort.getData();
        const ColorSettings l_colorSetting = getColorSettingFromIndex(l_pColorStateDaddy->m_Data, m_pFramework);
        uniform->set(l_colorSetting.m_chrome3Brightness); // PRQA S 3803
    }
#else // for byd sghl
    if (m_pFramework->asCustomFramework()->m_vehColorSts_ReceiverPort.isConnected())
    {
        const cc::daddy::ColorIndexDaddy* const l_pColorStateDaddy =
            m_pFramework->asCustomFramework()->m_vehColorSts_ReceiverPort.getData();
        const ColorSettings l_colorSetting = getColorSettingFromIndex(l_pColorStateDaddy->m_Data, m_pFramework);
        if(l_colorSetting.m_chrome3Brightness < 100.0f )
        {
            uniform->set(l_colorSetting.m_chrome3Brightness); // PRQA S 3803
        }
        else
        {
            // m_chrome3Brightness is using special value, it's index
            if (m_pFramework->asCustomFramework()->m_GoldenEmblem_ReceiverPort.isConnected())
            {
                const cc::daddy::GoldenEmblem_t* const l_pGoldenEmblemDaddy= m_pFramework->asCustomFramework()->m_GoldenEmblem_ReceiverPort.getData();
                if (l_pGoldenEmblemDaddy== nullptr)
                {
                    return;
                }
                const bool l_isGolden =l_pGoldenEmblemDaddy->m_Data;
                if(l_isGolden)
                {
                    // golden logo
                    uniform->set(300.0f);
                }
                else
                {
                    // sliver logo
                    uniform->set(200.0f);
                }
            }
        }
    }
#endif
}

//!
//! VehicleVeh2dBrightnessUpdateCallback
//!
void CarPaintFinalizer::VehicleVeh2dBrightnessUpdateCallback::update(osg::Uniform* uniform, osg::NodeVisitor*)
{
    if (uniform == nullptr)
    {
        XLOG_ERROR(g_AppContext, "CarPaintFinalizer::VehicleVeh2dBrightnessUpdateCallback::update - uniform is nullptr");
        return;
    }
    if (m_pFramework->asCustomFramework()->m_vehColorSts_ReceiverPort.isConnected())
    {
        const cc::daddy::ColorIndexDaddy* const l_pColorStateDaddy =
            m_pFramework->asCustomFramework()->m_vehColorSts_ReceiverPort.getData();
        const ColorSettings l_colorSetting = getColorSettingFromIndex(l_pColorStateDaddy->m_Data, m_pFramework);
        uniform->set(l_colorSetting.m_veh2dBrightness); // PRQA S 3803
    }
}

#ifdef DOORWARN
class DoorWarningUpdateCallback : public osg::NodeCallback
{
public:
    explicit DoorWarningUpdateCallback(
        pc::core::Framework*                      pFramework,
        std::string                               f_uniform,
        pc::vehiclemodel::VehicleModel::Component f_componentId)
        : m_componentId(f_componentId)
        , m_pFramework(pFramework)
        , m_uniformName(f_uniform)
    {
    }

    virtual ~DoorWarningUpdateCallback() = default;

    const std::string& getUniformName() const
    {
        return m_uniformName;
    }

    void operator()(osg::Node* f_node, osg::NodeVisitor* f_nv) override
    {
        update(f_node, f_nv);
        traverse(f_node, f_nv);
    }

    void update(osg::Node* f_node, osg::NodeVisitor* f_nv);

protected:
    bool                                      getSignalState() const;
    pc::vehiclemodel::VehicleModel::Component m_componentId;

private:
    //! Copy constructor is not permitted.
    DoorWarningUpdateCallback(const DoorWarningUpdateCallback& other); // = delete
    //! Copy assignment operator is not permitted.
    DoorWarningUpdateCallback& operator=(const DoorWarningUpdateCallback& other); // = delete

    pc::core::Framework* m_pFramework;

    std::string m_uniformName;
};

void DoorWarningUpdateCallback::update(osg::Node* f_node, osg::NodeVisitor* f_nv)
{
    bool           l_open     = getSignalState();
    osg::StateSet* l_stateSet = f_node->getOrCreateStateSet();
    osg::Uniform*  l_uniform  = l_stateSet->getOrCreateUniform(m_uniformName, osg::Uniform::FLOAT);
    if (l_open)
    {
        l_uniform->set(g_modelSettings->m_doorWarningTrans); // PRQA S 3803
    }
    else
    {
        l_uniform->set(0.0f); // PRQA S 3803
    }
}

bool DoorWarningUpdateCallback::getSignalState() const
{
    const pc::daddy::DoorStateDaddy*     l_doorStateDaddy = m_pFramework->m_doorStateReceiver.getData();
    const cc::daddy::DoorLockStsDaddy_t* l_doorLockStsDaddy =
        m_pFramework->asCustomFramework()->m_DoorLockSts_ReceiverPort.getData();
    if (nullptr != l_doorStateDaddy && nullptr != l_doorLockStsDaddy)
    {
        switch (m_componentId)
        {
        case pc::vehiclemodel::VehicleModel::FRONT_LEFT_DOOR:
            return (
                (pc::daddy::CARDOORSTATE_CLOSED == l_doorStateDaddy->m_Data[pc::daddy::CARDOOR_FRONT_LEFT]) &&
                (cc::daddy::DoorLock_OPEN == l_doorLockStsDaddy->m_Data.m_FLdoorLockStatus));
        case pc::vehiclemodel::VehicleModel::FRONT_RIGHT_DOOR:
            return (
                (pc::daddy::CARDOORSTATE_CLOSED == l_doorStateDaddy->m_Data[pc::daddy::CARDOOR_FRONT_RIGHT]) &&
                (cc::daddy::DoorLock_OPEN == l_doorLockStsDaddy->m_Data.m_FRdoorLockStatus));
        case pc::vehiclemodel::VehicleModel::REAR_LEFT_DOOR:
            return (
                (pc::daddy::CARDOORSTATE_CLOSED == l_doorStateDaddy->m_Data[pc::daddy::CARDOOR_REAR_LEFT]) &&
                (cc::daddy::DoorLock_OPEN == l_doorLockStsDaddy->m_Data.m_RLdoorLockStatus));
        case pc::vehiclemodel::VehicleModel::REAR_RIGHT_DOOR:
            return (
                (pc::daddy::CARDOORSTATE_CLOSED == l_doorStateDaddy->m_Data[pc::daddy::CARDOOR_REAR_RIGHT]) &&
                (cc::daddy::DoorLock_OPEN == l_doorLockStsDaddy->m_Data.m_RRdoorLockStatus));
        case pc::vehiclemodel::VehicleModel::TRUNK:
            return (
                (pc::daddy::CARDOORSTATE_CLOSED == l_doorStateDaddy->m_Data[pc::daddy::CARDOOR_TRUNK]) &&
                (cc::daddy::DoorLock_OPEN == l_doorLockStsDaddy->m_Data.m_TrunkLockStatus));
        case pc::vehiclemodel::VehicleModel::HOOD:
            return (pc::daddy::CARDOORSTATE_OPEN == l_doorStateDaddy->m_Data[pc::daddy::CARDOOR_HOOD]);
        default:
            break;
        }
    }
    return false;
}

//!
//! DoorsWarningFinalizer
//!
class DoorsWarningFinalizer : public pc::vehiclemodel::IFinalizer
{
public:
    DoorsWarningFinalizer(pc::core::Framework* f_framework)
        : m_framework(f_framework)
    {
    }

    void finalize(pc::vehiclemodel::VehicleModel* f_vehicleModel) override
    {
        osg::Uniform* l_uniform =
            f_vehicleModel->getOrCreateStateSet()->getOrCreateUniform(g_uniformNameDoorOpen, osg::Uniform::FLOAT);
        l_uniform->set(0.0f); // PRQA S 3803
        typedef std::pair<pc::vehiclemodel::VehicleModel::Component, osg::StateAttribute::OverrideValue>
                                                          ComponentOverrideValuePair;
        typedef std::array<ComponentOverrideValuePair, 7> ComponentArray;
        const ComponentArray                              l_doors = {
            ComponentOverrideValuePair(pc::vehiclemodel::VehicleModel::FRONT_LEFT_DOOR, osg::StateAttribute::OVERRIDE),
            ComponentOverrideValuePair(pc::vehiclemodel::VehicleModel::FRONT_RIGHT_DOOR, osg::StateAttribute::OVERRIDE),
            ComponentOverrideValuePair(pc::vehiclemodel::VehicleModel::REAR_LEFT_DOOR, osg::StateAttribute::OVERRIDE),
            ComponentOverrideValuePair(pc::vehiclemodel::VehicleModel::REAR_RIGHT_DOOR, osg::StateAttribute::OVERRIDE),
            ComponentOverrideValuePair(pc::vehiclemodel::VehicleModel::TRUNK, osg::StateAttribute::OVERRIDE),
            ComponentOverrideValuePair(
                pc::vehiclemodel::VehicleModel::FRONT_LEFT_DOOR_MIRROR,
                osg::StateAttribute::OVERRIDE | osg::StateAttribute::PROTECTED),
            ComponentOverrideValuePair(
                pc::vehiclemodel::VehicleModel::FRONT_RIGHT_DOOR_MIRROR,
                osg::StateAttribute::OVERRIDE | osg::StateAttribute::PROTECTED)};
        for (ComponentArray::const_iterator l_itr = l_doors.begin(); l_itr != l_doors.end(); ++l_itr)
        {
            osg::Node* l_node = f_vehicleModel->getComponent(l_itr->first);
            if (l_node != nullptr)
            {
                l_node->addUpdateCallback(
                    new DoorWarningUpdateCallback(m_framework, g_uniformNameDoorOpen, l_itr->first));
            }
        }
    }

protected:
    virtual ~DoorsWarningFinalizer() = default;

    DoorsWarningFinalizer(const DoorsWarningFinalizer&)            = delete;
    DoorsWarningFinalizer& operator=(const DoorsWarningFinalizer&) = delete;

private:
    pc::core::Framework* m_framework;
};

#endif


//!
//! StateSetFinalizer
//!
class StateSetFinalizer : public pc::vehiclemodel::IFinalizer
{
public:
  StateSetFinalizer(pc::core::Framework* f_framework)
    : m_framework (f_framework)
    , m_StateSetUpdater(nullptr)
  {
  }
  ~StateSetFinalizer() = default;

  virtual void finalize(pc::vehiclemodel::VehicleModel* f_vehicleModel);

protected:
  StateSetFinalizer(const StateSetFinalizer&) = delete;
  StateSetFinalizer& operator = (const StateSetFinalizer&) = delete;

private:
  pc::core::Framework* m_framework;

  osg::ref_ptr<StateSetUpdater> m_StateSetUpdater;
};


void StateSetFinalizer::finalize(pc::vehiclemodel::VehicleModel* f_vehicleModel)
{
    if (nullptr != f_vehicleModel)
    {
        m_StateSetUpdater = new StateSetUpdater;
        //m_StateSetUpdater->setOldCarpintStateSetName("StateSet_Crystal");
        m_StateSetUpdater->initNewStateSetForCrystal();
        m_StateSetUpdater->initNewStateSetForPBRCarpaint();
        f_vehicleModel->accept(*m_StateSetUpdater);
    }
}


class TransparencySettingVisitor : public osg::NodeVisitor
{
public:
    TransparencySettingVisitor(float f_modelalpha, float f_modelalphaInterior)
        : osg::NodeVisitor(osg::NodeVisitor::TraversalMode::TRAVERSE_ALL_CHILDREN)
        , m_modelalpha(f_modelalpha)
        , m_modelalphaInterior(f_modelalphaInterior)
    {
    }

    void apply(osg::Node& node)
    {
        osg::Geode* const l_tmpGeode = node.asGeode();
        if (l_tmpGeode != nullptr)
        {
            for (unsigned int idx = 0; idx < l_tmpGeode->getNumDrawables(); idx++)
            {
                osg::Drawable* const l_tmpDrawable = l_tmpGeode->getDrawable(idx);
                osg::Geometry* const l_tmpGeometry = l_tmpDrawable->asGeometry();

                if (l_tmpGeometry != nullptr)
                {
                    osg::StateSet* const l_pStateSet = l_tmpGeometry->getStateSet();
                    if (l_pStateSet != nullptr)
                    {
                        // osg::ref_ptr<osg::Uniform> l_uniform = l_pStateSet->getUniform("modelAlpha");
                        const osg::ref_ptr<osg::Uniform> l_uniform =
                            l_pStateSet->getOrCreateUniform("modelAlpha", osg::Uniform::FLOAT);
                        if (l_uniform != nullptr)
                        {
                            l_uniform->set(m_modelalpha);
                        }

                        l_pStateSet->setMode(GL_BLEND, osg::StateAttribute::ON); // PRQA S 3143

                        // osg::ref_ptr<osg::Uniform> l_uniformInterior = l_pStateSet->getUniform("modelAlphaInterior");
                        // if (l_uniformInterior)
                        // {
                        //     l_uniformInterior->set(m_modelalphaInterior);
                        // }
                    }
                }
            }
        }
        traverse(node);
    }

private:
    float m_modelalpha         {0.0f};
    float m_modelalphaInterior {0.0f};
};

//!
//! VehicleTransparencyUpdateCallback
//!
void VehicleTransparencyUpdateCallback::update(osg::Node* f_node, osg::NodeVisitor* /* f_nv */)
{
	if (f_node == nullptr)
    {
        XLOG_ERROR(g_AppContext, "VehicleTransparencyUpdateCallback::update - f_node or f_nv is nullptr");
        return;
    }
	using namespace cc::target::common;
    osg::StateSet* const l_stateSet = f_node->getStateSet();
    const osg::ref_ptr<osg::Uniform> l_uniform  = l_stateSet->getOrCreateUniform("modelAlpha", osg::Uniform::FLOAT);
    vfc::float32_t l_transparentLevel = 0;

    //! Super transparent mode
    auto& l_superTransparentModeReceiverPort = m_pFramework->asCustomFramework()->m_HUvehTransLevelReceiver;
    if (l_superTransparentModeReceiverPort.hasData())
    {
        const auto* const l_pData = l_superTransparentModeReceiverPort.getData().front();
        if (l_pData != nullptr)
        {
            l_transparentLevel = l_pData->m_Data;
        }
    }

    if (static_cast<bool>(m_VehicleCrystalBodyNode) && static_cast<bool>(m_VehicleMainBodyNode))
    {
    //   if (m_transparencyLevel != sm_transparencyLevel)
    //   {

        m_transparencyLevel = sm_transparencyLevel;

        if (isEqual(sm_transparencyLevel,0.15f) && (l_transparentLevel == 3.0f))
        {
            //special value for crystal model
            m_VehicleMainBodyNode->setNodeMask(0u);
            m_VehicleCrystalBodyNode->setNodeMask(0xffffffffu);
            l_stateSet->setMode(GL_BLEND, osg::StateAttribute::OFF); // PRQA S 3143
            TransparencySettingVisitor l_TransparencySettingVisitor(0.0f, 0.0f);
            f_node->accept(l_TransparencySettingVisitor);
        }
        else
        {
            m_VehicleMainBodyNode->setNodeMask(0xffffffffu);
            m_VehicleCrystalBodyNode->setNodeMask(0u);
            l_stateSet->setMode(GL_BLEND, osg::StateAttribute::ON); // PRQA S 3143
            TransparencySettingVisitor l_TransparencySettingVisitor(1.0f - sm_transparencyLevel, 0.98f);
            // printf("Transparency alpha = %f \n", 1.0f - sm_transparencyLevel);
            f_node->accept(l_TransparencySettingVisitor);
        }
    //   }
    }

}

void VehicleTransparencyFinalizer::finalize(pc::vehiclemodel::VehicleModel* f_vehicleModel)
{
    if (nullptr == f_vehicleModel)
    {
        return;
    }
    for (vfc::uint32_t componentID = pc::vehiclemodel::VehicleModel::FRONT_LEFT_WHEEL;
         componentID <= pc::vehiclemodel::VehicleModel::REAR_RIGHT_WHEEL;
         componentID++)
    {
        vfc::uint32_t numOfChildren =
            0u; // =
                // f_vehicleModel->getComponent(static_cast<pc::vehiclemodel::VehicleModel::Component>(componentID))->asTransform()->asMatrixTransform()->getNumChildren();
        osg::Node* const f_node =
            f_vehicleModel->getComponent(static_cast<pc::vehiclemodel::VehicleModel::Component>(componentID));
        numOfChildren = f_node->asGroup()->getNumChildren();

        for (vfc::uint32_t childID = 0u; childID < numOfChildren; childID++)
        {
            osg::StateSet* const l_stateSet = f_node->asGroup()->getChild(childID)->getOrCreateStateSet();
            if (nullptr != l_stateSet)
            {
                l_stateSet->setRenderBinDetails(pc::core::g_renderOrder->m_carOpaque, "RenderBin");
            }
            else
            {
                // pf code. #code looks fine
                XLOG_ERROR(g_AppContext, "VehicleTransparencyFinalizer::finalize()::l_stateSet is nullptr\"");
            }
        }
    }

    pc::util::osgx::NodeFinder l_nodeFinder("");

    l_nodeFinder.setNodeName(m_vehicleCrystalBodyNodeName);
    f_vehicleModel->accept(l_nodeFinder);
    osg::Node* const l_VehicleCrystalBodyNode = l_nodeFinder.getFoundNode();
    if (l_VehicleCrystalBodyNode != nullptr)
    {
        //in-visible it by default
        l_VehicleCrystalBodyNode->setNodeMask(0u);
        XLOG_INFO(g_AppContext, "Found Vehicle Crystal Body Node Node: " << l_nodeFinder.getNodeName() <<",   set it as in-visible by default");
    }
    else
    {
        XLOG_ERROR(g_AppContext, "Not Found Vehicle Crystal Body Node Node: " << l_nodeFinder.getNodeName());
    }

    l_nodeFinder.setNodeName(m_vehicleMainBodyNodeName);
    f_vehicleModel->accept(l_nodeFinder);
    osg::Node* const l_VehicleMainBodyNode = l_nodeFinder.getFoundNode();
    if (l_VehicleMainBodyNode != nullptr)
    {
        //l_VehicleMainBodyNode->setNodeMask(0u);
        XLOG_INFO(g_AppContext, "Found Vehicle Main Body Node " << l_nodeFinder.getNodeName());
    }
    else
    {
        XLOG_ERROR(g_AppContext, "Not Found Vehicle Main Body Node " << l_nodeFinder.getNodeName());
    }

    for (vfc::uint32_t i = 0; i < f_vehicleModel->getNumChildren(); i++)
    {
        osg::Node* const l_node =f_vehicleModel->getChild(i);
        l_node->addUpdateCallback(new VehicleTransparencyUpdateCallback(m_framework, l_VehicleCrystalBodyNode, l_VehicleMainBodyNode));
    }

}

void VehicleSeatFinalizer::setNodeMask(
    const std::string&              f_name,
    pc::vehiclemodel::VehicleModel* f_vehicleModel,
    bool                            f_enable)
{
    if (nullptr == f_vehicleModel)
    {
        return;
    }
    pc::util::osgx::NodeFinder l_nodeFinder(f_name);
    f_vehicleModel->accept(l_nodeFinder);
    osg::Node* const l_node = l_nodeFinder.getFoundNode();
    if (l_node != nullptr)
    {
        l_node->setNodeMask(f_enable ? 0xffffffffu : 0u);
    }
    else
    {
        XLOG_ERROR(
            g_AppContext,
            "VehicleSeatFinalizer::getComponent( " << l_nodeFinder.getNodeName()
                                                   << ") could not be found in vehicle model scene graph");
    }
}

void VehicleSeatFinalizer::finalize(pc::vehiclemodel::VehicleModel* f_vehicleModel)
{
    // get number of seats from daddy
    const bool is7Seat = g_stateMachineParams->m_vehicleModels == 17; // STHPA
    const bool is6Seat = g_stateMachineParams->m_vehicleModels == 16;

    if (is6Seat)
    {
        setNodeMask("Int_base_5_Seater", f_vehicleModel, is7Seat);
        setNodeMask("Seats_5_Seater", f_vehicleModel, is7Seat);
        setNodeMask("Int_base_6_Seater", f_vehicleModel, is6Seat);
        setNodeMask("Seats_6_Seater", f_vehicleModel, is6Seat);
    }
    else
    {
        // do nothing for STHPA based on vehicle model 2023.08.15
    }
}

//!
//! Vehicle
//!
Vehicle::Vehicle(
    cc::core::AssetId    f_assetId,
    pc::core::Framework* f_framework,
    osg::Group*          f_frontWheelsGroup,
    osg::Group*          f_allWheelsGroup,
    osg::Group*          f_vehicleDoorsGroup)
    : Asset(f_assetId)
{
    // pc::vehiclemodel::VehicleModel* l_vehicleModel =  new pc::vehiclemodel::VehicleModel(f_framework);
    CustomVehicleModel* const l_vehicleModel = new CustomVehicleModel(f_framework);
    l_vehicleModel->addFinalizer(new StateSetFinalizer(f_framework));
    l_vehicleModel->addFinalizer(
        new pc::vehiclemodel::RenderBinFinalizer(core::RENDERBIN_ORDER_CAR_OPAQUE, core::RENDERBIN_ORDER_CAR_GLASS));
    l_vehicleModel->addFinalizer(new pc::vehiclemodel::WheelAnimationFinalizer());
    l_vehicleModel->addFinalizer(new pc::vehiclemodel::BrakeAnimationFinalizer());
    l_vehicleModel->addFinalizer(new pc::vehiclemodel::DoorAnimationFinalizer());
    l_vehicleModel->addFinalizer(new pc::vehiclemodel::InstallPostProcessingCallbackFinalizer());
    // l_vehicleModel->addFinalizer(new DoorsWarningFinalizer(f_framework));
    l_vehicleModel->addFinalizer(new FrontWheelsGetter(f_frontWheelsGroup));
    l_vehicleModel->addFinalizer(new AllWheelsGetter(f_allWheelsGroup, f_framework));
    l_vehicleModel->addFinalizer(new VehicleDoorsGetter(f_vehicleDoorsGroup));
    l_vehicleModel->addFinalizer(new LightFinalizer(f_framework));
    l_vehicleModel->addFinalizer(new VehicleNodeMaskFinalizer(f_framework));
    l_vehicleModel->addFinalizer(new CarPaintFinalizer(f_framework));
    l_vehicleModel->addFinalizer(new VehicleTransparencyFinalizer(f_framework));
    // l_vehicleModel->addFinalizer(new VehicleBodyFinalizer());
    l_vehicleModel->addFinalizer(new pc::vehiclemodel::CreateSteadyStateProxyFinalizer());
    l_vehicleModel->addFinalizer(new cc::assets::common::VehicleSeatFinalizer(f_framework));

    // l_vehicleModel->setMatrix( osg::Matrixf::translate(0.0f, 0.0f, pc::vehicle::g_mechanicalData->m_wheelRadius)
    // );//magic number. Official model positions car on ground level
    m_asset = l_vehicleModel;
    osg::Group::addChild(m_asset); // PRQA S 3803
}

//!
//! VehicleReadCommand
//!
class VehicleReadCommand : public pc::util::cli::CommandCallback
{
public:
    bool invoke(std::istream&, std::ostream& f_output, const pc::util::cli::CommandLineInterface& f_cli) override
    {
        pc::core::Framework* l_framework = nullptr;
        const bool                 l_res       = f_cli.getUserData().getValue<pc::core::Framework*>(l_framework);
        assert(l_res && (nullptr != l_framework));
        cc::core::CustomScene* const l_scene = static_cast<cc::core::CustomScene*>(l_framework->getScene());

        // Get vehicle model
        if (nullptr != l_scene)
        {
            f_output << l_scene->getVehicleModelName() << newline; // PRQA S 3803
        }
        return true;
    }

    void getDescription(std::ostream& f_output) const override
    {
        f_output << "Get a name of vehicle model root node" << newline; // PRQA S 3803
    }

    void getHelp(std::ostream& f_output) const override
    {
        f_output << newline; // PRQA S 3803
    }
};

pc::util::cli::Command<VehicleReadCommand> g_vehicleReadCommand("vehicleversion");

} // namespace common
} // namespace assets
} // namespace cc
