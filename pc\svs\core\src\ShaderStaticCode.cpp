#include <string>
#include <map>

#include "pc/svs/util/logging/inc/LoggingContexts.h"
#include "pc/svs/core/inc/ShaderStaticCode.h"

namespace pc
{
namespace core
{
namespace
{

using pc::util::logging::g_EngineContext;

const static std::string s_SnapshotToHistory_frag =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2017 by Robert <PERSON>sch GmbH. All rights reserved. \n"
"// This file is property of Robert <PERSON>sch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"precision highp float; \n"
" \n"
"varying vec2 v_texCoord0; \n"
"varying vec2 v_texCoord1; \n"
" \n"
"uniform sampler2D u_tex0; //!< snapshot texture \n"
"uniform sampler2D u_tex1; //!< history texture \n"
" \n"
"void main() \n"
"{ \n"
"  // sample both texture at the right coordinates (already accounted for odometry) \n"
"  vec4 l_tex0Color = texture2D(u_tex0, v_texCoord0); // snapshot \n"
"  vec4 l_tex1Color = texture2D(u_tex1, v_texCoord1); // history \n"
" \n"
"  // Since snapshot texture is being translated & rotated, alpha value \n"
"  // seems to take values between 0 and 1. \n"
"  // Use a discreet mixing value to avoid having holes in the final rendering. \n"
" \n"
"  float l_discret = 1.0; \n"
"  if (l_tex0Color.a == 1.0) // TODO: try to avoid using if \n"
"  { \n"
"    l_discret = 0.0; \n"
"  } \n"
"  vec4 l_color = mix(l_tex0Color, l_tex1Color, l_discret); \n"
" \n"
"  gl_FragColor = l_color; \n"
"} \n"
;

const static std::string s_SnapshotToHistory_vert =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2017 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"attribute vec4 osg_Vertex; \n"
"attribute vec2 osg_MultiTexCoord0; \n"
"uniform mat4 osg_ModelViewProjectionMatrix; \n"
" \n"
"varying vec2 v_texCoord0; //!< texture coordinates for snapshot \n"
"varying vec2 v_texCoord1; //!< texture coordinates for history \n"
" \n"
"uniform mat4 u_historyTextureToSnapshotTexture; \n"
" \n"
"void main() \n"
"{ \n"
"  // snapshot texture moves with the vehicle, so the texture coordinates corresponding to history, need to be \n"
"  // shifted to the current snapshot pose for sampling \n"
"  v_texCoord0 = (u_historyTextureToSnapshotTexture * vec4(osg_MultiTexCoord0, 1.0, 1.0)).xy; \n"
" \n"
"  // history texture coordinates do not need to be modified, since we are only moving the snapshot texture \n"
"  v_texCoord1 = osg_MultiTexCoord0; \n"
" \n"
"  gl_Position = osg_ModelViewProjectionMatrix * osg_Vertex; \n"
"} \n"
;

const static std::string s_advancedTex_frag =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2017 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"precision highp float; \n"
" \n"
"varying vec4 v_color; \n"
"varying vec2 v_texCoord; \n"
" \n"
"uniform sampler2D u_tex0; \n"
"uniform float u_alpha; \n"
"uniform float u_bias; \n"
" \n"
"void main() \n"
"{ \n"
"  vec4 l_color = v_color * texture2D(u_tex0, v_texCoord, u_bias); \n"
"  l_color.a *= u_alpha; \n"
"  gl_FragColor = l_color; \n"
"} \n"
;

const static std::string s_advancedTex_vert =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2017 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"attribute vec4 osg_Vertex; \n"
"attribute vec4 osg_Color; \n"
"attribute vec2 osg_MultiTexCoord0; \n"
" \n"
"uniform mat4 osg_ModelViewProjectionMatrix; \n"
"uniform vec4 u_texSelect; \n"
" \n"
"varying vec4 v_color; \n"
"varying vec2 v_texCoord; \n"
" \n"
"void main() \n"
"{ \n"
"  v_color = osg_Color; \n"
"  vec2 l_texCoord = osg_MultiTexCoord0 * u_texSelect.xy; \n"
"  v_texCoord = l_texCoord + u_texSelect.zw; \n"
"  gl_Position = osg_ModelViewProjectionMatrix * osg_Vertex; \n"
"} \n"
;

const static std::string s_augmentedViewInnerTransition_frag =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2017 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"precision highp float; \n"
"varying float v_wavefrontBlendingFactor; \n"
" \n"
"void main() \n"
"{ \n"
"  float alpha = 0.5*clamp(1.0 - v_wavefrontBlendingFactor, 0.0, 2.0); \n"
"  gl_FragColor = vec4(0.0, 0.0, 0.0, alpha); \n"
"} \n"
;

const static std::string s_augmentedViewInnerTransition_vert =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2017 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"attribute vec4 osg_Vertex; \n"
" \n"
"uniform highp mat4 osg_ModelViewProjectionMatrix; \n"
" \n"
"uniform highp vec3  u_wavefrontCenter; \n"
"uniform highp float u_wavefrontRadius; \n"
"uniform highp float u_wavefrontWidth; \n"
" \n"
"varying highp float v_wavefrontBlendingFactor; \n"
" \n"
" \n"
"void main() \n"
"{ \n"
"  highp float displacement = max(0.0, u_wavefrontRadius + osg_Vertex.z * u_wavefrontWidth); \n"
" \n"
"  highp vec3 vertex = vec3(osg_Vertex.xy * displacement, 0.0); \n"
" \n"
"  highp float radius = length(vertex.xy); \n"
" \n"
"  // Distance from outer wavefront radius, scaled such that [-1, 1] is the blend intervall \n"
"  v_wavefrontBlendingFactor = (radius - u_wavefrontRadius) / u_wavefrontWidth; \n"
" \n"
"  vertex.xyz+= u_wavefrontCenter; \n"
"  gl_Position = osg_ModelViewProjectionMatrix * vec4(vertex, 1.0); \n"
"} \n"
;

const static std::string s_augmentedViewWavefront_vert =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2017 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"attribute vec4 osg_Vertex; \n"
"attribute vec4 osg_Color; \n"
"attribute vec2 osg_MultiTexCoord0; \n"
" \n"
"uniform highp mat4 osg_ModelViewProjectionMatrix; \n"
" \n"
"uniform highp vec3  u_wavefrontCenter; \n"
"uniform highp float u_wavefrontRadius; \n"
"uniform highp float u_wavefrontWidth; \n"
" \n"
" \n"
"varying highp vec2 v_texCoord; \n"
"varying vec4 v_color; \n"
" \n"
"void main() \n"
"{ \n"
"  v_color = osg_Color; \n"
"  v_texCoord = osg_MultiTexCoord0; \n"
" \n"
"  float displacement = max(0.0, u_wavefrontRadius + osg_Vertex.z*u_wavefrontWidth); \n"
"  vec3 vertex = vec3(osg_Vertex.xy * displacement, 0.05); \n"
" \n"
" \n"
"  vertex.xyz+= u_wavefrontCenter; \n"
"  gl_Position = osg_ModelViewProjectionMatrix * vec4(vertex, 1.0); \n"
"} \n"
;

const static std::string s_augmentedViewWavefrontAdditive_frag =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2017 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"precision highp float; \n"
" \n"
"uniform float u_wavefrontRadius; \n"
"uniform float u_wavefrontFade; \n"
"uniform sampler2D s_waveTexture; \n"
"uniform vec4 u_trueColor; \n"
" \n"
"varying vec2 v_texCoord; \n"
"varying vec4 v_color; \n"
" \n"
"void main() \n"
"{ \n"
"  // Texture mapped radar-wave rendering \n"
"  vec4 radarWaveColor = texture2D(s_waveTexture, v_texCoord); \n"
"  radarWaveColor *= v_color; \n"
"  gl_FragColor = u_trueColor; \n"
"  gl_FragColor.a = vec4(u_wavefrontFade * radarWaveColor).a; \n"
"} \n"
;

const static std::string s_basePlateHistory_frag =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2017 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"precision highp float; \n"
" \n"
"varying vec2 v_texCoord; \n"
"varying vec2 v_texCoorOrig; \n"
"varying float v_increment; \n"
" \n"
"uniform sampler2D u_tex0; \n"
"uniform bool u_toggleBasePlate; \n"
"uniform float u_blurringMix; \n"
" \n"
"const vec4 g_color1 = vec4(0.6, 0.6, 0.6, 1.0); \n"
"const vec4 g_color2 = vec4(0.227, 0.227, 0.227, 1.0); \n"
"const vec4 g_blurColor = vec4(0.6, 0.6, 0.6, 1.0); \n"
" \n"
"void main() \n"
"{ \n"
"  float l_sawtooth = fract(v_increment * 4.0); \n"
"  float l_triangle = abs(2.0 * l_sawtooth - 1.0); \n"
"  float l_square   = step(0.5, l_triangle); \n"
"  vec4 l_background = mix(g_color1, g_color2, l_square); \n"
" \n"
"  vec4 l_mixColor; \n"
"  if (false == u_toggleBasePlate) \n"
"  { \n"
"    l_mixColor = l_background; \n"
"  } \n"
"  else \n"
"  { \n"
"    vec4 l_texColor = texture2D(u_tex0, v_texCoord); \n"
"    l_mixColor = mix(l_background, l_texColor, l_texColor.a); \n"
"  } \n"
" \n"
"  l_mixColor = mix(g_blurColor, l_mixColor, u_blurringMix); // apply blurring \n"
" \n"
"  gl_FragColor = l_mixColor; \n"
"} \n"
;

const static std::string s_basePlateHistory_vert =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2017 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"attribute vec4 osg_Vertex; \n"
"uniform mat4 osg_ModelViewProjectionMatrix; \n"
" \n"
"uniform mat4 u_transformTexture; \n"
" \n"
"uniform vec2 u_cornerMin; \n"
"uniform vec2 u_cornerMax; \n"
" \n"
"varying vec2 v_texCoord; \n"
"varying vec2 v_texCoorOrig; \n"
"varying highp float v_increment; \n"
" \n"
"void main() \n"
"{ \n"
"  v_increment = osg_Vertex.x - osg_Vertex.y; \n"
"  vec2 l_plateSize = u_cornerMax - u_cornerMin; \n"
"  v_texCoorOrig = (osg_Vertex.xy - u_cornerMin); \n"
"  v_texCoorOrig.x = clamp(v_texCoorOrig.x / l_plateSize.x, 0.0, 1.0); \n"
"  v_texCoorOrig.y = clamp(v_texCoorOrig.y / l_plateSize.y, 0.0, 1.0); \n"
"  v_texCoord = (u_transformTexture * vec4(v_texCoorOrig, 1.0, 1.0)).xy; \n"
" \n"
"  gl_Position = osg_ModelViewProjectionMatrix * osg_Vertex; \n"
"} \n"
;

const static std::string s_basicColor_frag =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2017 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"precision highp float; \n"
" \n"
"varying vec4 v_FragColor; \n"
" \n"
"void main(void) \n"
"{ \n"
"  gl_FragColor = v_FragColor; \n"
"} \n"
;

const static std::string s_basicColor_vert =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2017 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"attribute vec4 osg_Vertex; \n"
"attribute vec4 osg_Color; \n"
" \n"
"uniform mat4 osg_ModelViewProjectionMatrix; \n"
" \n"
"varying vec4 v_FragColor; \n"
" \n"
"void main(void) \n"
"{ \n"
"  v_FragColor = osg_Color; \n"
"  gl_Position = osg_ModelViewProjectionMatrix * osg_Vertex; \n"
"} \n"
;

const static std::string s_basicTex_frag =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2017 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"precision highp float; \n"
" \n"
"varying vec2 v_texCoord; \n"
"varying vec4 v_color; \n"
"uniform sampler2D u_tex0; \n"
" \n"
"void main() \n"
"{ \n"
"  vec4 l_color = texture2D(u_tex0, v_texCoord); \n"
"  l_color *= v_color; \n"
"  gl_FragColor = l_color; \n"
"} \n"
;

const static std::string s_basicTex_vert =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2017 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"attribute vec4 osg_Vertex; \n"
"attribute vec4 osg_Color; \n"
"attribute vec2 osg_MultiTexCoord0; \n"
"uniform mat4 osg_ModelViewProjectionMatrix; \n"
"varying vec4 v_color; \n"
"varying vec2 v_texCoord; \n"
" \n"
"void main() \n"
"{ \n"
"  v_color = osg_Color; \n"
"  v_texCoord = osg_MultiTexCoord0; \n"
"  gl_Position = osg_ModelViewProjectionMatrix * osg_Vertex; \n"
"} \n"
;

const static std::string s_basicTexAlpha_frag =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2017 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"precision highp float; \n"
" \n"
"varying vec2 v_texCoord; \n"
"varying vec4 v_color; \n"
"uniform sampler2D u_tex0; \n"
"uniform float gbc; \n"
"uniform float impostorBrightness; \n"
" \n"
"void main() \n"
"{ \n"
"  vec4 l_color = texture2D(u_tex0, v_texCoord); \n"
"  l_color *= v_color; \n"
"  gl_FragColor = vec4( (l_color.rgb * impostorBrightness), l_color.a*gbc); \n"
"} \n"
;

const static std::string s_basicTexAlpha_vert =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2017 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"attribute vec4 osg_Vertex; \n"
"attribute vec4 osg_Color; \n"
"attribute vec2 osg_MultiTexCoord0; \n"
"uniform mat4 osg_ModelViewProjectionMatrix; \n"
"varying vec4 v_color; \n"
"varying vec2 v_texCoord; \n"
" \n"
"void main() \n"
"{ \n"
"  v_color = osg_Color; \n"
"  v_texCoord = osg_MultiTexCoord0; \n"
"  gl_Position = osg_ModelViewProjectionMatrix * osg_Vertex; \n"
"} \n"
;

const static std::string s_basicTexMask_frag =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2017 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"precision highp float; \n"
" \n"
"varying vec2 v_texCoord; \n"
"varying vec4 v_color; \n"
"uniform sampler2D u_tex0; \n"
" \n"
"void main() \n"
"{ \n"
"  gl_FragColor = v_color * texture2D(u_tex0, v_texCoord).a; \n"
"} \n"
;

const static std::string s_blurredPlateTexture_frag =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2019 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"precision highp float; \n"
" \n"
"varying vec2 v_texCoord; \n"
"uniform sampler2D u_tex0; \n"
" \n"
"void main() \n"
"{ \n"
"  gl_FragColor = texture2D(u_tex0, v_texCoord); \n"
"} \n"
;

const static std::string s_blurredPlateTexture_vert =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2017 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
" \n"
"attribute vec4 osg_Vertex; \n"
"uniform mat4 osg_ModelViewProjectionMatrix; \n"
" \n"
"uniform highp vec2 u_minCorner, u_maxCorner; \n"
"varying vec2 v_texCoord; \n"
" \n"
"void main() \n"
"{ \n"
"  v_texCoord = (osg_Vertex.xy - u_minCorner)/(u_maxCorner - u_minCorner); \n"
"  gl_Position = osg_ModelViewProjectionMatrix*osg_Vertex; \n"
"} \n"
;

const static std::string s_cameraTexCombine_frag =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2017 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"#extension GL_OES_EGL_image_external : require \n"
" \n"
"precision highp float; \n"
" \n"
"#ifdef GL_ES \n"
"uniform samplerExternalOES u_tex0; \n"
"#else \n"
"uniform sampler2D u_tex0; \n"
"#endif \n"
" \n"
"varying vec2 v_texCoord; \n"
" \n"
"uniform int u_cam; \n"
" \n"
"uniform float u_brightFactor; \n"
" \n"
"void main() \n"
"{ \n"
"  vec2 l_newTexCoord = vec2(0.0, v_texCoord.y); \n"
"  if (0 == u_cam)  // front \n"
"  { \n"
"    l_newTexCoord.x = 0.75 + v_texCoord.x * 0.25; \n"
"  } \n"
"  else if (1 == u_cam)  // right \n"
"  { \n"
"    l_newTexCoord.x = 0.5 + v_texCoord.x * 0.25; \n"
"  } \n"
"  else if (2 == u_cam)  // rear \n"
"  { \n"
"    l_newTexCoord.x = v_texCoord.x * 0.25; \n"
"  } \n"
"  else  // left \n"
"  { \n"
"    l_newTexCoord.x = 0.25 + v_texCoord.x * 0.25; \n"
"  } \n"
" \n"
"  vec4 l_color = texture2D(u_tex0, l_newTexCoord); \n"
"  gl_FragColor = vec4(clamp((l_color).rgb * u_brightFactor, 0.0, 1.0), 1.0); \n"
"} \n"
;

const static std::string s_cameraTexCombine_vert =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2017 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"attribute vec4 osg_Vertex; \n"
"attribute vec4 osg_Color; \n"
"attribute vec2 osg_MultiTexCoord0; \n"
" \n"
"uniform mat4 osg_ModelViewProjectionMatrix; \n"
" \n"
"varying vec2 v_texCoord; \n"
" \n"
"void main() \n"
"{ \n"
"  v_texCoord = osg_MultiTexCoord0; \n"
"  gl_Position = osg_ModelViewProjectionMatrix * osg_Vertex; \n"
"} \n"
;

const static std::string s_cameraTexCombineDebugView_frag =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2017 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"#extension GL_OES_EGL_image_external : require \n"
" \n"
"precision highp float; \n"
" \n"
"#ifdef GL_ES \n"
"uniform samplerExternalOES u_tex0; \n"
"#else \n"
"uniform sampler2D u_tex0; \n"
"#endif \n"
" \n"
"varying vec2 v_texCoord; \n"
" \n"
"uniform sampler2D u_tex1; \n"
"uniform int u_cam; \n"
"uniform vec4 u_maskColor; \n"
" \n"
"void main() \n"
"{ \n"
"  vec2 l_newTexCoord = vec2(0.0, v_texCoord.y); \n"
"  if (0 == u_cam)  // front \n"
"  { \n"
"    l_newTexCoord.x = 0.75 + v_texCoord.x * 0.25; \n"
"  } \n"
"  else if (1 == u_cam)  // right \n"
"  { \n"
"    l_newTexCoord.x = 0.5 + v_texCoord.x * 0.25; \n"
"  } \n"
"  else if (2 == u_cam)  // rear \n"
"  { \n"
"    l_newTexCoord.x = v_texCoord.x * 0.25; \n"
"  } \n"
"  else  // left \n"
"  { \n"
"    l_newTexCoord.x = 0.25 + v_texCoord.x * 0.25; \n"
"  } \n"
"  vec4 l_color = texture2D(u_tex0, l_newTexCoord) + ((1.0 - texture2D(u_tex1, v_texCoord).r))*u_maskColor; \n"
" \n"
"  gl_FragColor = vec4((l_color).rgb, 1.0); \n"
"} \n"
;

const static std::string s_cameraTexCombineDebugView_vert =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2017 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"attribute vec4 osg_Vertex; \n"
"attribute vec4 osg_Color; \n"
"attribute vec2 osg_MultiTexCoord0; \n"
" \n"
"uniform mat4 osg_ModelViewProjectionMatrix; \n"
" \n"
"varying vec2 v_texCoord; \n"
" \n"
"void main() \n"
"{ \n"
"  v_texCoord = osg_MultiTexCoord0; \n"
"  gl_Position = osg_ModelViewProjectionMatrix * osg_Vertex; \n"
"} \n"
;

const static std::string s_chamaeleonEstimator_frag =
"#version 300 es \n"
" \n"
"precision mediump float; \n"
" \n"
"in vec2 v_texCoord; \n"
" \n"
"uniform int u_roiLod; \n"
"uniform vec4 u_worldRoiDegradationMask; \n"
"uniform float u_rampingFactor; \n"
"uniform mat4x3 u_wbgRawGains; \n"
"uniform vec2 u_lowerUpperThresholdDeltaL; \n"
"uniform vec2 u_lowerUpperThresholdDeltaAB; \n"
"uniform vec2 u_minMaxOutputGain; \n"
" \n"
"uniform sampler2D s_chamRoiAsTexture; \n"
"uniform sampler2D s_chamEstiAsTexture; \n"
" \n"
"vec3 rgb2lab(vec3 f_rgb) { \n"
"  // RGB/BT.601 -> XYZ \n"
"  mat3 rgb2xyz = mat3( \n"
"      0.4124564, 0.2126729, 0.0193339, \n"
"      0.3575761, 0.7151522, 0.1191920, \n"
"      0.1804375, 0.0721750, 0.9503041 \n"
"  ); \n"
" \n"
"  // XYZ -> CieLab \n"
"  // viewing angle = 2 deg \n"
"  // Illuminant D65 (6500K) \n"
"  vec3 xyz_norm = (rgb2xyz * f_rgb) / vec3(0.95047, 1.0, 1.08883); \n"
" \n"
" \n"
"  vec3 xyz = mix( \n"
"      7.787037 * xyz_norm + (16.0 / 116.0), \n"
"      pow(xyz_norm, vec3(1.0 / 3.0)), \n"
"      step(vec3(0.008856), xyz_norm) \n"
"  ); \n"
" \n"
"  vec3 l_lab = vec3( \n"
"    clamp((116.0 * xyz.y) - 16., 0., 100. ), \n"
"    clamp(500.0 * (xyz.x - xyz.y) ,-128., 127.), \n"
"    clamp(200.0 * (xyz.y - xyz.z) ,-128., 127.) \n"
"  ); \n"
" \n"
"  return l_lab; \n"
"} \n"
" \n"
"vec3 calcWbGainsCoeff(vec3 f_gain1, vec3 f_gain2) \n"
"{ \n"
"    vec3 l_gain1 = clamp(f_gain1, 0.01, 1.99); \n"
"    vec3 l_gain2 = clamp(f_gain2, 0.01, 1.99); \n"
"    vec3 average = (l_gain1 + l_gain2) / 2.F; \n"
"    vec3 wbgain = average / l_gain1; \n"
"    return wbgain; \n"
"} \n"
" \n"
"out vec4 FragColor; \n"
" \n"
"void main() \n"
"{ \n"
"  // The following arrays define a mapping defined in chamaeleon_tabs.hpp \n"
" \n"
"  // Mapping ImageOverlapROI to corresponding ImageOverlapROI \n"
"  // e.g. 0 (IMAGE_FRONT_TO_RIGHT) -> 3 (IMAGE_RIGHT_TO_FRONT) \n"
"  const int imageRoiToCorrespondingImageRoi[8] = int[8](3,6,5,0,7,2,1,4); \n"
" \n"
"  // Mapping ImageOverlapROI to SingleCamArea \n"
"  // e.g. 0 (IMAGE_FRONT_TO_RIGHT) -> 0 (SINGLE_CAM_FRONT) \n"
"  const int imageRoiToSingleCamArea[8] = int[8](0,0,1,1,2,2,3,3); \n"
" \n"
"  // Mapping ImageOverlapROI To WorldOverlapROI \n"
"  // e.g. 0 (IMAGE_FRONT_TO_RIGHT) -> 0 (WORLD_FRONT_RIGHT) \n"
"  const int imageRoiToWorldRoi[8] = int[8](0,3,1,0,2,1,3,2); \n"
" \n"
"  const int NUM_IMAGE_ROIS = 8; \n"
"  const ivec2 iTextureSize = ivec2(NUM_IMAGE_ROIS,1); \n"
" \n"
"  ivec2 iTexCoord1 = ivec2(v_texCoord * vec2(iTextureSize)); \n"
"  ivec2 iTexCoord2 = ivec2(imageRoiToCorrespondingImageRoi[iTexCoord1.x],0); \n"
" \n"
"  // estimate next gain \n"
"  const float mipMapAverageRgbMin = 0.001; \n"
"  const float mipMapAverageRgbMax = 0.999; \n"
" \n"
"  vec4 mipMapAverage1 = texelFetch(s_chamRoiAsTexture, iTexCoord1, u_roiLod); \n"
"  vec3 mipMapAverageRgb1 = clamp(mipMapAverage1.rgb, mipMapAverageRgbMin, mipMapAverageRgbMax); \n"
" \n"
"  vec4 mipMapAverage2 = texelFetch(s_chamRoiAsTexture, iTexCoord2, u_roiLod); \n"
"  vec3 mipMapAverageRgb2 = clamp(mipMapAverage2.rgb, mipMapAverageRgbMin, mipMapAverageRgbMax); \n"
" \n"
"  vec3 overallAverage = (mipMapAverageRgb1 + mipMapAverageRgb2) / 2.; \n"
" \n"
"  vec4 prevTexel = texelFetch(s_chamEstiAsTexture, iTexCoord1, 0); \n"
"  vec3 nextGain = (overallAverage / mipMapAverageRgb1); \n"
"  nextGain = clamp(nextGain, u_minMaxOutputGain[0], u_minMaxOutputGain[1]); \n"
" \n"
"  // Degradation \n"
"  int worldRoi = imageRoiToWorldRoi[iTexCoord1.x]; \n"
"  float degradationStatus = u_worldRoiDegradationMask[worldRoi]; // 0:= degradate | 1:= noEffect \n"
" \n"
"  // Plausibility DeltaL \n"
"  float deltaL = distance(rgb2lab(mipMapAverageRgb1).x, rgb2lab(mipMapAverageRgb2).x); \n"
"  float lowerThresholdL = 1. - step(u_lowerUpperThresholdDeltaL[0], deltaL);// 0:= degradate | 1:= noEffect \n"
"  float upperThresholdL = 1. - step(u_lowerUpperThresholdDeltaL[1], deltaL);// 0:= degradate | 1:= noEffect \n"
" \n"
"  // Plausibility DeltaAB \n"
"  float deltaAB = distance(rgb2lab(mipMapAverageRgb1).yz, rgb2lab(mipMapAverageRgb2).yz); \n"
"  float lowerThresholdAB = 1. - step(u_lowerUpperThresholdDeltaAB[0], deltaAB);// 0:= degradate | 1:= noEffect \n"
"  float upperThresholdAB = 1. - step(u_lowerUpperThresholdDeltaAB[1], deltaAB);// 0:= degradate | 1:= noEffect \n"
" \n"
" \n"
"  float lowerThreshold = min(lowerThresholdAB, lowerThresholdL); \n"
"  float upperThreshold = min(upperThresholdAB, upperThresholdL); \n"
" \n"
"  // Plausibility_Hysterese \n"
"  float prevPlausibilityStatus = prevTexel.a; \n"
"  float plausibilityStatus = max(lowerThreshold, min(upperThreshold, prevPlausibilityStatus)); \n"
" \n"
"  // calc degradate gains \n"
"  int singleCamArea1 = imageRoiToSingleCamArea[iTexCoord1.x]; \n"
"  int singleCamArea2 = imageRoiToSingleCamArea[iTexCoord2.x]; \n"
"  vec3 plausiDegradadeGain = calcWbGainsCoeff(u_wbgRawGains[singleCamArea1], u_wbgRawGains[singleCamArea2]); \n"
" \n"
"  nextGain = mix(plausiDegradadeGain, nextGain, min(degradationStatus, plausibilityStatus)); \n"
" \n"
"  vec3 prevGain = prevTexel.rgb + vec3(0.5); \n"
" \n"
"  FragColor = vec4(mix(nextGain, prevGain, u_rampingFactor) - vec3(0.5), plausibilityStatus); \n"
"} \n"
;

const static std::string s_chamaeleonEstimator_vert =
"#version 300 es \n"
" \n"
"in vec4 osg_Vertex; \n"
"in vec2 osg_MultiTexCoord0; \n"
" \n"
"uniform mat4 osg_ModelViewProjectionMatrix; \n"
" \n"
"out vec2 v_texCoord; \n"
" \n"
"void main() \n"
"{ \n"
"  v_texCoord = osg_MultiTexCoord0; \n"
"  gl_Position = osg_ModelViewProjectionMatrix * osg_Vertex; \n"
"} \n"
;

const static std::string s_chamaeleonRoiFilter_frag =
"#version 300 es \n"
" \n"
"precision mediump float; \n"
" \n"
"in vec2 v_texCoord; \n"
"uniform int u_roiLod; \n"
"uniform int u_roiDim; \n"
"uniform int u_imageOverlapRoiId; \n"
" \n"
"uniform sampler2D s_chamRoiAsTexture; \n"
"uniform float u_thresholdDeltaE; \n"
" \n"
"out vec4 FragColor; \n"
" \n"
"vec3 rgb2lab(vec3 f_rgb) { \n"
"  // RGB/BT.601 -> XYZ \n"
"  mat3 rgb2xyz = mat3( \n"
"      0.4124564, 0.2126729, 0.0193339, \n"
"      0.3575761, 0.7151522, 0.1191920, \n"
"      0.1804375, 0.0721750, 0.9503041 \n"
"  ); \n"
" \n"
"  // XYZ -> CieLab \n"
"  // viewing angle = 2 deg \n"
"  // Illuminant D65 (6500K) \n"
"  vec3 xyz_norm = (rgb2xyz * f_rgb) / vec3(0.95047, 1.0, 1.08883); \n"
" \n"
" \n"
"  vec3 xyz = mix( \n"
"      7.787037 * xyz_norm + (16.0 / 116.0), \n"
"      pow(xyz_norm, vec3(1.0 / 3.0)), \n"
"      step(vec3(0.008856), xyz_norm) \n"
"  ); \n"
" \n"
"  vec3 l_lab = vec3( \n"
"    clamp((116.0 * xyz.y) - 16., 0., 100. ), \n"
"    clamp(500.0 * (xyz.x - xyz.y) ,-128., 127.), \n"
"    clamp(200.0 * (xyz.y - xyz.z) ,-128., 127.) \n"
"  ); \n"
" \n"
"  return l_lab; \n"
"} \n"
" \n"
"void main() \n"
"{ \n"
"  const int NUM_IMAGE_ROIS = 8; \n"
"  ivec2 iTextureSize = ivec2(u_roiDim, u_roiDim); \n"
" \n"
"  ivec2 iTexCoord = ivec2(v_texCoord * vec2(iTextureSize)) + ivec2(u_roiDim * u_imageOverlapRoiId, 0); \n"
" \n"
"  vec4 currentTexel = texelFetch(s_chamRoiAsTexture, iTexCoord, 0); \n"
"  vec4 roiAverage = texelFetch(s_chamRoiAsTexture, ivec2(u_imageOverlapRoiId, 0 ), u_roiLod); \n"
" \n"
"  float deltaE = distance(rgb2lab(roiAverage.rgb), rgb2lab(currentTexel.rgb)); \n"
" \n"
"  float thresholdE = 1. - step(u_thresholdDeltaE, deltaE);// 0:= degradate | 1:= noEffect \n"
" \n"
"  FragColor = mix(roiAverage, currentTexel, thresholdE); \n"
"} \n"
;

const static std::string s_chamaeleonRoiFilter_vert =
"#version 300 es \n"
" \n"
"in vec4 osg_Vertex; \n"
"in vec2 osg_MultiTexCoord0; \n"
" \n"
"uniform mat4 osg_ModelViewProjectionMatrix; \n"
" \n"
"out vec2 v_texCoord; \n"
" \n"
"void main() \n"
"{ \n"
"  v_texCoord = osg_MultiTexCoord0; \n"
"  gl_Position = osg_ModelViewProjectionMatrix * osg_Vertex; \n"
"} \n"
;

const static std::string s_chamaeleonRoiLeft_frag =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2022 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"// #version 320 es \n"
" \n"
"#extension GL_OES_EGL_image_external : require \n"
" \n"
"precision highp float; \n"
" \n"
"#ifdef GL_ES \n"
"uniform samplerExternalOES s_texCamsLeft; \n"
"#else \n"
"uniform sampler2D s_texCamsLeft; \n"
"#endif \n"
" \n"
"varying vec2 v_texLeftCam; \n"
"varying vec2 v_texRightCam; \n"
" \n"
"uniform int u_cam; \n"
"uniform int u_isTexCombine; \n"
" \n"
"vec2 getUv(vec2 f_uv) \n"
"{ \n"
"  if (1 == u_isTexCombine) \n"
"  { \n"
"    vec2 l_uv = vec2(0.0, f_uv.y); \n"
"    if (0 == u_cam)  // front \n"
"    { \n"
"      l_uv.x = 0.75 + f_uv.x * 0.25; \n"
"    } \n"
"    else if (1 == u_cam)  // right \n"
"    { \n"
"      l_uv.x = 0.5 + f_uv.x * 0.25; \n"
"    } \n"
"    else if (2 == u_cam)  // rear \n"
"    { \n"
"      l_uv.x = f_uv.x * 0.25; \n"
"    } \n"
"    else  // left \n"
"    { \n"
"      l_uv.x = 0.25 + f_uv.x * 0.25; \n"
"    } \n"
"    return l_uv; \n"
"  } \n"
"  else \n"
"  { \n"
"    return f_uv; \n"
"  } \n"
"} \n"
" \n"
"void main() \n"
"{ \n"
"  vec4 l_colorLeft = texture2D(s_texCamsLeft, getUv(v_texLeftCam.xy)); \n"
" \n"
"  gl_FragColor = vec4(l_colorLeft.rgb, 1.0); \n"
"} \n"
;

const static std::string s_chamaeleonRoiLeft_vert =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2023 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"// #version 320 es \n"
" \n"
"attribute highp vec4 osg_Vertex; \n"
"attribute highp vec2 osg_MultiTexCoord0; \n"
"attribute highp vec2 osg_MultiTexCoord1; \n"
" \n"
"uniform highp mat4 osg_ModelViewProjectionMatrix; \n"
" \n"
"varying highp vec2 v_texLeftCam; \n"
"varying highp vec2 v_texRightCam; \n"
" \n"
"void main() \n"
"{ \n"
"  v_texLeftCam = osg_MultiTexCoord0; \n"
"  v_texRightCam = osg_MultiTexCoord1; \n"
" \n"
"  gl_Position = osg_ModelViewProjectionMatrix * osg_Vertex; \n"
"} \n"
;

const static std::string s_chamaeleonRoiRight_frag =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2022 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"// #version 320 es \n"
" \n"
"#extension GL_OES_EGL_image_external : require \n"
" \n"
"precision highp float; \n"
" \n"
"#ifdef GL_ES \n"
"uniform samplerExternalOES s_texCamsRight; \n"
"#else \n"
"uniform sampler2D s_texCamsRight; \n"
"#endif \n"
" \n"
"varying vec2 v_texLeftCam; \n"
"varying vec2 v_texRightCam; \n"
" \n"
"uniform int u_cam; \n"
"uniform int u_isTexCombine; \n"
" \n"
"vec2 getUv(vec2 f_uv) \n"
"{ \n"
"  if (1 == u_isTexCombine) \n"
"  { \n"
"    vec2 l_uv = vec2(0.0, f_uv.y); \n"
"    if (0 == u_cam)  // front \n"
"    { \n"
"      l_uv.x = 0.75 + f_uv.x * 0.25; \n"
"    } \n"
"    else if (1 == u_cam)  // right \n"
"    { \n"
"      l_uv.x = 0.5 + f_uv.x * 0.25; \n"
"    } \n"
"    else if (2 == u_cam)  // rear \n"
"    { \n"
"      l_uv.x = f_uv.x * 0.25; \n"
"    } \n"
"    else  // left \n"
"    { \n"
"      l_uv.x = 0.25 + f_uv.x * 0.25; \n"
"    } \n"
"    return l_uv; \n"
"  } \n"
"  else \n"
"  { \n"
"    return f_uv; \n"
"  } \n"
"} \n"
" \n"
"void main() \n"
"{ \n"
"  vec4 l_colorRight = texture2D(s_texCamsRight, getUv(v_texRightCam.xy)); \n"
" \n"
"  gl_FragColor = vec4(l_colorRight.rgb, 1.0); \n"
"} \n"
;

const static std::string s_chamaeleonRoiRight_vert =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2022 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"// #version 320 es \n"
" \n"
"attribute highp vec4 osg_Vertex; \n"
"attribute highp vec2 osg_MultiTexCoord0; \n"
"attribute highp vec2 osg_MultiTexCoord1; \n"
" \n"
"uniform highp mat4 osg_ModelViewProjectionMatrix; \n"
" \n"
"varying highp vec2 v_texLeftCam; \n"
"varying highp vec2 v_texRightCam; \n"
" \n"
"void main() \n"
"{ \n"
"  v_texLeftCam = osg_MultiTexCoord0; \n"
"  v_texRightCam = osg_MultiTexCoord1; \n"
" \n"
"  gl_Position = osg_ModelViewProjectionMatrix * osg_Vertex; \n"
"} \n"
;

const static std::string s_chamaeleonVisuColorMapping_frag =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2023 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"#version 320 es \n"
" \n"
"precision highp float; \n"
" \n"
"in vec2 v_texCoord; \n"
" \n"
"uniform vec3 u_colorMask; \n"
" \n"
"uniform mat4x3 u_polyCoeff; \n"
" \n"
"uniform sampler2D u_colorPoly; \n"
"uniform sampler2D u_colorMatchR; \n"
"uniform sampler2D u_colorMatchG; \n"
"uniform sampler2D u_colorMatchB; \n"
" \n"
"vec3 polyCalculation(mat4x3 f_polyCoeff, vec3 f_color) \n"
"{ \n"
"  return f_polyCoeff[0] * pow(f_color, vec3(0.)) + f_polyCoeff[1] * pow(f_color, vec3(1.)) + \n"
"         f_polyCoeff[2] * pow(f_color, vec3(2.)) + f_polyCoeff[3] * pow(f_color, vec3(3.)); \n"
"} \n"
" \n"
"float drawColor(float f_colorPoly, float f_colorMatch, vec2 f_texCoord, float f_linesThickness) \n"
"{ \n"
"  float colorOutput = 0.1; \n"
"  float colorOutputPoly = 0.7; \n"
"  float colorOutputMatch = 0.5; \n"
" \n"
"  if(distance(vec2(f_texCoord.x, f_colorPoly), f_texCoord) < f_linesThickness) \n"
"  { \n"
"    colorOutput = clamp(colorOutput + colorOutputPoly, 0.0, 1.0) ; \n"
"  } \n"
" \n"
"  if(distance(vec2(f_texCoord.x, f_colorMatch), f_texCoord) < f_linesThickness) \n"
"  { \n"
"    colorOutput = clamp(colorOutput + colorOutputMatch, 0.0, 1.0) ; \n"
"  } \n"
" \n"
"  return colorOutput; \n"
"} \n"
" \n"
"out vec4 FragColor; \n"
" \n"
"void main() \n"
"{ \n"
"  float l_linesThickness = 0.02; \n"
" \n"
"  vec3 l_colorPoly = polyCalculation(u_polyCoeff, vec3(v_texCoord.x)); \n"
" \n"
"  vec3 l_colorMatch = vec3(texture(u_colorMatchR, vec2(v_texCoord.x, 1.)).x, \n"
"                           texture(u_colorMatchG, vec2(v_texCoord.x, 1.)).x, \n"
"                           texture(u_colorMatchB, vec2(v_texCoord.x, 1.)).x); \n"
" \n"
" \n"
"  FragColor = vec4(u_colorMask.r * drawColor(l_colorPoly.r, l_colorMatch.r, v_texCoord, l_linesThickness), \n"
"                   u_colorMask.g * drawColor(l_colorPoly.g, l_colorMatch.g, v_texCoord, l_linesThickness), \n"
"                   u_colorMask.b * drawColor(l_colorPoly.b, l_colorMatch.b, v_texCoord, l_linesThickness), 1.0); \n"
"} \n"
;

const static std::string s_chamaeleonVisuColorMapping_vert =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2023 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"#version 320 es \n"
" \n"
"in vec4 osg_Vertex; \n"
"in vec2 osg_MultiTexCoord0; \n"
" \n"
"uniform mat4 osg_ModelViewProjectionMatrix; \n"
" \n"
"out vec2 v_texCoord; \n"
" \n"
"void main() \n"
"{ \n"
"  v_texCoord = osg_MultiTexCoord0; \n"
"  gl_Position = osg_ModelViewProjectionMatrix * osg_Vertex; \n"
"} \n"
;

const static std::string s_colorText_frag =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2017 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"precision highp float; \n"
" \n"
"varying vec2 v_texCoord; \n"
"varying vec4 v_color; \n"
"uniform sampler2D u_tex0; \n"
" \n"
"void main() \n"
"{ \n"
"  vec4 l_color = texture2D(u_tex0, v_texCoord).aaaa; \n"
"  l_color *= v_color; \n"
"  gl_FragColor = l_color; \n"
"} \n"
;

const static std::string s_colorText_vert =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2017 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"attribute vec4 osg_Vertex; \n"
"attribute vec4 osg_Color; \n"
"attribute vec2 osg_MultiTexCoord0; \n"
"uniform mat4 osg_ModelViewProjectionMatrix; \n"
"varying vec4 v_color; \n"
"varying vec2 v_texCoord; \n"
" \n"
"void main() \n"
"{ \n"
"  v_color = osg_Color; \n"
"  v_texCoord = osg_MultiTexCoord0; \n"
"  gl_Position = osg_ModelViewProjectionMatrix * osg_Vertex; \n"
"} \n"
;

const static std::string s_floorplateApplication_frag =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2017 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"precision highp float; \n"
" \n"
"uniform vec4 u_outputColor; \n"
" \n"
"void main() \n"
"{ \n"
"  gl_FragColor = u_outputColor; \n"
"} \n"
;

const static std::string s_fourTexTraj_frag =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2017 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"precision highp float; \n"
" \n"
"varying vec2 v_texCoord; \n"
"varying vec4 v_color; \n"
"uniform sampler2D u_tex0; \n"
"uniform sampler2D u_tex1; \n"
"uniform sampler2D u_tex2; \n"
"uniform sampler2D u_tex3; \n"
" \n"
"void main() \n"
"{ \n"
"  vec4 color_1; \n"
"  vec4 color_2; \n"
"  vec4 color; \n"
" \n"
"  if (v_texCoord.y < 0.251) \n"
"  { \n"
"    color_1 = texture2D(u_tex0, vec2(v_texCoord.x, 0.0)); \n"
"    color_2 = texture2D(u_tex1, vec2(v_texCoord.x, 0.0)); \n"
"    color = mix(color_1, color_2, 4.0 * v_texCoord.y); \n"
"  } \n"
"  else if (v_texCoord.y < 0.501) \n"
"  { \n"
"    color_1 = texture2D(u_tex1, vec2(v_texCoord.x, 0.0)); \n"
"    color_2 = texture2D(u_tex2, vec2(v_texCoord.x, 0.0)); \n"
"    color = mix(color_1, color_2, 4.0 * (v_texCoord.y - 0.25)); \n"
"  } \n"
"  else \n"
"  { \n"
"    color_1 = texture2D(u_tex2, vec2(v_texCoord.x, 0.0)); \n"
"    color_2 = texture2D(u_tex3, vec2(v_texCoord.x, 0.0)); \n"
"    color = mix(color_1, color_2, 4.0 * (v_texCoord.y - 0.5)); \n"
"  } \n"
" \n"
"  gl_FragColor = color * v_color; \n"
"} \n"
;

const static std::string s_fourTexTraj_vert =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2017 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"attribute vec4 osg_Vertex; \n"
"attribute vec4 osg_Color; \n"
"attribute vec2 osg_MultiTexCoord0; \n"
"uniform mat4 osg_ModelViewProjectionMatrix; \n"
"varying vec4 v_color; \n"
"varying vec2 v_texCoord; \n"
" \n"
"void main() \n"
"{ \n"
"  v_color = osg_Color; \n"
"  v_texCoord = osg_MultiTexCoord0; \n"
"  gl_Position = osg_ModelViewProjectionMatrix * osg_Vertex; \n"
"} \n"
;

const static std::string s_historyToPlate_frag =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2017 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"precision highp float; \n"
" \n"
"varying vec2 v_texCoord; \n"
"varying float v_increment; \n"
" \n"
"uniform sampler2D u_tex0; \n"
"bool u_toggleBasePlate = true; // uniform \n"
"float u_blurringMix = 1.0; // uniform \n"
"uniform bool u_hybridRendering; \n"
"uniform float u_stripeAlpha;      // Overall transparency of history+stripe texture, e.g. to crossfade to blurred texture \n"
" \n"
"const vec4 g_color1 = vec4(0.8, 0.8, 0.8, 1.0); \n"
"const vec4 g_color2 = vec4(0.8, 0.8, 0.8, 1.0); \n"
"const vec4 g_blurColor = vec4(0.8, 0.8, 0.8, 1.0); \n"
" \n"
"void main() \n"
"{ \n"
"  float l_sawtooth = fract(v_increment * 4.0); \n"
"  float l_triangle = abs(2.0 * l_sawtooth - 1.0); \n"
"  float l_square   = step(0.5, l_triangle); \n"
"  vec4 l_background = mix(g_color1, g_color2, l_square); \n"
" \n"
"  vec4 l_mixColor; \n"
"  vec4 l_texColor = texture2D(u_tex0, v_texCoord); \n"
"  if (false == u_toggleBasePlate) \n"
"  { \n"
"    l_mixColor = l_background; \n"
"  } \n"
"  else \n"
"  { \n"
"    l_mixColor = mix(l_background, l_texColor, l_texColor.a); \n"
"  } \n"
" \n"
"  l_mixColor = mix(g_blurColor, l_mixColor, u_blurringMix); // apply blurring \n"
" \n"
"  gl_FragColor = l_mixColor; \n"
" \n"
"  if (u_hybridRendering) \n"
"  { \n"
"    gl_FragColor.a = u_stripeAlpha*l_texColor.a; \n"
"  } \n"
"  else \n"
"  { \n"
"    gl_FragColor.a = u_stripeAlpha; \n"
"  } \n"
"} \n"
;

const static std::string s_historyToPlate_vert =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2017 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"attribute vec4 osg_Vertex; \n"
"uniform mat4 osg_ModelViewProjectionMatrix; \n"
" \n"
"uniform mat4 u_d70kToHistoryTexture; //!< Any D70K point to history texture coordinate \n"
" \n"
"varying vec2 v_texCoord; //!< texture coordinates for history \n"
"varying highp float v_increment; \n"
" \n"
"void main() \n"
"{ \n"
"  v_increment = osg_Vertex.x - osg_Vertex.y; \n"
" \n"
"  v_texCoord = (u_d70kToHistoryTexture * vec4(osg_Vertex.xy, 1.0, 1.0)).xy; \n"
" \n"
"  gl_Position = osg_ModelViewProjectionMatrix * osg_Vertex; \n"
"} \n"
;

const static std::string s_iconAnimationAugmentedWave_frag =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2017 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"precision highp float; \n"
" \n"
"varying vec2 v_texCoord; \n"
"varying vec4 v_color; \n"
"uniform sampler2D u_tex0; \n"
"uniform bool isAugmentedWave; \n"
"uniform float alpha; \n"
" \n"
"void main() \n"
"{ \n"
"  vec4 l_color = texture2D(u_tex0, v_texCoord); \n"
"  l_color *= v_color; \n"
" \n"
"  if (isAugmentedWave) \n"
"  { \n"
"    float radient_factor = 10.0; \n"
"    float delta_radient = 0.1; \n"
"    float delta_fadeOut = 0.1; \n"
"    float delta_fadeOut_revert = 10.0; \n"
" \n"
"    if ( (v_texCoord.y >= (alpha - delta_radient)) && (v_texCoord.y <= (alpha + delta_radient)) ) \n"
"    { \n"
"      gl_FragColor = vec4(l_color*radient_factor); \n"
"    } \n"
"    else if ( (v_texCoord.y > alpha + delta_radient) && (v_texCoord.y <= alpha + delta_radient + delta_fadeOut)) \n"
"    { \n"
"      gl_FragColor = vec4(l_color*(1.0 + ( (delta_radient + delta_fadeOut - (v_texCoord.y - alpha))*delta_fadeOut_revert ))); \n"
"    } \n"
"    else if ( (v_texCoord.y >= alpha - delta_radient - delta_fadeOut) && (v_texCoord.y < alpha - delta_radient)) \n"
"    { \n"
"      gl_FragColor = vec4(l_color*(1.0 + ( (delta_radient + delta_fadeOut - (alpha - v_texCoord.y))*delta_fadeOut_revert ))); \n"
"    } \n"
"    else \n"
"    { \n"
"      gl_FragColor = l_color; \n"
"    } \n"
"  } \n"
"  else \n"
"  { \n"
"    gl_FragColor = l_color; \n"
"  } \n"
"} \n"
;

const static std::string s_iconAnimationAugmentedWave_vert =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2017 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"attribute vec4 osg_Vertex; \n"
"attribute vec4 osg_Color; \n"
"attribute vec2 osg_MultiTexCoord0; \n"
"uniform mat4 osg_ModelViewProjectionMatrix; \n"
"varying vec4 v_color; \n"
"varying vec2 v_texCoord; \n"
" \n"
"void main() \n"
"{ \n"
"  v_color = osg_Color; \n"
"  v_texCoord = osg_MultiTexCoord0; \n"
"  gl_Position = osg_ModelViewProjectionMatrix * osg_Vertex; \n"
"} \n"
;

const static std::string s_iconAnimationFadeInFadeOut_frag =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2017 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"precision highp float; \n"
" \n"
"varying vec2 v_texCoord; \n"
"varying vec4 v_color; \n"
"uniform sampler2D u_tex0; \n"
"uniform bool isFadeInOut; \n"
"uniform float alpha; \n"
" \n"
"void main() \n"
"{ \n"
"  vec4 l_color = texture2D(u_tex0, v_texCoord); \n"
"  l_color *= v_color; \n"
" \n"
"  //if (isFadeInOut && ( (v_texCoord.x <= 0.03) || (v_texCoord.x >= 0.97) || (v_texCoord.y <= 0.015) || (v_texCoord.y >= 0.985) )) \n"
"  { \n"
"    float radient_factor = 1.0; \n"
"    gl_FragColor = vec4(l_color*(max(0.7, alpha*radient_factor))); \n"
"  } \n"
"  // else \n"
"  // { \n"
"  //   gl_FragColor = l_color; \n"
"  // } \n"
"} \n"
;

const static std::string s_iconAnimationFadeInFadeOut_vert =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2017 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"attribute vec4 osg_Vertex; \n"
"attribute vec4 osg_Color; \n"
"attribute vec2 osg_MultiTexCoord0; \n"
"uniform mat4 osg_ModelViewProjectionMatrix; \n"
"varying vec4 v_color; \n"
"varying vec2 v_texCoord; \n"
" \n"
"void main() \n"
"{ \n"
"  v_color = osg_Color; \n"
"  v_texCoord = osg_MultiTexCoord0; \n"
"  gl_Position = osg_ModelViewProjectionMatrix * osg_Vertex; \n"
"} \n"
;

const static std::string s_infoPanel_frag =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2017 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"precision highp float; \n"
" \n"
"varying vec2 v_texCoord; \n"
"uniform sampler2D u_tex0; \n"
"uniform float u_alpha; \n"
" \n"
"void main(void) \n"
"{ \n"
"  vec4 l_color = texture2D(u_tex0, v_texCoord); \n"
"  gl_FragColor = vec4(l_color.rgb, u_alpha); \n"
"} \n"
;

const static std::string s_infoPanel_vert =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2017 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"attribute vec2 osg_MultiTexCoord0; \n"
"attribute vec4 osg_Vertex; \n"
"uniform mat4 osg_ModelViewProjectionMatrix; \n"
"varying vec2 v_texCoord; \n"
" \n"
" \n"
"void main(void) \n"
"{ \n"
"  v_texCoord = osg_MultiTexCoord0; \n"
"  gl_Position = osg_ModelViewProjectionMatrix * osg_Vertex; \n"
"} \n"
;

const static std::string s_junctionIcon_frag =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2017 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"precision highp float; \n"
" \n"
"varying vec2 v_texCoord; \n"
"uniform sampler2D u_tex0; \n"
"uniform float u_alpha; \n"
" \n"
"void main(void) \n"
"{ \n"
"  vec4 l_color = texture2D(u_tex0, v_texCoord); \n"
"  gl_FragColor = l_color; // vec4(l_color.rgb, u_alpha); \n"
"} \n"
;

const static std::string s_junctionIcon_vert =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2017 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"attribute vec2 osg_MultiTexCoord0; \n"
"attribute vec4 osg_Vertex; \n"
"uniform mat4 osg_ModelViewProjectionMatrix; \n"
"varying vec2 v_texCoord; \n"
" \n"
"void main(void) \n"
"{ \n"
"  v_texCoord = osg_MultiTexCoord0; \n"
"  gl_Position = osg_ModelViewProjectionMatrix * osg_Vertex; \n"
"} \n"
;

const static std::string s_noCam_frag =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2017 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"precision highp float; \n"
" \n"
"const vec4 outputColor = vec4(0.6, 0.6, 0.6, 1.0); \n"
" \n"
"void main() \n"
"{ \n"
"  gl_FragColor = outputColor; \n"
"} \n"
;

const static std::string s_noCam_vert =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2017 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"// varying \n"
"attribute highp vec4 osg_Vertex; \n"
"// uniform \n"
"uniform highp mat4 osg_ModelViewProjectionMatrix; \n"
" \n"
"void main() \n"
"{ \n"
"  gl_Position = osg_ModelViewProjectionMatrix * osg_Vertex; \n"
" \n"
"} \n"
;

const static std::string s_parkingspotTex_frag =
"// Copyright (c) 2017 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"precision highp float; \n"
" \n"
"varying vec4 v_color; \n"
"varying vec2 v_texCoord; \n"
" \n"
"uniform sampler2D u_tex0; \n"
"uniform bool  u_Selected; \n"
"uniform float u_alpha; \n"
"uniform float u_bias; \n"
" \n"
"void main() \n"
"{ \n"
"  vec4 l_color = v_color * texture2D(u_tex0, v_texCoord, u_bias); \n"
" \n"
"  if (u_Selected) \n"
"  { \n"
"    l_color.a *= u_alpha; \n"
"    gl_FragColor = l_color; \n"
"  } \n"
"  else \n"
"  { \n"
"    float grey = 0.21 * l_color.r + 0.71 * l_color.g + 0.07 * l_color.b; \n"
"    float u_colorFactor = 0.37; \n"
"    gl_FragColor = vec4(l_color.r * u_colorFactor + grey * (1.0 - u_colorFactor), l_color.g * u_colorFactor + grey * (1.0 - u_colorFactor), l_color.b * u_colorFactor + grey * (1.0 - u_colorFactor), l_color.a); \n"
"  } \n"
" \n"
"} \n"
;

const static std::string s_parkingspotTex_vert =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2017 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"attribute vec4 osg_Vertex; \n"
"attribute vec4 osg_Color; \n"
"attribute vec2 osg_MultiTexCoord0; \n"
" \n"
"uniform mat4 osg_ModelViewProjectionMatrix; \n"
"uniform vec4 u_texSelect; \n"
" \n"
"varying vec4 v_color; \n"
"varying vec2 v_texCoord; \n"
" \n"
"void main() \n"
"{ \n"
"  v_color = osg_Color; \n"
"  vec2 l_texCoord = osg_MultiTexCoord0 * u_texSelect.xy; \n"
"  v_texCoord = l_texCoord + u_texSelect.zw; \n"
"  gl_Position = osg_ModelViewProjectionMatrix * osg_Vertex; \n"
"} \n"
;

const static std::string s_points_frag =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2017 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"precision highp float; \n"
" \n"
"varying vec4 v_FragColor; \n"
"varying float v_PointSize; \n"
" \n"
"float circle(in vec2 _st, in float _radius) \n"
"{ \n"
"  vec2 dist = _st - vec2(0.5); \n"
"	return 1. - smoothstep(_radius - 2. / v_PointSize, _radius + 2. / v_PointSize, dot(dist, dist) * 4.0); \n"
"} \n"
" \n"
"void main(void) \n"
"{ \n"
"    float alpha = circle(gl_PointCoord, 0.9); \n"
" \n"
"    gl_FragColor = vec4(v_FragColor.rgb, alpha); \n"
"} \n"
;

const static std::string s_points_vert =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2017 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"attribute vec4 osg_Vertex; \n"
"attribute vec4 osg_Color; \n"
" \n"
"uniform mat4 osg_ModelViewProjectionMatrix; \n"
" \n"
"varying vec4 v_FragColor; \n"
"varying float v_PointSize; \n"
"uniform float pointSize; \n"
" \n"
"void main(void) \n"
"{ \n"
"  gl_Position = osg_ModelViewProjectionMatrix * osg_Vertex; \n"
"  gl_PointSize = pointSize; \n"
"   \n"
"  v_FragColor = osg_Color; \n"
"  v_PointSize = gl_PointSize; \n"
"} \n"
;

const static std::string s_rawTex_frag =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2017 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"#extension GL_OES_EGL_image_external : require \n"
" \n"
"precision highp float; \n"
" \n"
"#ifdef GL_ES \n"
"uniform samplerExternalOES u_tex0; \n"
"#else \n"
"uniform sampler2D u_tex0; \n"
"#endif \n"
" \n"
"varying vec2 v_texCoord; \n"
" \n"
"uniform float u_brightFactor; \n"
" \n"
"void main() \n"
"{ \n"
"  vec4 l_color = texture2D(u_tex0, v_texCoord); \n"
"  gl_FragColor = vec4(clamp((l_color).rgb * u_brightFactor, 0.0, 1.0), 1.0); \n"
"} \n"
;

const static std::string s_rawTex_vert =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2017 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"attribute vec4 osg_Vertex; \n"
"attribute vec2 osg_MultiTexCoord0; \n"
" \n"
"uniform mat4 osg_ModelViewProjectionMatrix; \n"
" \n"
"varying vec2 v_texCoord; \n"
" \n"
"void main() \n"
"{ \n"
"  v_texCoord = osg_MultiTexCoord0; \n"
"  gl_Position = osg_ModelViewProjectionMatrix * osg_Vertex; \n"
"} \n"
;

const static std::string s_rawTex_pxlDensity_frag =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2017 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"precision highp float; \n"
" \n"
"uniform sampler2D u_tex0; \n"
"varying vec2 v_texCoord; \n"
" \n"
"vec4 red2green(float x) { \n"
"	float g = clamp(x, 0.0, 1.0); \n"
"	float r = clamp(1.0-x, 0.0, 1.0); \n"
"	float b = 0.0; \n"
"	return vec4(r, g, b, 1.0); \n"
"} \n"
" \n"
"vec4 red2yellow2green(float x) { \n"
" \n"
"  float r, g; \n"
"  if ( x < 0.5) \n"
"  { \n"
"    r = 1.0; \n"
"    g = clamp(2.0*x, 0.0, 1.0); \n"
"  } \n"
"  else \n"
"  { \n"
"    r = clamp(2.0*(1.0-x), 0.0, 1.0); \n"
"    g = 1.0; \n"
"  } \n"
" \n"
"  return vec4(r, g, 0.0, 1.0); \n"
"} \n"
" \n"
"vec4 sixColors(float x) { \n"
" \n"
"  float r, g, b; \n"
"  float d; \n"
" \n"
"  if (x < 0) \n"
"  { \n"
"    r = 0; \n"
"    g = 1; \n"
"    b = 1; \n"
"  } \n"
"  else if (x < 0.2) \n"
"  { \n"
"    d = ((x - 0.0) * 5.0); \n"
"    r = 0.0 * (1.0-d) + 0.0 * d; \n"
"    g = 1.0; \n"
"    b = 1.0 * (1.0-d) + 0.0 * d; \n"
"  } \n"
"  else if (x < 0.4) \n"
"  { \n"
"    d = ((x-0.2) * 5.0); \n"
"    r = 0.0 * (1.0-d) + 1.0 * d; \n"
"    g = 1.0; \n"
"    b = 0.0; \n"
"  } \n"
"  else if (x < 0.6) \n"
"  { \n"
"    d = ((x-0.4) * 5.0); \n"
"    r = 1.0; \n"
"    g = 1.0 * (1.0-d) + 0.0 * d; \n"
"    b = 0.0; \n"
"  } \n"
"  else if (x < 0.8) \n"
"  { \n"
"    d = ((x-0.6) * 5.0); \n"
"    r = 1.0; \n"
"    g = 0.0; \n"
"    b = 0.0 * (1.0-d) + 1.0 * d; \n"
"  } \n"
"  else if (x < 1.0) \n"
"  { \n"
"    d = ((x-0.8) * 5.0); \n"
"    r = 1.0 * (1.0-d) + 0.0 * d; \n"
"    g = 0.0; \n"
"    b = 1.0; \n"
"  } \n"
"  else \n"
"  { \n"
"    r = 0.0; \n"
"    g = 0.0; \n"
"    b = 1.0; \n"
"  } \n"
"  return vec4(r, g, b, 1.0); \n"
"} \n"
" \n"
" \n"
"#define NORMAL_SHADING    0 \n"
" \n"
"#define RED_GREEN         0 \n"
"#define RED_YELLOW_GREEN  1 \n"
"#define SIX_COLORS        2 \n"
"#define COLORMAP          SIX_COLORS \n"
" \n"
"void main() \n"
"{ \n"
"#if NORMAL_SHADING \n"
"  gl_FragColor = texture2D(u_tex0, v_texCoord); \n"
"  return; \n"
"#endif \n"
" \n"
"	int image_width  = 1620; \n"
"	int image_height = 1280; \n"
"  vec2 deltaX = vec2(dFdx(v_texCoord.x) , dFdx(v_texCoord.y) ); \n"
"  vec2 deltaY = vec2(dFdy(v_texCoord.x) , dFdy(v_texCoord.y) ); \n"
" \n"
"  // input pixels per output fragment \n"
"  float ratio = (length(deltaX)*image_width) * (length(deltaY)*image_height); \n"
" \n"
"  // consider a scale different to [0-1] for the [red-green] transition \n"
"  // a value of 0.5 means red = 0, green = 0.5 \n"
"  // float scale_limit = 0.5; \n"
"  float scale_limit = 1.0; \n"
"  ratio = ratio / scale_limit; \n"
" \n"
"#if RED_YELLOW_GREEN == COLORMAP \n"
"  gl_FragColor = red2yellow2green(ratio); \n"
"#elif RED_GREEN == COLORMAP \n"
"  gl_FragColor = red2green(ratio); \n"
"#elif SIX_COLORS == COLORMAP \n"
"  gl_FragColor = sixColors(ratio); \n"
"#else // default \n"
"  gl_FragColor = red2green(ratio); \n"
"#endif \n"
"} \n"
;

const static std::string s_renderUV_frag =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2020 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"precision mediump float; \n"
" \n"
"varying mediump vec2 v_texCoord; \n"
" \n"
"vec4 encodeVec2ToRGBA(vec2 uv) \n"
"{ \n"
"    vec4 enc = uv.xxyy*vec4(1.0, 255.0, 1.0, 255.0); \n"
"    enc = fract(enc); \n"
"    enc.xz-= vec2(enc.y, enc.w)/255.0; \n"
"    return enc; \n"
"} \n"
" \n"
"void main() \n"
"{ \n"
"  gl_FragColor = encodeVec2ToRGBA(v_texCoord); \n"
"} \n"
;

const static std::string s_renderUV_vert =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2020 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
"precision mediump float; \n"
" \n"
"varying mediump vec2 v_texCoord; \n"
" \n"
"attribute vec3 osg_Vertex; \n"
"attribute vec2 osg_MultiTexCoord0; \n"
"uniform highp mat4 osg_ModelViewProjectionMatrix; \n"
" \n"
"void main() \n"
"{ \n"
"  v_texCoord = osg_MultiTexCoord0; \n"
"  gl_Position = osg_ModelViewProjectionMatrix * vec4(osg_Vertex, 1.0); \n"
"} \n"
;

const static std::string s_separableGaussian_frag =
"precision highp float; \n"
" \n"
"uniform vec2 u_offset; \n"
" \n"
"uniform sampler2D u_tex0; \n"
"varying vec2 v_texCoord; \n"
" \n"
"vec4 blur5(sampler2D f_texture, vec2 f_uv) \n"
"{ \n"
"  vec4 l_color = vec4(0.0); \n"
"  l_color+= texture2D(f_texture, f_uv) * 0.29411764705882354; \n"
"  l_color+= texture2D(f_texture, f_uv + u_offset) * 0.35294117647058826; \n"
"  l_color+= texture2D(f_texture, f_uv - u_offset) * 0.35294117647058826; \n"
"  return l_color; \n"
"} \n"
" \n"
"void main() \n"
"{ \n"
"    gl_FragColor = blur5(u_tex0, v_texCoord); \n"
"} \n"
;

const static std::string s_separableGaussian_vert =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2019 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"attribute vec4 osg_Vertex; \n"
"attribute vec2 osg_MultiTexCoord0; \n"
"uniform mat4 osg_ModelViewProjectionMatrix; \n"
" \n"
"varying vec2 v_texCoord; \n"
" \n"
"void main() \n"
"{ \n"
"  v_texCoord = osg_MultiTexCoord0; \n"
"  //gl_Position = (osg_Vertex-vec4(0.5, 0.5, 0.0, 0.0))*2.0; \n"
"  gl_Position = osg_ModelViewProjectionMatrix*osg_Vertex; \n"
"} \n"
;

const static std::string s_snapShot_frag =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2017 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"precision highp float; \n"
" \n"
"//varying \n"
"varying vec2 v_texCoord; \n"
" \n"
"uniform sampler2D u_texCam; \n"
"uniform sampler2D u_texMask; \n"
"uniform vec4 u_maskColor; \n"
" \n"
"void main() \n"
"{ \n"
"  vec4 l_color = texture2D(u_texCam, v_texCoord.xy) + ((1.0 - texture2D(u_texMask, v_texCoord.xy).r) * u_maskColor); \n"
"  gl_FragColor =  vec4(l_color.rgb, 1.0); \n"
"} \n"
;

const static std::string s_snapShot_vert =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2017 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"attribute highp vec4 osg_Vertex; \n"
"attribute vec2 osg_MultiTexCoord0; \n"
"uniform highp mat4 osg_ModelViewProjectionMatrix; \n"
" \n"
"varying vec2 v_texCoord; \n"
" \n"
"void main() \n"
"{ \n"
"  v_texCoord = osg_MultiTexCoord0; \n"
" \n"
"  gl_Position = osg_ModelViewProjectionMatrix * osg_Vertex; \n"
"} \n"
;

const static std::string s_stripedBasePlate_frag =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2019 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"precision highp float; \n"
" \n"
"varying vec2 v_texCoord; \n"
" \n"
"uniform sampler2D u_stripeTexture; \n"
" \n"
"varying float v_alpha; \n"
"uniform float u_basePlateAlpha; \n"
" \n"
"void main() \n"
"{ \n"
"  //float alpha = 1.0-(1.0-v_alpha)*(1.0-v_alpha); // quadratic falloff \n"
"  float alpha = v_alpha*u_basePlateAlpha; \n"
"  gl_FragColor = vec4(texture2D(u_stripeTexture, v_texCoord).rgb, alpha); \n"
"} \n"
;

const static std::string s_stripedBasePlate_vert =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2019 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"attribute vec4 osg_Vertex; \n"
"uniform mat4 osg_ModelViewProjectionMatrix; \n"
"uniform vec2 u_worldToUVScale; \n"
" \n"
"varying vec2 v_texCoord; \n"
"varying float v_alpha; \n"
" \n"
"void main() \n"
"{ \n"
"  v_texCoord = osg_Vertex.xy*u_worldToUVScale; \n"
"  v_alpha = 1.0-osg_Vertex.z; \n"
"  gl_Position = osg_ModelViewProjectionMatrix * vec4(osg_Vertex.xy, 0.0, 1.0); \n"
"} \n"
;

const static std::string s_texfloorHistory_frag =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2017 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"precision highp float; \n"
" \n"
"varying vec2 v_texCoord0; \n"
"varying vec2 v_texCoord1; \n"
" \n"
"uniform sampler2D u_tex0; \n"
"uniform sampler2D u_tex1; \n"
" \n"
"void main() \n"
"{ \n"
"  vec4 l_tex0Color = texture2D(u_tex0, v_texCoord0); \n"
"  vec4 l_tex1Color = texture2D(u_tex1, v_texCoord1); \n"
"  vec4 l_color = mix(l_tex0Color, l_tex1Color, 1.0 - l_tex0Color.a); \n"
" \n"
"  gl_FragColor = l_color; \n"
"} \n"
;

const static std::string s_texfloorHistory_vert =
"//------------------------------------------------------------------------------- \n"
"// Copyright (c) 2017 by Robert Bosch GmbH. All rights reserved. \n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or \n"
"// distribution is an offensive act against international law and may be \n"
"// prosecuted under federal law. Its content is company confidential. \n"
"//------------------------------------------------------------------------------- \n"
" \n"
"attribute vec4 osg_Vertex; \n"
"attribute vec2 osg_MultiTexCoord0; \n"
"uniform mat4 osg_ModelViewProjectionMatrix; \n"
" \n"
"varying vec2 v_texCoord0; \n"
"varying vec2 v_texCoord1; \n"
" \n"
"uniform mat4 u_textureTransform; \n"
" \n"
"void main() \n"
"{ \n"
"  v_texCoord0 = osg_MultiTexCoord0; \n"
"  v_texCoord1 = (u_textureTransform * vec4(osg_MultiTexCoord0, 1.0, 1.0)).xy; \n"
"  gl_Position = osg_ModelViewProjectionMatrix * osg_Vertex; \n"
"} \n"
;

const static std::string s_tnf_frag =
"#version 310 es \n"
" \n"
"// uncomment one of the following defines to use sum of absolute differences \n"
"// in the classical form or in the texture gather form \n"
"//#define USE_SAD \n"
"//#define USE_SAD_TEXTURE_GATHER \n"
" \n"
"#if defined(USE_SAD) && defined(USE_SAD_TEXTURE_GATHER) \n"
"  #error Both USE_SAD and USE_SAD_TEXTURE_GATHER are defined. Please define only one. \n"
"#endif \n"
" \n"
"#ifdef USE_SAMPLER_EXTERNAL_OES \n"
"  #extension GL_OES_EGL_image_external_essl3 : enable \n"
"  #extension GL_EXT_YUV_target : require \n"
"  #ifdef USE_SAD_TEXTURE_GATHER \n"
"    #extension GL_QCOM_YUV_texture_gather : require \n"
"  #endif \n"
"#endif \n"
" \n"
"precision highp float; \n"
" \n"
"in vec2 v_texCoord; \n"
"layout(location = 0) out highp float FragColor; // bound to frame buffer in R8 format for y-plane \n"
"layout(location = 1) out highp float MotionMap; \n"
" \n"
"#ifdef USE_SAMPLER_EXTERNAL_OES \n"
"  uniform __samplerExternal2DY2YEXT u_texCurrent; \n"
"#else \n"
"  uniform sampler2D u_texCurrent; \n"
"#endif \n"
" \n"
"uniform sampler2D u_texPreviousY; \n"
" \n"
"//Parameters \n"
"uniform int u_cam; \n"
"uniform int u_isTexCombine; \n"
" \n"
"uniform float u_threshold; \n"
"uniform float u_filteredImageWeight; \n"
"uniform bool u_combineMotionMap; \n"
"uniform bool u_setToPassThrough; \n"
"uniform bool u_outputMotionMap; \n"
" \n"
"vec2 getUv(vec2 f_uv) \n"
"{ \n"
"  if (1 == u_isTexCombine) \n"
"  { \n"
"    vec2 l_uv = vec2(0.0, f_uv.y); \n"
"    if (0 == u_cam)  // front \n"
"    { \n"
"      l_uv.x = 0.75 + f_uv.x * 0.25; \n"
"    } \n"
"    else if (1 == u_cam)  // right \n"
"    { \n"
"      l_uv.x = 0.5 + f_uv.x * 0.25; \n"
"    } \n"
"    else if (2 == u_cam)  // rear \n"
"    { \n"
"      l_uv.x = f_uv.x * 0.25; \n"
"    } \n"
"    else  // left \n"
"    { \n"
"      l_uv.x = 0.25 + f_uv.x * 0.25; \n"
"    } \n"
"    return l_uv; \n"
"  } \n"
"  else \n"
"  { \n"
"    return f_uv; \n"
"  } \n"
"} \n"
" \n"
"// for the SIL \n"
"#ifndef USE_SAMPLER_EXTERNAL_OES \n"
"  vec4 yuv_2_rgb_sil(vec4 src) \n"
"  { \n"
"      vec4 yuva = vec4((src.x-16.0/255.0), (src.y - 0.5), (src.z - 0.5), 1.0); \n"
" \n"
"      vec4 res; \n"
"      res.r = 1.164 * yuva.x                  + 1.596 * yuva.z; \n"
"      res.g = 1.164 * yuva.x - 0.392 * yuva.y - 0.813 * yuva.z; \n"
"      res.b = 1.164 * yuva.x + 2.017 * yuva.y; \n"
" \n"
"      res.a = src.a; \n"
" \n"
"      return res; \n"
"  } \n"
" \n"
" \n"
"  vec4 rgb_2_yuv_sil(vec4 src) \n"
"  { \n"
"      vec4 res; \n"
"      res.x =  0.257 * src.r + 0.504 * src.g + 0.098 * src.b + 16.0/255.0; \n"
"      res.y = -0.148 * src.r - 0.291 * src.g + 0.439 * src.b + 0.5; \n"
"      res.z =  0.439 * src.r - 0.368 * src.g - 0.071 * src.b + 0.5; \n"
" \n"
"      res.a = src.a; \n"
" \n"
"      return res; \n"
"  } \n"
" \n"
"  vec4 textureGatherSilY(sampler2D f_tex, vec2 f_uv) \n"
"  { \n"
"    vec4 gatherR = textureGather(f_tex, f_uv, 0); \n"
"    vec4 gatherG = textureGather(f_tex, f_uv, 1); \n"
"    vec4 gatherB = textureGather(f_tex, f_uv, 2); \n"
" \n"
"    return vec4(rgb_2_yuv_sil(vec4(gatherR.x, gatherG.x, gatherB.x, 1.0)).r, \n"
"                rgb_2_yuv_sil(vec4(gatherR.y, gatherG.y, gatherB.y, 1.0)).r, \n"
"                rgb_2_yuv_sil(vec4(gatherR.z, gatherG.z, gatherB.z, 1.0)).r, \n"
"                rgb_2_yuv_sil(vec4(gatherR.w, gatherG.w, gatherB.w, 1.0)).r); \n"
"  } \n"
" \n"
"#endif \n"
" \n"
"void main() \n"
"{ \n"
"  #ifndef USE_SAMPLER_EXTERNAL_OES \n"
"    float currentY = rgb_2_yuv_sil(texture(u_texCurrent, getUv(v_texCoord))).r; // get y-component of current input \n"
"  #else \n"
"    float currentY = texture(u_texCurrent, getUv(v_texCoord)).r; // get y-component of current input \n"
"  #endif \n"
"  if(u_setToPassThrough) \n"
"  { \n"
"    FragColor = currentY; \n"
"  } \n"
"  else \n"
"  { \n"
"    // Filter \n"
"    float previousY = texture(u_texPreviousY, v_texCoord).r; \n"
"#ifdef USE_SAD \n"
"    //SAD 3x3 kernel \n"
"    vec2 sizeOfTexture = vec2(textureSize(u_texCurrent, 0)); \n"
"    vec2 onePixel = vec2(1.0, 1.0) / sizeOfTexture; \n"
"    float difference = 0.0; \n"
"    float previousYsad = 0.0; \n"
"    float currentYsad = 0.0; \n"
"    for(int iy=-1; iy<=1; iy++) \n"
"    { \n"
"        for(int ix=-1; ix<=1; ix++) \n"
"        { \n"
"            if (ix == 0 && iy == 0) \n"
"            { \n"
"              previousYsad = previousY; \n"
"              currentYsad = currentY; \n"
"            } \n"
"            else \n"
"            { \n"
"              previousYsad = texture(u_texPreviousY, v_texCoord + onePixel * vec2(ix,iy)).r; \n"
"              #ifndef USE_SAMPLER_EXTERNAL_OES \n"
"                currentYsad  = rgb_2_yuv_sil(texture(u_texCurrent, getUv(v_texCoord) + onePixel * vec2(ix,iy))).r; \n"
"              #else \n"
"                currentYsad  = texture(u_texCurrent, getUv(v_texCoord) + onePixel * vec2(ix,iy)).r; \n"
"              #endif \n"
"            } \n"
"            float delta =  abs(previousYsad - currentYsad); \n"
"            difference += delta; \n"
"        } \n"
"    } \n"
"    // double weighting of center element \n"
"    float deltaCenter = abs(previousY - currentY); \n"
"    difference += deltaCenter; \n"
"    difference = difference / 10.0; \n"
"#elif defined USE_SAD_TEXTURE_GATHER \n"
"    vec2 sizeOfTexture = vec2(textureSize(u_texCurrent, 0)); \n"
"    vec2 onePixel = vec2(1.0, 1.0) / sizeOfTexture; \n"
" \n"
"    // X is the overlapping double weighted pixel \n"
"    //    A A \n"
"    //  B X A \n"
"    //  B B \n"
"    vec4 gatherUpRightPrev = textureGather(u_texPreviousY, v_texCoord + onePixel, 0); \n"
"    vec4 gatherCenterPrev = textureGather(u_texPreviousY, v_texCoord, 0); \n"
"    #ifndef USE_SAMPLER_EXTERNAL_OES \n"
"      vec4 gatherUpRightCurr = textureGatherSilY(u_texCurrent, getUv(v_texCoord) + onePixel); \n"
"      vec4 gatherCenterCurr = textureGatherSilY(u_texCurrent, getUv(v_texCoord)); \n"
"    #else \n"
"      vec4 gatherUpRightCurr = textureGather(u_texCurrent, getUv(v_texCoord) + onePixel, 0); \n"
"      vec4 gatherCenterCurr = textureGather(u_texCurrent, getUv(v_texCoord), 0); \n"
"   #endif \n"
"   vec4 differenceSad = (abs(gatherUpRightPrev - gatherUpRightCurr) + abs(gatherCenterPrev - gatherCenterCurr)) / 8.0; \n"
"   float difference = dot(differenceSad, vec4(1.0)); \n"
"#else \n"
"    float difference = abs(previousY - currentY); \n"
"#endif \n"
" \n"
"    //Blend \n"
"    // (currentY * (1.0 - u_filteredImageWeight)) + (previousY * u_filteredImageWeight) \n"
"    float filteredY = mix(currentY, previousY, u_filteredImageWeight); \n"
" \n"
"    //Filter Luminance \n"
"    float currentMotionMap = step(u_threshold, difference); // 1.0 if difference >= u_threshold, 0.0 otherwise \n"
"    float resultY = 0.0; \n"
" \n"
"    if (u_combineMotionMap) \n"
"    { \n"
"        float previousMotionMap = mod(floor(previousY * 255.0), 2.0); // Extract LSB \n"
"        float combinedMotionMap = max(currentMotionMap, previousMotionMap); \n"
"        resultY = mix(filteredY, currentY, combinedMotionMap); \n"
" \n"
"        // Encode MotionMap into the LSB of FragColor \n"
"        resultY = round(resultY * 255.0); // Scale to 8-bit and round \n"
"        resultY = floor(resultY / 2.0) * 2.0; // Remove the LSB \n"
"        resultY += currentMotionMap; // Add MotionMap into the LSB \n"
"        resultY /= 255.0; // Scale back to [0.0, 1.0] \n"
"        if (u_outputMotionMap) \n"
"        { \n"
"            MotionMap = combinedMotionMap; \n"
"        } \n"
"    } \n"
"    else \n"
"    { \n"
"        resultY = mix(filteredY, currentY, currentMotionMap); \n"
"        if (u_outputMotionMap) \n"
"        { \n"
"            MotionMap = currentMotionMap; \n"
"        } \n"
"    } \n"
" \n"
"    FragColor = resultY; \n"
"  } \n"
"} \n"
;

const static std::string s_tnf_vert =
"#version 310 es \n"
"in vec4 osg_Vertex; \n"
"in vec2 osg_MultiTexCoord0; \n"
" \n"
"uniform mat4 osg_ModelViewProjectionMatrix; \n"
" \n"
"out vec2 v_texCoord; \n"
" \n"
"void main() \n"
"{ \n"
"  v_texCoord = osg_MultiTexCoord0; \n"
"  gl_Position = osg_ModelViewProjectionMatrix * osg_Vertex; \n"
"} \n"
;

const static std::string s_wipingTex_vert =
"//-------------------------------------------------------------------------------\n"
"// Copyright (c) 2017 by Robert Bosch GmbH. All rights reserved.\n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or\n"
"// distribution is an offensive act against international law and may be\n"
"// prosecuted under federal law. Its content is company confidential.\n"
"//-------------------------------------------------------------------------------\n"
"\n"
"attribute vec4 osg_Vertex;\n"
"attribute vec4 osg_Color;\n"
"attribute vec2 osg_MultiTexCoord0;\n"
"\n"
"uniform mat4 osg_ModelViewProjectionMatrix;\n"
"uniform mediump vec2 u_uvOffset;\n"
"\n"
"varying vec4 v_color;\n"
"varying vec2 v_texCoord;\n"
"\n"
"void main()\n"
"{\n"
"  v_color = osg_Color;\n"
"  v_texCoord = osg_MultiTexCoord0 + u_uvOffset;\n"
"  gl_Position = osg_ModelViewProjectionMatrix * osg_Vertex;\n"
"}\n"
"\n"
;

const static std::string s_wipingTex_frag =
"//-------------------------------------------------------------------------------\n"
"// Copyright (c) 2017 by Robert Bosch GmbH. All rights reserved.\n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or\n"
"// distribution is an offensive act against international law and may be\n"
"// prosecuted under federal law. Its content is company confidential.\n"
"//-------------------------------------------------------------------------------\n"
"\n"
"precision mediump float;\n"
"\n"
"varying vec4 v_color;\n"
"varying vec2 v_texCoord;\n"
"\n"
"uniform sampler2D u_tex0;\n"
"uniform float u_time;\n"
"uniform float u_direction; // 0 : ->, 1 : <-\n"
"\n"
"\n"
"void main()\n"
"{\n"
"  vec4 l_color = v_color * texture2D(u_tex0, v_texCoord);\n"
"//   l_color.a = v_texCoord.y * sin(u_time);\n"
"  if (u_direction == 1.0) // right to left\n"
"  {\n"
"    float gradientAlpha = smoothstep(0.0, 1.0, (0.5 - v_texCoord.y) + u_time);\n"
"    l_color.a *= gradientAlpha;\n"
"  }\n"
"  else // left to right : default\n"
"  {\n"
"    float gradientAlpha = smoothstep(0.0, 1.0, (v_texCoord.y - 0.5) + u_time);\n"
"    l_color.a *= gradientAlpha;\n"
"  }\n"
"  gl_FragColor = l_color;\n"
"}\n"
;

const static std::string s_e3Tex_frag =
"//-------------------------------------------------------------------------------\n"
"// Copyright (c) 2017 by Robert Bosch GmbH. All rights reserved.\n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or\n"
"// distribution is an offensive act against international law and may be\n"
"// prosecuted under federal law. Its content is company confidential.\n"
"//-------------------------------------------------------------------------------\n"
"\n"
"precision highp float;\n"
"\n"
"varying vec2 v_texCoord;\n"
"varying vec4 v_color;\n"
"uniform sampler2D u_tex0;\n"
"uniform float u_startCutoff;\n"
"uniform float u_shutdownAlpha;\n"
"uniform float u_rotationPercentage;\n"
"\n"
"void main()\n"
"{\n"
"  vec4 l_color = texture2D(u_tex0, v_texCoord);\n"
"  if (v_texCoord.t < u_startCutoff) {\n"
"    l_color.a *= 0.0;\n"
"  } else {\n"
"    l_color.a *= v_texCoord.t;\n"
"    l_color.a *= (1.0-u_startCutoff);\n"
"  }\n"
"  if (v_texCoord.t < u_rotationPercentage && u_rotationPercentage < 0.5) {\n"
"    l_color.a *= max(1-u_rotationPercentage, 0.5);\n"
"  }\n"
"  else if (v_texCoord.t < 0.5 && u_rotationPercentage >= 0.5) {\n"
"    l_color.a *= 0.5;\n"
"  }\n"
"  else {\n"
"    l_color.a *= max(1-u_rotationPercentage, 0.5);\n"
"  }\n"
"\n"
"  l_color.a *= u_shutdownAlpha;\n"
"  l_color *= v_color;\n"
"  gl_FragColor = l_color;\n"
"}\n"
;
const static std::string s_e3Tex_vert =
"//-------------------------------------------------------------------------------\n"
"// Copyright (c) 2017 by Robert Bosch GmbH. All rights reserved.\n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or\n"
"// distribution is an offensive act against international law and may be\n"
"// prosecuted under federal law. Its content is company confidential.\n"
"//-------------------------------------------------------------------------------\n"
"\n"
"attribute vec4 osg_Vertex;\n"
"attribute vec4 osg_Color;\n"
"attribute vec2 osg_MultiTexCoord0;\n"
"uniform mat4 osg_ModelViewProjectionMatrix;\n"
"varying vec4 v_color;\n"
"varying vec2 v_texCoord;\n"
"\n"
"void main()\n"
"{\n"
"  v_color = osg_Color;\n"
"  v_texCoord = osg_MultiTexCoord0;\n"
"  gl_Position = osg_ModelViewProjectionMatrix * osg_Vertex;\n"
"}\n"
;
const static std::string s_e3TopviewArrow_frag =
"//-------------------------------------------------------------------------------\n"
"// Copyright (c) 2017 by Robert Bosch GmbH. All rights reserved.\n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or\n"
"// distribution is an offensive act against international law and may be\n"
"// prosecuted under federal law. Its content is company confidential.\n"
"//-------------------------------------------------------------------------------\n"
"\n"
"precision highp float;\n"
"\n"
"varying vec2 v_texCoord;\n"
"varying vec4 v_color;\n"
"uniform sampler2D u_tex0;\n"
"uniform float alpha;\n"
"\n"
"void main()\n"
"{\n"
"  vec4 l_color = texture2D(u_tex0, v_texCoord);\n"
"  l_color *= v_color;\n"
"  gl_FragColor = vec4(l_color*max(0.7, alpha));\n"
"}\n"
"\n"
;
const static std::string s_e3TopviewArrow_vert =
"//-------------------------------------------------------------------------------\n"
"// Copyright (c) 2017 by Robert Bosch GmbH. All rights reserved.\n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or\n"
"// distribution is an offensive act against international law and may be\n"
"// prosecuted under federal law. Its content is company confidential.\n"
"//-------------------------------------------------------------------------------\n"
"\n"
"attribute vec4 osg_Vertex;\n"
"attribute vec4 osg_Color;\n"
"attribute vec2 osg_MultiTexCoord0;\n"
"uniform mat4 osg_ModelViewProjectionMatrix;\n"
"varying vec4 v_color;\n"
"varying vec2 v_texCoord;\n"
"\n"
"void main()\n"
"{\n"
"  v_color = osg_Color;\n"
"  v_texCoord = osg_MultiTexCoord0;\n"
"  gl_Position = osg_ModelViewProjectionMatrix * osg_Vertex;\n"
"}\n"
;
const static std::string s_e3TopviewTrajectoryTex_frag =
"//-------------------------------------------------------------------------------\n"
"// Copyright (c) 2017 by Robert Bosch GmbH. All rights reserved.\n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or\n"
"// distribution is an offensive act against international law and may be\n"
"// prosecuted under federal law. Its content is company confidential.\n"
"//-------------------------------------------------------------------------------\n"
"\n"
"precision highp float;\n"
"\n"
"varying vec2 v_texCoord;\n"
"varying vec4 v_color;\n"
"uniform sampler2D u_tex0;\n"
"uniform float u_startCutoff;\n"
"\n"
"void main()\n"
"{\n"
"  vec4 l_color = texture2D(u_tex0, v_texCoord);\n"
"  if (v_texCoord.t > u_startCutoff) {\n"
"    l_color.a = 0.0;\n"
"  } else {\n"
"    if (v_texCoord.t < 0.2) {\n"
"        l_color.a *= 1.0 - (0.2 - v_texCoord.t) / 0.2;\n"
"    }\n"
"    else if (v_texCoord.t > 0.8) {\n"
"        l_color.a *= 1.0 - (v_texCoord.t - 0.8) / 0.2;\n"
"    }\n"
"  }\n"
"  l_color *= v_color;\n"
"  gl_FragColor = l_color;\n"
"}\n"
;
const static std::string s_e3TopviewTrajectoryTex_vert =
"//-------------------------------------------------------------------------------\n"
"// Copyright (c) 2017 by Robert Bosch GmbH. All rights reserved.\n"
"// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or\n"
"// distribution is an offensive act against international law and may be\n"
"// prosecuted under federal law. Its content is company confidential.\n"
"//-------------------------------------------------------------------------------\n"
"\n"
"attribute vec4 osg_Vertex;\n"
"attribute vec4 osg_Color;\n"
"attribute vec2 osg_MultiTexCoord0;\n"
"uniform mat4 osg_ModelViewProjectionMatrix;\n"
"varying vec4 v_color;\n"
"varying vec2 v_texCoord;\n"
"\n"
"void main()\n"
"{\n"
"  v_color = osg_Color;\n"
"  v_texCoord = osg_MultiTexCoord0;\n"
"  gl_Position = osg_ModelViewProjectionMatrix * osg_Vertex;\n"
"}\n"
;
}

sharderSourceType getPortingSourceCode(const std::string& f_filename, std::string& f_shaderSource)
{
    sharderSourceType l_return = NON_DETECT_SHADER_SOURCCE;

    // Shader source assignments based on filename
    if(f_filename.find("advancedTex.frag") != std::string::npos)
    {
        f_shaderSource += pc::core::s_advancedTex_frag;
        l_return = FRAGMENT_SHADER_SOURCCE;
    }
    else if(f_filename.find("advancedTex.vert") != std::string::npos)
    {
        f_shaderSource += pc::core::s_advancedTex_vert;
        l_return = VERTEXT_SHADER_SOURCCE;
    }
    else if(f_filename.find("augmentedViewInnerTransition.frag") != std::string::npos)
    {
        f_shaderSource += pc::core::s_augmentedViewInnerTransition_frag;
        l_return = FRAGMENT_SHADER_SOURCCE;
    }
    else if(f_filename.find("augmentedViewInnerTransition.vert") != std::string::npos)
    {
        f_shaderSource += pc::core::s_augmentedViewInnerTransition_vert;
        l_return = VERTEXT_SHADER_SOURCCE;
    }
    else if(f_filename.find("augmentedViewWavefront.vert") != std::string::npos)
    {
        f_shaderSource += pc::core::s_augmentedViewWavefront_vert;
        l_return = VERTEXT_SHADER_SOURCCE;
    }
    else if(f_filename.find("augmentedViewWavefrontAdditive.frag") != std::string::npos)
    {
        f_shaderSource += pc::core::s_augmentedViewWavefrontAdditive_frag;
        l_return = FRAGMENT_SHADER_SOURCCE;
    }
    else if(f_filename.find("basePlateHistory.frag") != std::string::npos)
    {
        f_shaderSource += pc::core::s_basePlateHistory_frag;
        l_return = FRAGMENT_SHADER_SOURCCE;
    }
    else if(f_filename.find("basePlateHistory.vert") != std::string::npos)
    {
        f_shaderSource += pc::core::s_basePlateHistory_vert;
        l_return = VERTEXT_SHADER_SOURCCE;
    }
    else if(f_filename.find("basicColor.frag") != std::string::npos)
    {
        f_shaderSource += pc::core::s_basicColor_frag;
        l_return = FRAGMENT_SHADER_SOURCCE;
    }
    else if(f_filename.find("basicColor.vert") != std::string::npos)
    {
        f_shaderSource += pc::core::s_basicColor_vert;
        l_return = VERTEXT_SHADER_SOURCCE;
    }
    else if(f_filename.find("basicTex.frag") != std::string::npos)
    {
        f_shaderSource += pc::core::s_basicTex_frag;
        l_return = FRAGMENT_SHADER_SOURCCE;
    }
    else if(f_filename.find("basicTex.vert") != std::string::npos)
    {
        f_shaderSource += pc::core::s_basicTex_vert;
        l_return = VERTEXT_SHADER_SOURCCE;
    }
    else if(f_filename.find("basicTexAlpha.frag") != std::string::npos)
    {
        f_shaderSource += pc::core::s_basicTexAlpha_frag;
        l_return = FRAGMENT_SHADER_SOURCCE;
    }
    else if(f_filename.find("basicTexAlpha.vert") != std::string::npos)
    {
        f_shaderSource += pc::core::s_basicTexAlpha_vert;
        l_return = VERTEXT_SHADER_SOURCCE;
    }
    else if(f_filename.find("basicTexMask.frag") != std::string::npos)
    {
        f_shaderSource += pc::core::s_basicTexMask_frag;
        l_return = FRAGMENT_SHADER_SOURCCE;
    }
    else if(f_filename.find("blurredPlateTexture.frag") != std::string::npos)
    {
        f_shaderSource += pc::core::s_blurredPlateTexture_frag;
        l_return = FRAGMENT_SHADER_SOURCCE;
    }
    else if(f_filename.find("blurredPlateTexture.vert") != std::string::npos)
    {
        f_shaderSource += pc::core::s_blurredPlateTexture_vert;
        l_return = VERTEXT_SHADER_SOURCCE;
    }
    else if(f_filename.find("cameraTexCombine.frag") != std::string::npos)
    {
        f_shaderSource += pc::core::s_cameraTexCombine_frag;
        l_return = FRAGMENT_SHADER_SOURCCE;
    }
    else if(f_filename.find("cameraTexCombine.vert") != std::string::npos)
    {
        f_shaderSource += pc::core::s_cameraTexCombine_vert;
        l_return = VERTEXT_SHADER_SOURCCE;
    }
    else if(f_filename.find("cameraTexCombineDebugView.frag") != std::string::npos)
    {
        f_shaderSource += pc::core::s_cameraTexCombineDebugView_frag;
        l_return = FRAGMENT_SHADER_SOURCCE;
    }
    else if(f_filename.find("cameraTexCombineDebugView.vert") != std::string::npos)
    {
        f_shaderSource += pc::core::s_cameraTexCombineDebugView_vert;
        l_return = VERTEXT_SHADER_SOURCCE;
    }
    else if(f_filename.find("chamaeleonRoiLeft.frag") != std::string::npos)
    {
        f_shaderSource += pc::core::s_chamaeleonRoiLeft_frag;
        l_return = FRAGMENT_SHADER_SOURCCE;
    }
    else if(f_filename.find("chamaeleonRoiLeft.vert") != std::string::npos)
    {
        f_shaderSource += pc::core::s_chamaeleonRoiLeft_vert;
        l_return = VERTEXT_SHADER_SOURCCE;
    }
    else if(f_filename.find("chamaeleonRoiRight.frag") != std::string::npos)
    {
        f_shaderSource += pc::core::s_chamaeleonRoiRight_frag;
        l_return = FRAGMENT_SHADER_SOURCCE;
    }
    else if(f_filename.find("chamaeleonRoiRight.vert") != std::string::npos)
    {
        f_shaderSource += pc::core::s_chamaeleonRoiRight_vert;
        l_return = VERTEXT_SHADER_SOURCCE;
    }
    else if(f_filename.find("chamaeleonVisuColorMapping.frag") != std::string::npos)
    {
        f_shaderSource += pc::core::s_chamaeleonVisuColorMapping_frag;
        l_return = FRAGMENT_SHADER_SOURCCE;
    }
    else if(f_filename.find("chamaeleonVisuColorMapping.vert") != std::string::npos)
    {
        f_shaderSource += pc::core::s_chamaeleonVisuColorMapping_vert;
        l_return = VERTEXT_SHADER_SOURCCE;
    }
    else if(f_filename.find("colorText.frag") != std::string::npos)
    {
        f_shaderSource += pc::core::s_colorText_frag;
        l_return = FRAGMENT_SHADER_SOURCCE;
    }
    else if(f_filename.find("colorText.vert") != std::string::npos)
    {
        f_shaderSource += pc::core::s_colorText_vert;
        l_return = VERTEXT_SHADER_SOURCCE;
    }
    else if(f_filename.find("historyToPlate.frag") != std::string::npos)
    {
        f_shaderSource += pc::core::s_historyToPlate_frag;
        l_return = FRAGMENT_SHADER_SOURCCE;
    }
    else if(f_filename.find("historyToPlate.vert") != std::string::npos)
    {
        f_shaderSource += pc::core::s_historyToPlate_vert;
        l_return = VERTEXT_SHADER_SOURCCE;
    }
    else if(f_filename.find("iconAnimationAugmentedWave.frag") != std::string::npos)
    {
        f_shaderSource += pc::core::s_iconAnimationAugmentedWave_frag;
        l_return = FRAGMENT_SHADER_SOURCCE;
    }
    else if(f_filename.find("iconAnimationAugmentedWave.vert") != std::string::npos)
    {
        f_shaderSource += pc::core::s_iconAnimationAugmentedWave_vert;
        l_return = VERTEXT_SHADER_SOURCCE;
    }
    else if(f_filename.find("iconAnimationFadeInFadeOut.frag") != std::string::npos)
    {
        f_shaderSource += pc::core::s_iconAnimationFadeInFadeOut_frag;
        l_return = FRAGMENT_SHADER_SOURCCE;
    }
    else if(f_filename.find("iconAnimationFadeInFadeOut.vert") != std::string::npos)
    {
        f_shaderSource += pc::core::s_iconAnimationFadeInFadeOut_vert;
        l_return = VERTEXT_SHADER_SOURCCE;
    }
    else if(f_filename.find("infoPanel.frag") != std::string::npos)
    {
        f_shaderSource += pc::core::s_infoPanel_frag;
        l_return = FRAGMENT_SHADER_SOURCCE;
    }
    else if(f_filename.find("infoPanel.vert") != std::string::npos)
    {
        f_shaderSource += pc::core::s_infoPanel_vert;
        l_return = VERTEXT_SHADER_SOURCCE;
    }
    else if(f_filename.find("junctionIcon.frag") != std::string::npos)
    {
        f_shaderSource += pc::core::s_junctionIcon_frag;
        l_return = FRAGMENT_SHADER_SOURCCE;
    }
    else if(f_filename.find("junctionIcon.vert") != std::string::npos)
    {
        f_shaderSource += pc::core::s_junctionIcon_vert;
        l_return = VERTEXT_SHADER_SOURCCE;
    }
    else if(f_filename.find("noCam.frag") != std::string::npos)
    {
        f_shaderSource += pc::core::s_noCam_frag;
        l_return = FRAGMENT_SHADER_SOURCCE;
    }
    else if(f_filename.find("noCam.vert") != std::string::npos)
    {
        f_shaderSource += pc::core::s_noCam_vert;
        l_return = VERTEXT_SHADER_SOURCCE;
    }
    else if(f_filename.find("parkingspotTex.frag") != std::string::npos)
    {
        f_shaderSource += pc::core::s_parkingspotTex_frag;
        l_return = FRAGMENT_SHADER_SOURCCE;
    }
    else if(f_filename.find("parkingspotTex.vert") != std::string::npos)
    {
        f_shaderSource += pc::core::s_parkingspotTex_vert;
        l_return = VERTEXT_SHADER_SOURCCE;
    }
    else if(f_filename.find("points.frag") != std::string::npos)
    {
        f_shaderSource += pc::core::s_points_frag;
        l_return = FRAGMENT_SHADER_SOURCCE;
    }
    else if(f_filename.find("points.vert") != std::string::npos)
    {
        f_shaderSource += pc::core::s_points_vert;
        l_return = VERTEXT_SHADER_SOURCCE;
    }
    else if(f_filename.find("rawTex.frag") != std::string::npos)
    {
        f_shaderSource += pc::core::s_rawTex_frag;
        l_return = FRAGMENT_SHADER_SOURCCE;
    }
    else if(f_filename.find("rawTex.vert") != std::string::npos)
    {
        f_shaderSource += pc::core::s_rawTex_vert;
        l_return = VERTEXT_SHADER_SOURCCE;
    }
    else if(f_filename.find("rawTex_pxlDensity.frag") != std::string::npos)
    {
        f_shaderSource += pc::core::s_rawTex_pxlDensity_frag;
        l_return = FRAGMENT_SHADER_SOURCCE;
    }
    else if(f_filename.find("renderUV.frag") != std::string::npos)
    {
        f_shaderSource += pc::core::s_renderUV_frag;
        l_return = FRAGMENT_SHADER_SOURCCE;
    }
    else if(f_filename.find("renderUV.vert") != std::string::npos)
    {
        f_shaderSource += pc::core::s_renderUV_vert;
        l_return = VERTEXT_SHADER_SOURCCE;
    }
    else if(f_filename.find("separableGaussian.frag") != std::string::npos)
    {
        f_shaderSource += pc::core::s_separableGaussian_frag;
        l_return = FRAGMENT_SHADER_SOURCCE;
    }
    else if(f_filename.find("separableGaussian.vert") != std::string::npos)
    {
        f_shaderSource += pc::core::s_separableGaussian_vert;
        l_return = VERTEXT_SHADER_SOURCCE;
    }
    else if(f_filename.find("snapShot.frag") != std::string::npos)
    {
        f_shaderSource += pc::core::s_snapShot_frag;
        l_return = FRAGMENT_SHADER_SOURCCE;
    }
    else if(f_filename.find("snapShot.vert") != std::string::npos)
    {
        f_shaderSource += pc::core::s_snapShot_vert;
        l_return = VERTEXT_SHADER_SOURCCE;
    }
    else if(f_filename.find("SnapshotToHistory.frag") != std::string::npos)
    {
        f_shaderSource += pc::core::s_SnapshotToHistory_frag;
        l_return = FRAGMENT_SHADER_SOURCCE;
    }
    else if(f_filename.find("SnapshotToHistory.vert") != std::string::npos)
    {
        f_shaderSource += pc::core::s_SnapshotToHistory_vert;
        l_return = VERTEXT_SHADER_SOURCCE;
    }
    else if(f_filename.find("stripedBasePlate.frag") != std::string::npos)
    {
        f_shaderSource += pc::core::s_stripedBasePlate_frag;
        l_return = FRAGMENT_SHADER_SOURCCE;
    }
    else if(f_filename.find("stripedBasePlate.vert") != std::string::npos)
    {
        f_shaderSource += pc::core::s_stripedBasePlate_vert;
        l_return = VERTEXT_SHADER_SOURCCE;
    }
    else if(f_filename.find("texfloorHistory.frag") != std::string::npos)
    {
        f_shaderSource += pc::core::s_texfloorHistory_frag;
        l_return = FRAGMENT_SHADER_SOURCCE;
    }
    else if(f_filename.find("texfloorHistory.vert") != std::string::npos)
    {
        f_shaderSource += pc::core::s_texfloorHistory_vert;
        l_return = VERTEXT_SHADER_SOURCCE;
    }
    else if(f_filename.find("tnf.frag") != std::string::npos)
    {
        f_shaderSource += pc::core::s_tnf_frag;
        l_return = FRAGMENT_SHADER_SOURCCE;
    }
    else if(f_filename.find("tnf.vert") != std::string::npos)
    {
        f_shaderSource += pc::core::s_tnf_vert;
        l_return = VERTEXT_SHADER_SOURCCE;
    }
    else if(f_filename.find("wipingTex.frag") != std::string::npos)
    {
        f_shaderSource += pc::core::s_wipingTex_frag;
        l_return = FRAGMENT_SHADER_SOURCCE;
    }
    else if(f_filename.find("wipingTex.vert") != std::string::npos)
    {
        f_shaderSource += pc::core::s_wipingTex_vert;
        l_return = VERTEXT_SHADER_SOURCCE;
    }
    else if (f_filename.find("floorplateApplication.frag") != std::string::npos)
    {
        f_shaderSource += pc::core::s_floorplateApplication_frag;
        l_return = FRAGMENT_SHADER_SOURCCE;
    }

    else if(f_filename.find("e3Tex.frag") != std::string::npos)
    {
        f_shaderSource += pc::core::s_e3Tex_frag;
        l_return = FRAGMENT_SHADER_SOURCCE;
    }
    else if(f_filename.find("e3Tex.vert") != std::string::npos)
    {
        f_shaderSource += pc::core::s_e3Tex_vert;
        l_return = VERTEXT_SHADER_SOURCCE;
    }
    else if(f_filename.find("e3TopviewArrow.frag") != std::string::npos)
    {
        f_shaderSource += pc::core::s_e3TopviewArrow_frag;
        l_return = FRAGMENT_SHADER_SOURCCE;
    }
    else if(f_filename.find("e3TopviewArrow.vert") != std::string::npos)
    {
        f_shaderSource += pc::core::s_e3TopviewArrow_vert;
        l_return = VERTEXT_SHADER_SOURCCE;
    }
    else if(f_filename.find("e3TopviewTrajectoryTex.frag") != std::string::npos)
    {
        f_shaderSource += pc::core::s_e3TopviewTrajectoryTex_frag;
        l_return = FRAGMENT_SHADER_SOURCCE;
    }
    else if(f_filename.find("e3TopviewTrajectoryTex.vert") != std::string::npos)
    {
        f_shaderSource += pc::core::s_e3TopviewTrajectoryTex_vert;
        l_return = VERTEXT_SHADER_SOURCCE;
    }
    else if (f_filename.find("chamaeleonEstimator.frag") != std::string::npos)
    {
        f_shaderSource += pc::core::s_chamaeleonEstimator_frag;
        l_return = FRAGMENT_SHADER_SOURCCE;
    }
    else if (f_filename.find("chamaeleonEstimator.vert") != std::string::npos)
    {
        f_shaderSource += pc::core::s_chamaeleonEstimator_vert;
        l_return = VERTEXT_SHADER_SOURCCE;
    }
    else if (f_filename.find("chamaeleonRoiFilter.frag") != std::string::npos)
    {
        f_shaderSource += pc::core::s_chamaeleonRoiFilter_frag ;
        l_return = FRAGMENT_SHADER_SOURCCE;
    }
    else if (f_filename.find("chamaeleonRoiFilter.vert") != std::string::npos)
    {
        f_shaderSource += pc::core::s_chamaeleonRoiFilter_vert;
        l_return = VERTEXT_SHADER_SOURCCE;
    }
    else
    {
        XLOG_ERROR(pc::util::logging::g_EngineContext, "Failed to porting shader" << f_filename);
        l_return = NON_DETECT_SHADER_SOURCCE;
    }

    if (l_return)
    {
        XLOG_ERROR(pc::util::logging::g_EngineContext, "porting shader:" << f_filename);
    }

    return l_return;
}

} // namespace core
} // namespace pc
