//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include "cc/assets/trajectory/inc/TrailerHitchTrajectory.h"
#include "cc/assets/trajectory/inc/GeneralTrajectoryLine.h"
#include "cc/assets/trajectory/inc/Helper.h"
#include "cc/core/inc/CustomFramework.h"
// #include "cc/assets/overlaycallback/inc/OverlayCallback.h"
#include "cc/core/inc/CustomScene.h"
#include "pc/svs/core/inc/ShaderManager.h"
#include "pc/svs/util/math/inc/Intersection.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"
#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h"
#include "pc/svs/util/osgx/inc/MemberWrapper.h"
#include "pc/svs/util/math/inc/CommonMath.h"
#include <osg/Texture2D>
#include <osg/Depth>

using pc::util::logging::g_AppContext;

/// @deviation NRCS2_071
/// Rule QACPP-4.3.0-3804
/// message: "The return value of this function call is not used."
namespace cc
{
namespace assets
{
namespace trajectory
{


//!
//! @brief Construct a new Trailer Hitch Trajectory:: Trailer Hitch Trajectory object
//!
//! @param f_trajParams
//! @param f_numOfVerts
//! @param f_framework
//!
TrailerHitchTrajectory::TrailerHitchTrajectory(
  const cc::assets::trajectory::TrajectoryParams_st& f_trajParams,
  vfc::uint32_t f_numOfVerts,
  pc::core::Framework* f_framework)
  : GeneralTrajectoryLine(
      f_framework,
      cc::assets::trajectory::commontypes::Middle_enm, // Dummy, not used.
      4u,
      0.0f, // Dummy height, it is overwritten in the constructor body.
      f_trajParams,
      true)
  , mc_numOfVerts(f_numOfVerts)
  , m_framework(f_framework)
  , m_lastCalibUpdate{}
{
  m_height = sm_mainLogicRefPtr->getInputDataRef().External.Car.THBallPos.z();

  m_geometry->addPrimitiveSet(new osg::DrawElementsUShort(static_cast<GLenum>(osg::PrimitiveSet::TRIANGLES)));  // PRQA S 3804

  osg::Vec4Array* const l_colors = static_cast<osg::Vec4Array*> (m_geometry->getColorArray());
  l_colors->setBinding(osg::Array::BIND_PER_VERTEX);
}


TrailerHitchTrajectory::~TrailerHitchTrajectory() = default;

void TrailerHitchTrajectory::generateVertexData()
{
  // *** 1. Create frame ***
  m_frame.removeAllPoints();

  if (cc::assets::trajectory::commontypes::Rotation_enm == sm_mainLogicRefPtr->getInputDataRef().Internal.VehicleMovementType)
  {
    // When it's rotation
    m_frameRadiuses[0u] = sm_mainLogicRefPtr->getModelDataRef().THBallCenter.Radius + m_trajParams.THTraj_Width * sm_mainLogicRefPtr->getModelDataRef().ToLeftOffsetMulHitch;
    m_frameRadiuses[1u] = m_frameRadiuses[0u] + m_trajParams.GradientWidth * sm_mainLogicRefPtr->getModelDataRef().ToRightOffsetMulHitch;
    m_frameRadiuses[3u] = sm_mainLogicRefPtr->getModelDataRef().THBallCenter.Radius + m_trajParams.THTraj_Width * sm_mainLogicRefPtr->getModelDataRef().ToRightOffsetMulHitch;
    m_frameRadiuses[2u] = m_frameRadiuses[3u] + m_trajParams.GradientWidth * sm_mainLogicRefPtr->getModelDataRef().ToLeftOffsetMulHitch;

    m_frame.setVertexLineRadius(0u, m_frameRadiuses[0u]);
    m_frame.setVertexLineRadius(1u, m_frameRadiuses[1u]);
    m_frame.setVertexLineRadius(2u, m_frameRadiuses[2u]);
    m_frame.setVertexLineRadius(3u, m_frameRadiuses[3u]);
  }
  else
  {
    // When it's translation
    m_frameLateralOffsets[0u] = sm_mainLogicRefPtr->getModelDataRef().THBallCenter.Pos.y() + m_trajParams.THTraj_Width * sm_mainLogicRefPtr->getModelDataRef().ToLeftOffsetMulHitch;
    m_frameLateralOffsets[1u] = m_frameLateralOffsets[0u] + m_trajParams.GradientWidth * sm_mainLogicRefPtr->getModelDataRef().ToRightOffsetMulHitch;
    m_frameLateralOffsets[3u] = sm_mainLogicRefPtr->getModelDataRef().THBallCenter.Pos.y() + m_trajParams.THTraj_Width * sm_mainLogicRefPtr->getModelDataRef().ToRightOffsetMulHitch;
    m_frameLateralOffsets[2u] = m_frameLateralOffsets[3u] + m_trajParams.GradientWidth * sm_mainLogicRefPtr->getModelDataRef().ToLeftOffsetMulHitch;

    m_frame.setVertexLineOffset(0u, m_frameLateralOffsets[0u]);
    m_frame.setVertexLineOffset(1u, m_frameLateralOffsets[1u]);
    m_frame.setVertexLineOffset(2u, m_frameLateralOffsets[2u]);
    m_frame.setVertexLineOffset(3u, m_frameLateralOffsets[3u]);
  }

  osg::Vec4f l_innerColor = m_trajParams.THTraj_Color;
  if (sm_mainLogicRefPtr->getInputDataRef().External.Parking.AutomaticParking)
  {
    l_innerColor = m_trajParams.OutermostLine_Color_Auto;
  }
  osg::Vec4f l_outerColor = l_innerColor;
  l_outerColor.a() = 0.0f;
  cc::assets::trajectory::commontypes::ControlPoint_st l_controlPoint;
  l_controlPoint.Angle = sm_mainLogicRefPtr->getModelDataRef().THBallCenter.Angle;
  l_controlPoint.LongitudinalPos = sm_mainLogicRefPtr->getModelDataRef().THBallCenter.Pos.x();
  l_controlPoint.Color = l_outerColor;
  l_controlPoint.Index = 0u; // Dummy value. Will be calculated later.
  m_frame.addControlPoint(0u, l_controlPoint);
  l_controlPoint.Color = l_innerColor;
  m_frame.addControlPoint(1u, l_controlPoint);
  m_frame.addControlPoint(2u, l_controlPoint);
  l_controlPoint.Color = l_outerColor;
  m_frame.addControlPoint(3u, l_controlPoint);

  l_controlPoint.Angle =
      sm_mainLogicRefPtr->getModelDataRef().THBallCenter.Angle
      + sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul
        * (-1.0f)
        * (m_trajParams.THTraj_Length / sm_mainLogicRefPtr->getModelDataRef().THBallCenter.Radius);
  l_controlPoint.LongitudinalPos =
      sm_mainLogicRefPtr->getModelDataRef().THBallCenter.Pos.x()
      + sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul
        * (-1.0f)
        * m_trajParams.THTraj_Length;
  l_controlPoint.Color = l_outerColor;
  m_frame.addControlPoint(0u, l_controlPoint);
  l_controlPoint.Color = l_innerColor;
  m_frame.addControlPoint(1u, l_controlPoint);
  m_frame.addControlPoint(2u, l_controlPoint);
  l_controlPoint.Color = l_outerColor;
  m_frame.addControlPoint(3u, l_controlPoint);

  //setting the fade in (so that the hitch trajectory does not touch the trailer ball)
  m_frame.setFadeIn(0u, true);
  m_frame.setFadeIn(1u, true);
  m_frame.setFadeIn(2u, true);
  m_frame.setFadeIn(3u, true);

  m_frame.setFadeOut(0u, false);
  m_frame.setFadeOut(1u, false);
  m_frame.setFadeOut(2u, false);
  m_frame.setFadeOut(3u, false);

  //set fade in length (straight trajectory)
  constexpr vfc::float32_t l_gradLength      = 0.382f;
  //set start point for fade in (straight trajectory) - trailer ball center
  const vfc::float32_t l_startPoint = sm_mainLogicRefPtr->getModelDataRef().THBallCenter.Pos.x();
  //set fade in length (curve)
  const vfc::float32_t l_gradLengthCurve = l_gradLength / sm_mainLogicRefPtr->getModelDataRef().THBallCenter.Radius;
  //start angle for fade in (curve)
  const vfc::float32_t l_startAngle      = sm_mainLogicRefPtr->getModelDataRef().THBallCenter.Angle;
  //take into account curve length and steering angle sign (curve)
  const vfc::float32_t l_delta           = -l_gradLengthCurve * sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul;

  m_frame.setFadeInStartAngle(0u, l_startAngle + l_delta);
  m_frame.setFadeInStartAngle(1u, l_startAngle + l_delta);
  m_frame.setFadeInStartAngle(2u, l_startAngle + l_delta);
  m_frame.setFadeInStartAngle(3u, l_startAngle + l_delta);

  m_frame.setFadeInEndAngle(0u, l_startAngle);
  m_frame.setFadeInEndAngle(1u, l_startAngle);
  m_frame.setFadeInEndAngle(2u, l_startAngle);
  m_frame.setFadeInEndAngle(3u, l_startAngle);

  m_frame.setFadeInStartPos(0u, l_startPoint - l_gradLength);
  m_frame.setFadeInStartPos(1u, l_startPoint - l_gradLength);
  m_frame.setFadeInStartPos(2u, l_startPoint - l_gradLength);
  m_frame.setFadeInStartPos(3u, l_startPoint - l_gradLength);

  m_frame.setFadeInEndPos(0u, l_startPoint);
  m_frame.setFadeInEndPos(1u, l_startPoint);
  m_frame.setFadeInEndPos(2u, l_startPoint);
  m_frame.setFadeInEndPos(3u, l_startPoint);

#if 0
  m_frame.setFadeOutStartAngle(0u, l_controlPoint.Angle);
  m_frame.setFadeOutStartAngle(1u, l_controlPoint.Angle);
  m_frame.setFadeOutStartAngle(2u, l_controlPoint.Angle);
  m_frame.setFadeOutStartAngle(3u, l_controlPoint.Angle);

  m_frame.setFadeOutStartPos(0u, l_controlPoint.LongitudinalPos);
  m_frame.setFadeOutStartPos(1u, l_controlPoint.LongitudinalPos);
  m_frame.setFadeOutStartPos(2u, l_controlPoint.LongitudinalPos);
  m_frame.setFadeOutStartPos(3u, l_controlPoint.LongitudinalPos);

  m_frame.setFadeOutEndAngle(0u, l_controlPoint.Angle);
  m_frame.setFadeOutEndAngle(1u, l_controlPoint.Angle);
  m_frame.setFadeOutEndAngle(2u, l_controlPoint.Angle);
  m_frame.setFadeOutEndAngle(3u, l_controlPoint.Angle);

  m_frame.setFadeOutEndPos(0u, l_controlPoint.LongitudinalPos);
  m_frame.setFadeOutEndPos(1u, l_controlPoint.LongitudinalPos);
  m_frame.setFadeOutEndPos(2u, l_controlPoint.LongitudinalPos);
  m_frame.setFadeOutEndPos(3u, l_controlPoint.LongitudinalPos);
#endif


  // *** 2. Create vertices (and colors) ***
  m_height = sm_mainLogicRefPtr->getInputDataRef().External.Car.THBallPos.z();

  // Generate line vertices
  osg::Vec3Array* const l_vertices = static_cast<osg::Vec3Array*> (m_geometry->getVertexArray());
  l_vertices->clear();

  osg::Vec4Array* const l_colors = static_cast<osg::Vec4Array*> (m_geometry->getColorArray());
  l_colors->clear();

  const osg::ref_ptr<osg::Vec3Array> l_verticesForProjection = new osg::Vec3Array;
  const vfc::float32_t l_translationAngle_Rad = osg::DegreesToRadians(sm_mainLogicRefPtr->getInputDataRef().Internal.TranslationAngle);
  for (vfc::uint32_t l_vertexLineIndex = 0u; l_vertexLineIndex < 4u; l_vertexLineIndex++)
  {
    m_frame.generateVertices(
        l_vertexLineIndex, 0u, 1u, sm_mainLogicRefPtr->getInputDataRef().Internal.AckermannPoint, m_height,
        l_verticesForProjection, l_colors, cc::assets::trajectory::frame::Manual_enm,
        mc_numOfVerts, sm_mainLogicRefPtr->getInputDataRef().Internal.VehicleMovementType,
        l_translationAngle_Rad);
  }

  // *** 3. Create indices ***
  osg::DrawElementsUShort* const l_indices = static_cast<osg::DrawElementsUShort*> (m_geometry->getPrimitiveSet(0u));
  l_indices->clear();

  constexpr vfc::uint32_t l_leftVertexLineIndexInc = 0u;
  constexpr vfc::uint32_t l_rightVertexLineIndexInc = 1u;

  for (vfc::uint32_t l_vertexLineIndex = 0u; l_vertexLineIndex < 3u; l_vertexLineIndex++)
  {
    m_frame.generateIndices(
        l_vertexLineIndex + l_leftVertexLineIndexInc, 0u,
        l_vertexLineIndex + l_rightVertexLineIndexInc, 0u,
        mc_numOfVerts, l_indices);
  }

  const vfc::uint32_t l_numVertices = static_cast<vfc::uint32_t> (l_verticesForProjection->size());
  for (vfc::uint32_t i = 0u; i < l_numVertices; ++i)
  {
    const osg::Vec3f& l_vertex = (*l_verticesForProjection)[i];
    if (true == m_framework->m_cameraCalibrationReceiver.isConnected())
    {
      const pc::daddy::CameraCalibrationDaddy* const l_pCalibDaddy =
          m_framework->m_cameraCalibrationReceiver.getData();
      if (l_pCalibDaddy != nullptr)
      {
        const pc::c2w::SatCam& l_satCam = l_pCalibDaddy->m_Data[pc::core::sysconf::REAR_CAMERA];
        pc::util::Ray l_ray(l_satCam.getExtrinsicCalibration().getTranslation(),
                            l_vertex - l_satCam.getExtrinsicCalibration().getTranslation());
        l_ray.m_direction.normalize();  // PRQA S 3804

        osg::Vec3f l_projectedVertex;
        if (true == pc::util::planeIntersection(l_ray, osg::Vec3f(0.0f, 0.0f, 0.001f), osg::Z_AXIS, l_projectedVertex))
        {
          l_vertices->push_back(l_projectedVertex);
        }
      }
    }
  }

  l_vertices->dirty();
  l_colors->dirty();
  l_indices->dirty();
  m_geometry->dirtyBound();

  setCull(false);
}


void TrailerHitchTrajectory::traverse(osg::NodeVisitor& f_nv)
{
  if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
  {
    const pc::daddy::CameraCalibrationDaddy* const l_calibration = m_framework->m_cameraCalibrationReceiver.getData();
    if (l_calibration != nullptr)
    {
      if ((l_calibration->m_sequenceNumber != m_lastCalibUpdate))
      {
        m_lastCalibUpdate = l_calibration->m_sequenceNumber;
        generateVertexData();
        osg::Geode::traverse(f_nv);
      }
    }
  }
  else
  {
    osg::Geode::traverse(f_nv);
  }
}
//!
//! @brief TrailerHitchCircleSettings
//!
//!
class TrailerHitchCircleSettings : public pc::util::coding::ISerializable
{
public:

  TrailerHitchCircleSettings()
    : m_width(0.03f)
    , m_gradientWidth(0.007f)
    , m_radius(0.25f)
    , m_numSegments(40u)
    , m_color(osg::Vec4f(1.0f, 0.0f, 0.0f, 0.9f))
  {
  }

  SERIALIZABLE(TrailerHitchCircleSettings)
  {
    ADD_FLOAT_MEMBER(width);
    ADD_FLOAT_MEMBER(gradientWidth);
    ADD_FLOAT_MEMBER(radius);
    ADD_UINT32_MEMBER(numSegments);
    ADD_MEMBER(osg::Vec4f, color);
  }

  vfc::float32_t m_width;
  vfc::float32_t m_gradientWidth;
  vfc::float32_t m_radius;
  vfc::uint32_t m_numSegments;
  osg::Vec4f m_color;
};

static pc::util::coding::Item<TrailerHitchCircleSettings> g_circleSettings("TrailerHitchCircle");


//!
//! @brief Construct a new Trailer Hitch Circle:: Trailer Hitch Circle object
//!
//! @param f_framework
//!
TrailerHitchCircle::TrailerHitchCircle(pc::core::Framework* f_framework)
  : m_circleGeode{} // PRQA S 4052
  , m_framework{f_framework}
  , m_lastCalibUpdate{}
{
  setNumChildrenRequiringUpdateTraversal(1u);
  setName("TrailerHitchCircle");
  m_circleGeode = new osg::Geode();
  m_circleGeode->setName("CircleGeode");
  osg::Group::addChild(m_circleGeode);  // PRQA S 3804
  osg::Geometry* const l_circleGeometry = new osg::Geometry();
  l_circleGeometry->setName("CircleGeometry");
  l_circleGeometry->setUseDisplayList(false);
  l_circleGeometry->setUseVertexBufferObjects(true);
  l_circleGeometry->setVertexArray(new osg::Vec3Array);
  osg::Vec4Array* const l_colors = new osg::Vec4Array(1u);
  (*l_colors)[0u] = osg::Vec4f(1.0f, 0.0f, 0.0f, 1.0f);
  l_circleGeometry->setColorArray(l_colors, osg::Array::BIND_OVERALL);
  l_circleGeometry->setTexCoordArray(0u, new osg::Vec2Array);
  l_circleGeometry->addPrimitiveSet(new osg::DrawElementsUShort(static_cast<GLenum>(osg::PrimitiveSet::TRIANGLES)));  // PRQA S 3804
  m_circleGeode->addDrawable(l_circleGeometry);

  loadTexture(l_circleGeometry);
  applyTexShaderToGeometry(l_circleGeometry, RENDERBIN_ORDER_TRAJECTORY_TRAILER_HITCH_CIRCLE, false, false, true);
//   addCullCallback(new overlaycallback::OverlayCallback{m_framework, overlaycallback::TRAILER_CIRCLE});
}


TrailerHitchCircle::~TrailerHitchCircle() = default;


osg::Image* TrailerHitchCircle::create1DTexture()
{
  constexpr vfc::uint32_t  lc_imageWidth  = 64u;  // Image width in pixels.
  constexpr vfc::uint32_t  lc_imageHeight = 1u;   // Image height in pixels.
  constexpr vfc::uint32_t  lc_imageDepth  = 1u;   // Image depth in pixels, in case of a 3D image.
  constexpr vfc::float32_t         lc_imageGeometryWidth = static_cast<vfc::float32_t>(lc_imageWidth - 1u);

  // This multiplier is to widen the quad stripe to have enough room for the blur on the downsampled mipmaps.
  constexpr vfc::float32_t lc_blurMul = 1.0f; // (1 <= )

  const vfc::float32_t lc_halfGradientWidth = std::abs(g_circleSettings->m_gradientWidth) * 0.5f;
  const vfc::float32_t lc_halfWholeWidth = g_circleSettings->m_width * 0.5f; // Half of the width of the line including the borders.
  std::array<vfc::float32_t, 2> l_absDistancesFromCenter; // 0..1: From outermost to innermost
  std::array<vfc::float32_t, 4> l_normalizedPositions;    // 0..3: From left to right

  l_absDistancesFromCenter[0u] = lc_halfWholeWidth + lc_halfGradientWidth;
  l_absDistancesFromCenter[1u] = lc_halfWholeWidth - lc_halfGradientWidth;

  const vfc::float32_t lc_halfGeometryWidth = l_absDistancesFromCenter[0u] * lc_blurMul;

  const vfc::float32_t l_geometryWidth = lc_halfGeometryWidth * 2.0f;

  l_normalizedPositions[0u] = (lc_halfGeometryWidth - l_absDistancesFromCenter[0u]) / l_geometryWidth;
  l_normalizedPositions[1u] = (lc_halfGeometryWidth - l_absDistancesFromCenter[1u]) / l_geometryWidth;
  l_normalizedPositions[2u] = 1.0f - l_normalizedPositions[1u];
  l_normalizedPositions[3u] = 1.0f - l_normalizedPositions[0u];

  osg::Vec4ub l_lineColor_Inside;
  l_lineColor_Inside.r() = pc::util::round2uInt(g_circleSettings->m_color.r() * 255.0f); // PRQA S 3010
  l_lineColor_Inside.g() = pc::util::round2uInt(g_circleSettings->m_color.g() * 255.0f); // PRQA S 3010
  l_lineColor_Inside.b() = pc::util::round2uInt(g_circleSettings->m_color.b() * 255.0f); // PRQA S 3010
  l_lineColor_Inside.a() = pc::util::round2uInt(g_circleSettings->m_color.a() * 255.0f); // PRQA S 3010

  osg::Vec4ub l_lineColor_Outside = l_lineColor_Inside;
  l_lineColor_Outside.a() = 0u;

  osg::Image* const l_image = new osg::Image;
  l_image->allocateImage(lc_imageWidth, lc_imageHeight, lc_imageDepth, GL_RGBA, GL_UNSIGNED_BYTE);
  osg::Vec4ub* l_data = reinterpret_cast<osg::Vec4ub*> (l_image->data());
  for (vfc::uint32_t x = 0u; x < lc_imageWidth; ++x)
  {
    const vfc::float32_t l_x_normalized = static_cast<vfc::float32_t>(x) / lc_imageGeometryWidth;

    if (l_x_normalized < l_normalizedPositions[0u])
    {
      // Left outside
      (*l_data) = l_lineColor_Outside;
    }
    else if (l_x_normalized < l_normalizedPositions[1u])
    {
      // Left gradient
      (*l_data) = cc::assets::trajectory::helper::TrajectoryHelper::smoothstep_Vec4ub( // PRQA S 2759
          l_lineColor_Outside, l_lineColor_Inside, l_normalizedPositions[0u], l_normalizedPositions[1u], l_x_normalized);
    }
    else if (l_x_normalized < l_normalizedPositions[2u])
    {
      // Middle
      (*l_data) = l_lineColor_Inside;
    }
    else if (l_x_normalized < l_normalizedPositions[3u])
    {
      // Right gradient
      (*l_data) = cc::assets::trajectory::helper::TrajectoryHelper::smoothstep_Vec4ub( // PRQA S 2759
          l_lineColor_Inside, l_lineColor_Outside, l_normalizedPositions[2u], l_normalizedPositions[3u], l_x_normalized);
    }
    else
    {
      // Right outside
      (*l_data) = l_lineColor_Outside;
    }
    ++l_data;
  }
  return l_image;
}


void TrailerHitchCircle::createCircleGeometry()
{
  const vfc::uint32_t l_numSegments = (1u < g_circleSettings->m_numSegments) ?
     g_circleSettings->m_numSegments : 2u;

  osg::Geometry* const l_circleGeometry = m_circleGeode->getDrawable(0u)->asGeometry();
  osg::Vec3Array* const l_vertices = static_cast<osg::Vec3Array*> (l_circleGeometry->getVertexArray());
  l_vertices->resize(2u*l_numSegments);

  osg::Vec2Array* const l_texCoords = static_cast<osg::Vec2Array*> (l_circleGeometry->getTexCoordArray(0u));
  l_texCoords->resize(2u*l_numSegments);
  osg::DrawElementsUShort* const l_indices = static_cast<osg::DrawElementsUShort*> (l_circleGeometry->getPrimitiveSet(0u));

  const vfc::uint32_t l_numIndices = (2u * l_numSegments - 1u) * 6u;
  if (l_indices->size() != l_numIndices)
  {
    l_indices->resize(l_numIndices);
    vfc::uint32_t l_indexCounter = 0u;
    for (vfc::uint32_t l_quadIndex = 0u; l_quadIndex < (l_numSegments - 1u); ++l_quadIndex)
    {
      typedef osg::DrawElementsUShort::value_type UShort;
      const vfc::uint32_t l_vertexIndexTemp = l_quadIndex * 2u;
      // Top left triangle
      (*l_indices)[l_indexCounter]      = static_cast<UShort> (l_vertexIndexTemp + 1u);
      (*l_indices)[l_indexCounter + 1u] = static_cast<UShort> (l_vertexIndexTemp + 0u);
      (*l_indices)[l_indexCounter + 2u] = static_cast<UShort> (l_vertexIndexTemp + 2u);
      // Bottom right triangle
      (*l_indices)[l_indexCounter + 3u] = static_cast<UShort> (l_vertexIndexTemp + 2u);
      (*l_indices)[l_indexCounter + 4u] = static_cast<UShort> (l_vertexIndexTemp + 3u);
      (*l_indices)[l_indexCounter + 5u] = static_cast<UShort> (l_vertexIndexTemp + 1u);
      l_indexCounter += 6u;
    }
    l_indices->dirty();
  }



  //draw half circle
  std::vector<osg::Vec3f> l_vertexArrayForProjection(l_numSegments);
  std::vector<osg::Vec2f> l_vertexArray(l_numSegments);

  const vfc::float32_t l_angleDelta = static_cast<vfc::float32_t> (osg::PI) / (static_cast<vfc::float32_t> (l_numSegments) - 1.0f);
  for (vfc::uint32_t i = 0u; i < l_numSegments; ++i)
  {
    l_vertexArrayForProjection[i].x() = pc::vehicle::g_mechanicalData->m_trailerBallPosition.x() - g_circleSettings->m_radius * std::sin(i*l_angleDelta);
    l_vertexArrayForProjection[i].y() = pc::vehicle::g_mechanicalData->m_trailerBallPosition.y() + g_circleSettings->m_radius * std::cos(i*l_angleDelta);
    l_vertexArrayForProjection[i].z() = pc::vehicle::g_mechanicalData->m_trailerBallPosition.z();
  }

  for (vfc::uint32_t i = 0u; i < l_numSegments; ++i)
  {
    const osg::Vec3f& l_vertex = l_vertexArrayForProjection[i];
    if (true == m_framework->m_cameraCalibrationReceiver.isConnected())
    {
      const pc::daddy::CameraCalibrationDaddy* const l_pCalibDaddy = m_framework->m_cameraCalibrationReceiver.getData();
      if (l_pCalibDaddy != nullptr)
      {
        const pc::c2w::SatCam& l_satCam = l_pCalibDaddy->m_Data[pc::core::sysconf::REAR_CAMERA];
        pc::util::Ray l_ray(l_satCam.getExtrinsicCalibration().getTranslation(),
                            l_vertex - l_satCam.getExtrinsicCalibration().getTranslation());
        l_ray.m_direction.normalize();  // PRQA S 3804
        osg::Vec3f l_projectedVertex;
        if (true == pc::util::planeIntersection(l_ray, osg::Vec3f(0.0f, 0.0f, 0.001f), osg::Z_AXIS, l_projectedVertex))
        {
          l_vertexArray[i].x() = l_projectedVertex.x();
          l_vertexArray[i].y() = l_projectedVertex.y();
        }
      }
    }
  }

  // Right perpendicular vectors
  osg::Vec2f l_segment0_RightPerpVec;
  osg::Vec2f l_segment1_RightPerpVec;
  // Normals
  osg::Vec2f l_leftNormal;
  osg::Vec2f l_rightNormal;
  // Transform to the left or right edge of the line
  osg::Vec2f l_toLineLeftEdge;
  osg::Vec2f l_toLineRightEdge;


  for (vfc::uint32_t l_vertexIndex = 0u; l_vertexIndex < l_numSegments; ++l_vertexIndex)
  {
    if (0u == l_vertexIndex)
    {
      // First spline vertex
      l_segment1_RightPerpVec = l_vertexArray[l_vertexIndex + 1u]
                              - l_vertexArray[l_vertexIndex + 0u];
      l_segment1_RightPerpVec = osg::Vec2f( - l_segment1_RightPerpVec.y(), l_segment1_RightPerpVec.x() );

      l_rightNormal = l_segment1_RightPerpVec;
      l_rightNormal.normalize();  // PRQA S 3804
    }
    else if ((l_numSegments - 1u) == l_vertexIndex)
    {
      // Last spline vertex
      l_segment0_RightPerpVec = l_vertexArray[l_vertexIndex + 0u]
                              - l_vertexArray[l_vertexIndex - 1u];
      l_segment0_RightPerpVec = osg::Vec2f( - l_segment0_RightPerpVec.y(), l_segment0_RightPerpVec.x() );

      l_rightNormal = l_segment0_RightPerpVec;
      l_rightNormal.normalize();  // PRQA S 3804
    }
    else
    {
      l_segment0_RightPerpVec = l_vertexArray[l_vertexIndex + 0u]
                              - l_vertexArray[l_vertexIndex - 1u];
      l_segment0_RightPerpVec = osg::Vec2f( - l_segment0_RightPerpVec.y(), l_segment0_RightPerpVec.x() );

      l_segment1_RightPerpVec = l_vertexArray[l_vertexIndex + 1u]
                              - l_vertexArray[l_vertexIndex + 0u];
      l_segment1_RightPerpVec = osg::Vec2f( - l_segment1_RightPerpVec.y(), l_segment1_RightPerpVec.x() );

      l_rightNormal = l_segment0_RightPerpVec + l_segment1_RightPerpVec;
      l_rightNormal.normalize();  // PRQA S 3804
    }
    l_leftNormal = -l_rightNormal;

    l_toLineLeftEdge  = l_leftNormal  * g_circleSettings->m_width * 0.5f;
    l_toLineRightEdge = l_rightNormal * g_circleSettings->m_width * 0.5f;

    const vfc::uint32_t l_vertexIndexTemp = l_vertexIndex * 2u;


    (*l_vertices)[l_vertexIndexTemp  + 0u].set( osg::Vec3f( l_vertexArray[l_vertexIndex] + l_toLineLeftEdge,  0.001f) );
    (*l_vertices)[l_vertexIndexTemp  + 1u].set( osg::Vec3f( l_vertexArray[l_vertexIndex] + l_toLineRightEdge, 0.001f) );
    (*l_texCoords) [l_vertexIndexTemp  + 0u].set( 0.0f, 0.5f );
    (*l_texCoords) [l_vertexIndexTemp  + 1u].set( 1.0f, 0.5f );
  }

  l_vertices->dirty();
  l_circleGeometry->dirtyBound();
}


void TrailerHitchCircle::loadTexture(osg::Geometry* f_geometry)
{

  osg::Image* const l_texImage = create1DTexture();
  osg::Texture2D* const l_tex2D = new osg::Texture2D(l_texImage);
  l_tex2D->setFilter(osg::Texture::MIN_FILTER, osg::Texture::LINEAR_MIPMAP_LINEAR);
  l_tex2D->setFilter(osg::Texture::MAG_FILTER, osg::Texture::LINEAR);
  l_tex2D->setWrap(osg::Texture::WRAP_S, osg::Texture::CLAMP_TO_EDGE);
  l_tex2D->setWrap(osg::Texture::WRAP_T, osg::Texture::CLAMP_TO_EDGE);
  l_tex2D->setResizeNonPowerOfTwoHint(false);
  l_tex2D->setUnRefImageDataAfterApply(true);

  osg::StateSet* const l_stateSet = f_geometry->getOrCreateStateSet();
  l_stateSet->setTextureAttribute(0u, l_tex2D);
}


void TrailerHitchCircle::applyTexShaderToGeometry(osg::Geometry* f_geometry, vfc::uint32_t f_renderBinOrder, bool f_depthTest, bool f_depthBufferWrite, bool f_blend)
{
  osg::Depth * const l_depthStateAttrib = new osg::Depth(osg::Depth::LESS);
  l_depthStateAttrib->setWriteMask(f_depthBufferWrite);
  const osg::StateAttribute::GLModeValue l_depthTest = f_depthTest ? osg::StateAttribute::ON : osg::StateAttribute::OFF;
  const osg::StateAttribute::GLModeValue l_blend     = f_blend     ? osg::StateAttribute::ON : osg::StateAttribute::OFF;

  osg::StateSet* const l_stateSet = f_geometry->getOrCreateStateSet();
  pc::core::TextureShaderProgramDescriptor l_basicTexShader("basicTex");
  l_basicTexShader.apply(l_stateSet);
  l_stateSet->setMode(GL_BLEND, l_blend);
  l_stateSet->setMode(GL_DEPTH_TEST, l_depthTest);
  l_stateSet->setRenderingHint(osg::StateSet::TRANSPARENT_BIN);
  l_stateSet->setRenderBinDetails(f_renderBinOrder, "RenderBin");
  l_stateSet->setAttributeAndModes(l_depthStateAttrib);
}

void TrailerHitchCircle::traverse(osg::NodeVisitor& f_nv)
{
  if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
  {
    const pc::daddy::CameraCalibrationDaddy* const l_calibration = m_framework->m_cameraCalibrationReceiver.getData();
    if (l_calibration != nullptr)
    {
      if ((l_calibration->m_sequenceNumber != m_lastCalibUpdate))
      {
        m_lastCalibUpdate = l_calibration->m_sequenceNumber;
        createCircleGeometry();
        osg::Group::traverse(f_nv);
      }
    }
  }
  else
  {
    osg::Group::traverse(f_nv);
  }
}


} // namespace trajectory
} // namespace assets
} // namespace cc
