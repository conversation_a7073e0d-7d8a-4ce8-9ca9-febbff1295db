/// @copyright (C) 2023 Robert <PERSON> GmbH.
/// The reproduction, distribution and utilization of this file as well as the
/// communication of its contents to others without express authorization is
/// prohibited. Offenders will be held liable for the payment of damages. All
/// rights reserved in the event of the grant of a patent, utility model or
/// design.
/// @file

#include "pc/svs/imp/chamaeleon/inc/chamaeleon_rois.hpp"
#include "pc/svs/factory/inc/StitchingLinesManager.h"
#include "pc/svs/imp/common/inc/common_tabs.hpp"
#include "pc/svs/util/osgx/inc/Utils.h"

#include "vfc/core/vfc_math.hpp"

namespace rbp
{
namespace vis
{
namespace imp
{
namespace chamaeleon
{

static void setupRoiTexture(osg::Texture2D* f_roiTexture)
{
    f_roiTexture->setTextureSize(
        RTPBO_ROI_WIDTH * static_cast<vfc::int32_t>(ETabImageOverlapROI::NUM_IMAGE_ROIS), RTPBO_ROI_HEIGHT);
    f_roiTexture->setSourceFormat(GL_RGBA);
    f_roiTexture->setDataVariance(osg::Object::DYNAMIC);
    f_roiTexture->setInternalFormat(GL_RGBA);
    f_roiTexture->setSourceType(GL_UNSIGNED_BYTE);
    f_roiTexture->setResizeNonPowerOfTwoHint(false);
    f_roiTexture->setNumMipmapLevels(ROI_DIM_LOD + 1);
    f_roiTexture->setFilter(osg::Texture::MIN_FILTER, osg::Texture::NEAREST_MIPMAP_NEAREST);
    f_roiTexture->setFilter(osg::Texture::MAG_FILTER, osg::Texture::NEAREST);
    f_roiTexture->setWrap(osg::Texture::WRAP_S, osg::Texture::CLAMP_TO_EDGE);
    f_roiTexture->setWrap(osg::Texture::WRAP_T, osg::Texture::CLAMP_TO_EDGE);
    f_roiTexture->setUseHardwareMipMapGeneration(true);
}

ChamaeleonRois::ChamaeleonRois(
    pc::factory::RenderManagerRegistry* f_pRegistry,
    pc::factory::SV3DNode*              f_pFloor,
    const pc::virtcam::VirtualCamera*   f_virtCam)
    : m_chamaeleonRoisCamera{}
    , m_chamaeleonRoisFilteredCamera{}
    , m_useFilterRois{false}
    , m_renderManagerLeft{osg_ext::make_ref<ChamaeleonRoiRenderManager>(f_pRegistry, f_virtCam, !IS_RIGHT)}
    , m_renderManagerRight{osg_ext::make_ref<ChamaeleonRoiRenderManager>(f_pRegistry, f_virtCam, IS_RIGHT)}
{
    //  Configure RoiAsTexture
    m_roisAsTexture = osg_ext::make_ref<osg::Texture2D>();
    setupRoiTexture(m_roisAsTexture);

    constexpr vfc::int32_t l_numSamples{0};
    for (const auto imageOverlap : CImageOverlapIndex::ALL)
    {
        m_chamaeleonRoisCamera[imageOverlap] =
            osg_ext::make_ref<::pc::util::osgx::RenderToTextureCamera>(m_roisAsTexture, l_numSamples);

        const vfc::int32_t l_imageOverlapIndex{imageOverlap.asInteger()};
        m_chamaeleonRoisCamera[imageOverlap].get()->setViewport(
            l_imageOverlapIndex * RTPBO_ROI_WIDTH, 0, RTPBO_ROI_WIDTH, RTPBO_ROI_HEIGHT);
        m_chamaeleonRoisCamera[imageOverlap].get()->setCullMask(
            static_cast<osg::Node::NodeMask>(pc::factory::SV3DNode::SV3DNodeMask::TWO_CAM_NODE));
    }

    for (const auto worldOverlap : CWorldOverlapIndex::ALL)
    {
        m_renderManagerLeft->assignCamera(m_chamaeleonRoisCamera[getOverlapROI1(worldOverlap.asEnum())]);
        m_renderManagerRight->assignCamera(m_chamaeleonRoisCamera[getOverlapROI2(worldOverlap.asEnum())]);
    }

    for (const auto imageOverlap : CImageOverlapIndex::ALL)
    {
        m_chamaeleonRoisCamera[imageOverlap].get()->addChild(f_pFloor);
    }

    //  Configure RoiAsTextureFiltered
    m_roisAsTextureFiltered = osg_ext::make_ref<osg::Texture2D>();
    setupRoiTexture(m_roisAsTextureFiltered);

    for (const auto imageOverlap : CImageOverlapIndex::ALL)
    {
        m_chamaeleonRoisFilteredCamera[imageOverlap] =
            osg_ext::make_ref<::pc::util::osgx::RenderToTextureCamera>(m_roisAsTextureFiltered, l_numSamples);

        const vfc::int32_t l_imageOverlapIndex{imageOverlap.asInteger()};
        m_chamaeleonRoisFilteredCamera[imageOverlap].get()->setViewport(
            l_imageOverlapIndex * RTPBO_ROI_WIDTH, 0, RTPBO_ROI_WIDTH, RTPBO_ROI_HEIGHT);

        osg::Geode* const    l_geodeRoiFilter = new osg::Geode();
        osg::Geometry* const l_quad           = osg::createTexturedQuadGeometry(
            osg::Vec3(0.F, 0.F, 0.F), osg::Vec3(1.F, 0.F, 0.F), osg::Vec3(0.F, 1.F, 0.F), 0.F, 0.F, 1.F, 1.F);
        l_geodeRoiFilter->addDrawable(l_quad);
        pc::core::TextureShaderProgramDescriptor l_chamaeleonRoiFilterShader("chamaeleonRoiFilter");
        osg::StateSet* const                     l_stateSet = l_geodeRoiFilter->getOrCreateStateSet();
        l_chamaeleonRoiFilterShader.apply(l_stateSet);
        m_chamaeleonRoisFilteredCamera[imageOverlap]->addChild(l_geodeRoiFilter);
    }

    setParameters();
}

void ChamaeleonRois::updateProjectionMatrix(vfc::float32_t f_width, vfc::float32_t f_hight)
{
    for (const auto imageOverlap : CImageOverlapIndex::ALL)
    {
        m_chamaeleonRoisCamera[imageOverlap].get()->setProjectionMatrixAsOrtho2D(
            -(f_width / 2.F), f_width / 2.F, -(f_hight / 2.F), f_hight / 2.F);
    }
}

void ChamaeleonRois::updateMatrixAsLookAt(vfc::float32_t f_stitchingLineFactor, bool f_useOrthoLine)
{
    // The used VirtualCamEnum for the renderManager is the same for m_renderManagerRight and m_renderManagerLeft
    // The information about the Stitchingline (origin, stitchingLine and orthoLine) should be the same
    const pc::factory::StitchingLinesManager* const l_stitchingLineManager{m_renderManagerRight->getStitchMng()};

    if (nullptr != l_stitchingLineManager)
    {
        for (const auto twoCamArea : common::CTwoCamAreaIndex::ALL)
        {
            osg::Vec3f l_origin{l_stitchingLineManager->getStitchingLineOrigin(twoCamArea.asEnum())};
            // We use a Ortho2DMatrix here, so the z coordinate indicates only the postion of the cameraEye (above or
            // below groundplate)
            l_origin.z() = 1.F;

            osg::Vec3f l_stitch{l_stitchingLineManager->getStitchingLine(twoCamArea.asEnum())};
            // The stitching line is in the format of a linear equation (ax + by + c -> l_stitch.x() * x + l_stitch.y()
            // * y
            // + l_stitch.z() = 0) The constant c results by using the l_origin (l_origin is a point on the
            // stitchingLine) We only need the direction of the stitching line so we can set the constant to zero
            l_stitch.z() = 0.F;

            osg::Vec3f l_up{1.F, 0.F, 0.F};

            if (f_useOrthoLine)
            {
                l_up = l_stitchingLineManager->getOrthoLine(twoCamArea.asEnum());
            }

            setRoiViewMatrixAsLookAt(
                getWorldOverlap(twoCamArea.asEnum()), l_origin + (l_stitch * f_stitchingLineFactor), l_up);
        }
    }
}

void ChamaeleonRois::setParameters(const ChamaeleonRoisData& f_params)
{
    m_useFilterRois         = f_params.m_useFilterRois;
    m_thresholdFilterDeltaE = f_params.m_filterDeltaE;
    updateMatrixAsLookAt(f_params.m_stitchingLineFactor, f_params.m_useOrthoLine);
    updateProjectionMatrix(f_params.m_roiSizeWidth, f_params.m_roiSizeHight);
}

static void getOrCreateUniformFilterdCameras(
    osg::StateSet* const f_stateSet,
    vfc::float32_t       f_thresholdFilterDeltaE,
    osg::Texture2D*      f_roisAsTexture,
    ETabImageOverlapROI  f_imageRoi)
{
    // delta_e threshold
    osg::Uniform* const l_uniformThresholdDeltaE{
        f_stateSet->getOrCreateUniform("u_thresholdDeltaE", osg::Uniform::FLOAT)};
    l_uniformThresholdDeltaE->set(f_thresholdFilterDeltaE);

    osg::Uniform* const l_imageOverlapRoiId{f_stateSet->getOrCreateUniform("u_imageOverlapRoiId", osg::Uniform::INT)};
    l_imageOverlapRoiId->set(static_cast<vfc::int32_t>(f_imageRoi));

    osg::Uniform* const l_roiLod{f_stateSet->getOrCreateUniform("u_roiLod", osg::Uniform::INT)};
    l_roiLod->set(ROI_DIM_LOD);

    osg::Uniform* const l_roiDim{f_stateSet->getOrCreateUniform("u_roiDim", osg::Uniform::INT)};
    l_roiDim->set(ROI_DIM);

    // add chamaeleon rois as texture
    f_stateSet->setTextureAttribute(
        0U,
        f_roisAsTexture,
        static_cast<osg::StateAttribute::OverrideValue>(osg::StateAttribute::ON) |
            static_cast<osg::StateAttribute::OverrideValue>(osg::StateAttribute::OVERRIDE));
    osg::Uniform* const l_samplerUniformRoiAsTexture =
        f_stateSet->getOrCreateUniform("s_chamRoiAsTexture", osg::Uniform::SAMPLER_2D);
    l_samplerUniformRoiAsTexture->set(0);
}

void ChamaeleonRois::updateRoiCameras(osg::NodeVisitor& f_nv)
{
    const ETabImageOverlapROI l_imageRoi1{getOverlapROI1(m_roundRobinCounter.asEnum())};
    const ETabImageOverlapROI l_imageRoi2{getOverlapROI2(m_roundRobinCounter.asEnum())};

    m_chamaeleonRoisCamera[l_imageRoi1]->accept(f_nv);
    m_chamaeleonRoisCamera[l_imageRoi2]->accept(f_nv);

    if (m_useFilterRois)
    {
        getOrCreateUniformFilterdCameras(
            m_chamaeleonRoisFilteredCamera[l_imageRoi1].get()->getStateSet(),
            m_thresholdFilterDeltaE,
            m_roisAsTexture,
            l_imageRoi1);
        getOrCreateUniformFilterdCameras(
            m_chamaeleonRoisFilteredCamera[l_imageRoi2].get()->getStateSet(),
            m_thresholdFilterDeltaE,
            m_roisAsTexture,
            l_imageRoi2);

        m_chamaeleonRoisFilteredCamera[l_imageRoi1].get()->accept(f_nv);
        m_chamaeleonRoisFilteredCamera[l_imageRoi2].get()->accept(f_nv);
    }

    // increment round robin counter
    const vfc::int32_t l_roundRobinCounterNextInt{
        (m_roundRobinCounter.asInteger() + 1) % static_cast<vfc::int32_t>(ETabWorldOverlapROI::NUM_WORLD_ROIS)};
    m_roundRobinCounter.setFromInteger(l_roundRobinCounterNextInt);
}

void ChamaeleonRois::setRoiViewMatrixAsLookAt(
    ETabWorldOverlapROI f_worldRoi,
    const osg::Vec3f&   f_roiEye,
    const osg::Vec3f&   f_roiUp)
{
    const ETabImageOverlapROI l_imageRoi1{getOverlapROI1(f_worldRoi)};
    const ETabImageOverlapROI l_imageRoi2{getOverlapROI2(f_worldRoi)};

    // We set the camera directly above the roi center.
    m_chamaeleonRoisCamera[l_imageRoi1].get()->setViewMatrixAsLookAt(
        f_roiEye, osg::Vec3f(f_roiEye.x(), f_roiEye.y(), 0.F), f_roiUp);
    m_chamaeleonRoisCamera[l_imageRoi2].get()->setViewMatrixAsLookAt(
        f_roiEye, osg::Vec3f(f_roiEye.x(), f_roiEye.y(), 0.F), f_roiUp);
}

} // namespace chamaeleon
} // namespace imp
} // namespace vis
} // namespace rbp
