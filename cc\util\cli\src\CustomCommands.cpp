//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: LIA1LR David Liepelt (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  CustomCommands.cpp
/// @brief
//=============================================================================
#include <array>
#include "pc/generic/util/cli/inc/CommandRegistry.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h"
#include "pc/svs/core/inc/Framework.h"
#include "pc/svs/core/inc/Scene.h"
#include "pc/svs/animation/inc/FadeOutAnimation.h"
#include "pc/svs/animation/inc/AnimationManager.h"
#include "pc/generic/util/cli/inc/CommandLineInterface.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h"
#include "pc/svs/util/cli/inc/BaseCommands.h"
#include "pc/generic/rapidjson/document.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "cc/daddy/inc/CustomDaddyPorts.h"
#include "cc/sm/viewmode/inc/ViewModeNames.h"
#include "cc/sm/viewmode/inc/ViewModexViewStateMachine_R2015b_types.h"
#include "cc/target/common/inc/ttactl_output_svs.h"
#include "cc/core/inc/CustomMechanicalData.h"
#include "cc/util/cli/inc/CustomCommands.hpp"
#include "cc/target/common/inc/ParkInterface.hpp"
#include "cc/util/common/inc/CommonUtil.hpp"


#define ENABLE_VERTICAL_MODE 1
using pc::util::logging::g_COMSocketContext;

#define USE_RADAR_WALL 1

namespace cc
{
namespace util
{
namespace cli
{

vfc::uint8_t g_rotate = 1u;  //1: hori; 2: vert. //PRQA S 2641

// 读取JSON文件内容并返回字符串
std::string readJsonFile(const std::string& filename) {
    std::ifstream file(filename);
    if (!file.is_open()) {
        std::cerr << "Failed to open the file: " << filename << std::endl;
        return "";
    }

    std::string jsonStr((std::istreambuf_iterator<char>(file)), std::istreambuf_iterator<char>());
    return jsonStr;
}

void parseApaJsonData(const std::string& jsonStr, cc::target::common::ParkhmiToSvs &f_apaData) {
    rapidjson::Document document;
    document.Parse(jsonStr.c_str());

    static int index = 0;

    XLOG_INFO_OS(g_COMSocketContext) << "new frame!!!!!!!!!!!!!!!!!!!!!!!!!!!! "<< index << XLOG_ENDL; // PRQA S 4060

    index++;

    if (index>100000){
        index = 0;
    }

    // init object info
    for (int i=0; i<cc::target::common::NUM_OBJECT; i++)
    {
        f_apaData.m_parkingRealTimeData.m_parkingObjectInfo.m_objectArray[i].m_objectId = 0;
        f_apaData.m_parkingRealTimeData.m_parkingObjectInfo.m_objectArray[i].m_typeInfo = cc::target::common::ObjectType::NoInformation;
    }

    // init slot info
    for (int i=0; i<cc::target::common::NUM_SLOT; i++)
    {
        f_apaData.m_parkingRealTimeData.m_parkingSlotInfo[i].m_parkngSpcID = 0;
        f_apaData.m_parkingRealTimeData.m_parkingSlotInfo[i].m_parkngSpcType = cc::target::common::EParkingSpaceType::NoInformation;
    }

    // init guideline info
    for (int i=0; i<cc::target::common::NUM_GUIDELINE_POINT; i++)
    {
        f_apaData.m_parkingRealTimeData.m_GuideLineInfo[i].m_trackPointID = 0;
    }


    if (document["NewParkingRealTimeDataNotify"]["ParkingObjectInfoNotify"]["ObjectArray"].IsArray()) {
        const rapidjson::Value& objectArray = document["NewParkingRealTimeDataNotify"]["ParkingObjectInfoNotify"]["ObjectArray"];

        XLOG_INFO_OS(g_COMSocketContext) << "objectArray.Size()" << objectArray.Size()<<XLOG_ENDL;

        for (rapidjson::SizeType i = 0; i < objectArray.Size(); ++i) {
            const rapidjson::Value& obj = objectArray[i];

            if (obj.HasMember("ObjectID_i") && obj["ObjectID_i"].IsInt()) {
                f_apaData.m_parkingRealTimeData.m_parkingObjectInfo.m_objectArray[i].m_objectId = obj["ObjectID_i"].GetInt();
                // XLOG_INFO_OS(g_COMSocketContext) << "m_parkingObjectInfo.m_objectArray[" << i << "].m_objectId " << \
                //             static_cast<vfc::int32_t>(f_apaData.m_parkingRealTimeData.m_parkingObjectInfo.m_objectArray[i].m_objectId) << XLOG_ENDL; // PRQA S 4060
            }else{
                XLOG_INFO_OS(g_COMSocketContext) << "Wrong!!!!!!!!!!!! " << XLOG_ENDL;
            }

            if (obj.HasMember("shape_height_i") && obj["shape_height_i"].IsFloat()) {
                f_apaData.m_parkingRealTimeData.m_parkingObjectInfo.m_objectArray[i].m_shapeHeight = obj["shape_height_i"].GetFloat();
                // XLOG_INFO_OS(g_COMSocketContext) << "m_parkingObjectInfo.m_objectArray[" << i << "].m_shapeHeight " << \
                //             f_apaData.m_parkingRealTimeData.m_parkingObjectInfo.m_objectArray[i].m_shapeHeight << XLOG_ENDL; // PRQA S 4060
            }else{
                XLOG_INFO_OS(g_COMSocketContext) << "Wrong!!!!!!!!!!!! " << XLOG_ENDL;
            }

            if (obj.HasMember("shape_length_i") && obj["shape_length_i"].IsFloat()) {
                f_apaData.m_parkingRealTimeData.m_parkingObjectInfo.m_objectArray[i].m_shapeLength = obj["shape_length_i"].GetFloat();
                // XLOG_INFO_OS(g_COMSocketContext) << "m_parkingObjectInfo.m_objectArray[" << i << "].m_shapeLength " << \
                //             f_apaData.m_parkingRealTimeData.m_parkingObjectInfo.m_objectArray[i].m_shapeLength << XLOG_ENDL; // PRQA S 4060
            }else{
                XLOG_INFO_OS(g_COMSocketContext) << "Wrong!!!!!!!!!!!! " << XLOG_ENDL;
            }

            if (obj.HasMember("shape_width_i") && obj["shape_width_i"].IsFloat()) {
                f_apaData.m_parkingRealTimeData.m_parkingObjectInfo.m_objectArray[i].m_shapeWidth = obj["shape_width_i"].GetFloat();
                // XLOG_INFO_OS(g_COMSocketContext) << "m_parkingObjectInfo.m_objectArray[" << i << "].m_shapeWidth " << \
                //             f_apaData.m_parkingRealTimeData.m_parkingObjectInfo.m_objectArray[i].m_shapeWidth << XLOG_ENDL; // PRQA S 4060
            }else{
                XLOG_INFO_OS(g_COMSocketContext) << "Wrong!!!!!!!!!!!! " << XLOG_ENDL;
            }

            // if (obj.HasMember("position_z_i") && obj["position_z_i"].IsDouble()) {
            //     f_apaData.m_parkingRealTimeData.m_parkingObjectInfo.m_objectArray[i].m_position.m_z = obj["position_z_i"].GetDouble();
            //     // XLOG_INFO_OS(g_COMSocketContext) << "m_parkingObjectInfo.m_objectArray[" << i << "].m_position.m_z " << \
            //     //             f_apaData.m_parkingRealTimeData.m_parkingObjectInfo.m_objectArray[i].m_position.m_z << XLOG_ENDL; // PRQA S 4060
            // }

            if (obj.HasMember("position_x_i") && obj["position_x_i"].IsDouble()) {
                f_apaData.m_parkingRealTimeData.m_parkingObjectInfo.m_objectArray[i].m_position.m_x = obj["position_x_i"].GetDouble();
                XLOG_INFO_OS(g_COMSocketContext) << "m_parkingObjectInfo.m_objectArray[" << i << "].m_position.m_x " << f_apaData.m_parkingRealTimeData.m_parkingObjectInfo.m_objectArray[i].m_position.m_x << XLOG_ENDL; // PRQA S 4060
            }else{
                XLOG_INFO_OS(g_COMSocketContext) << "Wrong!!!!!!!!!!!! " << XLOG_ENDL;
            }

            if (obj.HasMember("position_y_i") && obj["position_y_i"].IsDouble()) {
                f_apaData.m_parkingRealTimeData.m_parkingObjectInfo.m_objectArray[i].m_position.m_y = obj["position_y_i"].GetDouble();
                XLOG_INFO_OS(g_COMSocketContext) << "m_parkingObjectInfo.m_objectArray[" << i << "].m_position.m_y " << f_apaData.m_parkingRealTimeData.m_parkingObjectInfo.m_objectArray[i].m_position.m_y << XLOG_ENDL; // PRQA S 4060
            }else{
                XLOG_INFO_OS(g_COMSocketContext) << "Wrong!!!!!!!!!!!! " << XLOG_ENDL;
            }

            if (obj.HasMember("position_z_i") && obj["position_z_i"].IsDouble()) {
                f_apaData.m_parkingRealTimeData.m_parkingObjectInfo.m_objectArray[i].m_position.m_z = obj["position_z_i"].GetDouble();
                // XLOG_INFO_OS(g_COMSocketContext) << "m_parkingObjectInfo.m_objectArray[" << i << "].m_position.m_z " << \
                //             f_apaData.m_parkingRealTimeData.m_parkingObjectInfo.m_objectArray[i].m_position.m_z << XLOG_ENDL; // PRQA S 4060
            }else{
                XLOG_INFO_OS(g_COMSocketContext) << "Wrong!!!!!!!!!!!! " << XLOG_ENDL;
            }

            if (obj.HasMember("Heading_i") && obj["Heading_i"].IsDouble()) {
                f_apaData.m_parkingRealTimeData.m_parkingObjectInfo.m_objectArray[i].m_heading = obj["Heading_i"].GetDouble();
                XLOG_INFO_OS(g_COMSocketContext) << "m_parkingObjectInfo.m_objectArray[" << i << "].m_heading " << \
                            f_apaData.m_parkingRealTimeData.m_parkingObjectInfo.m_objectArray[i].m_heading << XLOG_ENDL; // PRQA S 4060
            }else{
                XLOG_INFO_OS(g_COMSocketContext) << "Wrong!!!!!!!!!!!! " << XLOG_ENDL;
            }

            if (obj.HasMember("TypeInfo") && obj["TypeInfo"].IsInt()) {
                f_apaData.m_parkingRealTimeData.m_parkingObjectInfo.m_objectArray[i].m_typeInfo = static_cast<cc::target::common::ObjectType>(obj["TypeInfo"].GetInt());
                XLOG_INFO_OS(g_COMSocketContext) << "m_parkingObjectInfo.m_objectArray[" << i << "].m_typeInfo " << \
                            static_cast<vfc::int32_t>(f_apaData.m_parkingRealTimeData.m_parkingObjectInfo.m_objectArray[i].m_typeInfo) << XLOG_ENDL; // PRQA S 4060
            }else{
                XLOG_INFO_OS(g_COMSocketContext) << "Wrong!!!!!!!!!!!! " << XLOG_ENDL;
            }
        }
    }


    if (document["NewParkingRealTimeDataNotify"]["ParkingSlotInfoNotify"].IsArray()) {
        const rapidjson::Value& slotArray = document["NewParkingRealTimeDataNotify"]["ParkingSlotInfoNotify"];
        XLOG_INFO_OS(g_COMSocketContext) << "slotArray.Size()" << slotArray.Size()<<XLOG_ENDL;

        for (rapidjson::SizeType i = 0; i < slotArray.Size(); ++i) {
            const rapidjson::Value& obj = slotArray[i];

            if (obj.HasMember("ParkngSpcID_i") && obj["ParkngSpcID_i"].IsInt()) {
                f_apaData.m_parkingRealTimeData.m_parkingSlotInfo[i].m_parkngSpcID = obj["ParkngSpcID_i"].GetInt();
                XLOG_INFO_OS(g_COMSocketContext) << "m_parkingObjectInfo.m_parkingSlotInfo[" << i << "].m_parkngSpcID " << \
                            static_cast<vfc::int32_t>(f_apaData.m_parkingRealTimeData.m_parkingSlotInfo[i].m_parkngSpcID) << XLOG_ENDL; // PRQA S 4060
            }else{
                XLOG_INFO_OS(g_COMSocketContext) << "Wrong!!!!!!!!!!!! " << XLOG_ENDL;
            }

            if (obj.HasMember("ParkngSpcSts") && obj["ParkngSpcSts"].IsInt()) {
                f_apaData.m_parkingRealTimeData.m_parkingSlotInfo[i].m_parkngSpcSts = static_cast<cc::target::common::EParkingSpaceStatus>(obj["ParkngSpcSts"].GetInt());
                // XLOG_INFO_OS(g_COMSocketContext) << "m_parkingObjectInfo.m_parkingSlotInfo[" << i << "].m_parkngSpcSts " << \
                //             static_cast<vfc::int32_t>(f_apaData.m_parkingRealTimeData.m_parkingSlotInfo[i].m_parkngSpcSts) << XLOG_ENDL; // PRQA S 4060
            }else{
                XLOG_INFO_OS(g_COMSocketContext) << "Wrong!!!!!!!!!!!! " << XLOG_ENDL;
            }

            if (obj.HasMember("ParkngSpcCode_i") && obj["ParkngSpcCode_i"].IsInt()) {
                f_apaData.m_parkingRealTimeData.m_parkingSlotInfo[i].m_parkngSpcCode = static_cast<vfc::uint8_t>(obj["ParkngSpcCode_i"].GetInt());
                // XLOG_INFO_OS(g_COMSocketContext) << "m_parkingObjectInfo.m_parkingSlotInfo[" << i << "].m_parkngSpcCode " << \
                //             static_cast<vfc::int32_t>(f_apaData.m_parkingRealTimeData.m_parkingSlotInfo[i].m_parkngSpcCode) << XLOG_ENDL; // PRQA S 4060
            }else{
                XLOG_INFO_OS(g_COMSocketContext) << "Wrong!!!!!!!!!!!! " << XLOG_ENDL;
            }

            if (obj.HasMember("ParkngSpcType") && obj["ParkngSpcType"].IsInt()) {
                f_apaData.m_parkingRealTimeData.m_parkingSlotInfo[i].m_parkngSpcType = static_cast<cc::target::common::EParkingSpaceType>(obj["ParkngSpcType"].GetInt());
                XLOG_INFO_OS(g_COMSocketContext) << "m_parkingObjectInfo.m_parkingSlotInfo[" << i << "].m_parkngSpcType " << \
                            static_cast<vfc::int32_t>(f_apaData.m_parkingRealTimeData.m_parkingSlotInfo[i].m_parkngSpcType) << XLOG_ENDL; // PRQA S 4060
            }else{
                XLOG_INFO_OS(g_COMSocketContext) << "Wrong!!!!!!!!!!!! " << XLOG_ENDL;
            }

            // if (obj.HasMember("ParkngSpcNum") && obj["ParkngSpcNum"].IsInt()) {
            //     f_apaData.m_parkingRealTimeData.m_parkingSlotInfo[i].m_parkngSpcNum = obj["ParkngSpcNum"].GetInt();
            //     XLOG_INFO_OS(g_COMSocketContext) << "m_parkingObjectInfo.m_parkingSlotInfo[" << i << "].m_parkngSpcNum " << \
            //                 static_cast<vfc::int32_t>(f_apaData.m_parkingRealTimeData.m_parkingSlotInfo[i].m_parkngSpcNum) << XLOG_ENDL; // PRQA S 4060
            // }

            //Corner 1
            if (obj.HasMember("x1_i") && obj["x1_i"].IsFloat()) {
                f_apaData.m_parkingRealTimeData.m_parkingSlotInfo[i].m_corner1.m_x = obj["x1_i"].GetFloat();
                // XLOG_INFO_OS(g_COMSocketContext) << "m_parkingObjectInfo.m_parkingSlotInfo[" << i << "].m_corner1.m_x " << \
                //             f_apaData.m_parkingRealTimeData.m_parkingSlotInfo[i].m_corner1.m_x << XLOG_ENDL; // PRQA S 4060
            }else{
                XLOG_INFO_OS(g_COMSocketContext) << "Wrong!!!!!!!!!!!! " << XLOG_ENDL;
            }

            if (obj.HasMember("y1_i") && obj["y1_i"].IsFloat()) {
                f_apaData.m_parkingRealTimeData.m_parkingSlotInfo[i].m_corner1.m_y = obj["y1_i"].GetFloat();
                // XLOG_INFO_OS(g_COMSocketContext) << "m_parkingObjectInfo.m_parkingSlotInfo[" << i << "].m_corner1.m_y " << \
                //             f_apaData.m_parkingRealTimeData.m_parkingSlotInfo[i].m_corner1.m_y << XLOG_ENDL; // PRQA S 4060
            }else{
                XLOG_INFO_OS(g_COMSocketContext) << "Wrong!!!!!!!!!!!! " << XLOG_ENDL;
            }

            //Corner 2
            if (obj.HasMember("x2_i") && obj["x2_i"].IsFloat()) {
                f_apaData.m_parkingRealTimeData.m_parkingSlotInfo[i].m_corner2.m_x = obj["x2_i"].GetFloat();
                // XLOG_INFO_OS(g_COMSocketContext) << "m_parkingObjectInfo.m_parkingSlotInfo[" << i << "].m_corner2.m_x " << \
                //             f_apaData.m_parkingRealTimeData.m_parkingSlotInfo[i].m_corner2.m_x << XLOG_ENDL; // PRQA S 4060
            }else{
                XLOG_INFO_OS(g_COMSocketContext) << "Wrong!!!!!!!!!!!! " << XLOG_ENDL;
            }

            if (obj.HasMember("y2_i") && obj["y2_i"].IsFloat()) {
                f_apaData.m_parkingRealTimeData.m_parkingSlotInfo[i].m_corner2.m_y = obj["y2_i"].GetFloat();
                // XLOG_INFO_OS(g_COMSocketContext) << "m_parkingObjectInfo.m_parkingSlotInfo[" << i << "].m_corner2.m_y " << \
                //             f_apaData.m_parkingRealTimeData.m_parkingSlotInfo[i].m_corner2.m_y << XLOG_ENDL; // PRQA S 4060
            }else{
                XLOG_INFO_OS(g_COMSocketContext) << "Wrong!!!!!!!!!!!! " << XLOG_ENDL;
            }

            //Corner 3
            if (obj.HasMember("x3_i") && obj["x3_i"].IsFloat()) {
                f_apaData.m_parkingRealTimeData.m_parkingSlotInfo[i].m_corner3.m_x = obj["x3_i"].GetFloat();
                // XLOG_INFO_OS(g_COMSocketContext) << "m_parkingObjectInfo.m_parkingSlotInfo[" << i << "].m_corner3.m_x " << \
                //             f_apaData.m_parkingRealTimeData.m_parkingSlotInfo[i].m_corner3.m_x << XLOG_ENDL; // PRQA S 4060
            }else{
                XLOG_INFO_OS(g_COMSocketContext) << "Wrong!!!!!!!!!!!! " << XLOG_ENDL;
            }

            if (obj.HasMember("y3_i") && obj["y3_i"].IsFloat()) {
                f_apaData.m_parkingRealTimeData.m_parkingSlotInfo[i].m_corner3.m_y = obj["y3_i"].GetFloat();
                // XLOG_INFO_OS(g_COMSocketContext) << "m_parkingObjectInfo.m_parkingSlotInfo[" << i << "].m_corner3.m_y " << \
                //             f_apaData.m_parkingRealTimeData.m_parkingSlotInfo[i].m_corner3.m_y << XLOG_ENDL; // PRQA S 4060
            }else{
                XLOG_INFO_OS(g_COMSocketContext) << "Wrong!!!!!!!!!!!! " << XLOG_ENDL;
            }

            //Corner 4
            if (obj.HasMember("x4_i") && obj["x4_i"].IsFloat()) {
                f_apaData.m_parkingRealTimeData.m_parkingSlotInfo[i].m_corner4.m_x = obj["x4_i"].GetFloat();
                // XLOG_INFO_OS(g_COMSocketContext) << "m_parkingObjectInfo.m_parkingSlotInfo[" << i << "].m_corner4.m_x " << \
                //             f_apaData.m_parkingRealTimeData.m_parkingSlotInfo[i].m_corner4.m_x << XLOG_ENDL; // PRQA S 4060
            }else{
                XLOG_INFO_OS(g_COMSocketContext) << "Wrong!!!!!!!!!!!! " << XLOG_ENDL;
            }

            if (obj.HasMember("y4_i") && obj["y4_i"].IsFloat()) {
                f_apaData.m_parkingRealTimeData.m_parkingSlotInfo[i].m_corner4.m_y = obj["y4_i"].GetFloat();
                // XLOG_INFO_OS(g_COMSocketContext) << "m_parkingObjectInfo.m_parkingSlotInfo[" << i << "].m_corner4.m_y " << \
                //             f_apaData.m_parkingRealTimeData.m_parkingSlotInfo[i].m_corner4.m_y << XLOG_ENDL; // PRQA S 4060
            }else{
                XLOG_INFO_OS(g_COMSocketContext) << "Wrong!!!!!!!!!!!! " << XLOG_ENDL;
            }


        }
    }
}


//!
//! SetViewModeCommand
//!
typedef std::array<pc::util::cli::IntValueNamePair, 79> ViewModeNameMap;
const ViewModeNameMap g_viewModeMapping = {
    EScreenID_NO_VIDEO_USER,                   "off",   //PRQA S 2427 //PRQA S 2428
    EScreenID_LSMG,                            "lsmg",
    // EScreenID_CONTEXT_ON_ROAD,                 "ctx_onroad",
    EScreenID_TRIPLE_ML_FV_MR,                 "offroad",
    // EScreenID_CONTEXT_JAPANESE,                "ctx_jap",
    // EScreenID_CONTEXT_TOWING,                  "ctx_towing",
    // EScreenID_CONTEXT_THREAT,                  "ctx_threat",
    EScreenID_SINGLE_FRONT_NORMAL,             "front",
    EScreenID_SINGLE_STB,                      "stbonnet",
    EScreenID_SINGLE_FRONT_JUNCTION,           "frontjunc",
    EScreenID_SINGLE_REAR_NORMAL_OFF_ROAD,     "rearoff",
    EScreenID_SINGLE_REAR_NORMAL_ON_ROAD,      "rear",
    EScreenID_MODEL_B_VIEW_ENLARGEMENT,        "rearEnlarge",
    EScreenID_SINGLE_REAR_JUNCTION,            "rearjunc",
    EScreenID_MODEL_F_VIEW_ENLARGEMENT,        "frontEnlarge",
    EScreenID_SINGLE_REAR_HITCH,               "hitch",
    //EView_HitchAssistZoom
    EScreenID_DUAL_FRONT_JAP,                  "japfront",
    EScreenID_DUAL_REAR_JAP,                   "japrear",
    EScreenID_DUAL_FRONT_ML,                   "dualDAleft",
    EScreenID_DUAL_FRONT_MR,                   "dualDAright",
    EScreenID_PARK_ASSIST_FRONT,               "paf",
    EScreenID_PARK_ASSIST_REAR,                "par",
    EScreenID_PARK_ASSIST_FRONT_JAP,           "pafjap",
    EScreenID_PARK_ASSIST_REAR_JAP,            "parjap",
    //EView_TowAssist
    //EView_Debug
    EScreenID_SINGLE_FRONT_RAW,                "rawfisheyeFront",
    EScreenID_SINGLE_REAR_RAW_DIAG,            "rawfisheyeRear",
    EScreenID_SINGLE_ML_RAW,                   "rawfisheyeLeft",
    EScreenID_SINGLE_MR_RAW,                   "rawfisheyeRight",
    EScreenID_QUAD_RAW,                        "rawfisheyeQuad",
    EScreenID_PERSPECTIVE_FL,                  "svfl",
    EScreenID_PERSPECTIVE_FR,                  "svfr",
    EScreenID_PERSPECTIVE_RL,                  "svrl",
    EScreenID_PERSPECTIVE_RR,                  "svrr",
    EScreenID_PERSPECTIVE_KL,                  "svkl",
    EScreenID_PERSPECTIVE_KR,                  "svkr",
    EScreenID_PERSPECTIVE_PFR,                 "svpfr",
    EScreenID_PERSPECTIVE_PRI,                 "svpri",
    EScreenID_PERSPECTIVE_PRE,                 "svpre",
    EScreenID_PERSPECTIVE_PLE,                 "svple",
    EScreenID_SINGLE_REAR_TRAILER,             "trailer",
    //EView_NoVideo
    // EScreenID_DUAL_ML_MR_TRAILER,              "tow2",
    EScreenID_TRIPLE_REAR_ML_MR_TOW_ASSIST,    "tow3",
    //EView_Engineering
    EScreenID_CAM_CALIB_ENG,                   "calibeng",
    EScreenID_LSMG_LSAEB_ENG,                  "lsmgaebeng",
    EScreenID_TOW_ASSIST_ENG,                  "toweng",
    EScreenID_THREAT_FRONT,                    "threatfront",
    EScreenID_THREAT_REAR,                     "threatrear",
    EScreenID_SINGLE_LEFT,                     "singleleft",
    EScreenID_SINGLE_RIGHT,                    "singleright",
    EScreenID_WHEEL_FRONT_DUAL,                "wheelfront",
    EScreenID_WHEEL_REAR_DUAL,                 "wheelrear",
    EScreenID_FULL_SCREEN,                     "fullscreen",
    EScreenID_BACK,                            "back",
#if ENABLE_VERTICAL_MODE
    EScreenID_VERT_SINGLE_FRONT,          "vertfrle",
    EScreenID_VERT_SINGLE_REAR,           "vertrele",
    EScreenID_VERT_SINGLE_FRONT_JUNCTION, "vertpafle",
    EScreenID_VERT_SINGLE_REAR_JUNCTION,  "vertparle",
    EScreenID_VERT_WHEEL_FRONT_DUAL,      "vertwhlfrle",
    EScreenID_VERT_PERSPECTIVE_PFR,       "vertsvpfrle",
    EScreenID_VERT_PERSPECTIVE_PRE,       "vertsvprele",
    EScreenID_VERT_PERSPECTIVE_RL,        "vertsvrlle",
    EScreenID_VERT_PERSPECTIVE_RR,        "vertsvrrle",
    EScreenID_VERT_PERSPECTIVE_FL,        "vertsvflle",
    EScreenID_VERT_PERSPECTIVE_FR,        "vertsvfrle",
#endif
    EScreenID_PLANVIEW_WITH_SEPARATOR,         "planviewsepa",
    EScreenID_REMOTE_SCREEN_FRONT,                 "remotefront",
    EScreenID_REMOTE_SCREEN_REAR,                  "remoteclarear",
    EScreenID_REMOTE_SCREEN_LEFT,                  "remoteclaleft",
    EScreenID_REMOTE_SCREEN_FRONT,                 "remoteclaright",
    EScreenID_FULLSCREEN,                      "full",
    EScreenID_FULLSCREEN_FRONT_ENLARGE,        "fullfront",
    EScreenID_FULLSCREEN_REAR_ENLARGE,         "fullrear",
    EScreenID_LEFTRIGHTVIEW_REAR_VIEW,          "fourwindowsrear",
    EScreenID_LEFTRIGHTVIEW_FRONT_VIEW,         "fourwindowsfront"
};

class SetViewModeCommand : public pc::util::cli::EnumSignalSetter<ViewModeNameMap>
{
public:

    explicit SetViewModeCommand(const ViewModeNameMap& f_mapping)
      : pc::util::cli::EnumSignalSetter<ViewModeNameMap>("ManualVideoSetupReq", f_mapping)
    {
    }

    void sendSignal(vfc::int32_t f_mode) override
    {
        static const vfc::uint8_t l_overlayStatus = 1u;
        if (cc::daddy::CustomDaddyPorts::sm_SVSDisplayedViewDaddy_SenderPort.isConnected())
        {
            cc::daddy::SVSDisplayedViewDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_SVSDisplayedViewDaddy_SenderPort.reserve() ;
            l_container.m_Data = static_cast<EScreenID>(f_mode);
            // l_container.m_Data.m_OverlayStatus_u8 = static_cast<vfc::uint8_t>(l_overlayStatus);
            cc::daddy::CustomDaddyPorts::sm_SVSDisplayedViewDaddy_SenderPort.deliver() ;
            // XLOG_INFO_OS(g_COMSocketContext) << "Setting ManualVideoSetupReq ScreenIDPIVI to " << EScreenID_TRIPLE_REAR_ML_MR_TOW_ASSIST << XLOG_ENDL;
            XLOG_INFO_OS(g_COMSocketContext) << "Setting ManualVideoSetupReq ScreenID to " << static_cast<vfc::int32_t> (f_mode) << XLOG_ENDL;//PRQA S 4060
            // XLOG_INFO_OS(g_COMSocketContext) << "Setting ManualVideoSetupReq m_OverlayStatus to " << static_cast<vfc::int32_t> (l_overlayStatus) << XLOG_ENDL;//PRQA S 4060
        }
    }
};

static SetViewModeCommand g_setViewModeCommand(g_viewModeMapping);


//!
//! GetViewModeCommand
//!
class GetViewModeCommand : public pc::util::cli::CommandCallback
{
public:

    GetViewModeCommand()
      : m_connected(false) // PRQA S 4050
    {
    }

    ~GetViewModeCommand() override
    {
        if (m_connected)
        {
            cc::daddy::CustomDaddyPorts::sm_ViewModeState_SenderPort.disconnect(m_viewModeReceiver);
        }
    }

    bool invoke(std::istream&, std::ostream& f_output, const pc::util::cli::CommandLineInterface&) override
    {
        if (!m_connected)
        {
            connect();
        }
        bool l_result = false;
        m_viewModeReceiver.update();
        const cc::daddy::VMStateDaddy_t* l_viewModeDaddy = m_viewModeReceiver.getData();
        if (l_viewModeDaddy != nullptr)
        {
            f_output << "NFS_ViewMode: " << cc::ViewModeNames::get(EScreenID_Convert_uint8ToEnum(static_cast<vfc::uint8_t>(l_viewModeDaddy->m_Data.mode))) << newline;   // PRQA S 3803
            l_result = true;
        }
        else
        {
            f_output << "NFS_ViewMode: Oh No!" << newline;   // PRQA S 3803
            l_result = false;
        }
        m_viewModeReceiver.cleanup();
        return l_result;
    }


    void getDescription(std::ostream& f_output) const override
    {
        f_output << "Querey the current view mode state" << newline;   // PRQA S 3803
    }

private:

    //! Copy constructor is not permitted.
    GetViewModeCommand (const GetViewModeCommand& other); // = delete
    //! Copy assignment operator is not permitted.
    GetViewModeCommand& operator=(const GetViewModeCommand& other); // = delete

    void connect()
    {
        cc::daddy::CustomDaddyPorts::sm_ViewModeState_SenderPort.connect(m_viewModeReceiver);
        m_connected = true;
    }

    bool m_connected;
    ::daddy::TLatestReceiverPort< cc::daddy::VMStateDaddy_t > m_viewModeReceiver;

    static EScreenID EScreenID_Convert_uint8ToEnum(vfc::uint8_t f_value)  // PRQA S 6040
    {
        switch (static_cast<vfc::int32_t>(f_value))
        {
            case EScreenID_NO_VIDEO_USER:
            return EScreenID_NO_VIDEO_USER;
            case EScreenID_LSMG:
            return EScreenID_LSMG;
            case EScreenID_CONTEXT_ON_ROAD:
            return EScreenID_CONTEXT_ON_ROAD;
            case EScreenID_CONTEXT_OFF_ROAD:
            return EScreenID_CONTEXT_OFF_ROAD;
            case EScreenID_CONTEXT_TOWING:
            return EScreenID_CONTEXT_TOWING;
            case EScreenID_CONTEXT_JAPANESE:
            return EScreenID_CONTEXT_JAPANESE;
            case EScreenID_CONTEXT_THREAT:
            return EScreenID_CONTEXT_THREAT;
            case EScreenID_SINGLE_FRONT_NORMAL:
            return EScreenID_SINGLE_FRONT_NORMAL;
            case EScreenID_SINGLE_STB:
            return EScreenID_SINGLE_STB;
            case EScreenID_SINGLE_FRONT_JUNCTION:
            return EScreenID_SINGLE_FRONT_JUNCTION;
            case EScreenID_SINGLE_REAR_NORMAL_OFF_ROAD:
            return EScreenID_SINGLE_REAR_NORMAL_OFF_ROAD;
            case EScreenID_PARK_ASSIST_FRONT:
            return EScreenID_PARK_ASSIST_FRONT;
            case EScreenID_SINGLE_FRONT_RAW:
            return EScreenID_SINGLE_FRONT_RAW;
            case EScreenID_SINGLE_REAR_NORMAL_ON_ROAD:
            return EScreenID_SINGLE_REAR_NORMAL_ON_ROAD;
            case EScreenID_SINGLE_REAR_JUNCTION:
            return EScreenID_SINGLE_REAR_JUNCTION;
            case EScreenID_SINGLE_REAR_RAW_DIAG:
            return EScreenID_SINGLE_REAR_RAW_DIAG;
            case EScreenID_SINGLE_REAR_HITCH:
            return EScreenID_SINGLE_REAR_HITCH;
            case EScreenID_SINGLE_REAR_HITCH_ZOOM:
            return EScreenID_SINGLE_REAR_HITCH_ZOOM;
            case EScreenID_PARK_ASSIST_REAR:
            return EScreenID_PARK_ASSIST_REAR;
            case EScreenID_SINGLE_REAR_TRAILER:
            return EScreenID_SINGLE_REAR_TRAILER;
            case EScreenID_PERSPECTIVE_KL:
            return EScreenID_PERSPECTIVE_KL;
            case EScreenID_PARK_ASSIST_FRONT_JAP:
            return EScreenID_PARK_ASSIST_FRONT_JAP;
            case EScreenID_PARK_ASSIST_REAR_JAP:
            return EScreenID_PARK_ASSIST_REAR_JAP;
            case EScreenID_SINGLE_ML_RAW:
            return EScreenID_SINGLE_ML_RAW;
            case EScreenID_PERSPECTIVE_KR:
            return EScreenID_PERSPECTIVE_KR;
            case EScreenID_SINGLE_LEFT:
            return EScreenID_SINGLE_LEFT;
            case EScreenID_SINGLE_RIGHT:
            return EScreenID_SINGLE_RIGHT;
            case EScreenID_SINGLE_MR_RAW:
            return EScreenID_SINGLE_MR_RAW;
            case EScreenID_THREAT_FRONT:
            return EScreenID_THREAT_FRONT;
            case EScreenID_THREAT_REAR:
            return EScreenID_THREAT_REAR;
            case EScreenID_WHEEL_FRONT_DUAL:
            return EScreenID_WHEEL_FRONT_DUAL;
            case EScreenID_PERSPECTIVE_FL:
            return EScreenID_PERSPECTIVE_FL;
            case EScreenID_PERSPECTIVE_FR:
            return EScreenID_PERSPECTIVE_FR;
            case EScreenID_PERSPECTIVE_RL:
            return EScreenID_PERSPECTIVE_RL;
            case EScreenID_PERSPECTIVE_RR:
            return EScreenID_PERSPECTIVE_RR;
            case EScreenID_DUAL_FRONT_ML:
            return EScreenID_DUAL_FRONT_ML;
            case EScreenID_DUAL_FRONT_MR:
            return EScreenID_DUAL_FRONT_MR;
            case EScreenID_PERSPECTIVE_PRE:
            return EScreenID_PERSPECTIVE_PRE;
            case EScreenID_PERSPECTIVE_PLE:
            return EScreenID_PERSPECTIVE_PLE;
            case EScreenID_DUAL_FRONT_JAP:
            return EScreenID_DUAL_FRONT_JAP;
            case EScreenID_CAM_CALIB_ENG:
            return EScreenID_CAM_CALIB_ENG;
            case EScreenID_DUAL_REAR_JAP:
            return EScreenID_DUAL_REAR_JAP;
            case EScreenID_TOW_ASSIST_ENG:
            return EScreenID_TOW_ASSIST_ENG;
            case EScreenID_LSM_ENG:
            return EScreenID_LSM_ENG;
            case EScreenID_WHEEL_REAR_DUAL:
            return EScreenID_WHEEL_REAR_DUAL;
            case EScreenID_LSMG_LSAEB_ENG:
            return EScreenID_LSMG_LSAEB_ENG;
            case EScreenID_FULL_SCREEN:
            return EScreenID_FULL_SCREEN;
            case EScreenID_TRIPLE_ML_FV_MR:
            return EScreenID_TRIPLE_ML_FV_MR;
            case EScreenID_TRIPLE_REAR_ML_MR_TOW_ASSIST:
            return EScreenID_TRIPLE_REAR_ML_MR_TOW_ASSIST;
            case EScreenID_QUAD_RAW:
            return EScreenID_QUAD_RAW;
            case EScreenID_NO_VIDEO_SYSTEM:
            return EScreenID_NO_VIDEO_SYSTEM;
            case EScreenID_PERSPECTIVE_PFR:
            return EScreenID_PERSPECTIVE_PFR;
            case EScreenID_PERSPECTIVE_PRI:
            return EScreenID_PERSPECTIVE_PRI;
            case EScreenID_BACK:
            return EScreenID_BACK;
#if ENABLE_VERTICAL_MODE
            case EScreenID_VERT_SINGLE_FRONT:
            return EScreenID_VERT_SINGLE_FRONT;
            case EScreenID_VERT_SINGLE_REAR:
            return EScreenID_VERT_SINGLE_REAR;
            case EScreenID_VERT_SINGLE_FRONT_JUNCTION:
            return EScreenID_VERT_SINGLE_FRONT_JUNCTION;
            case EScreenID_VERT_SINGLE_REAR_JUNCTION:
            return EScreenID_VERT_SINGLE_REAR_JUNCTION;
            case EScreenID_VERT_WHEEL_FRONT_DUAL:
            return EScreenID_VERT_WHEEL_FRONT_DUAL;
            case EScreenID_VERT_PERSPECTIVE_PFR:
            return EScreenID_VERT_PERSPECTIVE_PFR;
            case EScreenID_VERT_PERSPECTIVE_PRE:
            return EScreenID_VERT_PERSPECTIVE_PRE;
            case EScreenID_VERT_PERSPECTIVE_RL:
            return EScreenID_VERT_PERSPECTIVE_RL;
            case EScreenID_VERT_PERSPECTIVE_RR:
            return EScreenID_VERT_PERSPECTIVE_RR;
#endif
            case EScreenID_PLANVIEW_WITH_SEPARATOR:
            return EScreenID_PLANVIEW_WITH_SEPARATOR;
            case EScreenID_DEBUG:
            return EScreenID_DEBUG;
            case EScreenID_REMOTE_SCREEN_FRONT:
            return EScreenID_REMOTE_SCREEN_FRONT;
            case EScreenID_REMOTE_SCREEN_REAR:
            return EScreenID_REMOTE_SCREEN_REAR;
            case EScreenID_REMOTE_SCREEN_LEFT:
            return EScreenID_REMOTE_SCREEN_LEFT;
            case EScreenID_REMOTE_SCREEN_RIGHT:
            return EScreenID_REMOTE_SCREEN_RIGHT;
            case EScreenID_FULLSCREEN:
            return EScreenID_FULLSCREEN;
            case EScreenID_FULLSCREEN_FRONT_ENLARGE:
            return EScreenID_FULLSCREEN_FRONT_ENLARGE;
            case EScreenID_FULLSCREEN_REAR_ENLARGE:
            return EScreenID_FULLSCREEN_REAR_ENLARGE;
            case EScreenID_NO_CHANGE:
            default:
            return EScreenID_NO_CHANGE;
        }
    }


};

static GetViewModeCommand g_getViewModeCommand;

//!
//! GetSystemStateCommand
//!
class GetSystemStateCommand : public pc::util::cli::CommandCallback
{
public:

    GetSystemStateCommand()
      : m_connected(false) // PRQA S 4050
    {
    }

    ~GetSystemStateCommand() override
    {
        if (m_connected)
        {
            cc::daddy::CustomDaddyPorts::sm_systemState_SenderPort.disconnect(m_systemStateReceiver);
            cc::daddy::CustomDaddyPorts::sm_SVSShowReqDaddy_SenderPort.disconnect(m_SVSShowReqDaddyCCReceiver);
        }
    }

    bool invoke(std::istream&, std::ostream& f_output, const pc::util::cli::CommandLineInterface&) override  // PRQA S 6040  // PRQA S 6041
    {
        if (!m_connected)
        {
            connect();
        }
        bool l_result = false;
        m_systemStateReceiver.update();
        m_SVSShowReqDaddyCCReceiver.update();
        const cc::daddy::SystemStateDaddy* l_systemStateDaddy = m_systemStateReceiver.getData();
        const cc::daddy::SVSShowReqDaddy_t* l_SVSShowReqDaddy = m_SVSShowReqDaddyCCReceiver.getData();
        f_output << "NFS system state: ";  // PRQA S 3803
        if (l_systemStateDaddy != nullptr)
        {
            switch (l_systemStateDaddy->m_Data.systemAvailability)
            {
                case 0:
                    f_output << "unavailable";  // PRQA S 3803
                    break;
                case 1:
                    f_output << "available";  // PRQA S 3803
                    break;
                case 2:
                    f_output << "partially available";  // PRQA S 3803
                    break;
                default:
                    break;
            }
            f_output << "; active ";  // PRQA S 3803
            if (l_systemStateDaddy->m_Data.systemacitve)
            {
                f_output << "yes";  // PRQA S 3803
            }
            else
            {
                f_output << "no";  // PRQA S 3803
            }
            if (l_SVSShowReqDaddy != nullptr)
            {
                f_output << "; ShoWReq ";  // PRQA S 3803
                switch (static_cast<vfc::int32_t>(l_SVSShowReqDaddy->m_Data))
                {
                    case 0: f_output << "off"; break;  // PRQA S 3803
                    case 1: f_output << "yes"; break;  // PRQA S 3803
                    default: f_output << l_SVSShowReqDaddy->m_Data; break;  // PRQA S 3803
                }
            }
            l_result = true;
        }
        else
        {
            f_output << "no status available";  // PRQA S 3803
            l_result = false;
        }
        f_output << newline;  // PRQA S 3803
        m_systemStateReceiver.cleanup();
        m_SVSShowReqDaddyCCReceiver.cleanup();
        return l_result;
    }


    void getDescription(std::ostream& f_output) const override
    {
        f_output << "Qurey the current system state" << newline;  // PRQA S 3803
    }

private:

    //! Copy constructor is not permitted.
    GetSystemStateCommand (const GetSystemStateCommand& other); // = delete
    //! Copy assignment operator is not permitted.
    GetSystemStateCommand& operator=(const GetSystemStateCommand& other); // = delete

    void connect()
    {
        cc::daddy::CustomDaddyPorts::sm_systemState_SenderPort.connect(m_systemStateReceiver);
        cc::daddy::CustomDaddyPorts::sm_SVSShowReqDaddy_SenderPort.connect(m_SVSShowReqDaddyCCReceiver);
        m_connected = true;
    }

    bool m_connected;
    ::daddy::TLatestReceiverPort< cc::daddy::SystemStateDaddy > m_systemStateReceiver;
    ::daddy::TLatestReceiverPort< cc::daddy::SVSShowReqDaddy_t > m_SVSShowReqDaddyCCReceiver;
};

static GetSystemStateCommand g_getSystemStateCommand;

//!
//! GetVehicleStateCommand
//!
class GetVehicleStateCommand : public pc::util::cli::CommandCallback
{
public:
    GetVehicleStateCommand()
        : m_connected(false) // PRQA S 4050
    {
    }
    ~GetVehicleStateCommand() override
    {
        if (m_connected)
        {
            cc::daddy::CustomDaddyPorts::sm_DriverSteeringWheelAngleDaddy_SenderPort.disconnect(m_driverSteeringWheelAngleCCReceiver);
            pc::daddy::BaseDaddyPorts::sm_indicatorStateSenderPort.disconnect( m_indicatorStateCCReceiver );
            pc::daddy::BaseDaddyPorts::sm_gearSenderPort.disconnect( m_GearViewModeCCReceiver ) ;
            pc::daddy::BaseDaddyPorts::sm_SpeedSenderPort.disconnect( m_VehicleSpeedViewModeCCReceiver );
        }
    }
    bool invoke(std::istream&, std::ostream& f_output, const pc::util::cli::CommandLineInterface&) override
    {
        if (!m_connected)
        {
            connect();
        }
        bool l_result = false;
        m_driverSteeringWheelAngleCCReceiver.update();
        m_indicatorStateCCReceiver.update();
        m_GearViewModeCCReceiver.update();
        m_VehicleSpeedViewModeCCReceiver.update();
        const cc::daddy::DriverSteeringWheelAngleDaddy_t* l_steeringWheelAngleDaddy = m_driverSteeringWheelAngleCCReceiver.getData();
        const pc::daddy::IndicatorStateDaddy* l_indicatorStateDaddy = m_indicatorStateCCReceiver.getData();
        const pc::daddy::GearDaddy* l_gearDaddy = m_GearViewModeCCReceiver.getData();
        const pc::daddy::SpeedDaddy* l_speedDaddy = m_VehicleSpeedViewModeCCReceiver.getData();
        f_output << "Vehicle state: ";  // PRQA S 3803
        if ((l_speedDaddy != nullptr) || (l_gearDaddy != nullptr) || (l_steeringWheelAngleDaddy != nullptr) || (l_indicatorStateDaddy != nullptr))
        {
            if (l_speedDaddy != nullptr)
            {
                f_output << " Speed " << l_speedDaddy->m_Data << " km/h";  // PRQA S 3803
            }
            if (l_gearDaddy != nullptr)
            {
                f_output << "; Gear ";  // PRQA S 3803
                switch (l_gearDaddy->m_Data)
                {
                    case 10: f_output << "N"; break;  // PRQA S 3803
                    case 11: f_output << "R"; break;  // PRQA S 3803
                    case 12: f_output << "P"; break;  // PRQA S 3803
                    case 13: f_output << "D"; break;  // PRQA S 3803
                    case 14: f_output << "Invalid"; break;  // PRQA S 3803
                    default: f_output << l_gearDaddy->m_Data; break;  // PRQA S 3803
                }
            }
            if (l_steeringWheelAngleDaddy != nullptr)
            {
                f_output << "; SteerWheelAngle " << l_steeringWheelAngleDaddy->m_Data << " deg";  // PRQA S 3803
            }
            if (l_indicatorStateDaddy != nullptr)
            {
                f_output << "; Indicator ";  // PRQA S 3803
                switch (l_indicatorStateDaddy->m_Data)
                {
                    case 0: f_output << "off"; break;  // PRQA S 3803
                    case 1: f_output << "left"; break;  // PRQA S 3803
                    case 2: f_output << "right"; break;  // PRQA S 3803
                    case 3: f_output << "warn"; break;  // PRQA S 3803
                    default: f_output << l_indicatorStateDaddy->m_Data; break;  // PRQA S 3803
                }
            }
            l_result = true;
        }
        else
        {
            f_output << "no vehicle status available";  // PRQA S 3803
            l_result = false;
        }
        f_output << newline;  // PRQA S 3803
        m_driverSteeringWheelAngleCCReceiver.cleanup();
        m_indicatorStateCCReceiver.cleanup();
        m_GearViewModeCCReceiver.cleanup();
        m_VehicleSpeedViewModeCCReceiver.cleanup();
        return l_result;
    }
    void getDescription(std::ostream& f_output) const override
    {
        f_output << "Qurey the current vehicle state" << newline;  // PRQA S 3803
    }
private:

    //! Copy constructor is not permitted.
    GetVehicleStateCommand (const GetVehicleStateCommand& other); // = delete
    //! Copy assignment operator is not permitted.
    GetVehicleStateCommand& operator=(const GetVehicleStateCommand& other); // = delete

    void connect()
    {
        cc::daddy::CustomDaddyPorts::sm_DriverSteeringWheelAngleDaddy_SenderPort.connect(m_driverSteeringWheelAngleCCReceiver);
        pc::daddy::BaseDaddyPorts::sm_indicatorStateSenderPort.connect( m_indicatorStateCCReceiver );
        pc::daddy::BaseDaddyPorts::sm_gearSenderPort.connect( m_GearViewModeCCReceiver ) ;
        pc::daddy::BaseDaddyPorts::sm_SpeedSenderPort.connect( m_VehicleSpeedViewModeCCReceiver );
        m_connected = true;
    }
    bool m_connected;
    ::daddy::TLatestReceiverPort< cc::daddy::DriverSteeringWheelAngleDaddy_t > m_driverSteeringWheelAngleCCReceiver;
    ::daddy::TLatestReceiverPort< pc::daddy::IndicatorStateDaddy > m_indicatorStateCCReceiver;
    ::daddy::TLatestReceiverPort< pc::daddy::GearDaddy > m_GearViewModeCCReceiver;
    ::daddy::TLatestReceiverPort< pc::daddy::SpeedDaddy > m_VehicleSpeedViewModeCCReceiver;
};
static GetVehicleStateCommand g_getVehicleStateCommand;

//!
//! GetHUSettingCommand
//!
class GetHUSettingCommand : public pc::util::cli::CommandCallback
{
public:
    GetHUSettingCommand()
        : m_connected(false) // PRQA S 4050
    {
    }
    ~GetHUSettingCommand() override
    {
        if (m_connected)
        {
            cc::daddy::CustomDaddyPorts::sm_HUShoWReqDaddy_SenderPort.disconnect(m_huShoWReqCCReceiver);
            cc::daddy::CustomDaddyPorts::sm_HUselSVSModeDaddy_SenderPort.disconnect(m_huselSVSModeCCReceiver);
        }
    }
    bool invoke(std::istream&, std::ostream& f_output, const pc::util::cli::CommandLineInterface&) override  // PRQA S 6041
    {
        if (!m_connected)
        {
            connect();
        }
        bool l_result = false;
        m_huShoWReqCCReceiver.update();
        m_huselSVSModeCCReceiver.update();
        const cc::daddy::HUShoWReqDaddy_t* l_huShoWReqDaddy = m_huShoWReqCCReceiver.getData();
        const cc::daddy::HUselSVSModeDaddy_t* l_huselSVSModeDaddy = m_huselSVSModeCCReceiver.getData();
        f_output << "HU Setting state: ";  // PRQA S 3803
        if ((l_huShoWReqDaddy != nullptr) || (l_huselSVSModeDaddy != nullptr))
        {
            if (l_huShoWReqDaddy != nullptr)
            {
                f_output << " ShoWReq ";  // PRQA S 3803
                switch (static_cast<vfc::int32_t>(l_huShoWReqDaddy->m_Data))
                {
                    case 0: f_output << "none"; break;  // PRQA S 3803
                    case 1: f_output << "on"; break;  // PRQA S 3803
                    // case 2: f_output << "off"; break;  // PRQA S 3803
                    default: f_output << l_huShoWReqDaddy->m_Data; break;  // PRQA S 3803
                }
            }
            if (l_huselSVSModeDaddy != nullptr)
            {
                f_output << "; SVSMode ";  // PRQA S 3803
                switch (static_cast<vfc::int32_t>(l_huselSVSModeDaddy->m_Data))
                {
                    case 0: f_output << "none"; break;  // PRQA S 3803
                    case 1: f_output << "2d"; break;  // PRQA S 3803
                    case 2: f_output << "3d"; break;  // PRQA S 3803
                    default: f_output << l_huselSVSModeDaddy->m_Data; break;  // PRQA S 3803
                }
            }
            l_result = true;
        }
        else
        {
            f_output << "no HU Setting available";  // PRQA S 3803
            l_result = false;
        }
        f_output << newline;  // PRQA S 3803
        m_huShoWReqCCReceiver.cleanup();
        m_huselSVSModeCCReceiver.cleanup();
        return l_result;
    }
    void getDescription(std::ostream& f_output) const override
    {
        f_output << "Qurey the current HU Setting state" << newline;  // PRQA S 3803
    }
private:

    //! Copy constructor is not permitted.
    GetHUSettingCommand (const GetHUSettingCommand& other); // = delete
    //! Copy assignment operator is not permitted.
    GetHUSettingCommand& operator=(const GetHUSettingCommand& other); // = delete

    void connect()
    {
        cc::daddy::CustomDaddyPorts::sm_HUShoWReqDaddy_SenderPort.connect(m_huShoWReqCCReceiver);
        cc::daddy::CustomDaddyPorts::sm_HUselSVSModeDaddy_SenderPort.connect(m_huselSVSModeCCReceiver);
        m_connected = true;
    }
    bool m_connected;
    ::daddy::TLatestReceiverPort< cc::daddy::HUShoWReqDaddy_t > m_huShoWReqCCReceiver;
    ::daddy::TLatestReceiverPort< cc::daddy::HUselSVSModeDaddy_t > m_huselSVSModeCCReceiver;
};
static GetHUSettingCommand g_getHUSettingCommand;

//!
//! Get current view ID and Zoom status to HU
//!
class GetCurrentViewCommand : public pc::util::cli::CommandCallback
{
public:
    GetCurrentViewCommand()
        : m_connected(false) // PRQA S 4050
    {
    }
    ~GetCurrentViewCommand() override
    {
        if (m_connected)
        {
            cc::daddy::CustomDaddyPorts::sm_SVSCurrentViewDaddy_SenderPort.disconnect(m_SVSCurrentViewCCReceiver);
            cc::daddy::CustomDaddyPorts::sm_SVSzoomStsDaddy_SenderPort.disconnect(m_SVSzoomStsCCReceiver);
        }
    }
    bool invoke(std::istream&, std::ostream& f_output, const pc::util::cli::CommandLineInterface&) override  // PRQA S 6040
    {
        if (!m_connected)
        {
            connect();
        }
        bool l_result = false;
        m_SVSCurrentViewCCReceiver.update();
        m_SVSzoomStsCCReceiver.update();
        const cc::daddy::SVSCurrentViewDaddy_t* l_SVSCurrentViewDaddy = m_SVSCurrentViewCCReceiver.getData();
        const cc::daddy::SVSzoomStsDaddy_t* l_SVSzoomStsDaddy = m_SVSzoomStsCCReceiver.getData();
        if ( (l_SVSCurrentViewDaddy != nullptr) || (l_SVSzoomStsDaddy != nullptr) )
        {
            if (l_SVSCurrentViewDaddy != nullptr)
            {
                // f_output << "Current view: " << (static_cast<EScreenID> (l_SVSCurrentViewDaddy->m_Data)) << newline;
                f_output << " Current-view: ";  // PRQA S 3803
                switch (static_cast<vfc::int32_t>(l_SVSCurrentViewDaddy->m_Data))
                {
                    case 0:  f_output << "none_view";           break;  // PRQA S 3803
                    case 8:  f_output << "front_view";          break;  // PRQA S 3803
                    case 14: f_output << "rear_view";           break;  // PRQA S 3803
                    case 26: f_output << "left_view";           break;  // PRQA S 3803
                    case 27: f_output << "right_view";          break;  // PRQA S 3803
                    case 10: f_output << "front_junction_view"; break;  // PRQA S 3803
                    case 15: f_output << "rear_junction_view";  break;  // PRQA S 3803
                    case 29: f_output << "front_bumper_view";   break;  // PRQA S 3803
                    case 30: f_output << "rear_bumper_view";    break;  // PRQA S 3803
                    case 47: f_output << "full_screen_view";    break;  // PRQA S 3803
                    case 31: f_output << "front_wheel_view";    break;  // PRQA S 3803
                    case 45: f_output << "rear_wheel_view";     break;  // PRQA S 3803
                    case 52: f_output << "3d_front_view";       break;  // PRQA S 3803
                    case 38: f_output << "3d_rear_view";        break;  // PRQA S 3803
                    case 39: f_output << "3d_left_view";        break;  // PRQA S 3803
                    case 53: f_output << "3d_right_view";       break;  // PRQA S 3803
                    case 32: f_output << "3d_front_left_view";  break;  // PRQA S 3803
                    case 34: f_output << "3d_rear_left_view";   break;  // PRQA S 3803
                    case 33: f_output << "3d_front_right_view"; break;  // PRQA S 3803
                    case 35: f_output << "3d_rear_right_view";  break;  // PRQA S 3803
                    default: f_output << "none_view";           break;  // PRQA S 3803
                }
            }
            if(l_SVSzoomStsDaddy != nullptr)
            {
                // f_output << " Zoom-Sts: Lev_" << (l_SVSzoomStsDaddy->m_Data) << newline;
                f_output << " Zoom-Sts: ";  // PRQA S 3803
                switch (static_cast<vfc::int32_t>(l_SVSzoomStsDaddy->m_Data))
                {
                    case 0:  f_output << "Lev_0";           break;  // PRQA S 3803
                    case 1:  f_output << "Lev_1";           break;  // PRQA S 3803
                    case 2:  f_output << "Lev_2";           break;  // PRQA S 3803
                    case 3:  f_output << "Lev_3";           break;  // PRQA S 3803
                    case 4:  f_output << "Lev_4";           break;  // PRQA S 3803
                    case 5:  f_output << "Lev_5";           break;  // PRQA S 3803
                    case 6:  f_output << "Lev_6";           break;  // PRQA S 3803
                    case 7:  f_output << "Lev_7";           break;  // PRQA S 3803
                    default: f_output << "Lev_0";           break;  // PRQA S 3803
                }
            }
            l_result = true;
        }
        else
        {
            f_output << "Current view: Oh No!";  // PRQA S 3803
            l_result = false;
        }
        f_output << newline;  // PRQA S 3803
        m_SVSCurrentViewCCReceiver.cleanup();
        m_SVSzoomStsCCReceiver.cleanup();
        return l_result;
    }
    void getDescription(std::ostream& f_output) const override
    {
        f_output << "Qurey the current view ID and ZoomSts" << newline;  // PRQA S 3803
    }
private:

    //! Copy constructor is not permitted.
    GetCurrentViewCommand (const GetCurrentViewCommand& other); // = delete
    //! Copy assignment operator is not permitted.
    GetCurrentViewCommand& operator=(const GetCurrentViewCommand& other); // = delete

    void connect()
    {
        cc::daddy::CustomDaddyPorts::sm_SVSCurrentViewDaddy_SenderPort.connect(m_SVSCurrentViewCCReceiver);
        cc::daddy::CustomDaddyPorts::sm_SVSzoomStsDaddy_SenderPort.connect(m_SVSzoomStsCCReceiver);
        m_connected = true;
    }
    bool m_connected;
    ::daddy::TLatestReceiverPort< cc::daddy::SVSCurrentViewDaddy_t > m_SVSCurrentViewCCReceiver;
    ::daddy::TLatestReceiverPort< cc::daddy::SVSzoomStsDaddy_t > m_SVSzoomStsCCReceiver;
};
static GetCurrentViewCommand g_getCurrentViewCommand;

//!
//! Get camera view status
//!
class GetCamViewStsCommand : public pc::util::cli::CommandCallback
{
public:
    GetCamViewStsCommand()
        : m_connected(false) // PRQA S 4050
    {
    }
    ~GetCamViewStsCommand() override
    {
        if (m_connected)
        {
            cc::daddy::CustomDaddyPorts::sm_SVSFrViewStsDaddy_SenderPort.disconnect(m_svsFrViewStsCCReceiver);
            cc::daddy::CustomDaddyPorts::sm_SVSLeViewStsDaddy_SenderPort.disconnect(m_svsLeViewStsCCReceiver);
            cc::daddy::CustomDaddyPorts::sm_SVSReViewStsDaddy_SenderPort.disconnect(m_svsReViewStsCCReceiver);
            cc::daddy::CustomDaddyPorts::sm_SVSRiViewStsDaddy_SenderPort.disconnect(m_svsRiViewStsCCReceiver);
            cc::daddy::CustomDaddyPorts::sm_SVSUnavlMsgsDaddy_SenderPort.disconnect(m_svsUnavlMsgsCCReceiver);
        }
    }
    bool invoke(std::istream&, std::ostream& f_output, const pc::util::cli::CommandLineInterface&) override  // PRQA S 6040  // PRQA S 6041
    {
        if (!m_connected)
        {
            connect();
        }
        bool l_result = false;
        m_svsFrViewStsCCReceiver.update();
        m_svsLeViewStsCCReceiver.update();
        m_svsReViewStsCCReceiver.update();
        m_svsRiViewStsCCReceiver.update();
        m_svsUnavlMsgsCCReceiver.update();
        const cc::daddy::SVSViewStsDaddy_t* l_svsFrViewStsDaddy = m_svsFrViewStsCCReceiver.getData();
        const cc::daddy::SVSViewStsDaddy_t* l_svsLeViewStsDaddy = m_svsLeViewStsCCReceiver.getData();
        const cc::daddy::SVSViewStsDaddy_t* l_svsReViewStsDaddy = m_svsReViewStsCCReceiver.getData();
        const cc::daddy::SVSViewStsDaddy_t* l_svsRiViewStsDaddy = m_svsRiViewStsCCReceiver.getData();
        const cc::daddy::SVSUnavlMsgsDaddy_t* l_svsUnavlMsgsDaddy = m_svsUnavlMsgsCCReceiver.getData();
        f_output << "SVS CamView state: ";  // PRQA S 3803
        if ((l_svsFrViewStsDaddy != nullptr)
         || (l_svsLeViewStsDaddy != nullptr)
         || (l_svsReViewStsDaddy != nullptr)
         || (l_svsRiViewStsDaddy != nullptr)
         || (l_svsUnavlMsgsDaddy != nullptr)
        )
        {
            if (l_svsFrViewStsDaddy != nullptr)
            {
                f_output << " FrViewSts ";  // PRQA S 3803
                switch (static_cast<vfc::int32_t>(l_svsFrViewStsDaddy->m_Data))
                {
                    case 0: f_output << "av"; break;  // PRQA S 3803
                    case 1: f_output << "unav"; break;  // PRQA S 3803
                    default: f_output << l_svsFrViewStsDaddy->m_Data; break;  // PRQA S 3803
                }
            }
            if (l_svsLeViewStsDaddy != nullptr)
            {
                f_output << "; LeViewSts ";  // PRQA S 3803
                switch (static_cast<vfc::int32_t>(l_svsLeViewStsDaddy->m_Data))
                {
                    case 0: f_output << "av"; break;  // PRQA S 3803
                    case 1: f_output << "unav"; break;  // PRQA S 3803
                    default: f_output << l_svsLeViewStsDaddy->m_Data; break;  // PRQA S 3803
                }
            }
            if (l_svsReViewStsDaddy != nullptr)
            {
                f_output << "; ReViewSts ";  // PRQA S 3803
                switch (static_cast<vfc::int32_t>(l_svsReViewStsDaddy->m_Data))
                {
                    case 0: f_output << "av"; break;  // PRQA S 3803
                    case 1: f_output << "unav"; break;  // PRQA S 3803
                    default: f_output << l_svsReViewStsDaddy->m_Data; break;  // PRQA S 3803
                }
            }
            if (l_svsRiViewStsDaddy != nullptr)
            {
                f_output << "; RiViewSts ";  // PRQA S 3803
                switch (static_cast<vfc::int32_t>(l_svsRiViewStsDaddy->m_Data))
                {
                    case 0: f_output << "av"; break;  // PRQA S 3803
                    case 1: f_output << "unav"; break;  // PRQA S 3803
                    default: f_output << l_svsRiViewStsDaddy->m_Data; break;  // PRQA S 3803
                }
            }
            if (l_svsUnavlMsgsDaddy != nullptr)
            {
                f_output << "; UnavlMsgs ";  // PRQA S 3803
                switch (static_cast<vfc::int32_t>(l_svsUnavlMsgsDaddy->m_Data))
                {
                    case static_cast<vfc::int32_t>(cc::target::common::ESVSUnavlMsgs::ESVSUNAVLMSG_NONE)          : f_output << "av"; break;  // PRQA S 3803
                    case static_cast<vfc::int32_t>(cc::target::common::ESVSUnavlMsgs::ESVSUNAVLMSG_VIDOUT_ERROR)  : f_output << "vidouterror"; break;  // PRQA S 3803
                    case static_cast<vfc::int32_t>(cc::target::common::ESVSUnavlMsgs::ESVSUNAVLMSG_IGN_OFF)       : f_output << "ignoff"; break;  // PRQA S 3803
                    case static_cast<vfc::int32_t>(cc::target::common::ESVSUnavlMsgs::ESVSUNAVLMSG_ALLCAM_ERROR)  : f_output << "allcamerror"; break;  // PRQA S 3803
                    case static_cast<vfc::int32_t>(cc::target::common::ESVSUnavlMsgs::ESVSUNAVLMSG_SPEED_TOO_HIGH): f_output << "spdtoohigh"; break;  // PRQA S 3803
                    default                         : f_output << static_cast<vfc::uint8_t>(l_svsUnavlMsgsDaddy->m_Data); break;  // PRQA S 3803
                }
            }
            l_result = true;
        }
        else
        {
            f_output << "no CamViewSts available";  // PRQA S 3803
            l_result = false;
        }
        f_output << newline;  // PRQA S 3803
        m_svsFrViewStsCCReceiver.cleanup();
        m_svsLeViewStsCCReceiver.cleanup();
        m_svsReViewStsCCReceiver.cleanup();
        m_svsRiViewStsCCReceiver.cleanup();
        m_svsUnavlMsgsCCReceiver.cleanup();
        return l_result;
    }
    void getDescription(std::ostream& f_output) const override
    {
        f_output << "Qurey the current CamViewSts state" << newline;  // PRQA S 3803
    }
private:

    //! Copy constructor is not permitted.
    GetCamViewStsCommand (const GetCamViewStsCommand& other); // = delete
    //! Copy assignment operator is not permitted.
    GetCamViewStsCommand& operator=(const GetCamViewStsCommand& other); // = delete

    void connect()
    {
        cc::daddy::CustomDaddyPorts::sm_SVSFrViewStsDaddy_SenderPort.connect(m_svsFrViewStsCCReceiver);
        cc::daddy::CustomDaddyPorts::sm_SVSLeViewStsDaddy_SenderPort.connect(m_svsLeViewStsCCReceiver);
        cc::daddy::CustomDaddyPorts::sm_SVSReViewStsDaddy_SenderPort.connect(m_svsReViewStsCCReceiver);
        cc::daddy::CustomDaddyPorts::sm_SVSRiViewStsDaddy_SenderPort.connect(m_svsRiViewStsCCReceiver);
        cc::daddy::CustomDaddyPorts::sm_SVSUnavlMsgsDaddy_SenderPort.connect(m_svsUnavlMsgsCCReceiver);
        m_connected = true;
    }
    bool m_connected;
    ::daddy::TLatestReceiverPort< cc::daddy::SVSViewStsDaddy_t > m_svsFrViewStsCCReceiver;
    ::daddy::TLatestReceiverPort< cc::daddy::SVSViewStsDaddy_t > m_svsLeViewStsCCReceiver;
    ::daddy::TLatestReceiverPort< cc::daddy::SVSViewStsDaddy_t > m_svsReViewStsCCReceiver;
    ::daddy::TLatestReceiverPort< cc::daddy::SVSViewStsDaddy_t > m_svsRiViewStsCCReceiver;
    ::daddy::TLatestReceiverPort< cc::daddy::SVSUnavlMsgsDaddy_t > m_svsUnavlMsgsCCReceiver;
};
static GetCamViewStsCommand g_getCamViewStsCommand;

#if USE_RADAR_WALL
#else
//!
//! SetObstacleCommand
//!
class SetObstacleCommand : public pc::util::cli::SetUltrasonicCommand
{
protected:

    static vfc::uint16_t getZoneCount(vfc::uint16_t f_sector)
    {
        switch (f_sector)
        {
            case USS_SECTOR_FRONT: return 4u;
            case USS_SECTOR_RIGHT: return 4u;
            case USS_SECTOR_REAR:  return 4u;
            case USS_SECTOR_LEFT:  return 4u;
            default:
                break;
        }
        return 0u;
    }

    vfc::uint16_t getZoneIndex(vfc::uint16_t f_sector, vfc::uint16_t f_sectorLocalZoneIndex) const override
    {
        vfc::int32_t l_zoneIndex = 0;
        switch (f_sector)
        {
            case USS_SECTOR_FRONT:
                l_zoneIndex = 1;
                break;
            case USS_SECTOR_RIGHT:
                l_zoneIndex = 13;
                break;
            case USS_SECTOR_REAR:
                l_zoneIndex = 9;
                break;
            case USS_SECTOR_LEFT:
                l_zoneIndex = 5;
                break;
            default:
                break;
        }
        l_zoneIndex -= static_cast<vfc::int32_t>(f_sectorLocalZoneIndex);
        if (0 > l_zoneIndex)
        {
        l_zoneIndex = static_cast<vfc::int32_t> (cc::target::sysconf::E_ULTRASONIC_NUM_ZONES) + l_zoneIndex;
        }
        return static_cast<vfc::uint16_t> (l_zoneIndex);
    }

    void setUssData(const DistanceList& f_distances, const OnPathList& f_onPathHints, const FlagList& f_flags, vfc::int32_t f_sector) override
    {
        //pc::util::cli::SetUltrasonicCommand::setUssData(f_distances, f_onPathHints, f_flags, f_sector);
        // Use this logic instead for the 16 zone layout:
        auto& l_ussData = pc::daddy::BaseDaddyPorts::sm_ultrasonicDataDaddySenderPort.reserveLastDelivery();
        vfc::uint16_t l_zoneCount = getZoneCount(static_cast<vfc::uint16_t>(f_sector));
        for (vfc::uint16_t i = 0u; i < l_zoneCount; ++i)
        {
        vfc::uint16_t l_zoneIndex = getZoneIndex(static_cast<vfc::uint16_t>(f_sector), i);
        l_ussData.m_Data[l_zoneIndex].setDistance(f_distances[i+2u]);
        l_ussData.m_Data[l_zoneIndex].setOnPath(f_flags[i+2u]);
        l_ussData.m_Data[l_zoneIndex].setOnPath(f_onPathHints[i+2u]);
        }
        pc::daddy::BaseDaddyPorts::sm_ultrasonicDataDaddySenderPort.deliver();
    }

};

static SetObstacleCommand g_setObstacleCommand;
#endif

//!
//! SetCustomObstacleCommand
//!
class SetCustomObstacleCommand : public pc::util::cli::SetUltrasonicCommand
{
#if USE_RADAR_WALL
public:

    SetCustomObstacleCommand()
        : pc::util::cli::SetUltrasonicCommand(
        12u, //! f_numZonesFrontRear
        8u) //! f_numZonesLeftRight
    {
    }

    protected:

    virtual void setUssData(const DistanceList& f_distances, const OnPathList& f_onPathHints, const FlagList& f_flags, int f_sector) override
    {
        pc::util::cli::SetUltrasonicCommand::setUssData(f_distances, f_onPathHints, f_flags, f_sector);
    }
#else
protected:

    static vfc::uint16_t getZoneCount(vfc::uint16_t f_sector)
    {
        switch (f_sector)
        {
            case USS_SECTOR_FRONT: return 4u;
            case USS_SECTOR_RIGHT: return 4u;
            case USS_SECTOR_REAR:  return 4u;
            case USS_SECTOR_LEFT:  return 4u;
            default:
                break;
        }
        return 0u;
    }

    vfc::uint16_t getZoneIndex(vfc::uint16_t f_sector, vfc::uint16_t f_sectorLocalZoneIndex) const override
    {
        vfc::int32_t l_zoneIndex = 0;
        switch (f_sector)
        {
            case USS_SECTOR_FRONT:
                l_zoneIndex = 1;
                break;
            case USS_SECTOR_RIGHT:
                l_zoneIndex = 13;
                break;
            case USS_SECTOR_REAR:
                l_zoneIndex = 9;
                break;
            case USS_SECTOR_LEFT:
                l_zoneIndex = 5;
                break;
            default:
                break;
        }
        l_zoneIndex -= static_cast<vfc::int32_t>(f_sectorLocalZoneIndex);
        if (0 > l_zoneIndex)
        {
        l_zoneIndex = static_cast<vfc::int32_t> (cc::target::sysconf::E_ULTRASONIC_NUM_ZONES) + l_zoneIndex;
        }
        return static_cast<vfc::uint16_t> (l_zoneIndex);
    }

    void setUssData(const DistanceList& f_distances, const OnPathList& f_onPathHints, const FlagList& f_flags, vfc::int32_t f_sector) override
    {
        //pc::util::cli::SetUltrasonicCommand::setUssData(f_distances, f_onPathHints, f_flags, f_sector);
        // Use this logic instead for the 16 zone layout:
        pc::daddy::UltrasonicDataDaddy& l_ussData = cc::daddy::CustomDaddyPorts::sm_customUltrasonicDataDaddySenderPort.reserveLastDelivery();
        vfc::uint16_t l_zoneCount = getZoneCount(static_cast<vfc::uint16_t>(f_sector));
        for (vfc::uint16_t i = 0u; i < l_zoneCount; ++i)
        {
        vfc::uint16_t l_zoneIndex = getZoneIndex(static_cast<vfc::uint16_t>(f_sector), i);
        l_ussData.m_Data[l_zoneIndex].setDistance(f_distances[i+2u]);
        l_ussData.m_Data[l_zoneIndex].setOnPath(f_flags[i+2u]);
        l_ussData.m_Data[l_zoneIndex].setOnPath(f_onPathHints[i+2u]);
        }
        cc::daddy::CustomDaddyPorts::sm_customUltrasonicDataDaddySenderPort.deliver();
    }
#endif
};

static SetCustomObstacleCommand g_setCustomObstacleCommand;

//!
//! Set Parking Type Command
//!
typedef std::array<pc::util::cli::IntValueNamePair, 3> ParkTypeMap;
const ParkTypeMap g_parkTypeMapping = {
  static_cast<vfc::int32_t>(cc::target::common::PARKTYPE_None),           "None",
  static_cast<vfc::int32_t>(cc::target::common::PARKTYPE_APA),            "APA",
  static_cast<vfc::int32_t>(cc::target::common::PARKTYPE_RPA),            "RPA"
};

class SetParkTypeCommand : public pc::util::cli::EnumSignalSetter<ParkTypeMap>
{
public:

    explicit SetParkTypeCommand(const ParkTypeMap& f_mapping)
        : pc::util::cli::EnumSignalSetter<ParkTypeMap>("Park Type", f_mapping)
    {
    }

    void sendSignal(vfc::int32_t f_mode) override
    {
        cc::daddy::ParkTypeDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_ParkTypeDaddy_SenderPort.reserve();
        l_container.m_Data = static_cast<cc::target::common::EPARKTypeR2L>(f_mode);      // PRQA S 3013
        cc::daddy::CustomDaddyPorts::sm_ParkTypeDaddy_SenderPort.deliver();
        XLOG_INFO_OS(g_COMSocketContext) << "Setting Park Type to " << f_mode << XLOG_ENDL;//PRQA S 4060
    }
};
static SetParkTypeCommand g_setParkTypeCommand(g_parkTypeMapping);

//!
//! Set Parking Type variant Command
//!
typedef std::array<pc::util::cli::IntValueNamePair, 4> ParkTypeVariantMap;
const ParkTypeVariantMap g_parkTypeVariantMapping = {
  static_cast<vfc::int32_t>(PARKTYPECONFIG_NONE),           "None", // PRQA S 3080
  static_cast<vfc::int32_t>(PARKTYPECONFIG_APA),            "APA", // PRQA S 3080
  static_cast<vfc::int32_t>(PARKTYPECONFIG_RPA),            "RPA", // PRQA S 3080
  static_cast<vfc::int32_t>(PARKTYPECONFIG_APA_RPA),        "Both" // PRQA S 3080
};

class SetParkTypeVariantCommand : public pc::util::cli::EnumSignalSetter<ParkTypeVariantMap>
{
public:

    explicit SetParkTypeVariantCommand(const ParkTypeVariantMap& f_mapping)
        : pc::util::cli::EnumSignalSetter<ParkTypeVariantMap>("Park Type Variant", f_mapping)
    {
    }

    void sendSignal(vfc::int32_t f_mode) override
    {
        cc::daddy::ParkTypeVariantDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_ParkTypeVariantDaddy_SenderPort.reserve();
        l_container.m_Data = static_cast<cc::target::common::EPARKTypeVariantR2L>(f_mode);       // PRQA S 3013
        cc::daddy::CustomDaddyPorts::sm_ParkTypeVariantDaddy_SenderPort.deliver();
        XLOG_INFO_OS(g_COMSocketContext) << "Setting Park Type Variant to " << f_mode << XLOG_ENDL;//PRQA S 4060
    }
};
static SetParkTypeVariantCommand g_setParkTypeVariantCommand(g_parkTypeVariantMapping);


//!
//! Set Parking Mode Command
//!
typedef std::array<pc::util::cli::IntValueNamePair, 4> ParkModeMap;
const ParkModeMap g_parkModeMapping = {
  static_cast<vfc::int32_t>(cc::target::common::PARKMODE_None),                    "None",
  static_cast<vfc::int32_t>(cc::target::common::PARKMODE_PPSC_Park_In),            "PPSC_Park_In",
  static_cast<vfc::int32_t>(cc::target::common::PARKMODE_CPSC_Park_In),            "CPSC_Park_In",
  static_cast<vfc::int32_t>(cc::target::common::PARKMODE_Park_Out),                "Park_Out"
};

class SetParkModeCommand : public pc::util::cli::EnumSignalSetter<ParkModeMap>
{
public:

    explicit SetParkModeCommand(const ParkModeMap& f_mapping)
        : pc::util::cli::EnumSignalSetter<ParkModeMap>("Park Mode", f_mapping)
    {
    }

    void sendSignal(vfc::int32_t f_mode) override
    {
        cc::daddy::ParkModeDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_ParkModeDaddy_SenderPort.reserve();
        l_container.m_Data = static_cast<cc::target::common::EPARKModeR2L>(f_mode);      // PRQA S 3013
        cc::daddy::CustomDaddyPorts::sm_ParkModeDaddy_SenderPort.deliver();
        XLOG_INFO_OS(g_COMSocketContext) << "Setting Park Mode to " << f_mode << XLOG_ENDL;//PRQA S 4060
    }
};
static SetParkModeCommand g_setParkModeCommand(g_parkModeMapping);

//!
//! Set Parking Mode Command
//!
typedef std::array<pc::util::cli::IntValueNamePair, 3> APAPARKMODEMap;
const APAPARKMODEMap g_APAPARKMODEMapping = {
  static_cast<vfc::int32_t>(cc::target::common::EAPAPARKMODE::APAPARKMODE_IDLE),           "IDLE",
  static_cast<vfc::int32_t>(cc::target::common::EAPAPARKMODE::APAPARKMODE_APA),            "APA",
  static_cast<vfc::int32_t>(cc::target::common::EAPAPARKMODE::APAPARKMODE_RPA),            "RPA"

};

class SetAPAPARKMODECommand : public pc::util::cli::EnumSignalSetter<APAPARKMODEMap>
{
public:

    explicit SetAPAPARKMODECommand(const APAPARKMODEMap& f_mapping)
        : pc::util::cli::EnumSignalSetter<APAPARKMODEMap>("PARK MODE", f_mapping)
    {
    }

    void sendSignal(vfc::int32_t f_mode) override
    {
        cc::daddy::ParkAPAPARKMODE_t& l_container = cc::daddy::CustomDaddyPorts::sm_ParkAPAPARKMODE_SenderPort.reserve();
        l_container.m_Data = static_cast<cc::target::common::EAPAPARKMODE>(f_mode);      // PRQA S 3013
        cc::daddy::CustomDaddyPorts::sm_ParkAPAPARKMODE_SenderPort.deliver();
        XLOG_INFO_OS(g_COMSocketContext) << "Setting PARK MODE to " << f_mode << XLOG_ENDL;//PRQA S 4060
    }
};
static SetAPAPARKMODECommand g_setAPAPARKMODECommand(g_APAPARKMODEMapping);

//!
//! Set RPA Avaliable Command
//!
typedef std::array<pc::util::cli::IntValueNamePair, 2> RPAAvaliableMap;
const RPAAvaliableMap g_RPAAvaliableMapping = {
  static_cast<vfc::int32_t>(cc::target::common::RPAAvailable::RPA_NotAvailable),           "RPA Not Available",
  static_cast<vfc::int32_t>(cc::target::common::RPAAvailable::RPA_Available),              "RPA Available",
};

class SetRPAAvaliableCommand : public pc::util::cli::EnumSignalSetter<RPAAvaliableMap>
{
public:

    explicit SetRPAAvaliableCommand(const RPAAvaliableMap& f_mapping)
        : pc::util::cli::EnumSignalSetter<RPAAvaliableMap>("RPA Avaliable", f_mapping)
    {
    }

    void sendSignal(vfc::int32_t f_mode) override
    {
        cc::daddy::ParkRPAAvaliableDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_ParkRPAAvaliableDaddy_SenderPort.reserve();
        l_container.m_Data = static_cast<cc::target::common::RPAAvailable>(f_mode);      // PRQA S 3013
        cc::daddy::CustomDaddyPorts::sm_ParkRPAAvaliableDaddy_SenderPort.deliver();
        XLOG_INFO_OS(g_COMSocketContext) << "Setting RPA Avaliable to " << f_mode << XLOG_ENDL;//PRQA S 4060
    }
};
static SetRPAAvaliableCommand g_setRPAAvaliableCommand(g_RPAAvaliableMapping);



//!
//! Set Parking Mode Command
//!
typedef std::array<pc::util::cli::IntValueNamePair, 3> ParkngTypeSeldMap;
const ParkngTypeSeldMap g_ParkngTypeSeldCommandMapping = {
  static_cast<vfc::int32_t>(cc::target::common::EParkngTypeSeld::PARKING_NONE),          "None",
  static_cast<vfc::int32_t>(cc::target::common::EParkngTypeSeld::PARKING_IN),            "cc::target::common::EParkngTypeSeld::PARKING_IN",
  static_cast<vfc::int32_t>(cc::target::common::EParkngTypeSeld::PARKING_OUT),           "cc::target::common::EParkngTypeSeld::PARKING_OUT",
};

class SetParkngTypeSeldCommand : public pc::util::cli::EnumSignalSetter<ParkngTypeSeldMap>
{
public:

    explicit SetParkngTypeSeldCommand(const ParkngTypeSeldMap& f_mapping)
        : pc::util::cli::EnumSignalSetter<ParkngTypeSeldMap>("Parking Type Seld", f_mapping)
    {
    }

    void sendSignal(vfc::int32_t f_mode) override
    {
        cc::daddy::ParkParkngTypeSeld_t& l_container = cc::daddy::CustomDaddyPorts::sm_ParkParkngTypeSeldDaddy_SenderPort.reserve();
        l_container.m_Data = static_cast<cc::target::common::EParkngTypeSeld>(f_mode);      // PRQA S 3013
        cc::daddy::CustomDaddyPorts::sm_ParkParkngTypeSeldDaddy_SenderPort.deliver();
        XLOG_INFO_OS(g_COMSocketContext) << "Setting Parking Type Seld to " << f_mode << XLOG_ENDL;//PRQA S 4060
    }
};
static SetParkngTypeSeldCommand g_setParkngTypeSeldCommand(g_ParkngTypeSeldCommandMapping);


//!
//! Set Parking Status Command
//!
typedef std::array<pc::util::cli::IntValueNamePair, 9> ParkStatusMap;
const ParkStatusMap g_parkStatusMapping = {
    static_cast<vfc::int32_t>(cc::target::common::PARK_Off),              "Off",
    static_cast<vfc::int32_t>(cc::target::common::PARK_Standby),          "Standby",
    static_cast<vfc::int32_t>(cc::target::common::PARK_Searching),        "Searching",
    static_cast<vfc::int32_t>(cc::target::common::PARK_Guidance_active),  "GuidActive",
    static_cast<vfc::int32_t>(cc::target::common::PARK_Guidance_suspend), "GuidSuspend",
    static_cast<vfc::int32_t>(cc::target::common::PARK_Completed),        "Completed",
    static_cast<vfc::int32_t>(cc::target::common::PARK_Failure),          "Failure",
    static_cast<vfc::int32_t>(cc::target::common::PARK_Terminated),       "Terminated",
    static_cast<vfc::int32_t>(cc::target::common::PARK_AssistStandby),    "Confirming"
};

class SetParkStatusCommand : public pc::util::cli::EnumSignalSetter<ParkStatusMap>
{
public:

    explicit SetParkStatusCommand(const ParkStatusMap& f_mapping)
        : pc::util::cli::EnumSignalSetter<ParkStatusMap>("Park Status", f_mapping)
    {
    }

    void sendSignal(vfc::int32_t f_mode) override
    {
        cc::daddy::ParkStatusDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_ParkStatusDaddy_SenderPort.reserve();
        l_container.m_Data = static_cast<cc::target::common::EPARKStatusR2L>(f_mode);       // PRQA S 3013
        cc::daddy::CustomDaddyPorts::sm_ParkStatusDaddy_SenderPort.deliver();
        XLOG_INFO_OS(g_COMSocketContext) << "Setting Park Status to " << f_mode << XLOG_ENDL;//PRQA S 4060
    }
};
static SetParkStatusCommand g_setParkStatusCommand(g_parkStatusMapping);

//!
//! Set Parking Side Command
//!
typedef std::array<pc::util::cli::IntValueNamePair, 3> ParkSideMap;
const ParkSideMap g_parkSideMapping = {
    static_cast<vfc::int32_t>(cc::target::common::PARKSIDE_None),         "None",
    static_cast<vfc::int32_t>(cc::target::common::PARKSIDE_Left),         "Left",
    static_cast<vfc::int32_t>(cc::target::common::PARKSIDE_Right),        "Right"
};

class SetParkSideCommand : public pc::util::cli::EnumSignalSetter<ParkSideMap>
{
public:

    explicit SetParkSideCommand(const ParkSideMap& f_mapping)
        : pc::util::cli::EnumSignalSetter<ParkSideMap>("Park Side", f_mapping)
    {
    }

    void sendSignal(vfc::int32_t f_mode) override
    {
        cc::daddy::ParkSideDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_ParkSideDaddy_SenderPort.reserve();
        l_container.m_Data = static_cast<cc::target::common::EPARKSideR2L>(f_mode);        // PRQA S 3013
        cc::daddy::CustomDaddyPorts::sm_ParkSideDaddy_SenderPort.deliver();
        XLOG_INFO_OS(g_COMSocketContext) << "Setting Park Side to " << f_mode << XLOG_ENDL;//PRQA S 4060
    }
};
static SetParkSideCommand g_setParkSideCommand(g_parkSideMapping);

//!
//! Set Parking FunctionIndex Command
//!
typedef std::array<pc::util::cli::IntValueNamePair, 9> ParkFunctionIndMap;
const ParkFunctionIndMap g_parkFunctionIndMapping = {
    static_cast<vfc::int32_t>(cc::target::common::PARKFUC_Inactive),       "Inactive",
    static_cast<vfc::int32_t>(cc::target::common::PARKFUC_cPSC),           "cPSC",
    static_cast<vfc::int32_t>(cc::target::common::PARKFUC_pPSC),           "pPSC",
    static_cast<vfc::int32_t>(cc::target::common::PARKFUC_cPOC),           "cPOC",
    static_cast<vfc::int32_t>(cc::target::common::PARKFUC_pPOC),           "pPOC",
    static_cast<vfc::int32_t>(cc::target::common::PARKFUC_Remote_cPSC),    "Remote_cPSC",
    static_cast<vfc::int32_t>(cc::target::common::PARKFUC_Remote_pPSC),    "Remote_pPSC",
    static_cast<vfc::int32_t>(cc::target::common::PARKFUC_Remote_cPOC),    "Remote_cPOC",
    static_cast<vfc::int32_t>(cc::target::common::PARKFUC_Remote_pPOC),    "Remote_pPOC"
};

class SetParkFunctionIndCommand : public pc::util::cli::EnumSignalSetter<ParkFunctionIndMap>
{
public:

    explicit SetParkFunctionIndCommand(const ParkFunctionIndMap& f_mapping)
        : pc::util::cli::EnumSignalSetter<ParkFunctionIndMap>("Park FunctionInd", f_mapping)
    {
    }

    void sendSignal(vfc::int32_t f_mode) override
    {
        cc::daddy::ParkFunctionIndDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_ParkFunctionIndDaddy_SenderPort.reserve();
        l_container.m_Data = static_cast<cc::target::common::EPARKFunctionIndR2L>(f_mode);         // PRQA S 3013
        cc::daddy::CustomDaddyPorts::sm_ParkFunctionIndDaddy_SenderPort.deliver();
        XLOG_INFO_OS(g_COMSocketContext) << "Setting Park Function Index to " << f_mode << XLOG_ENDL;//PRQA S 4060
    }
};
static SetParkFunctionIndCommand g_setParkFunctionIndCommand(g_parkFunctionIndMapping);

//!
//! Set Parking Quit Index Command
//!
typedef std::array<pc::util::cli::IntValueNamePair, 32> ParkQuitIndMap;
const ParkQuitIndMap g_parkQuitIndMapping = {
        static_cast<vfc::int32_t>(cc::target::common::PARKQUIT_None)                           ,"cc::target::common::PARKQUIT_None                 ",
        static_cast<vfc::int32_t>(cc::target::common::PARKQUIT_EPSFailure)                     ,"PARKQUIT_EPSFailure           ",
        static_cast<vfc::int32_t>(cc::target::common::PARKQUIT_ESCFailure)                     ,"PARKQUIT_ESCFailure           ",
        static_cast<vfc::int32_t>(cc::target::common::PARKQUIT_SCUFailure)                     ,"SCUFailure                    ",
        static_cast<vfc::int32_t>(cc::target::common::PARKQUIT_SASFailure)                     ,"SASFailure                    ",
        static_cast<vfc::int32_t>(cc::target::common::PARKQUIT_APAFailure)                     ,"APAFailure                    ",
        static_cast<vfc::int32_t>(cc::target::common::PARKQUIT_InvalidVehicleSpeed)            ,"InvalidVehicleSpeed           ",
        static_cast<vfc::int32_t>(cc::target::common::PARKQUIT_RadarDirty)                     ,"RadarDirty                    ",
        static_cast<vfc::int32_t>(cc::target::common::PARKQUIT_SASNotCalibrated)               ,"SASNotCalibrated              ",
        static_cast<vfc::int32_t>(cc::target::common::PARKQUIT_ABSTCSESPACCAEBActive)          ,"ABSTCSESPACCAEBActive         ",
        static_cast<vfc::int32_t>(cc::target::common::PARKQUIT_EPBActive)                      ,"EPBActive                     ",
        static_cast<vfc::int32_t>(cc::target::common::PARKQUIT_DriverOverride)                 ,"DriverOverride                ",
        static_cast<vfc::int32_t>(cc::target::common::PARKQUIT_ChargingGunActive)              ,"ChargingGunActive             ",
        static_cast<vfc::int32_t>(cc::target::common::PARKQUIT_SpaceIsLimitedInParkOutMode)    ,"SpaceIsLimitedInParkOutMode   ",
        static_cast<vfc::int32_t>(cc::target::common::PARKQUIT_CurrentStepNumberOverThreshold) ,"CurrentStepNumberOverThreshold",
        static_cast<vfc::int32_t>(cc::target::common::PARKQUIT_InterruptNumberOverThreshold)   ,"InterruptNumberOverThreshold  ",
        static_cast<vfc::int32_t>(cc::target::common::PARKQUIT_APSTimeout)                     ,"APSTimeout                    ",
        static_cast<vfc::int32_t>(cc::target::common::PARKQUIT_VehicleSpeedOverthreshold)      ,"VehicleSpeedOverthreshold     ",
        static_cast<vfc::int32_t>(cc::target::common::PARKQUIT_RoutePlanningFailure)           ,"RoutePlanningFailure          ",
        static_cast<vfc::int32_t>(cc::target::common::PARKQUIT_BluetoothDisconnectionTimeout)  ,"BluetoothDisconnectionTimeout ",
        static_cast<vfc::int32_t>(cc::target::common::PARKQUIT_EPBFailure)                     ,"EPBFailure                    ",
        static_cast<vfc::int32_t>(cc::target::common::PARKQUIT_SeatBeltUnbuckle)               ,"SeatBeltUnbuckle              ",
        static_cast<vfc::int32_t>(cc::target::common::PARKQUIT_ExcessiveSlope)                 ,"Excessiveslope                ",
        static_cast<vfc::int32_t>(cc::target::common::PARKQUIT_VehicleBlock)                   ,"VehicleBlock                  ",
        static_cast<vfc::int32_t>(cc::target::common::PARKQUIT_SurroundView)                   ,"SurroundView                  ",
        static_cast<vfc::int32_t>(cc::target::common::PARKQUIT_DoorLock)                       ,"DoorLock                      ",
        static_cast<vfc::int32_t>(cc::target::common::PARKQUIT_TBOXFailureRPA)                 ,"TBOXFailureRPA                ",
        static_cast<vfc::int32_t>(cc::target::common::PARKQUIT_RemoteDeviceReasonRPA)          ,"RemoteDeviceReasonRPA         ",
        static_cast<vfc::int32_t>(cc::target::common::PARKQUIT_UnsafeBehavior)                 ,"UnsafeBehavior                ",
        static_cast<vfc::int32_t>(cc::target::common::PARKQUIT_DoorOpen)                       ,"DoorOpen                      ",
        static_cast<vfc::int32_t>(cc::target::common::PARKQUIT_ExternalECUFailure)             ,"ExternalECUFailure            ",
        static_cast<vfc::int32_t>(cc::target::common::PARKQUIT_OtherReason)                    ,"OtherReason                   ",
};

class SetParkQuitIndCommand : public pc::util::cli::EnumSignalSetter<ParkQuitIndMap>
{
public:

    explicit SetParkQuitIndCommand(const ParkQuitIndMap& f_mapping)
        : pc::util::cli::EnumSignalSetter<ParkQuitIndMap>("Park QuitInd", f_mapping)
    {
    }

    void sendSignal(vfc::int32_t f_mode) override
    {
        cc::daddy::ParkQuitIndDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_ParkQuitIndDaddy_SenderPort.reserve();
        l_container.m_Data = static_cast<cc::target::common::EPARKQuitIndR2L>(f_mode);    // PRQA S 3013
        cc::daddy::CustomDaddyPorts::sm_ParkQuitIndDaddy_SenderPort.deliver();
        XLOG_INFO_OS(g_COMSocketContext) << "Setting Park Quit Index to " << f_mode << XLOG_ENDL;//PRQA S 4060
    }
};
static SetParkQuitIndCommand g_setParkQuitIndCommand(g_parkQuitIndMapping);

//!
//! Set Parking Quit Index Command
//!
typedef std::array<pc::util::cli::IntValueNamePair, 11> ParkQuitIndExtMap;
const ParkQuitIndExtMap g_parkQuitIndExtMapping = {
        static_cast<vfc::int32_t>(cc::target::common::PARKQUITExt_Nofault)                           ,"Nofault                     ",
        static_cast<vfc::int32_t>(cc::target::common::PARKQUITExt_DisusHeightAdjust)                 ,"DisusHeightAdjust           ",
        static_cast<vfc::int32_t>(cc::target::common::PARKQUITExt_CurrentDiSusHeightNotSupport)      ,"CurrentDiSusHeightNotSupport",
        static_cast<vfc::int32_t>(cc::target::common::PARKQUITExt_TrailerHitchConnected)             ,"TrailerHitchConnected       ",
        static_cast<vfc::int32_t>(cc::target::common::PARKQUITExt_TemperatureSensorFault)            ,"TemperatureSensorFault      ",
        static_cast<vfc::int32_t>(cc::target::common::PARKQUITExt_ResponseTimeout)                   ,"ResponseTimeout             ",
        static_cast<vfc::int32_t>(cc::target::common::PARKQUITExt_BrakeSignalAbnormal)               ,"BrakeSignalAbnormal         ",
        static_cast<vfc::int32_t>(cc::target::common::PARKQUITExt_LowerTirePressure)                 ,"LowerTirePressure           ",
        static_cast<vfc::int32_t>(cc::target::common::PARKQUITExt_CollisionHappened)                 ,"CollisionHappened           ",
        static_cast<vfc::int32_t>(cc::target::common::PARKQUITExt_DriveModeUnsuitable)               ,"DriveModeUnsuitable         ",
        static_cast<vfc::int32_t>(cc::target::common::PARKQUITExt_Reserved)                          ,"Reserved                    "
};

class SetParkQuitIndExtCommand : public pc::util::cli::EnumSignalSetter<ParkQuitIndExtMap>
{
public:

    explicit SetParkQuitIndExtCommand(const ParkQuitIndExtMap& f_mapping)
        : pc::util::cli::EnumSignalSetter<ParkQuitIndExtMap>("Park QuitIndExt", f_mapping)
    {
    }

    void sendSignal(vfc::int32_t f_mode) override
    {
        cc::daddy::ParkQuitIndExtDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_ParkQuitIndExtDaddy_SenderPort.reserve();
        l_container.m_Data = static_cast<cc::target::common::EPARKQuitIndR2LExt>(f_mode);    // PRQA S 3013
        cc::daddy::CustomDaddyPorts::sm_ParkQuitIndExtDaddy_SenderPort.deliver();
        XLOG_INFO_OS(g_COMSocketContext) << "Setting Park Quit Ext Index to " << f_mode << XLOG_ENDL;    // PRQA S 4060
    }
};
namespace
{
    SetParkQuitIndExtCommand g_setParkQuitIndExtCommand(g_parkQuitIndExtMapping);
}


//!
//! Set Parking Recover Index Command
//!
typedef std::array<pc::util::cli::IntValueNamePair, 12> ParkRecoverIndMap;
const ParkRecoverIndMap g_parkRecoverIndMapping = {
    static_cast<vfc::int32_t>(cc::target::common::PARKREC_NoPrompt)            ,"NoPrompt            ",
    static_cast<vfc::int32_t>(cc::target::common::PARKREC_PauseCommand)        ,"PauseCommand        ",
    static_cast<vfc::int32_t>(cc::target::common::PARKREC_ObjectOnPath)        ,"ObjectOnPath        ",
    static_cast<vfc::int32_t>(cc::target::common::PARKREC_DoorOpen)            ,"DoorOpen            ",
    static_cast<vfc::int32_t>(cc::target::common::PARKREC_BrakePadal)          ,"BrakePadal          ",
    static_cast<vfc::int32_t>(cc::target::common::PARKREC_MirrorFold)          ,"MirrorFold          ",
    static_cast<vfc::int32_t>(cc::target::common::PARKREC_SeatBelt)            ,"SeatBelt            ",
    static_cast<vfc::int32_t>(cc::target::common::PARKREC_BlueTooth)           ,"BlueTooth           ",
    static_cast<vfc::int32_t>(cc::target::common::PARKREC_HoodOpen)            ,"HoodOpen            ",
    static_cast<vfc::int32_t>(cc::target::common::PARKREC_TrunkOpen)           ,"TrunkOpen           ",
    static_cast<vfc::int32_t>(cc::target::common::PARKREC_RearSideCommingCar)  ,"RearSideCommingCar  ",
    static_cast<vfc::int32_t>(cc::target::common::PARKREC_FrontSideCommingCar) ,"FrontSideCommingCar "
};

class SetParkRecoverIndCommand : public pc::util::cli::EnumSignalSetter<ParkRecoverIndMap>
{
public:

    explicit SetParkRecoverIndCommand(const ParkRecoverIndMap& f_mapping)
        : pc::util::cli::EnumSignalSetter<ParkRecoverIndMap>("Park RecoverInd", f_mapping)
    {
    }

    void sendSignal(vfc::int32_t f_mode) override
    {
        cc::daddy::ParkRecoverIndDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_ParkRecoverIndDaddy_SenderPort.reserve();
        l_container.m_Data = static_cast<cc::target::common::EPARKRecoverIndR2L>(f_mode);      // PRQA S 3013
        cc::daddy::CustomDaddyPorts::sm_ParkRecoverIndDaddy_SenderPort.deliver();
        XLOG_INFO_OS(g_COMSocketContext) << "Setting Park Recover Index to " << f_mode << XLOG_ENDL;//PRQA S 4060
    }
};
static SetParkRecoverIndCommand g_setParkRecoverIndCommand(g_parkRecoverIndMapping);

//!
//! Set Parking Driver Index Command
//!
typedef std::array<pc::util::cli::IntValueNamePair, 32> ParkDriverIndMap;
const ParkDriverIndMap g_parkDriverIndMapping = {
    static_cast<vfc::int32_t>(cc::target::common::PARKDRV_NoRequest),             "NoRequest",  //PRQA S 2427  //PRQA S 2428
    static_cast<vfc::int32_t>(cc::target::common::PARKDRV_ExpandedMirror),        "ExpandedMirror",
    static_cast<vfc::int32_t>(cc::target::common::PARKDRV_GearD),                 "GearD",
    static_cast<vfc::int32_t>(cc::target::common::PARKDRV_SlowDown),              "SlowDown",
    static_cast<vfc::int32_t>(cc::target::common::PARKDRV_SearchingProcess),      "SearchingProcess",
    static_cast<vfc::int32_t>(cc::target::common::PARKDRV_Stop),                  "Stop",
    static_cast<vfc::int32_t>(cc::target::common::PARKDRV_ConnectPhone),          "ConnectPhone",
    static_cast<vfc::int32_t>(cc::target::common::PARKDRV_EPBApplied),            "EPBApplied",
    static_cast<vfc::int32_t>(cc::target::common::PARKDRV_LeaveCar),              "LeaveCar",
    static_cast<vfc::int32_t>(cc::target::common::PARKDRV_CloseTrunk),            "CloseTrunk",
    static_cast<vfc::int32_t>(cc::target::common::PARKDRV_SmallParkSlot),         "SmallParkSlot",
    static_cast<vfc::int32_t>(cc::target::common::PARKDRV_CloseDoor),             "CloseDoor",
    static_cast<vfc::int32_t>(cc::target::common::PARKDRV_SeatBelt),              "SeatBelt",
    static_cast<vfc::int32_t>(cc::target::common::PARKDRV_SurroundView),          "SurroundView",
    static_cast<vfc::int32_t>(cc::target::common::PARKDRV_PressBrakePedal),       "PressBrakePedal",
    static_cast<vfc::int32_t>(cc::target::common::PARKDRV_Confirm_Press_DM),      "Confirm_Press_DM",
    static_cast<vfc::int32_t>(cc::target::common::PARKDRV_ReleaseBrake),          "ReleaseBrake",
    static_cast<vfc::int32_t>(cc::target::common::PARKDRV_ProcessBar),            "ProcessBar",
    static_cast<vfc::int32_t>(cc::target::common::PARKDRV_FunctionOff),           "FunctionOff",
    static_cast<vfc::int32_t>(cc::target::common::PARKDRV_POCDirecSelect),        "POCDirecSelect",
    static_cast<vfc::int32_t>(cc::target::common::PARKDRV_NoFrontObjectDetected), "NoFrontObjectDetected",
    static_cast<vfc::int32_t>(cc::target::common::PARKDRV_PSSelection),           "PSSelection",
    static_cast<vfc::int32_t>(cc::target::common::PARKDRV_ResponseTimeout),       "ResponseTimeout",
    static_cast<vfc::int32_t>(cc::target::common::PARKDRV_ExternalECUError),      "ExternalECUError",
    static_cast<vfc::int32_t>(cc::target::common::PARKDRV_CloseHood),             "CloseHood",
    static_cast<vfc::int32_t>(cc::target::common::PARKDRV_HeavyRain),             "HeavyRain",
    static_cast<vfc::int32_t>(cc::target::common::PARKDRV_Notice_Rolling),        "NoticeRolling"
};

class SetParkDriverIndCommand : public pc::util::cli::EnumSignalSetter<ParkDriverIndMap>
{
public:

    explicit SetParkDriverIndCommand(const ParkDriverIndMap& f_mapping)
        : pc::util::cli::EnumSignalSetter<ParkDriverIndMap>("Park DriverInd", f_mapping)
    {
    }

    void sendSignal(vfc::int32_t f_mode) override
    {
        cc::daddy::ParkDriverIndDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_ParkDriverIndDaddy_SenderPort.reserve();
        l_container.m_Data = static_cast<cc::target::common::EPARKDriverIndR2L>(f_mode);     //PRQA S 3013
        cc::daddy::CustomDaddyPorts::sm_ParkDriverIndDaddy_SenderPort.deliver();
        XLOG_INFO_OS(g_COMSocketContext) << "Setting Park Driver Index to " << f_mode << XLOG_ENDL;//PRQA S 4060
    }
};
static SetParkDriverIndCommand g_setParkDriverIndCommand(g_parkDriverIndMapping);


//!
//! Set Parking Driver Extend Index Command
//!
typedef std::array<pc::util::cli::IntValueNamePair, 10> ParkDriverIndExtMap;
const ParkDriverIndExtMap g_parkDriverIndExtMapping = {
    static_cast<vfc::int32_t>(cc::target::common::PARKDRVEXT_NoRequest),                               "No Request",
    static_cast<vfc::int32_t>(cc::target::common::PARKDRVEXT_PayAttentionToSurrounding),               "PayAttention To Surrounding",
    static_cast<vfc::int32_t>(cc::target::common::PARKDRVEXT_ParkingHasBeenCompleted),                 "Parking Has Been Completed",
    static_cast<vfc::int32_t>(cc::target::common::PARKDRVEXT_ConfirmTheParkingDirection),              "Confirm The Parking Direction",
    static_cast<vfc::int32_t>(cc::target::common::PARKDRVEXT_ParkingOutPayAttentionToSurrounding),     "Parking Out Pay Attention To Surrounding",
    static_cast<vfc::int32_t>(cc::target::common::PARKDRVEXT_FrontIsClear),                            "Front Is Clear",
    static_cast<vfc::int32_t>(cc::target::common::PARKDRVEXT_Reserved),                                "Reserved",
    static_cast<vfc::int32_t>(cc::target::common::PARKDRVEXT_SelectTheParkingMode),                    "Select The Parking Mode",
    static_cast<vfc::int32_t>(cc::target::common::PARKDRVEXT_ConnectToBluetooth),                      "Connect To Bluetooth",
    static_cast<vfc::int32_t>(cc::target::common::PARKDRVEXT_CleanTheCamera),                          "Clean The Camera"
};

class SetParkDriverIndExtCommand : public pc::util::cli::EnumSignalSetter<ParkDriverIndExtMap>
{
public:

    explicit SetParkDriverIndExtCommand(const ParkDriverIndExtMap& f_mapping)
        : pc::util::cli::EnumSignalSetter<ParkDriverIndExtMap>("Park DriverIndExt", f_mapping)
    {
    }

    void sendSignal(vfc::int32_t f_mode) override
    {
        cc::daddy::ParkDriverIndExtDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_ParkDriverIndExtDaddy_SenderPort.reserve();
        l_container.m_Data = static_cast<cc::target::common::EPARKDriverIndExtR2L>(f_mode);     //PRQA S 3013
        cc::daddy::CustomDaddyPorts::sm_ParkDriverIndExtDaddy_SenderPort.deliver();
        XLOG_INFO_OS(g_COMSocketContext) << "Setting Park Driver Extend Index to " << f_mode << XLOG_ENDL;//PRQA S 4060
    }
};
static SetParkDriverIndExtCommand g_setParkDriverIndExtCommand(g_parkDriverIndExtMapping);

//!
//! Set Park Driver IndSearch Index Command
//!
typedef std::array<pc::util::cli::IntValueNamePair, 6> ParkDriverIndSearchMap;
const ParkDriverIndSearchMap g_parkDriverIndSearchMapMapping = {
    static_cast<vfc::int32_t>(cc::target::common::PARKDRVSEARCH_NoRequest),                            "NoRequest",
    static_cast<vfc::int32_t>(cc::target::common::PARKDRVSEARCH_SlotSearching),                        "Slot Searching",
    static_cast<vfc::int32_t>(cc::target::common::PARKDRVSEARCH_WaitingForVehicleStop),                "Waiting For VehicleStop",
    static_cast<vfc::int32_t>(cc::target::common::PARKDRVSEARCH_WaitForVehicleSlowdown),               "Wait For Vehicle Slow down",
    static_cast<vfc::int32_t>(cc::target::common::PARKDRVSEARCH_WaitForDriverOperateGear),             "Wait For Driver Operate Gear",
    static_cast<vfc::int32_t>(cc::target::common::PARKDRVSEARCH_WaitForVehicleDriverConfirmPark),      "Wait For Vehicle Driver Confirm Park",
};

class SetParkDriverIndSearchCommand : public pc::util::cli::EnumSignalSetter<ParkDriverIndSearchMap>
{
public:

    explicit SetParkDriverIndSearchCommand(const ParkDriverIndSearchMap& f_mapping)
        : pc::util::cli::EnumSignalSetter<ParkDriverIndSearchMap>("Park Driver IndSearch", f_mapping)
    {
    }

    void sendSignal(vfc::int32_t f_mode) override
    {
        cc::daddy::ParkDriverIndSearchDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_ParkDriverIndSearchDaddy_SenderPort.reserve();
        l_container.m_Data = static_cast<cc::target::common::EPARKDriverIndSearchR2L>(f_mode);     //PRQA S 3013
        cc::daddy::CustomDaddyPorts::sm_ParkDriverIndSearchDaddy_SenderPort.deliver();
        XLOG_INFO_OS(g_COMSocketContext) << "Setting Park Driver IndSearch to " << f_mode << XLOG_ENDL;//PRQA S 4060
    }
};
static SetParkDriverIndSearchCommand g_setParkDriverIndSearchCommand(g_parkDriverIndSearchMapMapping);

//!
//! Set Park Out Side available Command
//!
class SetParkOutSideAvailCommand : public pc::util::cli::NumericSignalSetter<vfc::int32_t>
{
public:

    explicit SetParkOutSideAvailCommand()
        : pc::util::cli::NumericSignalSetter<vfc::int32_t>("Park OutSideAvl")
    {
    }

    void sendSignal(vfc::int32_t f_value) override
    {
        cc::daddy::ParkOutSideAvlDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_ParkOutSideAvlDaddy_SenderPort.reserve();
        l_container.m_Data = static_cast<bool>(f_value);
        cc::daddy::CustomDaddyPorts::sm_ParkOutSideAvlDaddy_SenderPort.deliver();
    }
};
static SetParkOutSideAvailCommand g_setParkOutSideAvailCommand;


//!
//! Set Req Release button Command
//!
class SetParkReqReleaseBtnCommand : public pc::util::cli::NumericSignalSetter<vfc::int32_t>
{
public:

    explicit SetParkReqReleaseBtnCommand()
        : pc::util::cli::NumericSignalSetter<vfc::int32_t>("Park ReqReleaseBtn")
    {
    }

    void sendSignal(vfc::int32_t f_value) override
    {
        cc::daddy::ParkReqReleaseBtnDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_ParkReqReleaseBtnDaddy_SenderPort.reserve();
        l_container.m_Data = static_cast<bool>(f_value);
        cc::daddy::CustomDaddyPorts::sm_ParkReqReleaseBtnDaddy_SenderPort.deliver();
    }
};
static SetParkReqReleaseBtnCommand g_setParkReqReleaseBtnCommand;

//!
//! Set park rpa driver selected command
//!
class SetParkRPADriverSelectedBtnCommand : public pc::util::cli::NumericSignalSetter<vfc::int32_t>
{
public:

    explicit SetParkRPADriverSelectedBtnCommand()
        : pc::util::cli::NumericSignalSetter<vfc::int32_t>("Park Driver RPA Selected")
    {
    }

    void sendSignal(vfc::int32_t f_value) override
    {
        cc::daddy::ParkRPADriverSelectedDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_ParkRPADriverSelectedDaddy_SenderPort.reserve();
        l_container.m_Data = static_cast<bool>(f_value);
        cc::daddy::CustomDaddyPorts::sm_ParkRPADriverSelectedDaddy_SenderPort.deliver();
    }
};
static SetParkRPADriverSelectedBtnCommand g_setParkRPADriverSelectedBtnCommand;

//!
//! Set POC front object exist or not
//!
typedef std::array<pc::util::cli::IntValueNamePair, 3> ParkFrontObjIndMap;
const ParkFrontObjIndMap g_parkFrontObjIndMapping = {
    static_cast<vfc::int32_t>(cc::target::common::PARKOBJ_NotExist),      "NotExist",
    static_cast<vfc::int32_t>(cc::target::common::PARKOBJ_Exist),         "Exist",
    static_cast<vfc::int32_t>(cc::target::common::PARKOBJ_Invalid),       "Invalid"
};

class SetParkFrontObjIndCommand : public pc::util::cli::EnumSignalSetter<ParkFrontObjIndMap>
{
public:

    explicit SetParkFrontObjIndCommand(const ParkFrontObjIndMap& f_mapping)
        : pc::util::cli::EnumSignalSetter<ParkFrontObjIndMap>("Park out front object ", f_mapping)
    {
    }

    void sendSignal(vfc::int32_t f_mode) override
    {
        cc::daddy::ParkFrontObjDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_ParkFrontObjDaddy_SenderPort.reserve();
        l_container.m_Data = static_cast<cc::target::common::EPARKObjectExistR2L>(f_mode);   // PRQA S 3013
        cc::daddy::CustomDaddyPorts::sm_ParkFrontObjDaddy_SenderPort.deliver();
        XLOG_INFO_OS(g_COMSocketContext) << "Setting Park front obj to " << f_mode << XLOG_ENDL;//PRQA S 4060
    }
};
static SetParkFrontObjIndCommand g_setParkFrontObjIndCommand(g_parkFrontObjIndMapping);

//!
//! Set Parking Space Command
//!
class SetParkSpaceCommand : public pc::util::cli::CommandCallback
{
public:
  bool invoke(std::istream& f_input, std::ostream& f_output, const pc::util::cli::CommandLineInterface&) override
  {
    vfc::float32_t l_parameter0 = 0.0f;
    vfc::float32_t l_parameter1 = 0.0f;
    vfc::float32_t l_parameter2 = 0.0f;
    vfc::float32_t l_parameter3 = 0.0f;
    vfc::float32_t l_parameter4 = 0.0f;
    vfc::float32_t l_parameter5 = 0.0f;
    vfc::float32_t l_parameter6 = 0.0f;
    vfc::float32_t l_parameter7 = 0.0f;
    vfc::float32_t l_parameter8 = 0.0f;
    vfc::float32_t l_parameter9 = 0.0f;
    vfc::float32_t l_parameter10 = 0.0f;
    vfc::float32_t l_parameter11 = 0.0f;
    vfc::uint16_t l_parameter12 = 0u;
    vfc::uint16_t l_parameter13 = 0u;
    vfc::uint16_t l_parameter14 = 0u;
    vfc::uint16_t l_parameter15 = 0u;
    vfc::uint16_t l_parameter16 = 0u;
    vfc::uint16_t l_parameter17 = 0u;
    f_input >> l_parameter0 >> l_parameter1 >> l_parameter2 >> l_parameter3 >> l_parameter4 >> l_parameter5
            >> l_parameter6 >> l_parameter7 >> l_parameter8 >> l_parameter9 >> l_parameter10 >> l_parameter11
            >> l_parameter12 >> l_parameter13 >> l_parameter14 >> l_parameter15 >> l_parameter16 >> l_parameter17;  // PRQA S 3803
    if (f_input.fail())
    {
      f_output << parseError;    // PRQA S 3803
      getHelp(f_output);
      return false;
    }
    cc::daddy::ParkSpaceDaddy_t& l_parkingSpaceDaddy = cc::daddy::CustomDaddyPorts::sm_ParkSpaceDaddy_SenderPort.reserve();
    l_parkingSpaceDaddy.m_Data.m_APA_ParkSlotX_1L   = static_cast<vfc::float32_t>(l_parameter0);
    l_parkingSpaceDaddy.m_Data.m_APA_ParkSlotX_2L   = static_cast<vfc::float32_t>(l_parameter1);
    l_parkingSpaceDaddy.m_Data.m_APA_ParkSlotX_3L   = static_cast<vfc::float32_t>(l_parameter2);
    l_parkingSpaceDaddy.m_Data.m_APA_ParkSlotX_1R   = static_cast<vfc::float32_t>(l_parameter3);
    l_parkingSpaceDaddy.m_Data.m_APA_ParkSlotX_2R   = static_cast<vfc::float32_t>(l_parameter4);
    l_parkingSpaceDaddy.m_Data.m_APA_ParkSlotX_3R   = static_cast<vfc::float32_t>(l_parameter5);
    l_parkingSpaceDaddy.m_Data.m_APA_ParkSlotAng_1L = static_cast<vfc::float32_t>(l_parameter6);
    l_parkingSpaceDaddy.m_Data.m_APA_ParkSlotAng_2L = static_cast<vfc::float32_t>(l_parameter7);
    l_parkingSpaceDaddy.m_Data.m_APA_ParkSlotAng_3L = static_cast<vfc::float32_t>(l_parameter8);
    l_parkingSpaceDaddy.m_Data.m_APA_ParkSlotAng_1R = static_cast<vfc::float32_t>(l_parameter9);
    l_parkingSpaceDaddy.m_Data.m_APA_ParkSlotAng_2R = static_cast<vfc::float32_t>(l_parameter10);
    l_parkingSpaceDaddy.m_Data.m_APA_ParkSlotAng_3R = static_cast<vfc::float32_t>(l_parameter11);

    l_parkingSpaceDaddy.m_Data.m_APA_ParkSlotSt_1L = static_cast<vfc::uint8_t>(l_parameter12);      // PRQA S 3013
    l_parkingSpaceDaddy.m_Data.m_APA_ParkSlotSt_2L = static_cast<vfc::uint8_t>(l_parameter13);      // PRQA S 3013
    l_parkingSpaceDaddy.m_Data.m_APA_ParkSlotSt_3L = static_cast<vfc::uint8_t>(l_parameter14);      // PRQA S 3013
    l_parkingSpaceDaddy.m_Data.m_APA_ParkSlotSt_1R = static_cast<vfc::uint8_t>(l_parameter15);      // PRQA S 3013
    l_parkingSpaceDaddy.m_Data.m_APA_ParkSlotSt_2R = static_cast<vfc::uint8_t>(l_parameter16);      // PRQA S 3013
    l_parkingSpaceDaddy.m_Data.m_APA_ParkSlotSt_3R = static_cast<vfc::uint8_t>(l_parameter17);      // PRQA S 3013

    cc::daddy::CustomDaddyPorts::sm_ParkSpaceDaddy_SenderPort.deliver();
    return true;
  }
  void getDescription(std::ostream& f_output) const override
  {
    f_output << "Set parking space position and angle" << newline;    // PRQA S 3803
  }
  void getHelp(std::ostream& f_output) const override
  {
    f_output << "angle1L <x> angle2L <x>" << newline
    << "angle1R <x> angle2R <x>" << newline
    << "valid1L <x> valid2L <x>" << newline
    << "valid1R <x> valid2R <x>" << newline;    // PRQA S 3803
  }
};
static SetParkSpaceCommand g_setParkSpaceCommand;


//! Set FIDs FrontCamDeg Command
//!
class SetIsFrontCamDegCommand : public pc::util::cli::NumericSignalSetter<vfc::int32_t>
{
public:
    SetIsFrontCamDegCommand()
      : pc::util::cli::NumericSignalSetter<vfc::int32_t>("isFrontCamDegCommand")
    {
    }

    void sendSignal( vfc::int32_t f_value ) override
    {
        cc::daddy::DegradationFid_t& l_container = cc::daddy::CustomDaddyPorts::sm_degradationFid_SenderPort.reserve();
        l_container.m_Data.m_FiMFID_SVSCamFrontSt           = static_cast<vfc::uint8_t>(f_value);
        l_container.m_Data.m_FiMFID_SVSCamLeftSt            = 1u;
        l_container.m_Data.m_FiMFID_SVSCamRightSt           = 1u;
        l_container.m_Data.m_FiMFID_SVSCamRearSt            = 1u;
        l_container.m_Data.m_FiMFID_SVSEcuInternalStatus    = 1u;
        l_container.m_Data.m_FiMFID_SVSRxLampAll            = 1u;
        l_container.m_Data.m_FiMFID_SVSRxDoorSt             = 1u;
        l_container.m_Data.m_FiMFID_SVSRxLamp38A            = 1u;
        l_container.m_Data.m_FiMFID_SVSRxLamp496A           = 1u;
        l_container.m_Data.m_FiMFID_SVSRxLamp133A           = 1u;
        l_container.m_Data.m_FiMFID_SVSRxExtMirrorSt        = 1u;
        l_container.m_Data.m_FiMFID_SVSOdo                  = 1u;
        l_container.m_Data.m_FiMFID_SVSRxVehSpd             = 1u;
        l_container.m_Data.m_FiMFID_SVSRxDrvDir             = 1u;
        l_container.m_Data.m_FiMFID_SVSRxGear               = 1u;
        l_container.m_Data.m_FiMFID_SVSRxSteeringWheelAngle = 1u;
        l_container.m_Data.m_FiMFID_SVSRxHuViewModeFailure  = 1u;
        l_container.m_Data.m_FiMFID_SVSRxHUVehicleModeRotationFailure = 1u;
        l_container.m_Data.m_FiMFID_SVSRxHUPadRotateFailure = 1u;
        l_container.m_Data.m_FiMFID_SVSRxFctaWarning        = 1u;
        l_container.m_Data.m_FiMFID_SVSRxRctaLeftWarning    = 1u;
        l_container.m_Data.m_FiMFID_SVSRxRctaRightWarning   = 1u;
        l_container.m_Data.m_FiMFID_SVSRxHUVRSVMRequest     = 1u;
        cc::daddy::CustomDaddyPorts::sm_degradationFid_SenderPort.deliver();
    }
};

static SetIsFrontCamDegCommand g_setIsFrontCamDegCommand;

//!
//! Set FIDs all deg Command
//!
class SetIsAllDegCommand : public pc::util::cli::NumericSignalSetter<vfc::int32_t>
{
public:
    SetIsAllDegCommand()
      : pc::util::cli::NumericSignalSetter<vfc::int32_t>("isAllDegCommand")
    {
    }

    void sendSignal( vfc::int32_t f_value ) override
    {
        cc::daddy::DegradationFid_t& l_container = cc::daddy::CustomDaddyPorts::sm_degradationFid_SenderPort.reserve();
        if ( 0 == f_value )
        {
            l_container.m_Data.m_FiMFID_SVSCamFrontSt           = 0u;
            l_container.m_Data.m_FiMFID_SVSCamLeftSt            = 0u;
            l_container.m_Data.m_FiMFID_SVSCamRightSt           = 0u;
            l_container.m_Data.m_FiMFID_SVSCamRearSt            = 0u;
            l_container.m_Data.m_FiMFID_SVSEcuInternalStatus    = 0u;
            l_container.m_Data.m_FiMFID_SVSRxLampAll            = 0u;
            l_container.m_Data.m_FiMFID_SVSRxDoorSt             = 0u;
            l_container.m_Data.m_FiMFID_SVSRxLamp38A            = 0u;
            l_container.m_Data.m_FiMFID_SVSRxLamp496A           = 0u;
            l_container.m_Data.m_FiMFID_SVSRxLamp133A           = 0u;
            l_container.m_Data.m_FiMFID_SVSRxExtMirrorSt        = 0u;
            l_container.m_Data.m_FiMFID_SVSOdo                  = 0u;
            l_container.m_Data.m_FiMFID_SVSRxVehSpd             = 0u;
            l_container.m_Data.m_FiMFID_SVSRxDrvDir             = 0u;
            l_container.m_Data.m_FiMFID_SVSRxGear               = 0u;
            l_container.m_Data.m_FiMFID_SVSRxSteeringWheelAngle = 0u;
            l_container.m_Data.m_FiMFID_SVSRxHuViewModeFailure  = 0u;
            l_container.m_Data.m_FiMFID_SVSRxHUVehicleModeRotationFailure = 0u;
            l_container.m_Data.m_FiMFID_SVSRxHUPadRotateFailure  = 0u;
            l_container.m_Data.m_FiMFID_SVSRxFctaWarning        = 0u;
            l_container.m_Data.m_FiMFID_SVSRxRctaLeftWarning    = 0u;
            l_container.m_Data.m_FiMFID_SVSRxRctaRightWarning   = 0u;
            l_container.m_Data.m_FiMFID_SVSRxHUVRSVMRequest     = 0u;
        }
        if ( 1 == f_value )
        {
            l_container.m_Data.m_FiMFID_SVSCamFrontSt           = 1u;
            l_container.m_Data.m_FiMFID_SVSCamLeftSt            = 1u;
            l_container.m_Data.m_FiMFID_SVSCamRightSt           = 1u;
            l_container.m_Data.m_FiMFID_SVSCamRearSt            = 1u;
            l_container.m_Data.m_FiMFID_SVSEcuInternalStatus    = 1u;
            l_container.m_Data.m_FiMFID_SVSRxLampAll            = 1u;
            l_container.m_Data.m_FiMFID_SVSRxDoorSt             = 1u;
            l_container.m_Data.m_FiMFID_SVSRxLamp38A            = 1u;
            l_container.m_Data.m_FiMFID_SVSRxLamp496A           = 1u;
            l_container.m_Data.m_FiMFID_SVSRxLamp133A           = 1u;
            l_container.m_Data.m_FiMFID_SVSRxExtMirrorSt        = 1u;
            l_container.m_Data.m_FiMFID_SVSOdo                  = 1u;
            l_container.m_Data.m_FiMFID_SVSRxVehSpd             = 1u;
            l_container.m_Data.m_FiMFID_SVSRxDrvDir             = 1u;
            l_container.m_Data.m_FiMFID_SVSRxGear               = 1u;
            l_container.m_Data.m_FiMFID_SVSRxSteeringWheelAngle = 1u;
            l_container.m_Data.m_FiMFID_SVSRxHuViewModeFailure  = 1u;
            l_container.m_Data.m_FiMFID_SVSRxHUVehicleModeRotationFailure = 1u;
            l_container.m_Data.m_FiMFID_SVSRxHUPadRotateFailure = 1u;
            l_container.m_Data.m_FiMFID_SVSRxFctaWarning        = 1u;
            l_container.m_Data.m_FiMFID_SVSRxRctaLeftWarning    = 1u;
            l_container.m_Data.m_FiMFID_SVSRxRctaRightWarning   = 1u;
            l_container.m_Data.m_FiMFID_SVSRxHUVRSVMRequest     = 1u;
        }
        cc::daddy::CustomDaddyPorts::sm_degradationFid_SenderPort.deliver();
    }
};

static SetIsAllDegCommand g_setIsAllDegCommand;

//!
//! Set FIDs ECUDegCommand
//!
class SetIsECUDegCommand : public pc::util::cli::NumericSignalSetter<vfc::int32_t>
{
public:
    SetIsECUDegCommand()
      : pc::util::cli::NumericSignalSetter<vfc::int32_t>("isECUDegCommand")
    {
    }

    void sendSignal( vfc::int32_t f_value ) override
    {
        cc::daddy::DegradationFid_t& l_container = cc::daddy::CustomDaddyPorts::sm_degradationFid_SenderPort.reserve();
        l_container.m_Data.m_FiMFID_SVSCamFrontSt           = 1u;
        l_container.m_Data.m_FiMFID_SVSCamLeftSt            = 1u;
        l_container.m_Data.m_FiMFID_SVSCamRightSt           = 1u;
        l_container.m_Data.m_FiMFID_SVSCamRearSt            = 1u;
        l_container.m_Data.m_FiMFID_SVSEcuInternalStatus    = static_cast<vfc::uint8_t>(f_value);
        l_container.m_Data.m_FiMFID_SVSRxLampAll            = 1u;
        l_container.m_Data.m_FiMFID_SVSRxDoorSt             = 1u;
        l_container.m_Data.m_FiMFID_SVSRxLamp38A            = 1u;
        l_container.m_Data.m_FiMFID_SVSRxLamp496A           = 1u;
        l_container.m_Data.m_FiMFID_SVSRxLamp133A           = 1u;
        l_container.m_Data.m_FiMFID_SVSRxExtMirrorSt        = 1u;
        l_container.m_Data.m_FiMFID_SVSOdo                  = 1u;
        l_container.m_Data.m_FiMFID_SVSRxVehSpd             = 1u;
        l_container.m_Data.m_FiMFID_SVSRxDrvDir             = 1u;
        l_container.m_Data.m_FiMFID_SVSRxGear               = 1u;
        l_container.m_Data.m_FiMFID_SVSRxSteeringWheelAngle = 1u;
        l_container.m_Data.m_FiMFID_SVSRxHuViewModeFailure  = 1u;
        l_container.m_Data.m_FiMFID_SVSRxHUVehicleModeRotationFailure = 1u;
        l_container.m_Data.m_FiMFID_SVSRxHUPadRotateFailure = 1u;
        l_container.m_Data.m_FiMFID_SVSRxFctaWarning        = 1u;
        l_container.m_Data.m_FiMFID_SVSRxRctaLeftWarning    = 1u;
        l_container.m_Data.m_FiMFID_SVSRxRctaRightWarning   = 1u;
        l_container.m_Data.m_FiMFID_SVSRxHUVRSVMRequest     = 1u;
        cc::daddy::CustomDaddyPorts::sm_degradationFid_SenderPort.deliver();
    }
};

static SetIsECUDegCommand g_setIsECUDegCommand;

//!
//! Set FIDs OdoDegCommand
//!
class SetIsOdoDegCommand : public pc::util::cli::NumericSignalSetter<vfc::int32_t>
{
public:
    SetIsOdoDegCommand()
      : pc::util::cli::NumericSignalSetter<vfc::int32_t>("isOdoDegCommand")
    {
    }

    void sendSignal( vfc::int32_t f_value ) override
    {
        cc::daddy::DegradationFid_t& l_container = cc::daddy::CustomDaddyPorts::sm_degradationFid_SenderPort.reserve();
        l_container.m_Data.m_FiMFID_SVSCamFrontSt           = 1u;
        l_container.m_Data.m_FiMFID_SVSCamLeftSt            = 1u;
        l_container.m_Data.m_FiMFID_SVSCamRightSt           = 1u;
        l_container.m_Data.m_FiMFID_SVSCamRearSt            = 1u;
        l_container.m_Data.m_FiMFID_SVSEcuInternalStatus    = 1u;
        l_container.m_Data.m_FiMFID_SVSRxLampAll            = 1u;
        l_container.m_Data.m_FiMFID_SVSRxDoorSt             = 1u;
        l_container.m_Data.m_FiMFID_SVSRxLamp38A            = 1u;
        l_container.m_Data.m_FiMFID_SVSRxLamp496A           = 1u;
        l_container.m_Data.m_FiMFID_SVSRxLamp133A           = 1u;
        l_container.m_Data.m_FiMFID_SVSRxExtMirrorSt        = 1u;
        l_container.m_Data.m_FiMFID_SVSOdo                  = static_cast<vfc::uint8_t>(f_value);
        l_container.m_Data.m_FiMFID_SVSRxVehSpd             = 1u;
        l_container.m_Data.m_FiMFID_SVSRxDrvDir             = 1u;
        l_container.m_Data.m_FiMFID_SVSRxGear               = 1u;
        l_container.m_Data.m_FiMFID_SVSRxSteeringWheelAngle = 1u;
        l_container.m_Data.m_FiMFID_SVSRxHuViewModeFailure = 1u;
        l_container.m_Data.m_FiMFID_SVSRxHUVehicleModeRotationFailure = 1u;
        l_container.m_Data.m_FiMFID_SVSRxHUPadRotateFailure = 1u;
        l_container.m_Data.m_FiMFID_SVSRxFctaWarning        = 1u;
        l_container.m_Data.m_FiMFID_SVSRxRctaLeftWarning    = 1u;
        l_container.m_Data.m_FiMFID_SVSRxRctaRightWarning   = 1u;
        l_container.m_Data.m_FiMFID_SVSRxHUVRSVMRequest     = 1u;
        cc::daddy::CustomDaddyPorts::sm_degradationFid_SenderPort.deliver();
    }
};

static SetIsOdoDegCommand g_setIsOdoDegCommand;

//!
//! Set HU HMI Data Command
//!
class SetHUHmiCommand : public pc::util::cli::CommandCallback
{
public:
    bool invoke(std::istream& f_input, std::ostream& f_output, const pc::util::cli::CommandLineInterface&) override
    {
        vfc::uint16_t l_hux = 0u;
        vfc::uint16_t l_huy = 0u;
        vfc::uint16_t l_zoomfactor = 0u;
        f_input >> l_hux >> l_huy >> l_zoomfactor;  // PRQA S 3803

        if (f_input.fail())
        {
            f_output << parseError;  // PRQA S 3803
            getHelp(f_output);
            return false;
        }

        cc::daddy::HmiData_Daddy& l_hmiData = cc::daddy::CustomDaddyPorts::sm_HmiData_SenderPort.reserve();
        if (2u == g_rotate)  // vert
        {
          l_hmiData.m_Data.m_huX = static_cast<vfc::uint16_t>(static_cast<vfc::float32_t>(l_hux) * cc::target::common::VERT_X_SCALE_FACTOR);  // PRQA S 3016  // PRQA S 3132
          l_hmiData.m_Data.m_huY = static_cast<vfc::uint16_t>(static_cast<vfc::float32_t>(l_huy) * cc::target::common::VERT_Y_SCALE_FACTOR);  // PRQA S 3016  // PRQA S 3132
        }
        else // hori
        {
          l_hmiData.m_Data.m_huX = static_cast<vfc::uint16_t>(static_cast<vfc::float32_t>(l_hux) * cc::target::common::HORI_X_SCALE_FACTOR);  // PRQA S 3016  // PRQA S 3132
          l_hmiData.m_Data.m_huY = static_cast<vfc::uint16_t>(static_cast<vfc::float32_t>(l_huy) * cc::target::common::HORI_Y_SCALE_FACTOR);  // PRQA S 3016  // PRQA S 3132
        }
        l_hmiData.m_Data.m_zoomFactorRq = static_cast<vfc::uint16_t> (l_zoomfactor);
        cc::daddy::CustomDaddyPorts::sm_HmiData_SenderPort.deliver();
        return true;
    }
    void getDescription(std::ostream& f_output) const override
    {
        f_output << "Set pos of HUHmi camera" << newline;  // PRQA S 3803
    }
    void getHelp(std::ostream& f_output) const override
    {
        f_output << "position <hux> <huy> <zoomfactor>" << newline;  // PRQA S 3803
    }
};

static SetHUHmiCommand g_setHUHmiCommand;

//!
//! Set HU Touch Event Command
//!
class SetHUTouchEventCommand : public pc::util::cli::NumericSignalSetter<vfc::int32_t>
{
public:
    SetHUTouchEventCommand()
      : pc::util::cli::NumericSignalSetter<vfc::int32_t>("HU Touch Event ")
    {
    }

    void sendSignal( vfc::int32_t f_value ) override
    {
        cc::daddy::HUtouchEvenTypeDaddy_t& l_touchEvent = cc::daddy::CustomDaddyPorts::sm_HUtouchEvenTypeDaddy_SenderPort.reserve();
        l_touchEvent.m_Data          = static_cast<vfc::uint8_t>(f_value);
        cc::daddy::CustomDaddyPorts::sm_HUtouchEvenTypeDaddy_SenderPort.deliver();
    }
};

static SetHUTouchEventCommand g_setHUTouchEventCommand;

//!
//! SetHUSettingModeCommand
//!
typedef std::array<pc::util::cli::IntValueNamePair, 3> HUSettingModeMap;
const HUSettingModeMap g_huSettingMapping = {
    ESettingSts_Set_ON,        "on",
    ESettingSts_Set_OFF,       "off",
    ESettingSts_Set_Default,   "none"
};

class SetHUOverlayTubeModeCommand : public pc::util::cli::EnumSignalSetter<HUSettingModeMap>
{
public:

    explicit SetHUOverlayTubeModeCommand(const HUSettingModeMap& f_mapping)
        : pc::util::cli::EnumSignalSetter<HUSettingModeMap>("HU OverlayTube", f_mapping)
    {
    }

    void sendSignal(vfc::int32_t f_mode) override
    {
        if( f_mode == 1 )
        {
            cc::daddy::PIVI_ManualVideoSetupReq_t& l_container = cc::daddy::CustomDaddyPorts::sm_PIVI_ManualVideoSetupReq_SenderPort.reserve();
            l_container.m_Data.m_OverlayStatus_u8 = 1u;
            cc::daddy::CustomDaddyPorts::sm_PIVI_ManualVideoSetupReq_SenderPort.deliver();
        }
        if( f_mode == 2 )
        {
            cc::daddy::PIVI_ManualVideoSetupReq_t& l_container = cc::daddy::CustomDaddyPorts::sm_PIVI_ManualVideoSetupReq_SenderPort.reserve();
            l_container.m_Data.m_OverlayStatus_u8 = 0u;
            cc::daddy::CustomDaddyPorts::sm_PIVI_ManualVideoSetupReq_SenderPort.deliver();
        }
        XLOG_INFO_OS(g_COMSocketContext) << "Setting HU OverlayTube to " << f_mode << XLOG_ENDL;//PRQA S 4060
    }
};
static SetHUOverlayTubeModeCommand g_setHUOverlayTubeModeCommand(g_huSettingMapping);

class SetHUOverlayDistModeCommand : public pc::util::cli::EnumSignalSetter<HUSettingModeMap>
{
public:

    explicit SetHUOverlayDistModeCommand(const HUSettingModeMap& f_mapping)
        : pc::util::cli::EnumSignalSetter<HUSettingModeMap>("HU OverlayDist", f_mapping)
    {
    }

    void sendSignal(vfc::int32_t f_mode) override
    {
        cc::daddy::HUoverlayDistReqDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_HUoverlayDistReqDaddy_SenderPort.reserve();
        l_container.m_Data = static_cast<vfc::uint8_t>(f_mode);
        cc::daddy::CustomDaddyPorts::sm_HUoverlayDistReqDaddy_SenderPort.deliver();
        XLOG_INFO_OS(g_COMSocketContext) << "Setting HU OverlayDist to " << f_mode << XLOG_ENDL;//PRQA S 4060
    }
};
static SetHUOverlayDistModeCommand g_setHUOverlayDistModeCommand(g_huSettingMapping);


class SetHUBasePlateModeCommand : public pc::util::cli::EnumSignalSetter<HUSettingModeMap>
{
public:

    explicit SetHUBasePlateModeCommand(const HUSettingModeMap& f_mapping)
        : pc::util::cli::EnumSignalSetter<HUSettingModeMap>("HU BasePlate", f_mapping)
    {
    }

    void sendSignal(vfc::int32_t f_mode) override
    {
        cc::daddy::HUbasePlateReqDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_HUbasePlateReqDaddy_SenderPort.reserve();
        l_container.m_Data = static_cast<vfc::uint8_t>(f_mode);
        cc::daddy::CustomDaddyPorts::sm_HUbasePlateReqDaddy_SenderPort.deliver();
        XLOG_INFO_OS(g_COMSocketContext) << "Setting HU BasePlate to " << f_mode << XLOG_ENDL;//PRQA S 4060
    }
};
static SetHUBasePlateModeCommand g_setHUBasePlateModeCommand(g_huSettingMapping);


class SetHUShowReqModeCommand : public pc::util::cli::EnumSignalSetter<HUSettingModeMap>
{
public:
    explicit SetHUShowReqModeCommand(const HUSettingModeMap& f_mapping)
        : pc::util::cli::EnumSignalSetter<HUSettingModeMap>("HU ShowReq", f_mapping)
    {
    }
    void sendSignal(vfc::int32_t f_mode) override
    {
        cc::daddy::HUShoWReqDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_HUShoWReqDaddy_SenderPort.reserve();
        l_container.m_Data = static_cast<vfc::uint8_t>(f_mode);
        cc::daddy::CustomDaddyPorts::sm_HUShoWReqDaddy_SenderPort.deliver();
        XLOG_INFO_OS(g_COMSocketContext) << "Setting HU ShowReq to " << f_mode << XLOG_ENDL;//PRQA S 4060
    }
};
static SetHUShowReqModeCommand g_setHUShowReqModeCommand(g_huSettingMapping);


class SetHUShowSuspendModeCommand : public pc::util::cli::EnumSignalSetter<HUSettingModeMap>
{
public:
    explicit SetHUShowSuspendModeCommand(const HUSettingModeMap& f_mapping)
        : pc::util::cli::EnumSignalSetter<HUSettingModeMap>("HU ShowSuspend", f_mapping)
    {
    }
    void sendSignal(vfc::int32_t f_mode) override
    {
        cc::daddy::HUShoWSuspendDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_HUShoWSuspendDaddy_SenderPort.reserve();
        l_container.m_Data = static_cast<vfc::uint8_t>(f_mode);
        cc::daddy::CustomDaddyPorts::sm_HUShoWSuspendDaddy_SenderPort.deliver();
        XLOG_INFO_OS(g_COMSocketContext) << "Setting HU ShowSuspend to " << f_mode << XLOG_ENDL;//PRQA S 4060
    }
};
static SetHUShowSuspendModeCommand g_setHUShowSuspendModeCommand(g_huSettingMapping);


class SetVRSwitchModeCommand : public pc::util::cli::NumericSignalSetter<vfc::int32_t>
{
public:

    explicit SetVRSwitchModeCommand()
        : pc::util::cli::NumericSignalSetter<vfc::int32_t>("VR Switch")
    {
    }

    void sendSignal(vfc::int32_t f_value) override
    {
        cc::daddy::VRSwitchSVMDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_VRSwitchSVMDaddy_SenderPort.reserve();
        l_container.m_Data = static_cast<bool>(f_value);
        cc::daddy::CustomDaddyPorts::sm_VRSwitchSVMDaddy_SenderPort.deliver();
    }
};
static SetVRSwitchModeCommand g_setVRSwitchModeCommand;

class SetFCPButtonCommand : public pc::util::cli::NumericSignalSetter<vfc::int32_t>
{
public:

    explicit SetFCPButtonCommand()
        : pc::util::cli::NumericSignalSetter<vfc::int32_t>("FCP SVM Button")
    {
    }

    void sendSignal(vfc::int32_t f_value) override
    {
        cc::daddy::FCP_SVMButtonPressed_t& l_container = cc::daddy::CustomDaddyPorts::sm_FCP_SVMButtonPressedDaddy_SenderPort.reserve();
        l_container.m_Data = static_cast<bool>(f_value);
        cc::daddy::CustomDaddyPorts::sm_FCP_SVMButtonPressedDaddy_SenderPort.deliver();
    }
};
static SetFCPButtonCommand g_setFCPButtonCommand;

//! SVS view mode
typedef std::array<pc::util::cli::IntValueNamePair, 3> HUSVSViewModeMap;
const HUSVSViewModeMap g_huSVSViewMapping = {
    ESVSViewMode_VM_Standard,      "m2d",
    ESVSViewMode_VM_Perspective,   "m3d",
    ESVSViewMode_VM_Default,       "none"
};

class SetHUSVSViewModeCommand : public pc::util::cli::EnumSignalSetter<HUSVSViewModeMap>
{
public:

    explicit SetHUSVSViewModeCommand(const HUSVSViewModeMap& f_mapping)
        : pc::util::cli::EnumSignalSetter<HUSVSViewModeMap>("HU SVSViewMode", f_mapping)
    {
    }

    void sendSignal(vfc::int32_t f_mode) override
    {
        cc::daddy::HUselSVSModeDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_HUselSVSModeDaddy_SenderPort.reserve();
        l_container.m_Data = static_cast<vfc::uint8_t>(f_mode);
        cc::daddy::CustomDaddyPorts::sm_HUselSVSModeDaddy_SenderPort.deliver();
        XLOG_INFO_OS(g_COMSocketContext) << "Setting HU SVSViewMode to " << f_mode << XLOG_ENDL;//PRQA S 4060
    }
};
static SetHUSVSViewModeCommand g_setHUSVSViewModeCommand(g_huSVSViewMapping);


//!
//! SetSteeringAngleCommand
//!
class SetSteeringAngleCommand : public pc::util::cli::CommandCallback
{
public:
    bool invoke(std::istream& f_input, std::ostream& f_output, const pc::util::cli::CommandLineInterface&) override
    {
        vfc::float32_t l_steeringangle = 0.0f;
        f_input >> l_steeringangle;  // PRQA S 3803
        if (!f_input.fail())
        {
            cc::daddy::DriverSteeringWheelAngleDaddy_t& l_steeringangleDaddy = cc::daddy::CustomDaddyPorts::sm_DriverSteeringWheelAngleDaddy_SenderPort.reserve();
            l_steeringangleDaddy.m_Data = l_steeringangle;
            cc::daddy::CustomDaddyPorts::sm_DriverSteeringWheelAngleDaddy_SenderPort.deliver();
            f_output << "Setting steering angle to: " << l_steeringangle << " degree " << newline;  // PRQA S 3803
            return true;
        }
        f_output << parseError;  // PRQA S 3803
        return false;
    }
    void getDescription(std::ostream& f_output) const override
    {
        f_output << "Set steering angle" << newline;  // PRQA S 3803
    }

    void getHelp(std::ostream& f_output) const override
    {
        f_output << "<steering angle (deg)>";  // PRQA S 3803
    }
};
static SetSteeringAngleCommand g_setSteeringAngleCommand;

//!
//! HeadUnitCamPos3DCommand
//!
class HeadUnitCamPos3DCommand : public pc::util::cli::CommandCallback
{
public:
  bool invoke(std::istream& f_input, std::ostream& f_output, const pc::util::cli::CommandLineInterface&) override
  {
    vfc::int32_t l_parameter0 = 0;
    vfc::int32_t l_parameter1 = 0;
    f_input >> l_parameter0 >> l_parameter1;  // PRQA S 3803
    if (f_input.fail())
    {
      f_output << parseError;  // PRQA S 3803
      getHelp(f_output);
      return false;
    }
    cc::daddy::HUCameraCommandsDaddy& l_cameraCommandsDaddy = cc::daddy::CustomDaddyPorts::sm_HUCameraCommandsDaddySenderPort.reserve();
    l_cameraCommandsDaddy.m_Data.hemisphere3D.x() = l_parameter0;
    l_cameraCommandsDaddy.m_Data.hemisphere3D.y() = l_parameter1;
    l_cameraCommandsDaddy.m_Data.hemisphere3DSet = true;
    cc::daddy::CustomDaddyPorts::sm_HUCameraCommandsDaddySenderPort.deliver();
    return true;
  }
  void getDescription(std::ostream& f_output) const override
  {
    f_output << "Set x y hemisphere position of 3D SVS camera" << newline;  // PRQA S 3803
  }
  void getHelp(std::ostream& f_output) const override
  {
    f_output << "position <x> <y>" << newline;  // PRQA S 3803
  }
};
static HeadUnitCamPos3DCommand g_headUnitCamPos3DCommand;

//!
//! HeadUnitZoomFactor3DCommand
//!
class HeadUnitZoomFactor3DCommand : public pc::util::cli::CommandCallback
{
public:
  bool invoke(std::istream& f_input, std::ostream& f_output, const pc::util::cli::CommandLineInterface&) override
  {
    vfc::int32_t l_parameter0 = 0;
    f_input >> l_parameter0;  // PRQA S 3803
    if (f_input.fail())
    {
      f_output << parseError;  // PRQA S 3803
      getHelp(f_output);
      return false;
    }
    cc::daddy::HUCameraCommandsDaddy& l_cameraCommandsDaddy = cc::daddy::CustomDaddyPorts::sm_HUCameraCommandsDaddySenderPort.reserve();
    l_cameraCommandsDaddy.m_Data.zoom3D = static_cast<vfc::uint16_t>(l_parameter0);
    l_cameraCommandsDaddy.m_Data.zoom3DSet = true;
    cc::daddy::CustomDaddyPorts::sm_HUCameraCommandsDaddySenderPort.deliver();
    return true;
  }
  void getDescription(std::ostream& f_output) const override
  {
    f_output << "Set zoom factor of 3D SVS camera" << newline;  // PRQA S 3803
  }
  void getHelp(std::ostream& f_output) const override
  {
    f_output << "<zoom factor>" << newline;  // PRQA S 3803
  }
};
static HeadUnitZoomFactor3DCommand g_headUnitZoomFactor3DCommand;


//!
//! HeadUnitTouchTypeCommand
//!
class HeadUnitTouchTypeCommand : public pc::util::cli::CommandCallback
{
public:
  bool invoke(std::istream& f_input, std::ostream& f_output, const pc::util::cli::CommandLineInterface&) override
  {
    vfc::int32_t l_parameter0 = 0;
    f_input >> l_parameter0;    // PRQA S 3803
    if (f_input.fail())
    {
      f_output << parseError;    // PRQA S 3803
      getHelp(f_output);
      return false;
    }
    cc::daddy::HUtouchEvenTypeDaddy_t& l_touchTypeDaddy = cc::daddy::CustomDaddyPorts::sm_HUtouchEvenTypeDaddy_SenderPort.reserve();
    l_touchTypeDaddy.m_Data = static_cast<vfc::uint8_t>(l_parameter0);
    cc::daddy::CustomDaddyPorts::sm_HUtouchEvenTypeDaddy_SenderPort.deliver();
    return true;
  }
  void getDescription(std::ostream& f_output) const override
  {
    f_output << "Set touch type" << newline;    // PRQA S 3803
  }
  void getHelp(std::ostream& f_output) const override
  {
    f_output << "touch type <x>" << newline;    // PRQA S 3803
  }
};
static HeadUnitTouchTypeCommand g_headUnitTouchTypeCommand;

//!
//! HeadUnitRotate3DCommand
//!
class HeadUnitRotate3DCommand : public pc::util::cli::CommandCallback
{
public:
  bool invoke(std::istream& f_input, std::ostream& f_output, const pc::util::cli::CommandLineInterface&) override
  {
    vfc::int32_t l_parameter0 = 0;
    vfc::int32_t l_parameter1 = 0;
    f_input >> l_parameter0 >> l_parameter1;  // PRQA S 3803
    if (f_input.fail())
    {
      f_output << parseError;  // PRQA S 3803
      getHelp(f_output);
      return false;
    }
    cc::daddy::HmiData_Daddy& l_HUCommandsDaddy = cc::daddy::CustomDaddyPorts::sm_HmiData_SenderPort.reserve();
    if (2u == g_rotate)  // vert
    {
      l_HUCommandsDaddy.m_Data.m_huX = static_cast<vfc::uint16_t>(static_cast<vfc::float32_t>(l_parameter1) * cc::target::common::VERT_X_SCALE_FACTOR);  // PRQA S 3016  // PRQA S 3132
      l_HUCommandsDaddy.m_Data.m_huY = static_cast<vfc::uint16_t>(static_cast<vfc::float32_t>(l_parameter0) * cc::target::common::VERT_Y_SCALE_FACTOR);  // PRQA S 3016  // PRQA S 3132
    }
    else  // hori
    {
      l_HUCommandsDaddy.m_Data.m_huX = static_cast<vfc::uint16_t>(static_cast<vfc::float32_t>(l_parameter1) * cc::target::common::HORI_X_SCALE_FACTOR);  // PRQA S 3016  // PRQA S 3132
      l_HUCommandsDaddy.m_Data.m_huY = static_cast<vfc::uint16_t>(static_cast<vfc::float32_t>(l_parameter0) * cc::target::common::HORI_Y_SCALE_FACTOR);  // PRQA S 3016  // PRQA S 3132
    }
    cc::daddy::CustomDaddyPorts::sm_HmiData_SenderPort.deliver();
    return true;
  }
  void getDescription(std::ostream& f_output) const override
  {
    f_output << "Set pos of 3D SVS camera" << newline;  // PRQA S 3803
  }
  void getHelp(std::ostream& f_output) const override
  {
    f_output << "position <x> <y>" << newline;  // PRQA S 3803
  }
};
static HeadUnitRotate3DCommand g_headUnitRotate3DCommand;


//!
//! SetPasButtonStatusCommand
//!
class SetPasButtonStatusCommand : public pc::util::cli::NumericSignalSetter<vfc::int32_t>
{
public:

    SetPasButtonStatusCommand()
      : pc::util::cli::NumericSignalSetter<vfc::int32_t>("pasButton")
    {
    }

    void sendSignal(vfc::int32_t f_value) override
    {
        cc::daddy::PasButtonStatusDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_PasButtonStatusDaddy_SenderPort.reserve();
        l_container.m_Data = static_cast<bool>(f_value);
        cc::daddy::CustomDaddyPorts::sm_PasButtonStatusDaddy_SenderPort.deliver();
    }
};

static SetPasButtonStatusCommand g_setPasButtonStatusCommand;

//! FreeParkingStatus
class SetFreeParkingActiveCommand : public pc::util::cli::NumericSignalSetter<vfc::int32_t>
{
public:

    SetFreeParkingActiveCommand()
        : pc::util::cli::NumericSignalSetter<vfc::int32_t>("Free Parking Status ")
    {
    }

    void sendSignal(vfc::int32_t f_mode) override
    {
        cc::daddy::ParkFreeParkingActive_t & l_container = cc::daddy::CustomDaddyPorts::sm_ParkFreeParkingActiveDaddy_SenderPort.reserve();
        l_container.m_Data = static_cast<bool>(f_mode);
        cc::daddy::CustomDaddyPorts::sm_ParkFreeParkingActiveDaddy_SenderPort.deliver();
        XLOG_INFO_OS(g_COMSocketContext) << "Setting Free Parking Status" << f_mode << XLOG_ENDL;//PRQA S 4060
    }
};
static SetFreeParkingActiveCommand g_setFreeParkingActiveCommand;



//!
//! SetPasStatusCommand
//!
typedef std::array<pc::util::cli::IntValueNamePair, 6> PasModeMap;
const PasModeMap g_pasModeMapping = {
    static_cast<vfc::int32_t>(cc::target::common::PAS_Off),               "Off",
    static_cast<vfc::int32_t>(cc::target::common::PAS_Standby),           "Standby",
    static_cast<vfc::int32_t>(cc::target::common::PAS_FrontRearActive),   "FrontRearActive",
    static_cast<vfc::int32_t>(cc::target::common::PAS_FActiveRFailure),   "FActiveRFailure",
    static_cast<vfc::int32_t>(cc::target::common::PAS_RActiveFFailure),   "RActiveFFailure",
    static_cast<vfc::int32_t>(cc::target::common::PAS_SystemFailure),     "SystemFailure"
};

class SetPasStatusCommand : public pc::util::cli::EnumSignalSetter<PasModeMap>
{
public:

    explicit SetPasStatusCommand(const PasModeMap& f_mapping)
        : pc::util::cli::EnumSignalSetter<PasModeMap>("PasMode", f_mapping)
    {
    }

    void sendSignal(vfc::int32_t f_mode) override
    {
        cc::daddy::PasStatusDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_PasStatusDaddy_SenderPort.reserve();
        l_container.m_Data = static_cast<cc::target::common::EPasStatus>(f_mode);       // PRQA S 3013
        cc::daddy::CustomDaddyPorts::sm_PasStatusDaddy_SenderPort.deliver();
        XLOG_INFO_OS(g_COMSocketContext) << "Setting PasMode to " << f_mode << XLOG_ENDL;//PRQA S 4060
    }
};

static SetPasStatusCommand g_setPasStatusCommand(g_pasModeMapping);


//!
//! SetSdwStatusCommand
//!
typedef std::array<pc::util::cli::IntValueNamePair, 4> SdwModeMap;
const SdwModeMap g_sdwModeMapping = {
    static_cast<vfc::int32_t>(cc::target::common::SDWSTS_Off),      "Off",
    static_cast<vfc::int32_t>(cc::target::common::SDWSTS_Standby),  "Standby",
    static_cast<vfc::int32_t>(cc::target::common::SDWSTS_Active),   "Active",
    static_cast<vfc::int32_t>(cc::target::common::SDWSTS_Failure),  "Failure"
};

class SetSdwStatusCommand : public pc::util::cli::EnumSignalSetter<SdwModeMap>
{
public:

    explicit SetSdwStatusCommand(const SdwModeMap& f_mapping)
        : pc::util::cli::EnumSignalSetter<SdwModeMap>("SdwMode", f_mapping)
    {
    }

    void sendSignal(vfc::int32_t f_mode) override
    {
        cc::daddy::SdwStatusDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_SdwStatusDaddy_SenderPort.reserve();
        l_container.m_Data = static_cast<cc::target::common::ESdwStatus>(f_mode);       // PRQA S 3013
        cc::daddy::CustomDaddyPorts::sm_SdwStatusDaddy_SenderPort.deliver();
        XLOG_INFO_OS(g_COMSocketContext) << "Setting SdwMode to " << f_mode << XLOG_ENDL;//PRQA S 4060
    }
};

static SetSdwStatusCommand g_setSdwStatusCommand(g_sdwModeMapping);


//!
//! Set pas warn tone Command
//!
typedef std::array<pc::util::cli::IntValueNamePair, 6> PasWarnToneMap;
const PasWarnToneMap g_pasWarnToneMapping = {
    static_cast<vfc::int32_t>(cc::target::common::SOUND_OFF),             "Off",
    static_cast<vfc::int32_t>(cc::target::common::SOUND_LONG_BEEP),       "Long_Beep",
    static_cast<vfc::int32_t>(cc::target::common::SOUND_Fast),            "Fast",
    static_cast<vfc::int32_t>(cc::target::common::SOUND_Medium),          "Medium",
    static_cast<vfc::int32_t>(cc::target::common::SOUND_Slow),            "Slow",
    static_cast<vfc::int32_t>(cc::target::common::SOUND_Mute),            "Mute",
};

class SetPasWarnToneCommand : public pc::util::cli::EnumSignalSetter<PasWarnToneMap>
{
public:

    explicit SetPasWarnToneCommand(const PasWarnToneMap& f_mapping)
        : pc::util::cli::EnumSignalSetter<PasWarnToneMap>("PasWarnTone", f_mapping)
    {
    }

    void sendSignal(vfc::int32_t f_mode) override
    {
        cc::daddy::PasWarnToneDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_PasWarnToneDaddy_SenderPort.reserve();
        l_container.m_Data = static_cast<cc::target::common::EPASWarnToneR2L>(f_mode);    // PRQA S 3013
        cc::daddy::CustomDaddyPorts::sm_PasWarnToneDaddy_SenderPort.deliver();
        XLOG_INFO_OS(g_COMSocketContext) << "Setting Pas Warn Tone to " << f_mode << XLOG_ENDL;//PRQA S 4060
    }
};

static SetPasWarnToneCommand g_setPasWarnToneCommand(g_pasWarnToneMapping);


//!
//! SetDistanceToStopCommand
//!
class SetDistanceToStopCommand : public pc::util::cli::NumericSignalSetter<vfc::float32_t>
{
public:

    SetDistanceToStopCommand()
      : pc::util::cli::NumericSignalSetter<vfc::float32_t>("distance to stop")
    {
    }

    void sendSignal(vfc::float32_t f_value) override
    {
        cc::daddy::DistanceToStopDaddy& l_container = cc::daddy::CustomDaddyPorts::sm_DistanceToStopDaddy_SenderPort.reserve();
        l_container.m_Data = f_value;
        cc::daddy::CustomDaddyPorts::sm_DistanceToStopDaddy_SenderPort.deliver();
    }
};

static SetDistanceToStopCommand g_setDistanceToStopCommand;

//!
//! SetStopLineLocationCommand
//!
class SetStopLineLocationCommand : public pc::util::cli::NumericSignalSetter<vfc::int32_t>
{
public:

    SetStopLineLocationCommand()
      : pc::util::cli::NumericSignalSetter<vfc::int32_t>("stop line location")
    {
    }

    void sendSignal(vfc::int32_t f_value) override
    {
        cc::daddy::StopLineLocationDaddy& l_container = cc::daddy::CustomDaddyPorts::sm_StopLineLocationDaddy_SenderPort.reserve();
        l_container.m_Data = static_cast<cc::daddy::EStopLineLocation>(f_value);     // PRQA S 3013
        cc::daddy::CustomDaddyPorts::sm_StopLineLocationDaddy_SenderPort.deliver();
    }
};

static SetStopLineLocationCommand g_setStopLineLocationCommand;


class SetMovementDirectionCommand : public pc::util::cli::NumericSignalSetter<vfc::int32_t>
{
public:
    SetMovementDirectionCommand()
      : pc::util::cli::NumericSignalSetter<vfc::int32_t>("movementDirection")
    {

    }
    void sendSignal( vfc::int32_t f_mdUpdate ) override
    {
        cc::daddy::NFSM_MovementDirectionUpdate_t& l_container = cc::daddy::CustomDaddyPorts::sm_NFSM_MovementDirectionUpdate_SenderPort.reserve();
        l_container.m_Data.m_MovementDirection_u8 = static_cast<vfc::uint8_t>(f_mdUpdate);
        cc::daddy::CustomDaddyPorts::sm_NFSM_MovementDirectionUpdate_SenderPort.deliver();
    }
};

static SetMovementDirectionCommand g_setMovementDirectionCommand;

class SetViewBufferStatusACKCommand : public pc::util::cli::NumericSignalSetter<vfc::int32_t>
{
public:
    SetViewBufferStatusACKCommand()
      : pc::util::cli::NumericSignalSetter<vfc::int32_t>("viewBufferStatusACK")
    {

    }
    void sendSignal( vfc::int32_t f_vbsACK ) override
    {
        cc::daddy::PIVI_ViewBufferStatusACK_t& l_container = cc::daddy::CustomDaddyPorts::sm_PIVI_ViewBufferStatusACK_SenderPort.reserve();
        l_container.m_Data.m_ViewBufferStatusACK_u8 = static_cast<vfc::uint8_t>(f_vbsACK);
        cc::daddy::CustomDaddyPorts::sm_PIVI_ViewBufferStatusACK_SenderPort.deliver();
    }
};

static SetViewBufferStatusACKCommand g_setViewBufferStatusACKCommand;


//!
//! SetOdometryCommand
//!
class SetOdometryCommand : public pc::util::cli::CommandCallback
{
public:
  bool invoke(std::istream& f_input, std::ostream& f_output, const pc::util::cli::CommandLineInterface&) override
  {
    vfc::float32_t l_parameter0 = 0.0f;
    vfc::float32_t l_parameter1 = 0.0f;
    vfc::float32_t l_parameter2 = 0.0f;
    vfc::int32_t   l_parameter3 = 0;
    f_input >> l_parameter0 >> l_parameter1 >> l_parameter2>> l_parameter3;  // PRQA S 3803
    if (f_input.fail())
    {
      f_output << parseError;  // PRQA S 3803
      getHelp(f_output);
      return false;
    }

    if ( true == pc::daddy::BaseDaddyPorts::sm_OdometryDataDaddySenderPort.isConnected() )
    {
        pc::daddy::OdometryDataDaddy& l_container = pc::daddy::BaseDaddyPorts::sm_OdometryDataDaddySenderPort.reserve();
        l_container.m_Data.m_xPos     = static_cast<vfc::CSI::si_metre_f32_t>(l_parameter0);
        l_container.m_Data.m_yPos     = static_cast<vfc::CSI::si_metre_f32_t>(l_parameter1);
        l_container.m_Data.m_yawAngle = static_cast<vfc::CSI::si_radian_f32_t>(l_parameter2);
        l_container.m_Data.m_vehMoveDir = static_cast<pc::daddy::EVehMovingDirection>(l_parameter3);
        pc::daddy::BaseDaddyPorts::sm_OdometryDataDaddySenderPort.deliver();
    }
    return true;
  }
  void getDescription(std::ostream& f_output) const override
  {
    f_output << "Set Odometry data" << newline;  // PRQA S 3803
  }
  void getHelp(std::ostream& f_output) const override
  {
    f_output << "data <x> <y> <yawangle>" << newline;  // PRQA S 3803
  }
};
static SetOdometryCommand g_setOdometryCommand;


class SetViewBufferStatusCommand : public pc::util::cli::NumericSignalSetter<vfc::int32_t>
{
public:
    SetViewBufferStatusCommand()
      : pc::util::cli::NumericSignalSetter<vfc::int32_t>("movementDirectionUpdate")
    {

    }
    void sendSignal( vfc::int32_t f_vbs ) override
    {
        cc::daddy::NFSM_ViewBufferStatus_t& l_container = cc::daddy::CustomDaddyPorts::sm_ViewBufferStatus_SenderPort.reserve();
        l_container.m_Data.m_ViewBufferStatus_u8 = static_cast<vfc::uint8_t>(f_vbs);
        cc::daddy::CustomDaddyPorts::sm_ViewBufferStatus_SenderPort.deliver();
    }
};

static SetViewBufferStatusCommand g_setViewBufferStatusCommand;


typedef std::array<pc::util::cli::IntValueNamePair, 9> PowerModeMap;
const PowerModeMap g_PowerModeMapping = {
    static_cast<vfc::int32_t>(EPowerMode_ACCESSORY),           "accessory",          // PRQA S 3080
    static_cast<vfc::int32_t>(EPowerMode_CRANK),               "crank",              // PRQA S 3080
    static_cast<vfc::int32_t>(EPowerMode_IGNITION_ON),         "ignition_on",        // PRQA S 3080
    static_cast<vfc::int32_t>(EPowerMode_KEY_APPROVED),        "key_approved",       // PRQA S 3080
    static_cast<vfc::int32_t>(EPowerMode_KEY_OUT),             "key_out",            // PRQA S 3080
    static_cast<vfc::int32_t>(EPowerMode_KEY_RECENTLY_OUT),    "key_recently_out",   // PRQA S 3080
    static_cast<vfc::int32_t>(EPowerMode_POST_ACCESSORY),      "post_accessory",     // PRQA S 3080
    static_cast<vfc::int32_t>(EPowerMode_POST_IGNITION),       "post_ignition",      // PRQA S 3080
    static_cast<vfc::int32_t>(EPowerMode_RUNNING),             "running"             // PRQA S 3080
};

class SetPowerModeCommand : public pc::util::cli::EnumSignalSetter<PowerModeMap>
{
public:
    explicit SetPowerModeCommand(const PowerModeMap& f_mapping)
      : pc::util::cli::EnumSignalSetter<PowerModeMap>("powermode", f_mapping)
    {

    }
    void sendSignal( vfc::int32_t f_powermode ) override
    {
        cc::daddy::PowerModeDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_PowerModeDaddy_SenderPort.reserve();
        l_container.m_Data = static_cast<cc::daddy::EPowerMode>(f_powermode);       // PRQA S 3013
        cc::daddy::CustomDaddyPorts::sm_PowerModeDaddy_SenderPort.deliver();
    }
};

static SetPowerModeCommand g_setPowerModeCommand(g_PowerModeMapping);

// ! set fcta left command

typedef std::array<pc::util::cli::IntValueNamePair, 3> FCTALeftWarning;
const FCTALeftWarning g_FCTALeftWarning = {
    static_cast<vfc::int32_t>(cc::target::common::FCTA_LEFT_NONE),           "NONE",
    static_cast<vfc::int32_t>(cc::target::common::FCTA_LEFT_LV1),            "LV1",
    static_cast<vfc::int32_t>(cc::target::common::FCTA_LEFT_LV2),            "LV2"
};

class SetFctaLeftCommand : public pc::util::cli::EnumSignalSetter<FCTALeftWarning>
{
public:
    explicit SetFctaLeftCommand(const FCTALeftWarning& f_mapping)
      : pc::util::cli::EnumSignalSetter<FCTALeftWarning>("FCTA Left warning", f_mapping)
    {

    }
    void sendSignal( vfc::int32_t f_FCTALeft ) override
    {
        cc::daddy::FCTALeftDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_FCTALeftDaddy_SenderPort.reserve();
        l_container.m_Data = static_cast<cc::target::common::EFCTALeftWarnLevel>(f_FCTALeft);     // PRQA S 3013
        cc::daddy::CustomDaddyPorts::sm_FCTALeftDaddy_SenderPort.deliver();
    }
};
static SetFctaLeftCommand g_setFctaLeftCommand(g_FCTALeftWarning);


typedef std::array<pc::util::cli::IntValueNamePair, 3> FCTARightWarning;
const FCTARightWarning g_FCTARightWarning = {
    static_cast<vfc::int32_t>(cc::target::common::FCTA_RIGHT_NONE),           "NONE",
    static_cast<vfc::int32_t>(cc::target::common::FCTA_RIGHT_LV1),            "LV1",
    static_cast<vfc::int32_t>(cc::target::common::FCTA_RIGHT_LV2),            "LV2"
};

class SetFctaRightCommand : public pc::util::cli::EnumSignalSetter<FCTARightWarning>
{
public:
    explicit SetFctaRightCommand(const FCTARightWarning& f_mapping)
      : pc::util::cli::EnumSignalSetter<FCTARightWarning>("FCTA Right warning", f_mapping)
    {

    }
    void sendSignal( vfc::int32_t f_FCTARight ) override
    {
        cc::daddy::FCTARightDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_FCTARightDaddy_SenderPort.reserve();
        l_container.m_Data = static_cast<cc::target::common::EFCTARightWarnLevel>(f_FCTARight);      // PRQA S 3013
        cc::daddy::CustomDaddyPorts::sm_FCTARightDaddy_SenderPort.deliver();
    }
};
static SetFctaRightCommand g_setFctaRightCommand(g_FCTARightWarning);


typedef std::array<pc::util::cli::IntValueNamePair, 3> RCTALeftWarning;
const RCTALeftWarning g_RCTALeftWarning = {
    static_cast<vfc::int32_t>(cc::target::common::RCTA_LEFT_NONE),           "NONE",
    static_cast<vfc::int32_t>(cc::target::common::RCTA_LEFT_LV1),            "LV1",
    static_cast<vfc::int32_t>(cc::target::common::RCTA_LEFT_LV2),            "LV2"
};

class SetRctaLeftCommand : public pc::util::cli::EnumSignalSetter<RCTALeftWarning>
{
public:
    explicit SetRctaLeftCommand(const RCTALeftWarning& f_mapping)
      : pc::util::cli::EnumSignalSetter<RCTALeftWarning>("RCTA Left warning", f_mapping)
    {

    }
    void sendSignal( vfc::int32_t f_RCTALeft ) override
    {
        cc::daddy::RCTALeftDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_RCTALeftDaddy_SenderPort.reserve();
        l_container.m_Data = static_cast<cc::target::common::ERCTALeftWarnLevel>(f_RCTALeft);       // PRQA S 3013
        cc::daddy::CustomDaddyPorts::sm_RCTALeftDaddy_SenderPort.deliver();
    }
};
static SetRctaLeftCommand g_setRctaLeftCommand(g_RCTALeftWarning);


typedef std::array<pc::util::cli::IntValueNamePair, 3> RCTARightWarning;
const RCTARightWarning g_RCTARightWarning = {
    static_cast<vfc::int32_t>(cc::target::common::RCTA_RIGHT_NONE),           "NONE",
    static_cast<vfc::int32_t>(cc::target::common::RCTA_RIGHT_LV1),            "LV1",
    static_cast<vfc::int32_t>(cc::target::common::RCTA_RIGHT_LV2),            "LV2"
};

class SetRctaRightCommand : public pc::util::cli::EnumSignalSetter<RCTARightWarning>
{
public:
    explicit SetRctaRightCommand(const RCTARightWarning& f_mapping)
      : pc::util::cli::EnumSignalSetter<RCTARightWarning>("RCTA Right warning", f_mapping)
    {

    }
    void sendSignal( vfc::int32_t f_RCTARight ) override
    {
        cc::daddy::RCTARightDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_RCTARightDaddy_SenderPort.reserve();
        l_container.m_Data = static_cast<cc::target::common::ERCTARightWarnLevel>(f_RCTARight);        // PRQA S 3013
        cc::daddy::CustomDaddyPorts::sm_RCTARightDaddy_SenderPort.deliver();
    }
};
static SetRctaRightCommand g_setRctaRightCommand(g_RCTARightWarning);


//!
//! Set calibration status command
//!
class SetCalibStatusCommand : public pc::util::cli::CommandCallback
{
public:
  bool invoke(std::istream& f_input, std::ostream& f_output, const pc::util::cli::CommandLineInterface&) override
  {
    vfc::uint16_t l_parameter0 = 0u;
    vfc::uint16_t l_parameter1 = 0u;
    vfc::uint16_t l_parameter2 = 0u;
    vfc::uint16_t l_parameter3 = 0u;
    vfc::uint16_t l_parameter4 = 0u;
    vfc::uint16_t l_parameter5 = 0u;
    vfc::uint16_t l_parameter6 = 0u;
    vfc::uint16_t l_parameter7 = 0u;
    f_input >> l_parameter0 >> l_parameter1 >> l_parameter2 >> l_parameter3 >> l_parameter4 >> l_parameter5 >> l_parameter6 >> l_parameter7;  // PRQA S 3803
    if (f_input.fail())
    {
      f_output << parseError;  // PRQA S 3803
      getHelp(f_output);
      return false;
    }

    if ( true == cc::daddy::CustomDaddyPorts::sm_CamCalibrationStatusDaddy_SenderPort.isConnected() )
    {
        cc::daddy::C2WCalibStatusDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_CamCalibrationStatusDaddy_SenderPort.reserve();

        l_container.m_Data.m_calibstatus[pc::core::sysconf::FRONT_CAMERA].m_calibrationError = static_cast<cc::target::common::EGCMCalibrationError>(l_parameter0);    // PRQA S 3013
        l_container.m_Data.m_calibstatus[pc::core::sysconf::FRONT_CAMERA].m_calibrationState = static_cast<cc::target::common::EGCMCalibrationState>(l_parameter1);    // PRQA S 3013

        l_container.m_Data.m_calibstatus[pc::core::sysconf::RIGHT_CAMERA].m_calibrationError = static_cast<cc::target::common::EGCMCalibrationError>(l_parameter2);    // PRQA S 3013
        l_container.m_Data.m_calibstatus[pc::core::sysconf::RIGHT_CAMERA].m_calibrationState = static_cast<cc::target::common::EGCMCalibrationState>(l_parameter3);    // PRQA S 3013

        l_container.m_Data.m_calibstatus[pc::core::sysconf::REAR_CAMERA].m_calibrationError = static_cast<cc::target::common::EGCMCalibrationError>(l_parameter4);    // PRQA S 3013
        l_container.m_Data.m_calibstatus[pc::core::sysconf::REAR_CAMERA].m_calibrationState = static_cast<cc::target::common::EGCMCalibrationState>(l_parameter5);    // PRQA S 3013

        l_container.m_Data.m_calibstatus[pc::core::sysconf::LEFT_CAMERA].m_calibrationError = static_cast<cc::target::common::EGCMCalibrationError>(l_parameter6);    // PRQA S 3013
        l_container.m_Data.m_calibstatus[pc::core::sysconf::LEFT_CAMERA].m_calibrationState = static_cast<cc::target::common::EGCMCalibrationState>(l_parameter7);    // PRQA S 3013

        cc::daddy::CustomDaddyPorts::sm_CamCalibrationStatusDaddy_SenderPort.deliver();
    }
    return true;
  }
};
static SetCalibStatusCommand g_setCalibStatusCommand;


//!
//! Set CPC calibration status command
//!
// class SetCpcCalibStatusCommand : public pc::util::cli::CommandCallback
// {
// public:
//   virtual bool invoke(std::istream& f_input, std::ostream& f_output, const pc::util::cli::CommandLineInterface&) override
//   {
//     unsigned int l_parameter0 = 0u;
//     unsigned int l_parameter1 = 0u;
//     unsigned int l_parameter2 = 0u;
//     unsigned int l_parameter3 = 0u;
//     f_input >> l_parameter0 >> l_parameter1 >> l_parameter2 >> l_parameter3;  // PRQA S 3803
//     if (f_input.fail())
//     {
//       f_output << parseError;  // PRQA S 3803
//       getHelp(f_output);
//       return false;
//     }

//     if ( true == cc::daddy::CustomDaddyPorts::sm_cpcStatusDaddy_SenderPort.isConnected() )
//     {
//         cc::daddy::CpcStatus_st_t& l_container = cc::daddy::CustomDaddyPorts::sm_cpcStatusDaddy_SenderPort.reserve();

//         l_container.m_Data.m_calibstatus[0].m_camAvailability = static_cast<cc::cpc::ECpcCameraAvailability>(l_parameter0);    // PRQA S 3013
//         l_container.m_Data.m_calibstatus[1].m_camAvailability = static_cast<cc::cpc::ECpcCameraAvailability>(l_parameter1);    // PRQA S 3013
//         l_container.m_Data.m_calibstatus[2].m_camAvailability = static_cast<cc::cpc::ECpcCameraAvailability>(l_parameter2);    // PRQA S 3013
//         l_container.m_Data.m_calibstatus[3].m_camAvailability = static_cast<cc::cpc::ECpcCameraAvailability>(l_parameter3);    // PRQA S 3013

//         cc::daddy::CustomDaddyPorts::sm_cpcStatusDaddy_SenderPort.deliver();
//     }
//     return true;
//   }
// };
// SetCpcCalibStatusCommand g_setCpcCalibStatusCommand;


//!
//! SetSvsOverlayToCpcCommand
//!
// class SetSvsOverlayToCpcCommand : public pc::util::cli::CommandCallback
// {
// public:
//     bool invoke(std::istream& f_input, std::ostream& f_output, const pc::util::cli::CommandLineInterface&) override
//     {
//         bool l_isCpcTriggered;
//         bool l_isCpcRecalibrateBtnPressed;
//         bool l_isCpcCancelBtnPressed;

//         f_input >> l_isCpcTriggered >> l_isCpcRecalibrateBtnPressed >> l_isCpcCancelBtnPressed;

//         if (f_input.fail())
//         {
//           f_output << parseError;
//           getHelp(f_output);
//           return false;
//         }

//         cc::daddy::SvsOverlayToCpc_t& l_container = cc::daddy::CustomDaddyPorts::sm_svsOverlayToCpc_SenderPort.reserve();
//         l_container.m_Data.m_isCpcTriggered                      = l_isCpcTriggered;
//         l_container.m_Data.m_isRecalibrateButtonPressed          = l_isCpcRecalibrateBtnPressed;
//         l_container.m_Data.m_isCancelButtonPressed               = l_isCpcCancelBtnPressed;
//         cc::daddy::CustomDaddyPorts::sm_svsOverlayToCpc_SenderPort.deliver();

//         cc::daddy::CpcOverlaySwitchDaddy_t& l_rCpcOverlaySwitchContainer = cc::daddy::CustomDaddyPorts::sm_CpcOverlaySwitchDaddy_SenderPort.reserve();
//         l_rCpcOverlaySwitchContainer.m_Data = l_isCpcTriggered;
//         cc::daddy::CustomDaddyPorts::sm_CpcOverlaySwitchDaddy_SenderPort.deliver();

//         XLOG_INFO_OS(g_COMSocketContext)  //PRQA S 4060
//           << "Set isCpcTriggered: [" << l_isCpcTriggered << "]"
//           << " isRecalibrateBtnPressed: [" << l_isCpcRecalibrateBtnPressed << "]"
//           << " isCancelButtonPressed: [" << l_isCpcCancelBtnPressed << "]" << XLOG_ENDL;

//         return true;
//     }
// };

// static SetSvsOverlayToCpcCommand g_setSvsOverlayToCpcCommand;


//!
//! SetDiagTriggerCpcCommand
//!
class SetDiagTriggerCpcCommand : public pc::util::cli::CommandCallback
{
public:
  bool invoke(std::istream& f_input, std::ostream& f_output, const pc::util::cli::CommandLineInterface&) override
  {
    vfc::uint8_t isRequestTrigger = 0u;
    f_input >> isRequestTrigger;  // PRQA S 3803
    if (f_input.fail())
    {
      f_output << parseError;  // PRQA S 3803
      getHelp(f_output);
      return false;
    }

    if ( true == cc::daddy::CustomDaddyPorts::sm_diagRoutine_SenderPort.isConnected() )
    {
      cc::daddy::DiagRoutine_t& l_container = cc::daddy::CustomDaddyPorts::sm_diagRoutine_SenderPort.reserve();  //PRQA S 4208
    //   l_container.m_Data.m_diagRequestTriggerCpc = static_cast<bool>(isRequestTrigger);

      cc::daddy::CustomDaddyPorts::sm_diagRoutine_SenderPort.deliver();
    }
    return true;
  }

  void getDescription(std::ostream& f_output) const override
  {
    f_output << "Set Diag Trigger CPC" << newline;  // PRQA S 3803
  }

  void getHelp(std::ostream& f_output) const override
  {
    f_output << "diagTriggerCpc 0/1" << newline;  // PRQA S 3803
  }

};
static SetDiagTriggerCpcCommand g_diagTriggerCpcCommand;


//!
//! Set HUDislayModeSwitch Command
//!
class SetHUDislayModeSwitchCommand : public pc::util::cli::NumericSignalSetter<vfc::int32_t>
{
public:
    SetHUDislayModeSwitchCommand()
      : pc::util::cli::NumericSignalSetter<vfc::int32_t>("HU DislayMode ")
    {
    }

    void sendSignal( vfc::int32_t f_value ) override
    {
        if (cc::daddy::CustomDaddyPorts::sm_HUDislayModeSwitchDaddy_SenderPort.isConnected())
        {
            cc::daddy::HUDislayModeSwitchDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_HUDislayModeSwitchDaddy_SenderPort.reserve();
            l_container.m_Data          = static_cast<vfc::uint8_t>(f_value);
            cc::daddy::CustomDaddyPorts::sm_HUDislayModeSwitchDaddy_SenderPort.deliver();
        }
    }
};

static SetHUDislayModeSwitchCommand g_setHUDislayModeSwitchCommand;

class SetHUDislayColorCommand : public pc::util::cli::NumericSignalSetter<vfc::int32_t>
{
public:
    SetHUDislayColorCommand()
      : pc::util::cli::NumericSignalSetter<vfc::int32_t>("HU DislayColor ")
    {
    }

    void sendSignal( vfc::int32_t f_value ) override
    {
        cc::daddy::SVSVehColorAckDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_SVSVehColorAckDaddy_SenderPort.reserve();
        l_container.m_Data          = static_cast<vfc::uint8_t>(f_value);
        cc::daddy::CustomDaddyPorts::sm_SVSVehColorAckDaddy_SenderPort.deliver();
    }
};

static SetHUDislayColorCommand g_setHUDislayColorCommand;

//!
//! Set HUDislayModeExpand Command
//!
class SetHUDislayModeExpandCommand : public pc::util::cli::NumericSignalSetter<vfc::int32_t>
{
public:
    SetHUDislayModeExpandCommand()
      : pc::util::cli::NumericSignalSetter<vfc::int32_t>("HU DislayMode Expand ")
    {
    }

    void sendSignal( vfc::int32_t f_value ) override
    {
        cc::daddy::HUDislayModeExpandDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_HUDislayModeExpandDaddy_SenderPort.reserve();
        l_container.m_Data          = static_cast<vfc::uint8_t>(f_value);
        cc::daddy::CustomDaddyPorts::sm_HUDislayModeExpandDaddy_SenderPort.deliver();
    }
};

static SetHUDislayModeExpandCommand g_setHUDislayModeExpandCommand;


//!
//! Set HUImageWorkMode Command
//!
class SetHUImageWorkModeCommand : public pc::util::cli::NumericSignalSetter<vfc::int32_t>
{
public:
    SetHUImageWorkModeCommand()
      : pc::util::cli::NumericSignalSetter<vfc::int32_t>("HU ImageWorkMode ")
    {
    }

    void sendSignal( vfc::int32_t f_value ) override
    {
        cc::daddy::HUImageWorkModeDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_HUImageWorkModeDaddy_SenderPort.reserve();
        l_container.m_Data          = static_cast<vfc::uint8_t>(f_value);
        cc::daddy::CustomDaddyPorts::sm_HUImageWorkModeDaddy_SenderPort.deliver();
    }
};

static SetHUImageWorkModeCommand g_setHUImageWorkModeCommand;


//!
//! Set HUDislayModeExpand New Command
//!
class SetHUDislayModeExpandNewCommand : public pc::util::cli::NumericSignalSetter<vfc::int32_t>
{
public:
    SetHUDislayModeExpandNewCommand()
      : pc::util::cli::NumericSignalSetter<vfc::int32_t>("HU DislayMode Expand ")
    {
    }

    void sendSignal( vfc::int32_t f_value ) override
    {
        cc::daddy::HUDislayModeExpandNewDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_HUDislayModeExpandNewDaddy_SenderPort.reserve();
        l_container.m_Data          = static_cast<vfc::uint8_t>(f_value);
        cc::daddy::CustomDaddyPorts::sm_HUDislayModeExpandNewDaddy_SenderPort.deliver();
    }
};

static SetHUDislayModeExpandNewCommand g_setHUDislayModeExpandNewCommand;

//!
//! Set HURotateStatus Command
//!
class SetHURotateStatusCommand : public pc::util::cli::NumericSignalSetter<vfc::int32_t>
{
public:
    SetHURotateStatusCommand()
      : pc::util::cli::NumericSignalSetter<vfc::int32_t>("HU RotateStatus ")
    {
    }

    void sendSignal( vfc::int32_t f_value ) override
    {
        cc::daddy::HURotateStatusDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_HURotateStatusDaddy_SenderPort.reserve();
        l_container.m_Data          = static_cast<vfc::uint8_t>(f_value);
        g_rotate = static_cast<vfc::uint8_t>(f_value);
        cc::daddy::CustomDaddyPorts::sm_HURotateStatusDaddy_SenderPort.deliver();
    }
};

static SetHURotateStatusCommand g_setHURotateStatusCommand;


//!
//! Set dayNightTheme Command
//!
class SetdayNightThemeCommand : public pc::util::cli::NumericSignalSetter<vfc::int32_t>
{
public:
    SetdayNightThemeCommand()
      : pc::util::cli::NumericSignalSetter<vfc::int32_t>("HU RotateStatus ")
    {
    }

    void sendSignal( vfc::int32_t f_value ) override
    {
        auto& l_container = cc::daddy::CustomDaddyPorts::sm_dayNightThemeDaddy_SenderPort.reserve();
        l_container.m_Data          = static_cast<cc::target::common::EThemeTypeDayNight>(f_value);
        cc::daddy::CustomDaddyPorts::sm_dayNightThemeDaddy_SenderPort.deliver();
    }
};

static SetdayNightThemeCommand g_setdayNightThemeCommand;

//!
//! Set HU calibration flag Command
//!
class SetCalibrationFlagCommand : public pc::util::cli::NumericSignalSetter<vfc::int32_t>
{
public:
    SetCalibrationFlagCommand()
      : pc::util::cli::NumericSignalSetter<vfc::int32_t>("HU Cpc Flag ")
    {
    }

    void sendSignal( vfc::int32_t f_value ) override
    {
        auto& l_container = cc::daddy::CustomDaddyPorts::sm_HUCalibrationStartDaddy_SenderPort.reserve();
        l_container.m_Data          = static_cast<vfc::uint8_t>(f_value);
        cc::daddy::CustomDaddyPorts::sm_HUCalibrationStartDaddy_SenderPort.deliver();
    }
};

static SetCalibrationFlagCommand g_setHUCpcFlagCommand;



//!
//! Set remoteScreen Command
//!
class SetremoteScreenModeCommand : public pc::util::cli::NumericSignalSetter<vfc::int32_t>
{
public:
    SetremoteScreenModeCommand()
      : pc::util::cli::NumericSignalSetter<vfc::int32_t>("HU RemoteScreenMode ")
    {
    }

    void sendSignal( vfc::int32_t f_value ) override
    {
        auto& l_container = cc::daddy::CustomDaddyPorts::sm_HURemoteScreenReqDaddy_SenderPort.reserve();
        l_container.m_Data          = static_cast<vfc::uint8_t>(f_value);
        cc::daddy::CustomDaddyPorts::sm_HURemoteScreenReqDaddy_SenderPort.deliver();
    }
};

static SetremoteScreenModeCommand g_setRemoteScreenModeCommand;

//!
//! Set obstacleOverlay Command
//!
class SetobstacleOverlayCommand : public pc::util::cli::NumericSignalSetter<vfc::int32_t>
{
public:
    SetobstacleOverlayCommand()
      : pc::util::cli::NumericSignalSetter<vfc::int32_t>("HU ObstacleOverlay ")
    {
    }

    void sendSignal( vfc::int32_t f_value ) override
    {
        auto& l_container           = cc::daddy::CustomDaddyPorts::sm_HURadarWallButtonDaddy_SenderPort.reserve();
        l_container.m_Data          = static_cast<cc::target::common::EhuRadarWallButton>(f_value);
        cc::daddy::CustomDaddyPorts::sm_HURadarWallButtonDaddy_SenderPort.deliver();
    }
};

static SetobstacleOverlayCommand g_setobstacleOverlayCommand;

//!
//! Set TrailerMode Command
//!
class SetTrailerModeCommand : public pc::util::cli::NumericSignalSetter<vfc::int32_t>
{
public:
    SetTrailerModeCommand()
      : pc::util::cli::NumericSignalSetter<vfc::int32_t>("HU Trailer Mode ")
    {
    }

    void sendSignal( vfc::int32_t f_value ) override
    {
        if (cc::daddy::CustomDaddyPorts::sm_HUTrailerButtonDaddy_SenderPort.isConnected())
        {
            auto& l_container           = cc::daddy::CustomDaddyPorts::sm_HUTrailerButtonDaddy_SenderPort.reserve();
            l_container.m_Data          = static_cast<bool>(f_value);
            cc::daddy::CustomDaddyPorts::sm_HUTrailerButtonDaddy_SenderPort.deliver();
        }
    }
};

static SetTrailerModeCommand g_setTrailerModeCommand;

//!
//! Set Freeparking Command
//!
class SetFreeparkingCommand : public pc::util::cli::NumericSignalSetter<vfc::int32_t>
{
public:
    SetFreeparkingCommand()
      : pc::util::cli::NumericSignalSetter<vfc::int32_t>("HU Trailer Mode ")
    {
    }

    void sendSignal( vfc::int32_t f_value ) override
    {
        if (cc::daddy::CustomDaddyPorts::sm_ParkhmiToSvs_SenderPort.isConnected())
        {
            auto& l_container           = \
                                cc::daddy::CustomDaddyPorts::sm_ParkhmiToSvs_SenderPort.reserve();

            l_container.m_Data.m_freeParkingIn.m_parkStage   = \
                                static_cast<cc::target::common::EFreeParkingStage>(f_value);

            l_container.m_Data.m_freeParkingIn.m_is360FreeParking   =  true;

            cc::daddy::CustomDaddyPorts::sm_ParkhmiToSvs_SenderPort.deliver();
        }
    }
};

static SetFreeparkingCommand g_SetFreeparkingCommand;


//!
//! Set Parse APA Data Command
//!
class SetParseAPADataCommand : public pc::util::cli::NumericSignalSetter<vfc::int32_t>
{
public:
    SetParseAPADataCommand()
      : pc::util::cli::NumericSignalSetter<vfc::int32_t>("APA Data Parse ")
    {
    }

    void sendSignal( vfc::int32_t f_value ) override
    {
        cc::target::common::ParkhmiToSvs l_apaData;
        std::string jsonStr = "";

        jsonStr = readJsonFile("cc/resources_sil/apa/apa_interface_"+std::to_string(f_value)+".json");

        XLOG_INFO_OS(g_COMSocketContext) << "jsonStr filename" << jsonStr << XLOG_ENDL;

        cc::util::common::parseApaJsonData(jsonStr,l_apaData);

        if (cc::daddy::CustomDaddyPorts::sm_ParkhmiToSvs_SenderPort.isConnected())
        {
            auto& l_container           = cc::daddy::CustomDaddyPorts::sm_ParkhmiToSvs_SenderPort.reserveLastDelivery();
            l_container.m_Data          = l_apaData;
            cc::daddy::CustomDaddyPorts::sm_ParkhmiToSvs_SenderPort.deliver();
        }
    }
};

static SetParseAPADataCommand g_setApaDataCommand;


//!
//! Set huTransparencyMode Command
//!
class SethuTransparencyModeCommand : public pc::util::cli::NumericSignalSetter<vfc::int32_t>
{
public:
    SethuTransparencyModeCommand()
      : pc::util::cli::NumericSignalSetter<vfc::int32_t>("HU huTransparencyMode ")
    {
    }

    void sendSignal( vfc::int32_t f_value ) override
    {
        auto& l_container           = cc::daddy::CustomDaddyPorts::sm_HUTransparentModeDaddy_SenderPort.reserve();
        l_container.m_Data          = static_cast<cc::target::common::ETransparentMode>(f_value);
        cc::daddy::CustomDaddyPorts::sm_HUTransparentModeDaddy_SenderPort.deliver();
    }
};

static SethuTransparencyModeCommand g_sethuTransparencyModeCommand;

//!
//! Set viewMode5x Command
//!
typedef std::array<pc::util::cli::IntValueNamePair, 19> HuDisplayMode5xMap;
const HuDisplayMode5xMap g_HuDisplayMode5xMapping = {
    static_cast<vfc::int32_t>(cc::target::common::EHUDisplayMode5x::INVALID),                                                                      "INVALID",
    static_cast<vfc::int32_t>(cc::target::common::EHUDisplayMode5x::VERTICAL_PLANVIEW_FRONT_ENLARGE__FRONT_MAIN_VIEW__LEFT_SIDE_VIEW),             "VERTICAL_PLANVIEW_FRONT_ENLARGE__FRONT_MAIN_VIEW__LEFT_SIDE_VIEW",
    static_cast<vfc::int32_t>(cc::target::common::EHUDisplayMode5x::VERTICAL_PLANVIEW_FRONT_ENLARGE__FRONT_MAIN_VIEW__RIGHT_SIDE_VIEW),            "VERTICAL_PLANVIEW_FRONT_ENLARGE__FRONT_MAIN_VIEW__RIGHT_SIDE_VIEW",
    static_cast<vfc::int32_t>(cc::target::common::EHUDisplayMode5x::VERTICAL_PLANVIEW_REAR_ENLARGE__REAR_MAIN_VIEW__LEFT_SIDE_VIEW),               "VERTICAL_PLANVIEW_REAR_ENLARGE__REAR_MAIN_VIEW__LEFT_SIDE_VIEW",
    static_cast<vfc::int32_t>(cc::target::common::EHUDisplayMode5x::VERTICAL_PLANVIEW_REAR_ENLARGE__REAR_MAIN_VIEW__RIGHT_SIDE_VIEW),              "VERTICAL_PLANVIEW_REAR_ENLARGE__REAR_MAIN_VIEW__RIGHT_SIDE_VIEW",
    static_cast<vfc::int32_t>(cc::target::common::EHUDisplayMode5x::VERTICAL_FULLSCREEN),                                                          "VERTICAL_FULLSCREEN",
    static_cast<vfc::int32_t>(cc::target::common::EHUDisplayMode5x::VERTICAL_FULLSCREEN_ENLARGE_FRONT),                                            "VERTICAL_FULLSCREEN_ENLARGE_FRONT",
    static_cast<vfc::int32_t>(cc::target::common::EHUDisplayMode5x::VERTICAL_FULLSCREEN_ENLARGE_REAR),                                             "VERTICAL_FULLSCREEN_ENLARGE_REAR",
    static_cast<vfc::int32_t>(cc::target::common::EHUDisplayMode5x::VERTICAL_PLANVIEW__DUAL_FRONT_WHEEL_VIEW__FRONT_SIDE_VIEW),                    "VERTICAL_PLANVIEW__DUAL_FRONT_WHEEL_VIEW__FRONT_SIDE_VIEW",
    static_cast<vfc::int32_t>(cc::target::common::EHUDisplayMode5x::VERTICAL_PLANVIEW__DUAL_FRONT_WHEEL_VIEW__REAR_SIDE_VIEW),                     "VERTICAL_PLANVIEW__DUAL_FRONT_WHEEL_VIEW__REAR_SIDE_VIEW",
    static_cast<vfc::int32_t>(cc::target::common::EHUDisplayMode5x::VERTICAL_PLANVIEW__DUAL_REAR_WHEEL_VIEW__FRONT_SIDE_VIEW),                     "VERTICAL_PLANVIEW__DUAL_REAR_WHEEL_VIEW__FRONT_SIDE_VIEW",
    static_cast<vfc::int32_t>(cc::target::common::EHUDisplayMode5x::VERTICAL_PLANVIEW__DUAL_REAR_WHEEL_VIEW__REAR_SIDE_VIEW),                      "VERTICAL_PLANVIEW__DUAL_REAR_WHEEL_VIEW__REAR_SIDE_VIEW",
    static_cast<vfc::int32_t>(cc::target::common::EHUDisplayMode5x::VERTICAL_PLANVIEW__DUAL_WHEEL_VIEW__FRONT_SIDE_VIEW),                          "VERTICAL_PLANVIEW__DUAL_WHEEL_VIEW__FRONT_SIDE_VIEW",
    static_cast<vfc::int32_t>(cc::target::common::EHUDisplayMode5x::VERTICAL_PLANVIEW__DUAL_WHEEL_VIEW__REAR_SIDE_VIEW),                           "VERTICAL_PLANVIEW__DUAL_WHEEL_VIEW__REAR_SIDE_VIEW",
    static_cast<vfc::int32_t>(cc::target::common::EHUDisplayMode5x::HORIZONTAL_PLANVIEW_FRONT_ENLARGE),                                            "HORIZONTAL_PLANVIEW_FRONT_ENLARGE",
    static_cast<vfc::int32_t>(cc::target::common::EHUDisplayMode5x::HORIZONTAL_PLANVIEW_REAR_ENLARGE),                                             "HORIZONTAL_PLANVIEW_REAR_ENLARGE",
    static_cast<vfc::int32_t>(cc::target::common::EHUDisplayMode5x::HORIZONTAL_PLANVIEW__FRONT_VIEW__LEFT_SIDE_FRONT_VIEW__RIGHT_SIDE_FRONT_VIEW), "HORIZONTAL_PLANVIEW__FRONT_VIEW__LEFT_SIDE_FRONT_VIEW__RIGHT_SIDE_FRONT_VIEW",
    static_cast<vfc::int32_t>(cc::target::common::EHUDisplayMode5x::HORIZONTAL_PLANVIEW__REAR_VIEW__LEFT_SIDE_REAR_VIEW__RIGHT_SIDE_REAR_VIEW),    "HORIZONTAL_PLANVIEW__REAR_VIEW__LEFT_SIDE_REAR_VIEW__RIGHT_SIDE_REAR_VIEW",
    static_cast<vfc::int32_t>(cc::target::common::EHUDisplayMode5x::HORIZONTAL_SURROUND_VIEW),                                                     "HORIZONTAL_SURROUND_VIEW"
};
class SetviewMode5xCommand : public pc::util::cli::EnumSignalSetter<HuDisplayMode5xMap>
{
public:
    SetviewMode5xCommand(const HuDisplayMode5xMap& f_mapping)
      : pc::util::cli::EnumSignalSetter<HuDisplayMode5xMap>("HU viewMode5x", f_mapping)
    {
    }

    void sendSignal( vfc::int32_t f_value ) override
    {
        auto& l_container           = cc::daddy::CustomDaddyPorts::sm_HUDislayModeView5xDaddy_SenderPort.reserve();
        l_container.m_Data          = static_cast<cc::target::common::EHUDisplayMode5x>(f_value);
        cc::daddy::CustomDaddyPorts::sm_HUDislayModeView5xDaddy_SenderPort.deliver();
    }
};

static SetviewMode5xCommand g_setViewMode5xCommand(g_HuDisplayMode5xMapping);


//!
//! Color from Pdm and Variant
//!
class ColorPdmVariantCommand : public pc::util::cli::CommandCallback
{
public:
  bool invoke(std::istream& f_input, std::ostream& f_output, const pc::util::cli::CommandLineInterface&) override
  {
    vfc::int32_t l_parameter0 = 0;
    vfc::int32_t l_parameter1 = 0;
    f_input >> l_parameter0 >> l_parameter1;  // PRQA S 3803
    if (f_input.fail())
    {
      f_output << parseError;  // PRQA S 3803
      getHelp(f_output);
      return false;
    }
    cc::daddy::ColorIndexDaddy& l_colorPdmDaddy     = cc::daddy::CustomDaddyPorts::sm_PdmR5ToLinux_VehColorStsDaddy_SenderPort.reserve();
    // cc::daddy::ColorIndexDaddy& l_colorVariantDaddy = cc::daddy::CustomDaddyPorts::sm_Variant_VehColorStsDaddy_SenderPort.reserve();
    l_colorPdmDaddy.m_Data     = static_cast<vfc::uint8_t>(l_parameter0);
    // l_colorVariantDaddy.m_Data = static_cast<vfc::uint8_t>(l_parameter1);
    cc::daddy::CustomDaddyPorts::sm_PdmR5ToLinux_VehColorStsDaddy_SenderPort.deliver();
    // cc::daddy::CustomDaddyPorts::sm_Variant_VehColorStsDaddy_SenderPort.deliver();
    return true;
  }
  void getDescription(std::ostream& f_output) const override
  {
    f_output << "Set color from Pdm and Variant" << newline;  // PRQA S 3803
  }
  void getHelp(std::ostream& f_output) const override
  {
    f_output << "Color <Pdm> <Variant>" << newline;  // PRQA S 3803
  }
};
// example: ssi color 0 12
static ColorPdmVariantCommand g_colorPdmVariantCommand;


//!
//! Apg
//!
class SetAPGDataCommand : public pc::util::cli::CommandCallback
{
public:
  bool invoke(std::istream& f_input, std::ostream& f_output, const pc::util::cli::CommandLineInterface&) override
  {
    vfc::int32_t l_parameter0 = 0;
    vfc::int32_t l_parameter1 = 0;
    vfc::float32_t l_parameter2 = 0.f;
    f_input >> l_parameter0 >> l_parameter1 >> l_parameter2;  // PRQA S 3803
    if (f_input.fail())
    {
      f_output << parseError;  // PRQA S 3803
      getHelp(f_output);
      return false;
    }

    cc::daddy::APG_VehiclePathDaddy& l_travelDist = cc::daddy::CustomDaddyPorts::sm_PMA_TravelDistDesired_SenderPort.reserve();

    l_travelDist.m_Data.m_MoveType_en       = static_cast<vfc::int32_t>(l_parameter0);
    l_travelDist.m_Data.m_MoveDir_en        = static_cast<vfc::int32_t>(l_parameter1);
    l_travelDist.m_Data.m_TravelDistDesired = static_cast<vfc::float32_t>(l_parameter2);

    cc::daddy::CustomDaddyPorts::sm_PMA_TravelDistDesired_SenderPort.deliver();

    return true;
  }

  void getDescription(std::ostream& f_output) const override
  {
    f_output << "Set color from Pdm and Variant" << newline;  // PRQA S 3803
  }
  void getHelp(std::ostream& f_output) const override
  {
    f_output << "Color <Pdm> <Variant>" << newline;  // PRQA S 3803
  }
};
// example: ssi color 0 12
static SetAPGDataCommand g_apgDataCommand;

//!
//! SetImpostorTransparencyCommand
//!
class SetImpostorTransparencyCommand : public pc::util::cli::NumericSignalSetter<vfc::float32_t>
{
public:

  SetImpostorTransparencyCommand()
    : pc::util::cli::NumericSignalSetter<vfc::float32_t>("3D vehicle model transparency")
  {
  }

  void sendSignal(vfc::float32_t f_value) override
  {
    cc::daddy::ImpostorTransparencyDaddy& l_impostorTransparency = cc::daddy::CustomDaddyPorts::sm_impostorTransparencySenderPort.reserve();
    l_impostorTransparency.m_Data = f_value;
    cc::daddy::CustomDaddyPorts::sm_impostorTransparencySenderPort.deliver();
  }
};

static SetImpostorTransparencyCommand g_setImpostorTransparencyCommand;

//!
//! Set hu vehicle transparence Command
//!
class SetHUVehTranspStatusCommand : public pc::util::cli::NumericSignalSetter<vfc::int32_t>
{
public:
    SetHUVehTranspStatusCommand()
      : pc::util::cli::NumericSignalSetter<vfc::int32_t>("HU Vehicle Transparence ")
    {
    }

    void sendSignal( vfc::int32_t f_value ) override
    {
        cc::daddy::HUvehTransReq_t& l_container = cc::daddy::CustomDaddyPorts::sm_HUvehTransReqDaddy_SenderPort.reserve();
        l_container.m_Data          = static_cast<vfc::uint8_t>(f_value);
        cc::daddy::CustomDaddyPorts::sm_HUvehTransReqDaddy_SenderPort.deliver();
    }
};

static SetHUVehTranspStatusCommand g_setHUVehTranspStatusCommand;

//!
//! Set hu vehicle transparence level Command
//!
class SetHUVehTranspLevelCommand : public pc::util::cli::NumericSignalSetter<vfc::int32_t>
{
public:
    SetHUVehTranspLevelCommand()
      : pc::util::cli::NumericSignalSetter<vfc::int32_t>("HU Vehicle Transparence Level")
    {
    }

    void sendSignal( vfc::int32_t f_value ) override
    {
        cc::daddy::HUvehTransLevel_t& l_container = cc::daddy::CustomDaddyPorts::sm_HUvehTransLevelDaddy_SenderPort.reserve();
        l_container.m_Data          = static_cast<vfc::uint8_t>(f_value);
        cc::daddy::CustomDaddyPorts::sm_HUvehTransLevelDaddy_SenderPort.deliver();
    }
};

static SetHUVehTranspLevelCommand g_setHUVehTranspLevelCommand;

//!
//! Set hu freemode azimuth angle Command
//!
class SetHUFreemodeAzimuthAngleCommand : public pc::util::cli::NumericSignalSetter<vfc::uint16_t>
{
public:
    SetHUFreemodeAzimuthAngleCommand()
      : pc::util::cli::NumericSignalSetter<vfc::uint16_t>("HU azimuth Freemode Angle")
    {
    }

    void sendSignal( vfc::uint16_t f_value ) override
    {
        cc::daddy::HUFreemodeAngleDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_HUfreemodeAngleAzimuthDaddy_SenderPort.reserve();
        l_container.m_Data          = static_cast<vfc::uint16_t>(f_value);
        cc::daddy::CustomDaddyPorts::sm_HUfreemodeAngleAzimuthDaddy_SenderPort.deliver();
    }
};

static SetHUFreemodeAzimuthAngleCommand g_setHUFreemodeAngleAzimuthCommand;

//!
//! Set hu freemode angle Command
//!
class SetHUFreemodeElevationAngleCommand : public pc::util::cli::NumericSignalSetter<vfc::uint16_t>
{
public:
    SetHUFreemodeElevationAngleCommand()
      : pc::util::cli::NumericSignalSetter<vfc::uint16_t>("HU Freemode ElevationAngle")
    {
    }

    void sendSignal( vfc::uint16_t f_value ) override
    {
        cc::daddy::HUFreemodeAngleDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_HUfreemodeAngleElevationDaddy_SenderPort.reserve();
        l_container.m_Data          = static_cast<vfc::uint16_t>(f_value);
        cc::daddy::CustomDaddyPorts::sm_HUfreemodeAngleElevationDaddy_SenderPort.deliver();
    }
};

static SetHUFreemodeElevationAngleCommand g_setHUFreemodeAngleElevationCommand;

//!
//! Set parking direction Command
//!
class SetParkingdirectionCommand : public pc::util::cli::NumericSignalSetter<vfc::int32_t>
{
public:
    SetParkingdirectionCommand()
      : pc::util::cli::NumericSignalSetter<vfc::int32_t>("Parking direction ")
    {
    }

    void sendSignal( vfc::int32_t f_value ) override
    {
        cc::daddy::ParkPSDirectionSelected_t& l_container = cc::daddy::CustomDaddyPorts::sm_ParkPSDirectionSelectedDaddy_SenderPort.reserve();
        l_container.m_Data          = static_cast<cc::target::common::rbp_Type_ParkManeuverType_en>(f_value); // PRQA S 3013
        cc::daddy::CustomDaddyPorts::sm_ParkPSDirectionSelectedDaddy_SenderPort.deliver();
    }
};

static SetParkingdirectionCommand g_setParkingdirectionCommand;

//!
//! Set parking slots Command
//!
class SetParkingSpaceStatusCommand : public pc::util::cli::CommandCallback
{
public:
  bool invoke(std::istream& f_input, std::ostream& f_output, const pc::util::cli::CommandLineInterface&) override
  {
    vfc::uint16_t l_parameter0 = 0u;
    vfc::float32_t l_parameter1 = 0.0f;
    vfc::uint16_t l_parameter2 = 0u;
    vfc::uint16_t l_parameter3 = 0u;
    vfc::uint16_t l_parameter4 = 0u;
    vfc::float32_t l_parameter5 = 0.0f;
    vfc::uint16_t l_parameter6 = 0u;
    vfc::uint16_t l_parameter7 = 0u;
    vfc::uint16_t l_parameter8 = 0u;
    vfc::float32_t l_parameter9 = 0.0f;
    vfc::uint16_t l_parameter10 = 0u;
    vfc::uint16_t l_parameter11 = 0u;
    vfc::uint16_t l_parameter12 = 0u;
    vfc::float32_t l_parameter13 = 0.0f;
    vfc::uint16_t l_parameter14 = 0u;
    vfc::uint16_t l_parameter15 = 0u;
    vfc::uint16_t l_parameter16 = 0u;
    vfc::float32_t l_parameter17 = 0.0f;
    vfc::uint16_t l_parameter18 = 0u;
    vfc::uint16_t l_parameter19 = 0u;
    vfc::uint16_t l_parameter20 = 0u;
    vfc::float32_t l_parameter21 = 0.0f;
    vfc::uint16_t l_parameter22 = 0u;
    vfc::uint16_t l_parameter23 = 0u;
    vfc::uint16_t l_parameter24 = 0u;
    vfc::float32_t l_parameter25 = 0.0f;
    vfc::uint16_t l_parameter26 = 0u;
    vfc::uint16_t l_parameter27 = 0u;
    vfc::uint16_t l_parameter28 = 0u;
    vfc::float32_t l_parameter29 = 0.0f;
    vfc::uint16_t l_parameter30 = 0u;
    vfc::uint16_t l_parameter31 = 0u;
    f_input >> l_parameter0 >> l_parameter1 >> l_parameter2 >> l_parameter3 >> l_parameter4 >> l_parameter5
            >> l_parameter6 >> l_parameter7 >> l_parameter8 >> l_parameter9 >> l_parameter10 >> l_parameter11
            >> l_parameter12 >> l_parameter13 >> l_parameter14 >> l_parameter15 >> l_parameter16 >> l_parameter17
            >> l_parameter18 >> l_parameter19 >> l_parameter20 >> l_parameter21 >> l_parameter22 >> l_parameter23
            >> l_parameter24 >> l_parameter25 >> l_parameter26 >> l_parameter27 >> l_parameter28 >> l_parameter29
            >> l_parameter30 >> l_parameter31;  // PRQA S 3803
    if (f_input.fail())
    {
      f_output << parseError;    // PRQA S 3803
      getHelp(f_output);
      return false;
    }
    cc::daddy::ParkAPA_ParkSpace_t& l_container = cc::daddy::CustomDaddyPorts::sm_ParkAPA_ParkSpaceDaddy_SenderPort.reserve();
    l_container.m_Data[0][0].m_APA_PrkgSlot  = static_cast<cc::target::common::EPARKSlotStsR2L>(l_parameter0); // PRQA S 3013
    l_container.m_Data[0][0].m_APA_PrkgSlotSta_f32   = l_parameter1;
    l_container.m_Data[0][0].m_APA_PSType   = static_cast<cc::target::common::EFAPAParkSlotType>(l_parameter2); // PRQA S 3013
    l_container.m_Data[0][0].m_APA_ParkManeuverType  = static_cast<cc::target::common::rbp_Type_ParkManeuverType_en>(l_parameter3); // PRQA S 3013
    l_container.m_Data[0][1].m_APA_PrkgSlot  = static_cast<cc::target::common::EPARKSlotStsR2L>(l_parameter4); // PRQA S 3013
    l_container.m_Data[0][1].m_APA_PrkgSlotSta_f32   = l_parameter5;
    l_container.m_Data[0][1].m_APA_PSType   = static_cast<cc::target::common::EFAPAParkSlotType>(l_parameter6); // PRQA S 3013
    l_container.m_Data[0][1].m_APA_ParkManeuverType  = static_cast<cc::target::common::rbp_Type_ParkManeuverType_en>(l_parameter7); // PRQA S 3013
    l_container.m_Data[0][2].m_APA_PrkgSlot  = static_cast<cc::target::common::EPARKSlotStsR2L>(l_parameter8); // PRQA S 3013
    l_container.m_Data[0][2].m_APA_PrkgSlotSta_f32   = l_parameter9;
    l_container.m_Data[0][2].m_APA_PSType   = static_cast<cc::target::common::EFAPAParkSlotType>(l_parameter10); // PRQA S 3013
    l_container.m_Data[0][2].m_APA_ParkManeuverType  = static_cast<cc::target::common::rbp_Type_ParkManeuverType_en>(l_parameter11); // PRQA S 3013
    // l_container.m_Data[0][3].m_APA_PrkgSlot  = static_cast<cc::target::common::EPARKSlotStsR2L>(l_parameter12); // PRQA S 3013
    // l_container.m_Data[0][3].m_APA_PrkgSlotSta_f32   = l_parameter13;
    // l_container.m_Data[0][3].m_APA_PSType   = static_cast<cc::target::common::EFAPAParkSlotType>(l_parameter14); // PRQA S 3013
    // l_container.m_Data[0][3].m_APA_ParkManeuverType  = static_cast<cc::target::common::rbp_Type_ParkManeuverType_en>(l_parameter15); // PRQA S 3013
    l_container.m_Data[1][0].m_APA_PrkgSlot  = static_cast<cc::target::common::EPARKSlotStsR2L>(l_parameter16); // PRQA S 3013
    l_container.m_Data[1][0].m_APA_PrkgSlotSta_f32   = l_parameter17;
    l_container.m_Data[1][0].m_APA_PSType   = static_cast<cc::target::common::EFAPAParkSlotType>(l_parameter18); // PRQA S 3013
    l_container.m_Data[1][0].m_APA_ParkManeuverType  = static_cast<cc::target::common::rbp_Type_ParkManeuverType_en>(l_parameter19); // PRQA S 3013
    l_container.m_Data[1][1].m_APA_PrkgSlot  = static_cast<cc::target::common::EPARKSlotStsR2L>(l_parameter20); // PRQA S 3013
    l_container.m_Data[1][1].m_APA_PrkgSlotSta_f32   = l_parameter21;
    l_container.m_Data[1][1].m_APA_PSType   = static_cast<cc::target::common::EFAPAParkSlotType>(l_parameter22); // PRQA S 3013
    l_container.m_Data[1][1].m_APA_ParkManeuverType  = static_cast<cc::target::common::rbp_Type_ParkManeuverType_en>(l_parameter23); // PRQA S 3013
    l_container.m_Data[1][2].m_APA_PrkgSlot  = static_cast<cc::target::common::EPARKSlotStsR2L>(l_parameter24); // PRQA S 3013
    l_container.m_Data[1][2].m_APA_PrkgSlotSta_f32   = l_parameter25;
    l_container.m_Data[1][2].m_APA_PSType   = static_cast<cc::target::common::EFAPAParkSlotType>(l_parameter26); // PRQA S 3013
    l_container.m_Data[1][2].m_APA_ParkManeuverType  = static_cast<cc::target::common::rbp_Type_ParkManeuverType_en>(l_parameter27); // PRQA S 3013
    // l_container.m_Data[1][3].m_APA_PrkgSlot  = static_cast<cc::target::common::EPARKSlotStsR2L>(l_parameter28); // PRQA S 3013
    // l_container.m_Data[1][3].m_APA_PrkgSlotSta_f32   = l_parameter29;
    // l_container.m_Data[1][3].m_APA_PSType   = static_cast<cc::target::common::EFAPAParkSlotType>(l_parameter30); // PRQA S 3013
    // l_container.m_Data[1][3].m_APA_ParkManeuverType  = static_cast<cc::target::common::rbp_Type_ParkManeuverType_en>(l_parameter31); // PRQA S 3013
    cc::daddy::CustomDaddyPorts::sm_ParkAPA_ParkSpaceDaddy_SenderPort.deliver();
    return true;
  }
  void getDescription(std::ostream& f_output) const override
  {
    f_output << "Set parking space status & angle & type & maneuver Type" << newline;    // PRQA S 3803
  }
  void getHelp(std::ostream& f_output) const override
  {
    f_output << "8 slots including status <x> angle <x> type <x> naneuver type <x> for each" << newline; // PRQA S 3803
  }
};
static SetParkingSpaceStatusCommand g_setParkingSpaceCommand;


//! swinfo
class SetSwInfoCommand : public pc::util::cli::NumericSignalSetter<vfc::int32_t>
{
public:

    SetSwInfoCommand()
        : pc::util::cli::NumericSignalSetter<vfc::int32_t>("SW Info ")
    {
    }

    void sendSignal(vfc::int32_t f_mode) override
    {
        cc::daddy::SWInfoDaddy_t & l_container = cc::daddy::CustomDaddyPorts::sm_SWInfoDaddy_SenderPort.reserve();

        if (1==f_mode)
        {
            l_container.m_Data.m_InternalSoftwareID[0] = 0x02u;
            l_container.m_Data.m_InternalSoftwareID[1] = 0x8au;
            l_container.m_Data.m_InternalSoftwareID[2] = 0x2eu;
            l_container.m_Data.m_InternalSoftwareID[3] = 0x30u;
            l_container.m_Data.m_InternalSoftwareID[4] = 0x5fu;
            l_container.m_Data.m_InternalSoftwareID[5] = 0x53u;
            l_container.m_Data.m_InternalSoftwareID[6] = 0x54u;
            l_container.m_Data.m_InternalSoftwareID[7] = 0x45u;
            l_container.m_Data.m_InternalSoftwareID[8] = 0x53u;
            l_container.m_Data.m_InternalSoftwareID[9] = 0x00u;

            l_container.m_Data.m_HardwareVersion[0] = 1u;
            l_container.m_Data.m_HardwareVersion[1] = 16u;
            l_container.m_Data.m_HardwareVersion[2] = 3u;
            l_container.m_Data.m_HardwareVersion[3] = 28u;
        }
        else if(2==f_mode)
        {
            l_container.m_Data.m_InternalSoftwareID[0] = 0x52u;
            l_container.m_Data.m_InternalSoftwareID[1] = 0x39u;
            l_container.m_Data.m_InternalSoftwareID[2] = 0x2eu;
            l_container.m_Data.m_InternalSoftwareID[3] = 0x30u;
            l_container.m_Data.m_InternalSoftwareID[4] = 0x52u;
            l_container.m_Data.m_InternalSoftwareID[5] = 0x43u;
            l_container.m_Data.m_InternalSoftwareID[6] = 0x30u;
            l_container.m_Data.m_InternalSoftwareID[7] = 0x34u;
            l_container.m_Data.m_InternalSoftwareID[8] = 0x44u;
            l_container.m_Data.m_InternalSoftwareID[9] = 0x5fu;
            l_container.m_Data.m_InternalSoftwareID[10] = 0x53u;
            l_container.m_Data.m_InternalSoftwareID[11] = 0x54u;
            l_container.m_Data.m_InternalSoftwareID[12] = 0x45u;
            l_container.m_Data.m_InternalSoftwareID[13] = 0x53u;
            l_container.m_Data.m_InternalSoftwareID[14] = 0x00u;
            l_container.m_Data.m_InternalSoftwareID[15] = 0x00u;

            l_container.m_Data.m_HardwareVersion[0] = 0xffu;
            l_container.m_Data.m_HardwareVersion[1] = 0x15u;
            l_container.m_Data.m_HardwareVersion[2] = 12u;
            l_container.m_Data.m_HardwareVersion[3] = 31u;
        }
        else
        {
            // Do nothing
        }

        cc::daddy::CustomDaddyPorts::sm_SWInfoDaddy_SenderPort.deliver();

        XLOG_INFO_OS(g_COMSocketContext) << "Setting SW Info" << f_mode << XLOG_ENDL;//PRQA S 4060
    }
};
static SetSwInfoCommand g_setSwInfoCommand;

// //!
// //! DiagnosisModeCommand
// //!
// class ToggleFloorPlateApplicationModeCommand : public  pc::util::cli::CommandCallback
// {
// public:

//   virtual bool invoke(std::istream& /*f_input*/, std::ostream& /*f_output*/, const pc::util::cli::CommandLineInterface& ) override
//   {
//     // Reuse last delivered DiagnosisMode state
//     cc::daddy::SolidBasePlateStateDaddy& l_solidBasePlateStateDaddy = cc::daddy::CustomDaddyPorts::sm_groundplane_triangle_SenderPort.reserveLastDelivery();

//     // toggle application mode
//     if(cc::daddy::SolidBasePlateState::activated == l_solidBasePlateStateDaddy.m_Data )
//     {
//       l_solidBasePlateStateDaddy.m_Data = cc::daddy::SolidBasePlateState::deactivated;
//     }
//     else
//     {
//       l_solidBasePlateStateDaddy.m_Data = cc::daddy::SolidBasePlateState::activated;
//     }

//     cc::daddy::CustomDaddyPorts::sm_groundplane_triangle_SenderPort.deliver();
//     return true;
//   }

//   virtual void getDescription(std::ostream& f_output) const override
//   {
//     f_output << "Allows toggling of floor plate applicaton mode." << newline;  // PRQA S 3803
//   }
// };

// pc::util::cli::Command<ToggleFloorPlateApplicationModeCommand> g_ToggleFloorPlateApplicationModeCommand("floorplate_application");

//!
//! Set Door Lock status command
//!
class SetDoorLockStsCommand : public pc::util::cli::CommandCallback
{
public:
  bool invoke(std::istream& f_input, std::ostream& f_output, const pc::util::cli::CommandLineInterface&) override
  {
    vfc::uint16_t l_parameter0 = 0u;
    vfc::uint16_t l_parameter1 = 0u;
    vfc::uint16_t l_parameter2 = 0u;
    vfc::uint16_t l_parameter3 = 0u;
    vfc::uint16_t l_parameter4 = 0u;
    f_input >> l_parameter0 >> l_parameter1 >> l_parameter2 >> l_parameter3 >> l_parameter4;  // PRQA S 3803
    if (f_input.fail())
    {
      f_output << parseError;  // PRQA S 3803
      getHelp(f_output);
      return false;
    }

    if ( true == cc::daddy::CustomDaddyPorts::sm_DoorLockSts_SenderPort.isConnected() )
    {
        cc::daddy::DoorLockStsDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_DoorLockSts_SenderPort.reserve();

        l_container.m_Data.m_FLdoorLockStatus = static_cast<cc::target::common::EStateDoorLock>(l_parameter0);    // PRQA S 3013
        l_container.m_Data.m_FRdoorLockStatus = static_cast<cc::target::common::EStateDoorLock>(l_parameter1);    // PRQA S 3013
        l_container.m_Data.m_RLdoorLockStatus = static_cast<cc::target::common::EStateDoorLock>(l_parameter2);    // PRQA S 3013
        l_container.m_Data.m_RRdoorLockStatus = static_cast<cc::target::common::EStateDoorLock>(l_parameter3);    // PRQA S 3013
        l_container.m_Data.m_TrunkLockStatus  = static_cast<cc::target::common::EStateDoorLock>(l_parameter4);    // PRQA S 3013

        cc::daddy::CustomDaddyPorts::sm_DoorLockSts_SenderPort.deliver();
    }
    return true;
  }

  void getDescription(std::ostream& f_output) const override
  {
      f_output << "Set customer door lock status" << newline;  // PRQA S 3803
  }

  void getHelp(std::ostream& f_output) const override
  {
      f_output
      << "Set customer door lock status(0 == OPEN, 1 == CLOSE, 2 == INVALID): " << newline
      << "front left:            bit0" << newline
      << "front right:           bit1" << newline
      << "rear left:             bit2" << newline
      << "rear right:            bit3" << newline;  // PRQA S 3803
  }
};
static SetDoorLockStsCommand g_setDoorLockStsCommand;

//!
//! TriggerCalibrationCommand
//!
class TriggerCalibrationCommand : public  pc::util::cli::CommandCallback
{
public:

  bool invoke(std::istream&, std::ostream&, const pc::util::cli::CommandLineInterface& ) override
  {
    pc::daddy::BaseDaddyPorts::sm_cameraCalibrationDaddySenderPort.reserveLastDelivery();
    pc::daddy::BaseDaddyPorts::sm_cameraCalibrationDaddySenderPort.deliver();
    return true;
  }

  void getDescription(std::ostream& f_output) const override
  {
    f_output << "Resends the current camera calibration data in order force a recomputation of depending components" << newline;  // PRQA S 3803
  }
};

static pc::util::cli::Command<TriggerCalibrationCommand> g_triggerCalibrationCommand("calib");


//!
//! ParkBreakPedalBeenReleasedBfCommand
//!
class ParkBreakPedalBeenReleasedBf : public pc::util::cli::CommandCallback
{
public:

  bool invoke(std::istream&  f_input, std::ostream& f_output, const pc::util::cli::CommandLineInterface&) override
  {
    vfc::uint16_t l_parameter = 0u;

    f_input >> l_parameter;  // PRQA S 3803
    if (f_input.fail())
    {
      f_output << parseError;  // PRQA S 3803
      getHelp(f_output);
      return false;
    }

    cc::daddy::ParkBreakPedalBeenReleasedBf_t& l_pData = cc::daddy::CustomDaddyPorts::sm_brakePedalBeenReleasedBeforeDaddy_SenderPort.reserve();
    l_pData.m_Data = static_cast<bool>(l_parameter);
    cc::daddy::CustomDaddyPorts::sm_brakePedalBeenReleasedBeforeDaddy_SenderPort.deliver();

    return true;
  }

  void getDescription(std::ostream& f_output) const override
  {
    f_output << "Generates park whether it is last move" << newline;  // PRQA S 3803
  }

  void getHelp(std::ostream& f_output) const override
  {
    f_output << "<park lots (1 = 4 lots | 2 = 2 lots )>" << newline;  // PRQA S 3803
  }
};

static ParkBreakPedalBeenReleasedBf g_parkBreakPedalBeenReleasedBfCommand;

class ParkbrkPedlAppld : public pc::util::cli::CommandCallback
{
public:

  bool invoke(std::istream&  f_input, std::ostream& f_output, const pc::util::cli::CommandLineInterface&) override
  {
    vfc::uint16_t l_parameter = 0u;

    f_input >> l_parameter;  // PRQA S 3803
    if (f_input.fail())
    {
      f_output << parseError;  // PRQA S 3803
      getHelp(f_output);
      return false;
    }

    cc::daddy::ParkbrkPedlAppld_t& l_pData = cc::daddy::CustomDaddyPorts::sm_brakePedalAppliedDaddy_SenderPort.reserve();
    l_pData.m_Data = static_cast<cc::target::common::EBrkPedlAppldFlg>(l_parameter);
    cc::daddy::CustomDaddyPorts::sm_brakePedalAppliedDaddy_SenderPort.deliver();

    return true;
  }

  void getDescription(std::ostream& f_output) const override
  {
    f_output << "Generates park whether it is last move" << newline;  // PRQA S 3803
  }

  void getHelp(std::ostream& f_output) const override
  {
    f_output << "<park lots (1 = 4 lots | 2 = 2 lots )>" << newline;  // PRQA S 3803
  }
};

static ParkbrkPedlAppld g_parkbrkPedlAppldCommand;

//!
//! Set test Command
//!
class SetComponentTestCommand : public pc::util::cli::NumericSignalSetter<vfc::int32_t>
{
public:
    SetComponentTestCommand()
      : pc::util::cli::NumericSignalSetter<vfc::int32_t>("Component Test")
    {
    }

    void sendSignal( vfc::int32_t f_value ) override
    {
        cc::daddy::ComponentTestSwitchDaddy& l_container = cc::daddy::CustomDaddyPorts::sm_componentTestSwitch_SenderPort.reserve();
        l_container.m_Data = static_cast<cc::daddy::ComponentTestSwitch>(f_value); // PRQA S 3013
        cc::daddy::CustomDaddyPorts::sm_componentTestSwitch_SenderPort.deliver();
    }
};

static SetComponentTestCommand g_setComponentTestCommand;

//!
//! Set Side View
//!
class SetSideViewCommand : public pc::util::cli::NumericSignalSetter<vfc::int32_t>
{
public:
    SetSideViewCommand()
      : pc::util::cli::NumericSignalSetter<vfc::int32_t>("Side View ")
    {
    }

    void sendSignal( vfc::int32_t f_value ) override
    {
        cc::daddy::SideViewEnableStatusDaddy& l_container = cc::daddy::CustomDaddyPorts::sm_sideViewEnableStatus_SenderPort.reserve();
        l_container.m_Data = static_cast<cc::daddy::SideViewEnableStatus>(f_value);
        cc::daddy::CustomDaddyPorts::sm_sideViewEnableStatus_SenderPort.deliver();
    }
};
static SetSideViewCommand g_setSideViewCommand;


//!
//! Set Vehicle Type
//!
class SetVehicleTypeCommand : public pc::util::cli::NumericSignalSetter<vfc::int32_t>
{
public:
    SetVehicleTypeCommand()
      : pc::util::cli::NumericSignalSetter<vfc::int32_t>("Vehicle type ")
    {
    }

    void sendSignal( vfc::int32_t f_value ) override
    {
        switch (f_value)
        {
        case 1:
            g_dataContainerToSvs.m_vehicleInfo.vehicleType = "sghl";
            break;
        case 2:
            g_dataContainerToSvs.m_vehicleInfo.vehicleType = "htebu";
            break;
        case 3:
            g_dataContainerToSvs.m_vehicleInfo.vehicleType = "sghza";
            break;
        default:
            g_dataContainerToSvs.m_vehicleInfo.vehicleType = "sghza";
            break;
        }
    }
};
static SetVehicleTypeCommand g_setVehicleTypeCommand;


//!
//! Set View ID -- SGHL
//!
class SetViewIdSghlCommand : public pc::util::cli::NumericSignalSetter<vfc::int32_t>
{
public:
    SetViewIdSghlCommand()
      : pc::util::cli::NumericSignalSetter<vfc::int32_t>("SGHL View ID ")
    {
    }

    void sendSignal( vfc::int32_t f_value ) override
    {
        EScreenID l_screenId = EScreenID_NO_CHANGE;
        cc::daddy::SideViewEnableStatus l_sideEnableSts = cc::daddy::SIDEVIEW_DISABLE;
        cc::daddy::TopViewEnableStatus l_topEnableSts = cc::daddy::TOPVIEW_DISABLE;

        cc::util::common::mapViewIDFromSghl(f_value, l_screenId, l_sideEnableSts, l_topEnableSts);

        cc::daddy::HUDislayModeSwitchDaddy_t& l_EScreenID = cc::daddy::CustomDaddyPorts::sm_HUDislayModeSwitchDaddy_SenderPort.reserve();
        l_EScreenID.m_Data = static_cast<vfc::uint8_t>(l_screenId);   // PRQA S 3013
        cc::daddy::CustomDaddyPorts::sm_HUDislayModeSwitchDaddy_SenderPort.deliver();

        cc::daddy::SideViewEnableStatusDaddy& l_container = cc::daddy::CustomDaddyPorts::sm_sideViewEnableStatus_SenderPort.reserve();
        l_container.m_Data = l_sideEnableSts;
        cc::daddy::CustomDaddyPorts::sm_sideViewEnableStatus_SenderPort.deliver();

        cc::daddy::TopViewEnableStatusDaddy& l_container1 = cc::daddy::CustomDaddyPorts::sm_topViewEnableStatus_SenderPort.reserve();
        l_container1.m_Data = l_topEnableSts;
        cc::daddy::CustomDaddyPorts::sm_topViewEnableStatus_SenderPort.deliver();
    }
};
static SetViewIdSghlCommand g_setViewIdSghlCommand;

//!
//! Set View ID -- ST2
//!
class SetViewIdST2Command : public pc::util::cli::NumericSignalSetter<vfc::int32_t>
{
public:
    SetViewIdST2Command()
      : pc::util::cli::NumericSignalSetter<vfc::int32_t>("SGHL View ID ")
    {
    }

    void sendSignal( vfc::int32_t f_value ) override
    {
        EScreenID l_screenId = EScreenID_NO_CHANGE;
        cc::daddy::SideViewEnableStatus l_sideEnableSts = cc::daddy::SIDEVIEW_DISABLE;
        cc::daddy::TopViewEnableStatus l_topEnableSts = cc::daddy::TOPVIEW_DISABLE;

        cc::util::common::mapViewIDFromST2(f_value, l_screenId, l_sideEnableSts, l_topEnableSts);

        cc::daddy::HUDislayModeSwitchDaddy_t& l_EScreenID = cc::daddy::CustomDaddyPorts::sm_HUDislayModeSwitchDaddy_SenderPort.reserve();
        l_EScreenID.m_Data = static_cast<vfc::uint8_t>(l_screenId);   // PRQA S 3013
        cc::daddy::CustomDaddyPorts::sm_HUDislayModeSwitchDaddy_SenderPort.deliver();

        cc::daddy::SideViewEnableStatusDaddy& l_container = cc::daddy::CustomDaddyPorts::sm_sideViewEnableStatus_SenderPort.reserve();
        l_container.m_Data = l_sideEnableSts;
        cc::daddy::CustomDaddyPorts::sm_sideViewEnableStatus_SenderPort.deliver();

        cc::daddy::TopViewEnableStatusDaddy& l_container1 = cc::daddy::CustomDaddyPorts::sm_topViewEnableStatus_SenderPort.reserve();
        l_container1.m_Data = l_topEnableSts;
        cc::daddy::CustomDaddyPorts::sm_topViewEnableStatus_SenderPort.deliver();
    }
};
static SetViewIdST2Command g_setViewIdST2Command;

//!
//! Set View ID -- SGHZA
//!
class SetViewIdSghzaCommand : public pc::util::cli::NumericSignalSetter<vfc::int32_t>
{
public:
    SetViewIdSghzaCommand()
      : pc::util::cli::NumericSignalSetter<vfc::int32_t>("SGHL View ID ")
    {
    }

    void sendSignal( vfc::int32_t f_value ) override
    {
        EScreenID l_screenId = EScreenID_NO_CHANGE;
        cc::daddy::SideViewEnableStatus l_sideEnableSts = cc::daddy::SIDEVIEW_DISABLE;
        cc::daddy::TopViewEnableStatus l_topEnableSts = cc::daddy::TOPVIEW_DISABLE;

        cc::util::common::mapViewIDFromSghza(f_value, l_screenId, l_sideEnableSts, l_topEnableSts);

        cc::daddy::HUDislayModeSwitchDaddy_t& l_EScreenID = cc::daddy::CustomDaddyPorts::sm_HUDislayModeSwitchDaddy_SenderPort.reserve();
        l_EScreenID.m_Data = static_cast<vfc::uint8_t>(l_screenId);   // PRQA S 3013
        cc::daddy::CustomDaddyPorts::sm_HUDislayModeSwitchDaddy_SenderPort.deliver();

        cc::daddy::SideViewEnableStatusDaddy& l_container = cc::daddy::CustomDaddyPorts::sm_sideViewEnableStatus_SenderPort.reserve();
        l_container.m_Data = l_sideEnableSts;
        cc::daddy::CustomDaddyPorts::sm_sideViewEnableStatus_SenderPort.deliver();

        cc::daddy::TopViewEnableStatusDaddy& l_container1 = cc::daddy::CustomDaddyPorts::sm_topViewEnableStatus_SenderPort.reserve();
        l_container1.m_Data = l_topEnableSts;
        cc::daddy::CustomDaddyPorts::sm_topViewEnableStatus_SenderPort.deliver();
    }
};
static SetViewIdSghzaCommand g_setViewIdSghzaCommand;

class SetFadeOutCommand : public pc::util::cli::CommandCallback
{
public:
    SetFadeOutCommand()
    {
    }

    ~SetFadeOutCommand() override
    {
    }

    bool
    invoke(std::istream& f_input, std::ostream& f_output, const pc::util::cli::CommandLineInterface& f_cli) override
    {
        int   l_viewId   = 0;
        float l_fadeTime = 0;
        f_input >> l_viewId >> l_fadeTime;
        if (f_input.fail())
        {
            f_output << parseError;
            getHelp(f_output);
            return false;
        }

        pc::core::Framework* l_framework = nullptr;
        bool                 l_res       = f_cli.getUserData().getValue<pc::core::Framework*>(l_framework);
        if (!l_framework)
        {
            f_output << "Can't get Framework" << newline;
            return false;
        }

        pc::core::Scene* l_scene = l_framework->getScene();
        pc::core::View*  l_view  = l_scene->getView(l_viewId);
        if (!l_view)
        {
            f_output << "Can't find View" << newline;
            return false;
        }

        l_framework->getAnimationManager()->playAnimation(new pc::animation::FadeOutAnimation(l_view, l_fadeTime));

        return true;
    }

    void getHelp(std::ostream& f_output) const override
    {
        f_output << "fade <view_id> <fade_time>" << newline;
        f_output << "view_id must be added to the custom screen!" << newline;
    }

    void getDescription(std::ostream& f_output) const override
    {
        f_output << "Trigger a fade out animation on the given view" << newline;
    }

private:
};

static SetFadeOutCommand g_setFadeOutCommand;


//!
//! Set calibration status
//!
class SetCalibStsCommand : public pc::util::cli::NumericSignalSetter<vfc::int32_t>
{
public:
    SetCalibStsCommand()
        : pc::util::cli::NumericSignalSetter<vfc::int32_t>("Calib status ")
    {
    }

    void sendSignal( vfc::int32_t f_value ) override
    {
        pc::daddy::CalibrationStsDaddy& l_calibSts = pc::daddy::BaseDaddyPorts::sm_calibrationStsSenderPort.reserve();
        l_calibSts.m_Data = static_cast<bool>(f_value);   // PRQA S 3013
        pc::daddy::BaseDaddyPorts::sm_calibrationStsSenderPort.deliver();
    }
};
static SetCalibStsCommand g_setCalibStsCommand;

//!
//! Set Hemisphere status
//!
class SetHemisphereStsCommand : public pc::util::cli::CommandCallback
{
public:
  bool invoke(std::istream& f_input, std::ostream& f_output, const pc::util::cli::CommandLineInterface&) override
  {
    vfc::uint16_t l_parameter0 = 0u;
    vfc::uint16_t l_parameter1 = 0u;
    vfc::uint16_t l_parameter2 = 0u;
    f_input >> l_parameter0 >> l_parameter1 >> l_parameter2;  // PRQA S 3803
    if (f_input.fail())
    {
      f_output << parseError;  // PRQA S 3803
      getHelp(f_output);
      return false;
    }

    if ( true == cc::daddy::CustomDaddyPorts::sm_HemisphereParameterData_SenderPort.isConnected() )
    {
        cc::daddy::HemisphereParameterData_Daddy& l_container = cc::daddy::CustomDaddyPorts::sm_HemisphereParameterData_SenderPort.reserve();

        // Deliver data to viewmode state machine
        cc::daddy::HemisphereParameterData_Daddy& l_parameterDaddy = cc::daddy::CustomDaddyPorts::sm_HemisphereParameterData_SenderPort.reserve();
        l_parameterDaddy.m_Data.m_camPosAxis1Rq = static_cast<vfc::uint16_t>(l_parameter0);
        l_parameterDaddy.m_Data.m_camPosAxis2Rq = static_cast<vfc::uint16_t>(l_parameter1);
        l_parameterDaddy.m_Data.m_zoomFactorRq = static_cast<vfc::uint16_t>(l_parameter2);
        cc::daddy::CustomDaddyPorts::sm_HemisphereParameterData_SenderPort.deliver();
    }
    return true;
  }

  void getDescription(std::ostream& f_output) const override
  {
      f_output << "Set Hemisphere status" << newline;  // PRQA S 3803
  }

  void getHelp(std::ostream& f_output) const override
  {
  }
};
static SetHemisphereStsCommand g_setHemisphereStsCommand;



class SetCustomLogoColorCommand : public pc::util::cli::CommandCallback
{
public:

    bool invoke(std::istream& f_input, std::ostream& f_output, const pc::util::cli::CommandLineInterface&) override
    {
        vfc::uint32_t l_isGoldenLogo = 0u;

        f_input >> l_isGoldenLogo;

        if (!f_input.fail())
        {
            if ( true == cc::daddy::CustomDaddyPorts::sm_GoldenEmblem_SenderPort.isConnected() )
            {
                auto& l_rData = cc::daddy::CustomDaddyPorts::sm_GoldenEmblem_SenderPort.reserve();
                l_rData.m_Data = static_cast<bool>(l_isGoldenLogo);
                cc::daddy::CustomDaddyPorts::sm_GoldenEmblem_SenderPort.deliver();
            }

            return true;
        }

        f_output << parseError;  // PRQA S 3803
        getHelp(f_output);
        return false;
    }

    void getDescription(std::ostream& f_output) const override
    {
        f_output << "Set logo color(sliver or golden)" << newline;  // PRQA S 3803
    }

    void getHelp(std::ostream& f_output) const override
    {
        f_output << "Set logo color(sliver or golden): " << newline
        << "0                   sliver" << newline
        << "1                   golden" << newline;
    }
};

static SetCustomLogoColorCommand g_custLogoColorCommand;


//!
//! SetCustomSignalCommand
class SetCustomSignalCommand : public pc::util::cli::SetSignalCommand
{
public:

  SetCustomSignalCommand()
  {
    addCommand("vm",                &g_setViewModeCommand);
#if USE_RADAR_WALL
#else
    addCommand("pfuss",             &g_setObstacleCommand);
#endif
    addCommand("uss",               &g_setCustomObstacleCommand);
    addCommand("stopdist",          &g_setDistanceToStopCommand);
    addCommand("stopline",          &g_setStopLineLocationCommand);
    addCommand("mdir",              &g_setMovementDirectionCommand);
    addCommand("vbsack",            &g_setViewBufferStatusACKCommand);
    addCommand("vbs",               &g_setViewBufferStatusCommand);
    addCommand("powermode",         &g_setPowerModeCommand);
    addCommand("steerang",          &g_setSteeringAngleCommand);
    addCommand("showreq",           &g_setHUShowReqModeCommand);
    addCommand("showsuspend",       &g_setHUShowSuspendModeCommand);
    addCommand("VRSwitch",          &g_setVRSwitchModeCommand);
    addCommand("FCPButton",         &g_setFCPButtonCommand);
    addCommand("viewmode",          &g_setHUSVSViewModeCommand);
    addCommand("tube",              &g_setHUOverlayTubeModeCommand);
    addCommand("baseplt",           &g_setHUBasePlateModeCommand);
    addCommand("pos3d",             &g_headUnitCamPos3DCommand);
    addCommand("zoom3d",            &g_headUnitZoomFactor3DCommand);
    addCommand("type3d",            &g_headUnitTouchTypeCommand);
    addCommand("touch3d",           &g_headUnitRotate3DCommand);
    addCommand("overlaydist",       &g_setHUOverlayDistModeCommand);
    addCommand("parksts",           &g_setParkStatusCommand);
    addCommand("parktype",          &g_setParkTypeCommand);
    addCommand("parktypevariant",   &g_setParkTypeVariantCommand);
    addCommand("parkmode",          &g_setParkModeCommand);
    addCommand("parkAPAPARKMODE",   &g_setAPAPARKMODECommand);
    addCommand("parkRPAavailable",  &g_setRPAAvaliableCommand);
    addCommand("parkingTypeSeld",   &g_setParkngTypeSeldCommand);
    addCommand("parkside",          &g_setParkSideCommand);
    addCommand("parkfuncind",       &g_setParkFunctionIndCommand);
    addCommand("parkquitind",       &g_setParkQuitIndCommand);
    addCommand("parkquitindext",    &g_setParkQuitIndExtCommand);
    addCommand("parkrecoind",       &g_setParkRecoverIndCommand);
    addCommand("parkdriverind",     &g_setParkDriverIndCommand);
    addCommand("parkdriverindext",  &g_setParkDriverIndExtCommand);
    addCommand("parkdriverindsearch",  &g_setParkDriverIndSearchCommand);
    addCommand("parkoutsideavl",    &g_setParkOutSideAvailCommand);
    addCommand("parkreqreleasebtn", &g_setParkReqReleaseBtnCommand);
    addCommand("parkRPADriverSelected", &g_setParkRPADriverSelectedBtnCommand);
    addCommand("frontobj",          &g_setParkFrontObjIndCommand);
    addCommand("FrCamDeg",          &g_setIsFrontCamDegCommand);
    addCommand("parkspace",         &g_setParkSpaceCommand);
    addCommand("parkingspace",      &g_setParkingSpaceCommand);
    addCommand("ECUDeg",            &g_setIsECUDegCommand);
    addCommand("OdoDeg",            &g_setIsOdoDegCommand);
    addCommand("AllDeg",            &g_setIsAllDegCommand);
    addCommand("odometry",          &g_setOdometryCommand);
    addCommand("huhmi",             &g_setHUHmiCommand);
    addCommand("touchEvent",        &g_setHUTouchEventCommand);
    addCommand("pasbutton",         &g_setPasButtonStatusCommand);
    addCommand("pasWarnTone",       &g_setPasWarnToneCommand);
    addCommand("passt",             &g_setPasStatusCommand);
    addCommand("sdwst",             &g_setSdwStatusCommand);
    addCommand("fctaleft",          &g_setFctaLeftCommand);
    addCommand("fctaright",         &g_setFctaRightCommand);
    addCommand("rctaleft",          &g_setRctaLeftCommand);
    addCommand("rctaright",         &g_setRctaRightCommand);
    addCommand("calib",             &g_setCalibStatusCommand);
    // addCommand("cpccalib",          &g_setCpcCalibStatusCommand);
    addCommand("dispmode",          &g_setHUDislayModeSwitchCommand);
    addCommand("dispmodeExpand",    &g_setHUDislayModeExpandCommand);
    addCommand("imgmode",           &g_setHUImageWorkModeCommand);
    addCommand("dispmodeExpandNew", &g_setHUDislayModeExpandNewCommand);
    addCommand("rotate",            &g_setHURotateStatusCommand);
    addCommand("daynight",          &g_setdayNightThemeCommand);
    addCommand("transp",            &g_setImpostorTransparencyCommand);
    addCommand("vehtrans",          &g_setHUVehTranspStatusCommand);
    addCommand("vehtranslevel",     &g_setHUVehTranspLevelCommand);
    addCommand("HUfreemodeAngleAzimuth",   &g_setHUFreemodeAngleAzimuthCommand);
    addCommand("HUfreemodeAngleElevation",   &g_setHUFreemodeAngleElevationCommand);
    addCommand("remotescreenmode",  &g_setRemoteScreenModeCommand);
    addCommand("obstacleOverlay",   &g_setobstacleOverlayCommand);
    addCommand("HuTransparencyMode",   &g_sethuTransparencyModeCommand);
    addCommand("HuViewMode5x",   &g_setViewMode5xCommand);
    addCommand("color" ,               &g_colorPdmVariantCommand);
    addCommand("parkdirc",             &g_setParkingdirectionCommand);
    addCommand("fpActive",             &g_setFreeParkingActiveCommand);
    addCommand("swinfo",               &g_setSwInfoCommand);
    addCommand("doorlocksts",          &g_setDoorLockStsCommand);
    addCommand("apg",                  &g_apgDataCommand);
    addCommand("diagTriggerCpc",       &g_diagTriggerCpcCommand);
    // addCommand("svsToCpc",             &g_setSvsOverlayToCpcCommand);
    addCommand("test",                 &g_setComponentTestCommand);
    addCommand("releasePedal",         &g_parkBreakPedalBeenReleasedBfCommand);
    addCommand("hucolor",              &g_setHUDislayColorCommand);
    addCommand("brakePedal",           &g_parkbrkPedlAppldCommand);
    addCommand("hucpcflag",            &g_setHUCpcFlagCommand);
    addCommand("sideview",             &g_setSideViewCommand);
    addCommand("vehtype",              &g_setVehicleTypeCommand);
    addCommand("sghlid",               &g_setViewIdSghlCommand);
    addCommand("st2id",                &g_setViewIdST2Command);
    addCommand("sghzaid",              &g_setViewIdSghzaCommand);
    addCommand("trailerflag",          &g_setTrailerModeCommand);
    addCommand("setApadata",           &g_setApaDataCommand);
    addCommand("freepark",             &g_SetFreeparkingCommand);
    addCommand("fade",                 &g_setFadeOutCommand);
    addCommand("calibst",              &g_setCalibStsCommand);
    addCommand("hemsphere",            &g_setHemisphereStsCommand);
    addCommand("logocolor",            &g_custLogoColorCommand);
  }
};

static pc::util::cli::Command<SetCustomSignalCommand> g_setSignalCommand("ssi");


//!
//! GetCustomSignalCommand
//!
class GetCustomSignalCommand : public pc::util::cli::GetSignalCommand
{
public:

  GetCustomSignalCommand()
  {
    addCommand("vm", &g_getViewModeCommand);
    addCommand("sys", &g_getSystemStateCommand);
    addCommand("veh", &g_getVehicleStateCommand);
    addCommand("hu", &g_getHUSettingCommand);
    addCommand("view", &g_getCurrentViewCommand);
    addCommand("cam", &g_getCamViewStsCommand);
  }
};

static pc::util::cli::Command<GetCustomSignalCommand> g_getSignalCommand("gsi");


} // namespace cli
} // namespace util
} // namesace cc

