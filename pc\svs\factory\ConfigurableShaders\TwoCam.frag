#version 300 es

#ifdef USE_SAMPLER_EXTERNAL_OES
  #extension GL_OES_EGL_image_external_essl3 : require
  #if defined ENABLE_SHARPNESS_HARMONIZATION || defined ENABLE_TNF
    #extension GL_EXT_YUV_target : require
  #endif
#endif

precision highp float;

in highp vec2 v_texLeftCam;
in highp vec2 v_texRightCam;

#if !defined(LEFT_SOURCE) || !defined(RIGHT_SOURCE) || !defined(LEFT_TARGET) || !defined(RIGHT_TARGET)
#error Shader not configured correctly
#endif

#define CAM_ON 0
#define CAM_DISABLED 1
#define CAM_OFF 2

in vec2 v_alphaCam;

#ifdef ENABLE_CHAMAELEON
in vec3 v_cham_correctionMidLeft;
in vec3 v_cham_correctionMidRight;
in vec3 v_cham_correctionLeft;
in vec3 v_cham_correctionRight;

in float v_cham_weightRightMid;
in float v_cham_weightMidLeft;
#endif // ENABLE_CHAMAELEON

#ifdef USE_SAMPLER_EXTERNAL_OES
  #if defined ENABLE_SHARPNESS_HARMONIZATION || defined ENABLE_TNF
    uniform __samplerExternal2DY2YEXT s_texCamsLeft;
    uniform __samplerExternal2DY2YEXT s_texCamsRight;
  #else
    uniform samplerExternalOES s_texCamsLeft;
    uniform samplerExternalOES s_texCamsRight;
  #endif
#else
  uniform sampler2D s_texCamsLeft;
  uniform sampler2D s_texCamsRight;
#endif

#ifdef ENABLE_TNF
  uniform sampler2D s_filteredYLeft;
  uniform sampler2D s_filteredYRight;
#endif

#if (LEFT_SOURCE != LEFT_TARGET) || (RIGHT_SOURCE != RIGHT_TARGET)
uniform float u_FadeFactor;
#endif

uniform vec4 u_camDisabledColor;
uniform vec4 u_camOffColor;

uniform int u_camLeft;
uniform int u_camRight;
uniform int u_isTexCombine;
uniform float u_brightFactor;

vec2 getUv(vec2 f_uv, int f_cam)
{
  if (1 == u_isTexCombine)
  {
    vec2 l_uv = vec2(0.0, f_uv.y);
    if (0 == f_cam)  // front
    {
      l_uv.x = 0.75 + f_uv.x * 0.25;
    }
    else if (1 == f_cam)  // right
    {
      l_uv.x = 0.5 + f_uv.x * 0.25;
    }
    else if (2 == f_cam)  // rear
    {
      l_uv.x = f_uv.x * 0.25;
    }
    else  // left
    {
      l_uv.x = 0.25 + f_uv.x * 0.25;
    }
    return l_uv;
  }
  else
  {
    return f_uv;
  }
}

// for the SIL
#ifndef USE_SAMPLER_EXTERNAL_OES
  vec4 yuv_2_rgb_sil(vec4 src)
  {
      vec4 yuva = vec4((src.x-16.0/255.0), (src.y - 0.5), (src.z - 0.5), 1.0);

      vec4 res;
      res.r = 1.164 * yuva.x                  + 1.596 * yuva.z;
      res.g = 1.164 * yuva.x - 0.392 * yuva.y - 0.813 * yuva.z;
      res.b = 1.164 * yuva.x + 2.017 * yuva.y;

      res.a = src.a;

      return res;
  }


  vec4 rgb_2_yuv_sil(vec4 src)
  {
      vec4 res;
      res.x =  0.257 * src.r + 0.504 * src.g + 0.098 * src.b + 16.0/255.0;
      res.y = -0.148 * src.r - 0.291 * src.g + 0.439 * src.b + 0.5;
      res.z =  0.439 * src.r - 0.368 * src.g - 0.071 * src.b + 0.5;

      res.a = src.a;

      return res;
  }
#endif

#ifdef DEBUG_SHARPNESS_HARMONIZATION

vec4 sixColors(float x)
{
    float r, g, b;
    float d;

    if (x < 0.0)
    {
        r = 0.0;
        g = 1.0;
        b = 1.0;
    }
    else if (x < 0.2)
    {
        d = ((x - 0.0) * 5.0);
        r = 0.0 * (1.0 - d) + 0.0 * d;
        g = 1.0;
        b = 1.0 * (1.0 - d) + 0.0 * d;
    }
    else if (x < 0.4)
    {
        d = ((x - 0.2) * 5.0);
        r = 0.0 * (1.0 - d) + 1.0 * d;
        g = 1.0;
        b = 0.0;
    }
    else if (x < 0.6)
    {
        d = ((x - 0.4) * 5.0);
        r = 1.0;
        g = 1.0 * (1.0 - d) + 0.0 * d;
        b = 0.0;
    }
    else if (x < 0.8)
    {
        d = ((x - 0.6) * 5.0);
        r = 1.0;
        g = 0.0;
        b = 0.0 * (1.0 - d) + 1.0 * d;
    }
    else if (x < 1.0)
    {
        d = ((x - 0.8) * 5.0);
        r = 1.0 * (1.0 - d) + 0.0 * d;
        g = 0.0;
        b = 1.0;
    }
    else
    {
        r = 0.0;
        g = 0.0;
        b = 1.0;
    }
    return vec4(r, g, b, 1.0);
}

#endif // DEBUG_SHARPNESS_HARMONIZATION

#ifdef ENABLE_SHARPNESS_HARMONIZATION
uniform vec2 u_sharpeningSmoothingFactors;
uniform float u_maxSharpening;

vec4 getCameraTextureLeft(vec2 f_uv, vec2 f_uv_tnf)
{
    vec2 sizeOfTexture = vec2(textureSize(s_texCamsLeft, 0));
    vec2 texelSize = 1.0 / sizeOfTexture;

    // pixel density stuff for adaptive factors and right pixel selection
    highp vec2 deltaX = dFdx(f_uv);
    highp vec2 deltaY = dFdy(f_uv);

    // Normalize the direction vectors
    vec2 dirU = normalize(deltaX);
    vec2 dirV = normalize(deltaY);

    #ifndef USE_SAMPLER_EXTERNAL_OES
      vec4 center    = texture(s_texCamsLeft, f_uv); //RGBA
      vec3 centerYUV = rgb_2_yuv_sil(center).rgb;
    #else
      vec3 centerYUV = texture(s_texCamsLeft, f_uv).rgb; // texture stays in YUV due to __samplerExternal2DY2YEXT
    #endif

    #ifdef ENABLE_TNF
      float centerY = texture(s_filteredYLeft, f_uv_tnf).r;
      // Fetch neighboring pixels using the direction vectors scaled by texel size
      float leftY_U  = texture(s_filteredYLeft, f_uv_tnf - dirU * texelSize.x).r;
      float rightY_U = texture(s_filteredYLeft, f_uv_tnf + dirU * texelSize.x).r;
      float upY_V    = texture(s_filteredYLeft, f_uv_tnf + dirV * texelSize.y).r;
      float downY_V  = texture(s_filteredYLeft, f_uv_tnf - dirV * texelSize.y).r;
    #else
      float centerY = centerYUV.r;
      #ifndef USE_SAMPLER_EXTERNAL_OES
        vec4 left_U    = texture(s_texCamsLeft, f_uv - dirU * texelSize.x);
        vec4 right_U   = texture(s_texCamsLeft, f_uv + dirU * texelSize.x);
        vec4 up_V      = texture(s_texCamsLeft, f_uv + dirV * texelSize.y);
        vec4 down_V    = texture(s_texCamsLeft, f_uv - dirV * texelSize.y);

        float leftY_U  = rgb_2_yuv_sil(left_U).r;
        float rightY_U = rgb_2_yuv_sil(right_U).r;
        float upY_V    = rgb_2_yuv_sil(up_V).r;
        float downY_V  = rgb_2_yuv_sil(down_V).r;
      #else
        float leftY_U   = texture(s_texCamsLeft, f_uv - dirU * texelSize.x).r;
        float rightY_U  = texture(s_texCamsLeft, f_uv + dirU * texelSize.x).r;
        float upY_V     = texture(s_texCamsLeft, f_uv + dirV * texelSize.y).r;
        float downY_V   = texture(s_texCamsLeft, f_uv - dirV * texelSize.y).r;
      #endif
    #endif

    // gaussian blur filter, 1/4, 1/2, 1/4
    float blurX = (leftY_U + 2.0 * centerY + rightY_U ) / 4.0;
    float blurY = (upY_V + 2.0 * centerY + downY_V ) / 4.0;

    float highPassX = centerY - blurX;
    float highPassY = centerY - blurY;

    // input pixels per output fragment
    float ratioX = length(deltaX)*sizeOfTexture.x;
    float ratioY = length(deltaY)*sizeOfTexture.y;

    int needsSmoothingX = int(step(1.0, ratioX));
    int needsSmoothingY = int(step(1.0, ratioY));

    float factorX = 0.0;
    float factorY = 0.0;

    // use linear interpolation (from mix function) to find a sharpness/smoothing setting corresponding to the calculated pixel density
    // according to formula sharpening_or_smoothing_factor * (1 - ratio)
    // The clamping of the factors to -1.0 limits the applied degree of smoothing
    factorX = clamp(mix(u_sharpeningSmoothingFactors[needsSmoothingX], 0.0, ratioX), -1.0, u_maxSharpening);
    factorY = clamp(mix(u_sharpeningSmoothingFactors[needsSmoothingY], 0.0, ratioY), -1.0, u_maxSharpening);
#ifdef DEBUG_SHARPNESS_HARMONIZATION
    // The pixel density visualization overlaid by the original image is selected when sharpening and smoothing factors are zero
    bvec2 isZero = equal(u_sharpeningSmoothingFactors, vec2(0.0, 0.0));
    if (isZero.x && isZero.y)
    {
        // input pixels per output fragment
        float ratio = ratioX * ratioY;
        // display texture value together with color
        #ifndef USE_SAMPLER_EXTERNAL_OES
          return sixColors(ratio) * yuv_2_rgb_sil(vec4(centerYUV, 1.0));
        #else
          return sixColors(ratio) * vec4(yuv_2_rgb(centerYUV, itu_601_full_range), 1.0);
        #endif
    }
    else // NORMALIZED
    {
        // normalized outputs of pixel densities in x- and y-direction on red and green channels
        // e.g. a red value of 140 corresponds to pixel density in X of 1.4, a green value of 36 corresponds to a pixel
        // density in Y of 0.36
        return vec4(ratioX * 100.0 / 255.0, ratioY * 100.0 / 255.0, 0.0, 1.0);
        // return vec4(needsSmoothingX * 255, needsSmoothingY * 255, 0.0, 1.0);
    }
#else
        float yFiltered = centerY + factorX * highPassX + factorY * highPassY;
        vec3 yuvFinal = vec3(yFiltered, centerYUV.g, centerYUV.b);
        #ifndef USE_SAMPLER_EXTERNAL_OES
          return yuv_2_rgb_sil(vec4(yuvFinal, 1.0));
        #else
          return vec4(yuv_2_rgb(yuvFinal, itu_601_full_range), 1.0);
        #endif
#endif // DEBUG_SHARPNESS_HARMONIZATION
}

vec4 getCameraTextureRight(vec2 f_uv, vec2 f_uv_tnf)
{
    vec2 sizeOfTexture = vec2(textureSize(s_texCamsRight, 0));
    vec2 texelSize = 1.0 / sizeOfTexture;

    // pixel density stuff for adaptive factors and right pixel selection
    highp vec2 deltaX = dFdx(f_uv);
    highp vec2 deltaY = dFdy(f_uv);

    // Normalize the direction vectors
    vec2 dirU = normalize(deltaX);
    vec2 dirV = normalize(deltaY);

    #ifndef USE_SAMPLER_EXTERNAL_OES
      vec4 center    = texture(s_texCamsRight, f_uv); //RGBA
      vec3 centerYUV = rgb_2_yuv_sil(center).rgb;
    #else
      vec3 centerYUV = texture(s_texCamsRight, f_uv).rgb; // texture stays in YUV due to __samplerExternal2DY2YEXT
    #endif

    #ifdef ENABLE_TNF
      float centerY = texture(s_filteredYRight, f_uv_tnf).r;
      // Fetch neighboring pixels using the direction vectors scaled by texel size
      float leftY_U  = texture(s_filteredYRight, f_uv_tnf - dirU * texelSize.x).r;
      float rightY_U = texture(s_filteredYRight, f_uv_tnf + dirU * texelSize.x).r;
      float upY_V    = texture(s_filteredYRight, f_uv_tnf + dirV * texelSize.y).r;
      float downY_V  = texture(s_filteredYRight, f_uv_tnf - dirV * texelSize.y).r;
    #else
      float centerY = centerYUV.r;
      #ifndef USE_SAMPLER_EXTERNAL_OES
        vec4 left_U    = texture(s_texCamsRight, f_uv - dirU * texelSize.x);
        vec4 right_U   = texture(s_texCamsRight, f_uv + dirU * texelSize.x);
        vec4 up_V      = texture(s_texCamsRight, f_uv + dirV * texelSize.y);
        vec4 down_V    = texture(s_texCamsRight, f_uv - dirV * texelSize.y);

        float leftY_U  = rgb_2_yuv_sil(left_U).r;
        float rightY_U = rgb_2_yuv_sil(right_U).r;
        float upY_V    = rgb_2_yuv_sil(up_V).r;
        float downY_V  = rgb_2_yuv_sil(down_V).r;
      #else
        float leftY_U   = texture(s_texCamsRight, f_uv - dirU * texelSize.x).r;
        float rightY_U  = texture(s_texCamsRight, f_uv + dirU * texelSize.x).r;
        float upY_V     = texture(s_texCamsRight, f_uv + dirV * texelSize.y).r;
        float downY_V   = texture(s_texCamsRight, f_uv - dirV * texelSize.y).r;
      #endif
    #endif

    // gaussian blur filter, 1/4, 1/2, 1/4
    float blurX = (leftY_U + 2.0 * centerY + rightY_U ) / 4.0;
    float blurY = (upY_V + 2.0 * centerY + downY_V ) / 4.0;

    float highPassX = centerY - blurX;
    float highPassY = centerY - blurY;

    // input pixels per output fragment
    float ratioX = length(deltaX)*sizeOfTexture.x;
    float ratioY = length(deltaY)*sizeOfTexture.y;

    int needsSmoothingX = int(step(1.0, ratioX));
    int needsSmoothingY = int(step(1.0, ratioY));

    float factorX = 0.0;
    float factorY = 0.0;

    // use linear interpolation (from mix function) to find a sharpness/smoothing setting corresponding to the calculated pixel density
    // according to formula sharpening_or_smoothing_factor * (1 - ratio)
    // The clamping of the factors to -1.0 limits the applied degree of smoothing
    factorX = clamp(mix(u_sharpeningSmoothingFactors[needsSmoothingX], 0.0, ratioX), -1.0, u_maxSharpening);
    factorY = clamp(mix(u_sharpeningSmoothingFactors[needsSmoothingY], 0.0, ratioY), -1.0, u_maxSharpening);
#ifdef DEBUG_SHARPNESS_HARMONIZATION
    // The pixel density visualization overlaid by the original image is selected when sharpening and smoothing factors are zero
    bvec2 isZero = equal(u_sharpeningSmoothingFactors, vec2(0.0, 0.0));
    if (isZero.x && isZero.y)
    {
        // input pixels per output fragment
        float ratio = ratioX * ratioY;
        // display texture value together with color
        #ifndef USE_SAMPLER_EXTERNAL_OES
          return sixColors(ratio) * yuv_2_rgb_sil(vec4(centerYUV, 1.0));
        #else
          return sixColors(ratio) * vec4(yuv_2_rgb(centerYUV, itu_601_full_range), 1.0);
        #endif
    }
    else // NORMALIZED
    {
        // normalized outputs of pixel densities in x- and y-direction on red and green channels
        // e.g. a red value of 140 corresponds to pixel density in X of 1.4, a green value of 36 corresponds to a pixel
        // density in Y of 0.36
        return vec4(ratioX * 100.0 / 255.0, ratioY * 100.0 / 255.0, 0.0, 1.0);
        // return vec4(needsSmoothingX * 255, needsSmoothingY * 255, 0.0, 1.0);
    }
#else
        float yFiltered = centerY + factorX * highPassX + factorY * highPassY;
        vec3 yuvFinal = vec3(yFiltered, centerYUV.g, centerYUV.b);
        #ifndef USE_SAMPLER_EXTERNAL_OES
          return yuv_2_rgb_sil(vec4(yuvFinal, 1.0));
        #else
          return vec4(yuv_2_rgb(yuvFinal, itu_601_full_range), 1.0);
        #endif
#endif // DEBUG_SHARPNESS_HARMONIZATION
}

#else

vec4 getCameraTextureLeft(vec2 f_uv, vec2 f_uv_tnf)
{
    #ifdef ENABLE_TNF
      float filteredY = texture(s_filteredYLeft, f_uv_tnf).r;
      #ifndef USE_SAMPLER_EXTERNAL_OES
        vec2 UV = rgb_2_yuv_sil(texture(s_texCamsLeft, f_uv)).gb;
        return yuv_2_rgb_sil(vec4(filteredY, UV.x, UV.y, 1.0));
      #else
        vec2 UV = texture(s_texCamsLeft, f_uv).gb;
        return vec4(yuv_2_rgb(vec3(filteredY, UV.x, UV.y), itu_601_full_range), 1.0);
      #endif
    #else
      return texture(s_texCamsLeft, f_uv);
    #endif
}

vec4 getCameraTextureRight(vec2 f_uv, vec2 f_uv_tnf)
{
    #ifdef ENABLE_TNF
      float filteredY = texture(s_filteredYRight, f_uv_tnf).r;
      #ifndef USE_SAMPLER_EXTERNAL_OES
        vec2 UV = rgb_2_yuv_sil(texture(s_texCamsRight, f_uv)).gb;
        return yuv_2_rgb_sil(vec4(filteredY, UV.x, UV.y, 1.0));
      #else
        vec2 UV = texture(s_texCamsRight, f_uv).gb;
        return vec4(yuv_2_rgb(vec3(filteredY, UV.x, UV.y), itu_601_full_range), 1.0);
      #endif
    #else
      return texture(s_texCamsRight, f_uv);
    #endif
}
#endif // ENABLE_SHARPNESS_HARMONIZATION

vec4 getLeftCameraBlended(vec2 f_uv)
{
    // Blending case:
    // Get source color
#if (LEFT_SOURCE == CAM_DISABLED)
    vec4 sourceColor = getCameraTextureLeft(getUv(f_uv, u_camLeft), f_uv) * u_camDisabledColor;
#elif (LEFT_SOURCE == CAM_OFF)
    vec4 sourceColor = u_camOffColor;
#else
    vec4 sourceColor = getCameraTextureLeft(getUv(f_uv, u_camLeft), f_uv);
#ifdef ENABLE_CHAMAELEON
    vec3 sourceColorMidLeft = v_cham_correctionMidLeft * sourceColor.rgb;
    vec3 sourceColorLeft = v_cham_correctionLeft * sourceColor.rgb;
    sourceColor = vec4(mix(sourceColorLeft, sourceColorMidLeft, v_cham_weightMidLeft), 1.);
#endif // ENABLE_CHAMAELEON
#endif

#if (LEFT_SOURCE == LEFT_TARGET)
    return sourceColor;
#else
    // Get target color
#if (LEFT_TARGET == CAM_DISABLED)
    vec4 targetColor = getCameraTextureLeft(getUv(f_uv, u_camLeft), f_uv) * u_camDisabledColor;
#elif (LEFT_TARGET == CAM_OFF)
    vec4 targetColor = u_camOffColor;
#else
    vec4 targetColor = getCameraTextureLeft(getUv(f_uv, u_camLeft), f_uv);
#ifdef ENABLE_CHAMAELEON
    vec3 targetColorMidLeft = v_cham_correctionMidLeft * targetColor.rgb;
    vec3 targetColorLeft = v_cham_correctionLeft * targetColor.rgb;
    targetColor = vec4(mix(targetColorLeft, targetColorMidLeft, v_cham_weightMidLeft), 1.);
#endif // ENABLE_CHAMAELEON
#endif

    return mix(sourceColor, targetColor, u_FadeFactor);
#endif
}

vec4 getRightCameraBlended(vec2 f_uv)
{
    // Get source color
#if (RIGHT_SOURCE == CAM_DISABLED)
    vec4 sourceColor = getCameraTextureRight(getUv(f_uv, u_camRight), f_uv) * u_camDisabledColor;
#elif (RIGHT_SOURCE == CAM_OFF)
    vec4 sourceColor = u_camOffColor;
#else
    vec4 sourceColor = getCameraTextureRight(getUv(f_uv, u_camRight), f_uv);
#ifdef ENABLE_CHAMAELEON
    vec3 sourceColorMidRight = v_cham_correctionMidRight * sourceColor.rgb;
    vec3 sourceColorRight = v_cham_correctionRight * sourceColor.rgb;
    sourceColor = vec4(mix(sourceColorMidRight, sourceColorRight, v_cham_weightRightMid), 1.);   
#endif // ENABLE_CHAMAELEON
#endif

#if RIGHT_SOURCE == RIGHT_TARGET
    // No blending necessary
    return sourceColor;
#else
    // Get target color
#if (RIGHT_TARGET == CAM_DISABLED)
    vec4 targetColor = getCameraTextureRight(getUv(f_uv, u_camRight), f_uv) * u_camDisabledColor;
#elif (RIGHT_TARGET == CAM_OFF)
    vec4 targetColor = u_camOffColor;
#else
    vec4 targetColor = getCameraTextureRight(getUv(f_uv, u_camRight), f_uv);
#ifdef ENABLE_CHAMAELEON
    vec3 targetColorMidRight = v_cham_correctionMidRight * targetColor.rgb;
    vec3 targetColorRight = v_cham_correctionRight * targetColor.rgb;
    targetColor = vec4(mix(targetColorMidRight, targetColorRight, v_cham_weightRightMid), 1.);   
#endif // ENABLE_CHAMAELEON
#endif
    return mix(sourceColor, targetColor, u_FadeFactor);
#endif
}

out vec4 FragColor;

void main()
{
    vec4 l_colorRight = getRightCameraBlended(v_texRightCam.xy);
    vec4 l_colorLeft  = getLeftCameraBlended(v_texLeftCam.xy);

    // mix the alpha colors
    vec4 l_color;
    vec2 alphaCam = clamp(v_alphaCam, vec2(0.0), vec2(1.0));

    l_color = alphaCam.x * l_colorLeft;
    l_color += alphaCam.y * l_colorRight;

    FragColor = vec4(clamp((l_color).rgb * u_brightFactor, 0.0, 1.0), 1.0);
}
