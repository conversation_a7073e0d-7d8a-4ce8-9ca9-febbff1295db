/// @copyright (C) 2023 Robert <PERSON> GmbH.
/// The reproduction, distribution and utilization of this file as well as the
/// communication of its contents to others without express authorization is
/// prohibited. Offenders will be held liable for the payment of damages. All
/// rights reserved in the event of the grant of a patent, utility model or
/// design.
/// @file

#include "pc/svs/imp/chamaeleon/inc/chamaeleon.hpp"
#include "pc/svs/util/osgx/inc/Utils.h"

namespace rbp
{
namespace vis
{
namespace imp
{
namespace chamaeleon
{

Chamaeleon::Chamaeleon(
    pc::core::Framework*                    f_pFramework,
    pc::factory::RenderManagerRegistry*     f_pRegistry,
    pc::factory::SV3DNode*                  f_pFloor,
    const pc::virtcam::VirtualCamera* const f_virtCam)
    : osg::Group{}
    , m_chamaeleonBaseSignals{}
    , m_chamaeleonRois{f_pRegistry, f_pFloor, f_virtCam}
    , m_chamaeleonWBG{}
    , m_initialized{false}
    , m_framework{f_pFramework}
{
    // Comment in the following two line for debugging visu
    m_chamaeleonVisu = osg_ext::make_ref<visu::ChamaeleonVisu>(this);
    osg::Group::addChild(m_chamaeleonVisu);
    setNumChildrenRequiringUpdateTraversal(getNumChildrenRequiringUpdateTraversal() + 1U);
    init();
}

void Chamaeleon::init()
{
    setParameters();
    m_initialized = true;
}

void Chamaeleon::traverse(osg::NodeVisitor& f_nv)
{
    // It is necessary to divide the essential steps of Chamaeleon into two different visitor types.
    // We need the UPDATE_VISITOR to set the parameters and process the inputs received through the daddy port.
    // Furthermore, it is necessary to process the ROIs rendered in the CULL_VISITOR and deliver the resulting color
    // corrections.
    switch (f_nv.getVisitorType())
    {
    case osg::NodeVisitor::UPDATE_VISITOR:
    {
        if (g_settings->m_enableChamaeleon)
        {
            setParameters();

            receiveInputs();

            m_chamaeleonWBG.update();
        }

        deliverOutput();

        break;
    }
    case osg::NodeVisitor::CULL_VISITOR:
    {
        if (g_settings->m_enableChamaeleon)
        {
            if ((m_chamaeleonBaseSignals.getCalibState() == true) || g_settings->m_baseSignalData.m_ignoreCalibState)
            {
                m_chamaeleonRois.updateRoiCameras(f_nv);
            }

            m_chamaeleonEsti.updateEstimator(
                m_chamaeleonRois.getRoisAsTexture(),
                m_chamaeleonBaseSignals.getWorldROIValidityMask(),
                m_chamaeleonWBG.getRawGainsCam(),
                f_nv);
        }
    }
    break;
    default:
    {
        break;
    }
    }

    osg::Group::traverse(f_nv);
}

void Chamaeleon::setParameters()
{
    m_chamaeleonBaseSignals.setParameters(g_settings->m_baseSignalData);


    m_chamaeleonRois.setParameters(g_settings->m_roisData);

    m_chamaeleonWBG.setParameters(g_settings->m_wbGains);

    m_chamaeleonEsti.setParameters(g_settings->m_estimatorData);

    if (nullptr != m_chamaeleonVisu.get())
    {
        m_chamaeleonVisu->setParameters(g_settings->m_visu);
    }
}

void Chamaeleon::receiveInputs()
{
    const pc::daddy::DoorStateDaddy* const l_doorStateReceiverData{m_framework->m_doorStateReceiver.getData()};
    if (nullptr != l_doorStateReceiverData)
    {
        m_chamaeleonBaseSignals.setDoorState(l_doorStateReceiverData);
    }

    const pc::daddy::MirrorStateDaddy* const l_mirrorStateReceiverData{m_framework->m_mirrorStateReceiver.getData()};
    if (nullptr != l_mirrorStateReceiverData)
    {
        m_chamaeleonBaseSignals.setMirrorState(l_mirrorStateReceiverData);
    }

    const pc::daddy::CameraDegradationMaskDaddy* const l_degradationMaskReceiverData{
        m_framework->m_degradationMaskReceiver.getData()};
    if (nullptr != l_degradationMaskReceiverData)
    {
        m_chamaeleonBaseSignals.setDegradationState(l_degradationMaskReceiverData);
    }

    const pc::daddy::ISPDataDaddy* const l_ispDataReceiverData{m_framework->m_luminanceReceiver.getData()};
    if (nullptr != l_ispDataReceiverData)
    {
        m_chamaeleonWBG.setIspData(l_ispDataReceiverData);
    }

    const pc::daddy::CalibrationStsDaddy* l_calibStsReceiverData = m_framework->m_calibStsReceiver.getData();
    if (nullptr != l_calibStsReceiverData)
    {
        m_chamaeleonBaseSignals.setCalibState(l_calibStsReceiverData);
    }
}

void Chamaeleon::deliverOutput() const
{
    if (pc::daddy::BaseDaddyPorts::sm_chamaeleonDaddySenderPort.isConnected())
    {
        pc::daddy::ChamaeleonOutputDaddy& l_data{pc::daddy::BaseDaddyPorts::sm_chamaeleonDaddySenderPort.reserve()};

        l_data.m_Data.m_enableTopview        = g_settings->m_enableChamaeleon;
        l_data.m_Data.m_gainsAsTexture       = m_chamaeleonEsti.getGainsAsTexture();
        l_data.m_Data.m_enableSideBySide     = g_settings->m_enableChamaeleon && g_settings->m_enableSideBySide;
        l_data.m_Data.m_gainsAsVecSideBySide = m_chamaeleonWBG.getWbgSideCam();

        pc::daddy::BaseDaddyPorts::sm_chamaeleonDaddySenderPort.deliver();
    }
}

} // namespace chamaeleon
} // namespace imp
} // namespace vis
} // namespace rbp
