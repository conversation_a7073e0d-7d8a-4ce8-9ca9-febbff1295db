#===============================================================================
# Copyright (c) 2017 by <PERSON>. All rights reserved.
# This file is property of Robert <PERSON>. Any unauthorized copy, use or
# distribution is an offensive act against international law and may be
# prosecuted under federal law. Its content is company confidential.
#===============================================================================

#/////////////////////////////////////////////// QNX QAC ///////////////////////////////////////////////////////////////////////
ifeq ($(SOC),qc)

ifneq ($(SOC),qc)
COMPINC_gcc += $(SDKTARGETSYSROOT)/usr/include/libxml2
COMPINC_gcc += $(SDKTARGETSYSROOT)/usr/include/libdrm
COMPINC_gcc += $(SDKTARGETSYSROOT)/usr/include/opencv4
COMPINC_gcc += ./cc/$(subst _,/,$(TARGET_MODULE))/inc
endif

# add custom include path for standalone pc/generic
COMPINC_$(COMPILER_NAME) += ./pc/generic/

HARDWARE_TARGETS=target_ultrascale target_android
PROJECT_ROOT := $(shell git rev-parse --show-toplevel)

ifeq ($(TARGET_MODULE),target_adtf)
COMPDEF_$(COMPILER_NAME) += ADTF_LOGGING #sometimes the compiler name is msvc or msvc100 - take care about that
endif

# set defines in release builds to exclude socket connections, etc.
ifdef RELEASE
COMPDEF_$(COMPILER_NAME) += NO_SOCKET_SERVER
COMPDEF_$(COMPILER_NAME) += NO_RAPIDJSON
endif

ifdef IMGUI
COMPDEF_$(COMPILER_NAME) += ENABLE_IMGUI
endif

CHAMAELEON := 1
ifdef CHAMAELEON
COMPDEF_$(COMPILER_NAME) += CHAMAELEON
endif

# cpc settings.
COMPDEF_$(COMPILER_NAME) += CC_SV3D_BYD_SAAP

# why missing??
COMPDEF_$(COMPILER_NAME) += VFC_ENABLE_FLOAT64_TYPE

#-------------------------------------------------------------------------------
# Component library pathes
cc_$(TARGET_MODULE)_LIBPATH +=

#-------------------------------------------------------------------------------
# Component libraries
ifneq ($(COMPILER_NAME),qcc)
cc_$(TARGET_MODULE)_LIB += stdc++
cc_$(TARGET_MODULE)_LIB += rt
cc_$(TARGET_MODULE)_LIB += pthread
endif


ifeq ($(COMPILER_NAME),qcc)
CFLAGS += -DSUSE_OSG_STATIC_LIB
CXXFLAGS += -DSUSE_OSG_STATIC_LIB
CFLAGS += -DUSE_BUILD_IN_SHARDER_CODE #open: use build-in shader source code instead of cc/shaderstatic pc/svs
CXXFLAGS += -DUSE_BUILD_IN_SHARDER_CODE #open: use build-in shader source code instead of cc/shaderstatic pc/svs

CFLAGS += -DBYD_LINUX_LOG_FUNC_CALLBACK    #open: use byd linux log function
CXXFLAGS += -DBYD_LINUX_LOG_FUNC_CALLBACK  #open: use byd linux log function

# add lib
LDFLAGS += -Wl,--no-as-needed
LDFLAGS += -Wl,--start-group
LDFLAGS += -lstdc++
COMMON_LIB_PATH ?= ${LINUX_SDK_PATH}/lib64
LDFLAGS += -L${COMMON_LIB_PATH}/ -lGLESv2 \
-lEGL

# For vhm lib
VHM_LIB := $(PROJECT_ROOT)/bin/pmasip
$(info VHM_PATH is set to: !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!$(VHM_LIB))
LDFLAGS += -L${VHM_LIB} -lpma_sip



LDFLAGS += -L$(BSP_ROOT)/osg_static_lib/osgPlugins-3.2.3 -losgdb_zip -losgdb_png -losgdb_jpeg -losgdb_freetype -losgdb_ktx -losgdb_glsl -losgdb_dds -losgdb_openflight
LDFLAGS += -L$(BSP_ROOT)/osg_static_lib/osgPlugins-3.2.3 -losgdb_gz -losgdb_tgz -losgdb_tga -losgdb_rgb -losgdb_osgterrain -losgdb_osg -losgdb_ive
LDFLAGS += -L$(BSP_ROOT)/osg_static_lib/osgPlugins-3.2.3 -losgdb_deprecated_osgviewer -losgdb_deprecated_osgvolume -losgdb_deprecated_osgtext
LDFLAGS += -L$(BSP_ROOT)/osg_static_lib/osgPlugins-3.2.3 -losgdb_deprecated_osgterrain -losgdb_deprecated_osgsim
LDFLAGS += -L$(BSP_ROOT)/osg_static_lib/osgPlugins-3.2.3 -losgdb_deprecated_osgshadow -losgdb_deprecated_osgparticle -losgdb_deprecated_osgfx
LDFLAGS += -L$(BSP_ROOT)/osg_static_lib/osgPlugins-3.2.3 -losgdb_deprecated_osganimation -losgdb_deprecated_osg
LDFLAGS += -L$(BSP_ROOT)/osg_static_lib/osgPlugins-3.2.3 -losgdb_serializers_osgvolume -losgdb_serializers_osgtext -losgdb_serializers_osgterrain
LDFLAGS += -L$(BSP_ROOT)/osg_static_lib/osgPlugins-3.2.3 -losgdb_serializers_osgsim -losgdb_serializers_osgshadow
LDFLAGS += -L$(BSP_ROOT)/osg_static_lib/osgPlugins-3.2.3 -losgdb_serializers_osgparticle -losgdb_serializers_osgmanipulator
LDFLAGS += -L$(BSP_ROOT)/osg_static_lib/osgPlugins-3.2.3 -losgdb_serializers_osgfx -losgdb_serializers_osganimation -losgdb_serializers_osg
LDFLAGS += -L$(BSP_ROOT)/osg_static_lib/ -losgViewer  -losgVolume  -losgTerrain -losgText  -losgShadow  -losgSim  -losgParticle -losgManipulator
LDFLAGS += -L$(BSP_ROOT)/osg_static_lib/ -losgGA -losgFX -losgDB -losgAnimation -losgUtil -losg    -lOpenThreads

cc_$(TARGET_MODULE)_LIB += png
cc_$(TARGET_MODULE)_LIB += jpeg
cc_$(TARGET_MODULE)_LIB += freetype
cc_$(TARGET_MODULE)_LIB += opencv_core
cc_$(TARGET_MODULE)_LIB += opencv_imgproc
cc_$(TARGET_MODULE)_LIB += opencv_imgcodecs
cc_$(TARGET_MODULE)_LIB += opencv_features2d
cc_$(TARGET_MODULE)_LIB += opencv_calib3d
cc_$(TARGET_MODULE)_LIB += opencv_flann
cc_$(TARGET_MODULE)_LIB += opencv_video
else
cc_$(TARGET_MODULE)_LIB += OpenThreads
cc_$(TARGET_MODULE)_LIB += osg
cc_$(TARGET_MODULE)_LIB += osgDB
cc_$(TARGET_MODULE)_LIB += osgUtil
cc_$(TARGET_MODULE)_LIB += osgGA
cc_$(TARGET_MODULE)_LIB += osgViewer
cc_$(TARGET_MODULE)_LIB += osgText
cc_$(TARGET_MODULE)_LIB += osgFX
cc_$(TARGET_MODULE)_LIB += osgGA
cc_$(TARGET_MODULE)_LIB += osgDB
cc_$(TARGET_MODULE)_LIB += osgAnimation
cc_$(TARGET_MODULE)_LIB += osgSim
endif


cc_$(TARGET_MODULE)_LIB += m
cc_$(TARGET_MODULE)_LIB += GLESv2
cc_$(TARGET_MODULE)_LIB += EGL
#cc_$(TARGET_MODULE)_LIB += drm
cc_$(TARGET_MODULE)_LIB += xml2
cc_$(TARGET_MODULE)_LIB += z
ifneq ($(SOC),qc)
cc_$(TARGET_MODULE)_LIB += opencv_core
cc_$(TARGET_MODULE)_LIB += opencv_imgproc
cc_$(TARGET_MODULE)_LIB += opencv_imgcodecs
endif

cc_target_ultrascale_LIB += dlt
cc_target_ultrascale_LIB += jpeg
cc_target_ultrascale_LIB += png

cc_$(TARGET_MODULE)_simplerpc_tools_cli_LIB += rt

# --- qualcomm ---
ifeq ($(TARGET_MODULE),target_qualcomm)
# expect external dependencies like OSG-libs and GLFW-libs
#  to be installed first into INSTALL_ROOT-folders
cc_$(TARGET_MODULE)_LIBPATH += $(INSTALL_ROOT_nto)/aarch64le/usr/lib
cc_$(TARGET_MODULE)_LIBPATH += $(QNX_TARGET)/aarch64le/usr/lib
cc_$(TARGET_MODULE)_LIBPATH += $(BSP_ROOT)/opencv/lib

cc_$(TARGET_MODULE)_LIB += c++
cc_$(TARGET_MODULE)_LIB += socket
cc_$(TARGET_MODULE)_LIB += slog2
cc_$(TARGET_MODULE)_LIB += ais_log
cc_$(TARGET_MODULE)_LIB += ais_client
cc_$(TARGET_MODULE)_LIB += OSAbstraction
cc_$(TARGET_MODULE)_LIB += OSUser
cc_$(TARGET_MODULE)_LIB += pmem_client
cc_$(TARGET_MODULE)_LIB += pmemext
cc_$(TARGET_MODULE)_LIB += libstd
cc_$(TARGET_MODULE)_LIB += mmap_peer
cc_$(TARGET_MODULE)_LIB += screen


ifeq ($(COMPILER_NAME),qcc)
LDFLAGS += -Wl,-rpath,$(QNX_TARGET)/aarch64le/usr/lib
LDFLAGS += -L$(BSP_ROOT)/install/aarch64le/lib
LDFLAGS += -L$(BSP_ROOT)/prebuilt/aarch64le/usr/lib/graphics/qc/
LDFLAGS += -L$(INSTALL_ROOT_nto)/aarch64le/usr/lib

CFLAGS += -DSYSTEM_QNX
CXXFLAGS += -DSYSTEM_QNX
CFLAGS += -DUSE_QNX_SLOG  #open: open slog; close: log to console
CXXFLAGS += -DUSE_QNX_SLOG  #open: open slog; close: log to console

CFLAGS += -DUSE_BUILD_IN_SHARDER_CODE #open: use build-in shader source code instead of cc/shaderstatic pc/svs
CXXFLAGS += -DUSE_BUILD_IN_SHARDER_CODE #open: use build-in shader source code instead of cc/shaderstatic pc/svs

endif
endif

# Component libraries unit-tests
hw_ut_$(UT_MODULE)_LIB += stdc++
hw_ut_$(UT_MODULE)_LIB += rt
hw_ut_$(UT_MODULE)_LIB += pthread
hw_ut_$(UT_MODULE)_LIB += OpenThreads
hw_ut_$(UT_MODULE)_LIB += osg
hw_ut_$(UT_MODULE)_LIB += osgDB
hw_ut_$(UT_MODULE)_LIB += osgUtil
hw_ut_$(UT_MODULE)_LIB += osgGA
hw_ut_$(UT_MODULE)_LIB += osgViewer
hw_ut_$(UT_MODULE)_LIB += osgText
hw_ut_$(UT_MODULE)_LIB += osgFX
hw_ut_$(UT_MODULE)_LIB += osgGA
hw_ut_$(UT_MODULE)_LIB += osgDB
hw_ut_$(UT_MODULE)_LIB += osgAnimation
hw_ut_$(UT_MODULE)_LIB += osgSim
hw_ut_$(UT_MODULE)_LIB += m
hw_ut_$(UT_MODULE)_LIB += GLESv2
hw_ut_$(UT_MODULE)_LIB += EGL
hw_ut_$(UT_MODULE)_LIB += drm
hw_ut_$(UT_MODULE)_LIB += xml2
hw_ut_$(UT_MODULE)_LIB += z
ifneq ($(SOC),qc)
hw_ut_$(UT_MODULE)_LIB += opencv_core
hw_ut_$(UT_MODULE)_LIB += opencv_imgproc
hw_ut_$(UT_MODULE)_LIB += opencv_imgcodecs
endif
# some compilers does not have jpeg lib support
NON_JPEG_COMPILERS=gcc
ifneq ($(COMPILER_NAME),$(filter $(COMPILER_NAME),$(NON_JPEG_COMPILERS)))
hw_ut_$(UT_MODULE)_LIB += jpeg
endif
hw_ut_$(UT_MODULE)_LIB += png

#-------------------------------------------------------------------------------
# Project modules
SV3D_MODULES :=
SV3D_MODULES += cc_assets_augmentedview
# SV3D_MODULES += cc_assets_background
SV3D_MODULES += cc_assets_baseplate
SV3D_MODULES += cc_assets_common
SV3D_MODULES += cc_assets_corner
SV3D_MODULES += cc_assets_splineoverlay
SV3D_MODULES += cc_assets_tileoverlay
SV3D_MODULES += cc_assets_parkingspots
SV3D_MODULES += cc_assets_parkingslot
# SV3D_MODULES += cc_assets_caliboverlay
SV3D_MODULES += cc_assets_button
SV3D_MODULES += cc_assets_rctaoverlay
SV3D_MODULES +=
SV3D_MODULES += cc_assets_trajectory
# SV3D_MODULES += cc_assets_virtualreality
# ifeq ($(COMPILER_NAME),gcc)
# SV3D_MODULES += cc_assets_virtualreality
# endif
# ifeq ($(COMPILER_NAME),msvc)
# SV3D_MODULES += cc_assets_virtualreality
# endif

SV3D_MODULES += cc_assets_stb
SV3D_MODULES += cc_assets_viewbowl
SV3D_MODULES += cc_assets_impostor
SV3D_MODULES += cc_assets_overlaycallback
SV3D_MODULES += cc_assets_customfloorplategenerator
SV3D_MODULES += cc_assets_uielements
SV3D_MODULES += cc_assets_streetoverlay
SV3D_MODULES += cc_assets_superTransparentOverlay
SV3D_MODULES += cc_assets_fisheyeassets
SV3D_MODULES += cc_assets_fisheyetransition
SV3D_MODULES += cc_assets_freeparkingoverlay
SV3D_MODULES += cc_assets_parkingspace
SV3D_MODULES += cc_assets_ECALprogressoverlay
SV3D_MODULES += cc_assets_debugoverlay
# SV3D_MODULES += cc_assets_drivablepath
SV3D_MODULES += cc_assets_dynamicgearoverlay
SV3D_MODULES += cc_factory
SV3D_MODULES += cc_assets_vehicle2dwheels
SV3D_MODULES += cc_assets_rimprotectionline
# SV3D_MODULES += cc_assets_roundedcorners
SV3D_MODULES += cc_assets_dynamicwheelmask
SV3D_MODULES += cc_assets_e3parkingoverlay
ifeq ($(TARGET_MODULE),target_standalone)
SV3D_MODULES += cc_util_touchmanipulator
	ifdef IMGUI
		SV3D_MODULES += cc_imgui
	endif
endif

SV3D_MODULES += cc_assets_ptsoverlay
SV3D_MODULES += cc_assets_pdc
SV3D_MODULES += cc_core
SV3D_MODULES += cc_cpc
SV3D_MODULES += cc_daddy
SV3D_MODULES += cc_shaderstatic
SV3D_MODULES += cc_sm_viewmode
SV3D_MODULES += cc_$(TARGET_MODULE)
SV3D_MODULES += cc_util_cli
SV3D_MODULES += cc_util_beziercurve
SV3D_MODULES += cc_util_polygonmath
SV3D_MODULES += cc_util_time
SV3D_MODULES += cc_util_logging
ifeq ($(TARGET_MODULE),target_android)
SV3D_MODULES += cc_vhm
endif
ifeq ($(TARGET_MODULE),target_linux)
SV3D_MODULES += cc_vhm
endif
SV3D_MODULES += cc_views_panoramaview
SV3D_MODULES += cc_views_driveassistview
SV3D_MODULES += cc_views_bonnetview
SV3D_MODULES += cc_views_daynightview
SV3D_MODULES += cc_views_nfsengineeringview
SV3D_MODULES += cc_views_customrawfisheyeview
SV3D_MODULES += cc_views_customwarpfisheyeview
SV3D_MODULES += cc_views_planview
SV3D_MODULES += cc_views_surroundview
SV3D_MODULES += cc_views_parkview
# SV3D_MODULES += cc_views_combinedview
SV3D_MODULES += cc_views_callback
SV3D_MODULES += cc_virtcam
SV3D_MODULES += cc_worker_core
ifeq ($(TARGET_MODULE),target_standalone)
SV3D_MODULES += cc_test
endif
SV3D_MODULES +=
SV3D_MODULES += hw_version
SV3D_MODULES += hw_daddy
ifeq ($(TARGET_MODULE),$(filter $(TARGET_MODULE),$(HARDWARE_TARGETS)))
SV3D_MODULES += hw_ipcsync
endif
SV3D_MODULES +=
SV3D_MODULES += pc_generic_core
SV3D_MODULES += pc_generic_$(OS_MODULE)_com
SV3D_MODULES += pc_generic_$(OS_MODULE)_core
SV3D_MODULES += pc_generic_$(OS_MODULE)_logging
SV3D_MODULES += pc_generic_$(OS_MODULE)_sys
SV3D_MODULES += pc_generic_util_chrono
SV3D_MODULES += pc_generic_util_cli
SV3D_MODULES += pc_generic_util_coding
SV3D_MODULES += pc_generic_util_com
SV3D_MODULES += pc_generic_util_logging
SV3D_MODULES +=
SV3D_MODULES += pc_svs_animation
SV3D_MODULES += pc_svs_assets_imageoverlays
SV3D_MODULES += pc_svs_assets_vehiclemodel
SV3D_MODULES += pc_svs_assets_floorplate
SV3D_MODULES += pc_svs_c2w
SV3D_MODULES += pc_svs_core
SV3D_MODULES += pc_svs_daddy
SV3D_MODULES += pc_svs_factory
ifdef CHAMAELEON
SV3D_MODULES += pc_svs_imp_chamaeleon
SV3D_MODULES += pc_svs_imp_cwd
SV3D_MODULES += pc_svs_imp_iq
SV3D_MODULES += pc_svs_imp_sh
SV3D_MODULES += pc_svs_imp_tnf
endif
ifeq ($(TARGET_MODULE),target_standalone)
SV3D_MODULES += cc_imc_standalone
endif
SV3D_MODULES += pc_svs_texfloor_core
SV3D_MODULES += pc_svs_texfloor_odometry
SV3D_MODULES += pc_svs_util_cli
SV3D_MODULES += pc_svs_util_linestring
SV3D_MODULES += pc_svs_util_logging
SV3D_MODULES += pc_svs_util_math
SV3D_MODULES += pc_svs_util_osgx
SV3D_MODULES += pc_svs_vehicle
SV3D_MODULES += pc_svs_views_engineeringview
SV3D_MODULES += pc_svs_views_perfview
SV3D_MODULES += pc_svs_views_fusiview
SV3D_MODULES += pc_svs_views_warpfisheyeview
SV3D_MODULES += pc_svs_views_rawfisheyeview
SV3D_MODULES += pc_svs_virtcam
SV3D_MODULES += pc_svs_worker_bowlshaping
SV3D_MODULES += pc_svs_worker_core
SV3D_MODULES += pc_svs_worker_fusion
SV3D_MODULES += pc_svs_worker_stitching


#only add the unit test module to the project modules list if UT is defined
ifdef UT
SV3D_MODULES += hw_ut_$(UT_MODULE)
endif


EXCLUDE_MODULES_FROM_ALL:= pc_svs_tools_shaderfactory

#-------------------------------------------------------------------------------
# Include SW modules makefiles

# Include tool makefiles

include $(addsuffix /Makefile,$(subst _,/,$(SV3D_MODULES)))

#-------------------------------------------------------------------------------
# Link dependencies
SV3D_BINARIES :=
ifeq ($(TARGET_MODULE),target_standalone)
SV3D_BINARIES += $(cc_imc_standalone_BINARY)
endif
SV3D_BINARIES += $(pc_generic_$(OS_MODULE)_core_BINARY)
SV3D_BINARIES += $(pc_generic_$(OS_MODULE)_com_BINARY)
SV3D_BINARIES += $(pc_generic_$(OS_MODULE)_logging_BINARY)
SV3D_BINARIES += $(pc_generic_$(OS_MODULE)_sys_BINARY)
SV3D_BINARIES += $(pc_generic_util_chrono_BINARY)
SV3D_BINARIES += $(pc_generic_util_cli_BINARY)
SV3D_BINARIES +=
SV3D_BINARIES += $(pc_generic_core_BINARY)
SV3D_BINARIES += $(pc_generic_util_coding_BINARY)
SV3D_BINARIES += $(pc_generic_util_com_BINARY)
SV3D_BINARIES += $(pc_generic_util_logging_BINARY)
SV3D_BINARIES +=
SV3D_BINARIES += $(pc_svs_animation_BINARY)
SV3D_BINARIES += $(pc_svs_assets_imageoverlays_BINARY)
SV3D_BINARIES += $(pc_svs_assets_vehiclemodel_BINARY)
SV3D_BINARIES += $(pc_svs_assets_floorplate_BINARY)
SV3D_BINARIES += $(pc_svs_c2w_BINARY)
SV3D_BINARIES += $(pc_svs_core_BINARY)
SV3D_BINARIES += $(pc_svs_daddy_BINARY)
SV3D_BINARIES += $(pc_svs_factory_BINARY)
ifdef CHAMAELEON
SV3D_BINARIES += $(pc_svs_imp_chamaeleon_BINARY)
SV3D_BINARIES += $(pc_svs_imp_cwd_BINARY)
SV3D_BINARIES += $(pc_svs_imp_iq_BINARY)
SV3D_BINARIES += $(pc_svs_imp_sh_BINARY)
SV3D_BINARIES += $(pc_svs_imp_tnf_BINARY)
endif
SV3D_BINARIES += $(pc_svs_texfloor_core_BINARY)
SV3D_BINARIES += $(pc_svs_texfloor_odometry_BINARY)
SV3D_BINARIES += $(pc_svs_util_cli_BINARY)
SV3D_BINARIES += $(pc_svs_util_linestring_BINARY)
SV3D_BINARIES += $(pc_svs_util_logging_BINARY)
SV3D_BINARIES += $(pc_svs_util_math_BINARY)
SV3D_BINARIES += $(pc_svs_util_osgx_BINARY)
SV3D_BINARIES += $(pc_svs_vehicle_BINARY)
SV3D_BINARIES += $(pc_svs_views_engineeringview_BINARY)
SV3D_BINARIES += $(pc_svs_views_perfview_BINARY)
SV3D_BINARIES += $(pc_svs_views_fusiview_BINARY)
SV3D_BINARIES += $(pc_svs_views_warpfisheyeview_BINARY)
SV3D_BINARIES += $(pc_svs_views_rawfisheyeview_BINARY)
SV3D_BINARIES += $(pc_svs_virtcam_BINARY)
SV3D_BINARIES += $(pc_svs_worker_bowlshaping_BINARY)
SV3D_BINARIES += $(pc_svs_worker_core_BINARY)
SV3D_BINARIES += $(pc_svs_worker_fusion_BINARY)
SV3D_BINARIES += $(pc_svs_worker_stitching_BINARY)
SV3D_BINARIES +=
SV3D_BINARIES += $(cc_assets_augmentedview_BINARY)
# SV3D_BINARIES += $(cc_assets_background_BINARY)
SV3D_BINARIES += $(cc_assets_baseplate_BINARY)
SV3D_BINARIES += $(cc_assets_common_BINARY)
SV3D_BINARIES += $(cc_assets_corner_BINARY)
SV3D_BINARIES += $(cc_assets_splineoverlay_BINARY)
SV3D_BINARIES += $(cc_assets_tileoverlay_BINARY)
SV3D_BINARIES += $(cc_assets_parkingspots_BINARY)
SV3D_BINARIES += $(cc_assets_parkingslot_BINARY)
# SV3D_BINARIES += $(cc_assets_caliboverlay_BINARY)
SV3D_BINARIES += $(cc_assets_button_BINARY)
SV3D_BINARIES += $(cc_assets_rctaoverlay_BINARY)
SV3D_BINARIES += $(cc_assets_trajectory_BINARY)
# SV3D_BINARIES += $(cc_assets_virtualreality_BINARY)

# ifeq ($(COMPILER_NAME),gcc)
# SV3D_BINARIES += $(cc_assets_virtualreality_BINARY)
# endif
# ifeq ($(COMPILER_NAME),msvc)
# SV3D_BINARIES += $(cc_assets_virtualreality_BINARY)
# endif

SV3D_BINARIES += $(cc_assets_stb_BINARY)
SV3D_BINARIES += $(cc_assets_viewbowl_BINARY)
SV3D_BINARIES += $(cc_assets_impostor_BINARY)
SV3D_BINARIES += $(cc_assets_overlaycallback_BINARY)
SV3D_BINARIES += $(cc_assets_customfloorplategenerator_BINARY)
SV3D_BINARIES += $(cc_assets_uielements_BINARY)
SV3D_BINARIES += $(cc_assets_streetoverlay_BINARY)
SV3D_BINARIES += $(cc_assets_superTransparentOverlay_BINARY)
SV3D_BINARIES += $(cc_assets_fisheyeassets_BINARY)
SV3D_BINARIES += $(cc_assets_fisheyetransition_BINARY)
SV3D_BINARIES += $(cc_assets_freeparkingoverlay_BINARY)
SV3D_BINARIES += $(cc_assets_parkingspace_BINARY)
SV3D_BINARIES += $(cc_assets_ECALprogressoverlay_BINARY)
SV3D_BINARIES += $(cc_assets_debugoverlay_BINARY)
# SV3D_BINARIES += $(cc_assets_drivablepath_BINARY)
SV3D_BINARIES += $(cc_assets_dynamicgearoverlay_BINARY)
SV3D_BINARIES += $(cc_factory_BINARY)
SV3D_BINARIES += $(cc_assets_vehicle2dwheels_BINARY)
SV3D_BINARIES += $(cc_assets_rimprotectionline_BINARY)
# SV3D_BINARIES += $(cc_assets_roundedcorners_BINARY)
SV3D_BINARIES += $(cc_assets_dynamicwheelmask_BINARY)
SV3D_BINARIES += $(cc_assets_e3parkingoverlay_BINARY)
ifeq ($(TARGET_MODULE),target_standalone)
SV3D_BINARIES += $(cc_util_touchmanipulator_BINARY)
	ifdef IMGUI
		SV3D_BINARIES += $(cc_imgui_BINARY)
	endif
endif

SV3D_BINARIES += $(cc_assets_ptsoverlay_BINARY)
SV3D_BINARIES += $(cc_assets_pdc_BINARY)
SV3D_BINARIES += $(cc_core_BINARY)
SV3D_BINARIES += $(cc_cpc_BINARY)
SV3D_BINARIES += $(cc_daddy_BINARY)
SV3D_BINARIES += $(cc_sm_viewmode_BINARY)
SV3D_BINARIES += $(cc_$(TARGET_MODULE)_BINARY)
SV3D_BINARIES += $(cc_util_cli_BINARY)
SV3D_BINARIES += $(cc_util_beziercurve_BINARY)
SV3D_BINARIES += $(cc_util_polygonmath_BINARY)
SV3D_BINARIES += $(cc_util_time_BINARY)
SV3D_BINARIES += $(cc_util_logging_BINARY)
ifeq ($(TARGET_MODULE),target_android)
SV3D_BINARIES += $(cc_vhm_BINARY)
endif
ifeq ($(TARGET_MODULE),target_linux)
SV3D_BINARIES += $(cc_vhm_BINARY)
endif
SV3D_BINARIES += $(cc_views_panoramaview_BINARY)
SV3D_BINARIES += $(cc_views_driveassistview_BINARY)
SV3D_BINARIES += $(cc_views_bonnetview_BINARY)
SV3D_BINARIES += $(cc_views_daynightview_BINARY)
SV3D_BINARIES += $(cc_views_nfsengineeringview_BINARY)
SV3D_BINARIES += $(cc_views_customrawfisheyeview_BINARY)
SV3D_BINARIES += $(cc_views_customwarpfisheyeview_BINARY)
SV3D_BINARIES += $(cc_views_planview_BINARY)
SV3D_BINARIES += $(cc_views_surroundview_BINARY)
# SV3D_BINARIES += $(cc_views_combinedview_BINARY)
SV3D_BINARIES += $(cc_views_parkview_BINARY)
SV3D_BINARIES += $(cc_views_callback_BINARY)
SV3D_BINARIES += $(cc_virtcam_BINARY)

SV3D_BINARIES += $(cc_worker_core_BINARY)
ifeq ($(TARGET_MODULE),target_standalone)
SV3D_BINARIES += $(cc_test_BINARY)
endif
SV3D_BINARIES +=
SV3D_BINARIES += $(hw_version_BINARY)
SV3D_BINARIES += $(hw_daddy_BINARY)
ifeq ($(TARGET_MODULE),$(filter $(TARGET_MODULE),$(HARDWARE_TARGETS)))
SV3D_BINARIES += $(hw_ipcsync_BINARY)
endif

ifdef UT
_UT_BINARY := $(hw_ut_$(UT_MODULE)_UTBINARY)
$(_UT_BINARY):               \
$(SV3D_BINARIES)           \
$(hw_ut_$(UT_MODULE)_BINARY) \
$(UTOBJLIST)
endif




_COMPONENT_BINARIES += $(cc_$(TARGET_MODULE)_BINARY)

ifeq  ($(SOC),qc)
$(cc_$(TARGET_MODULE)_BINARY): $(SV3D_BINARIES)
else
$(cc_$(TARGET_MODULE)_BINARY): $(SV3D_BINARIES)
endif


else
#///////////////////////////////////////////////////////////Linux Only///////////////////////////////////////////////////////////////////
#-------------------------------------------------------------------------------
# Component include pathes
# Apply to all binaries built within this component
# COMPINC_gcc += $(SDKTARGETSYSROOT)/usr/include/libxml2
# COMPINC_gcc += $(SDKTARGETSYSROOT)/usr/include/libdrm
# COMPINC_gcc += $(SDKTARGETSYSROOT)/usr/include/opencv4
COMPINC_gcc += ./cc/$(subst _,/,$(TARGET_MODULE))/inc

# add custom include path for standalone pc/generic
COMPINC_$(COMPILER_NAME) += ./pc/generic/

HARDWARE_TARGETS=target_ultrascale target_android
PROJECT_ROOT := $(shell git rev-parse --show-toplevel)

ifeq ($(TARGET_MODULE),target_adtf)
COMPDEF_$(COMPILER_NAME) += ADTF_LOGGING #sometimes the compiler name is msvc or msvc100 - take care about that
endif

# set defines in release builds to exclude socket connections, etc.
ifdef RELEASE
COMPDEF_$(COMPILER_NAME) += NO_SOCKET_SERVER
COMPDEF_$(COMPILER_NAME) += NO_RAPIDJSON
endif

ifdef IMGUI
COMPDEF_$(COMPILER_NAME) += ENABLE_IMGUI
endif

CHAMAELEON := 1
ifdef CHAMAELEON
COMPDEF_$(COMPILER_NAME) += CHAMAELEON
endif

# cpc settings.
COMPDEF_$(COMPILER_NAME) += CC_SV3D_BYD_SAAP

# why missing??
COMPDEF_$(COMPILER_NAME) += VFC_ENABLE_FLOAT64_TYPE

#-------------------------------------------------------------------------------
# Component library pathes
cc_$(TARGET_MODULE)_LIBPATH +=

#-------------------------------------------------------------------------------
# Component libraries
# cc_$(TARGET_MODULE)_LIB += stdc++
# cc_$(TARGET_MODULE)_LIB += rt
# cc_$(TARGET_MODULE)_LIB += pthread
# cc_$(TARGET_MODULE)_LIB += OpenThreads
# cc_$(TARGET_MODULE)_LIB += osg
# cc_$(TARGET_MODULE)_LIB += osgDB
# cc_$(TARGET_MODULE)_LIB += osgUtil
# cc_$(TARGET_MODULE)_LIB += osgGA
# cc_$(TARGET_MODULE)_LIB += osgViewer
# cc_$(TARGET_MODULE)_LIB += osgText
# cc_$(TARGET_MODULE)_LIB += osgFX
# cc_$(TARGET_MODULE)_LIB += osgGA
# cc_$(TARGET_MODULE)_LIB += osgDB
# cc_$(TARGET_MODULE)_LIB += osgAnimation
# cc_$(TARGET_MODULE)_LIB += osgSim
# cc_$(TARGET_MODULE)_LIB += m
# cc_$(TARGET_MODULE)_LIB += GLESv2
# cc_$(TARGET_MODULE)_LIB += EGL
# cc_$(TARGET_MODULE)_LIB += drm
# cc_$(TARGET_MODULE)_LIB += xml2
# cc_$(TARGET_MODULE)_LIB += z
# cc_$(TARGET_MODULE)_LIB += opencv_core
# cc_$(TARGET_MODULE)_LIB += opencv_calib3d
# cc_$(TARGET_MODULE)_LIB += opencv_imgproc
# cc_$(TARGET_MODULE)_LIB += opencv_imgcodecs
# cc_target_ultrascale_LIB += dlt
# cc_target_ultrascale_LIB += jpeg
# cc_target_ultrascale_LIB += png

# cc_$(TARGET_MODULE)_simplerpc_tools_cli_LIB += rt


ifeq ($(COMPILER_NAME),gcc)
CFLAGS += -DSUSE_OSG_STATIC_LIB
CXXFLAGS += -DSUSE_OSG_STATIC_LIB

CFLAGS += -DUSE_BUILD_IN_SHARDER_CODE #open: use build-in shader source code instead of cc/shaderstatic pc/svs
CXXFLAGS += -DUSE_BUILD_IN_SHARDER_CODE #open: use build-in shader source code instead of cc/shaderstatic pc/svs

CFLAGS += -DBYD_LINUX_LOG_FUNC_CALLBACK    #open: use byd linux log function
CXXFLAGS += -DBYD_LINUX_LOG_FUNC_CALLBACK  #open: use byd linux log function

# add lib
LDFLAGS += -Wl,--no-as-needed
LDFLAGS += -Wl,--start-group
LDFLAGS += -lstdc++
COMMON_LIB_PATH ?= ${LINUX_SDK_PATH}/lib64
LDFLAGS += -L${COMMON_LIB_PATH}/ -lGLESv2 \
-lEGL

# For vhm lib
VHM_LIB := $(PROJECT_ROOT)/bin/pmasip
$(info VHM_PATH is set to: !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!$(VHM_LIB))
LDFLAGS += -L${VHM_LIB} -lpma_sip

# add lib
OSG_LIB ?= ${OSG_SDK_PATH}/lib
LDFLAGS += -L${OSG_LIB} \
-losg \
-losgAnimation \
-losgBatchedText \
-losgFX \
-losgGA \
-losgManipulator \
-losgParticle \
-losgShadow \
-losgSim \
-losgTerrain \
-losgText \
-losgUtil \
-losgViewer \
-losgVolume \
-losgWidget \
-lOpenThreads \
-losgDB

OSG_LIB_OTHER ?= ${OSG_SDK_PATH}/lib/osgPlugins-3.2.3
LDFLAGS += -L${OSG_LIB_OTHER}/ -losgdb_3dc \
-losgdb_3ds \
-losgdb_ac \
-losgdb_bmp \
-losgdb_bsp \
-losgdb_bvh \
-losgdb_dds \
-losgdb_deprecated_osg \
-losgdb_deprecated_osganimation \
-losgdb_deprecated_osgfx \
-losgdb_deprecated_osgparticle \
-losgdb_deprecated_osgshadow \
-losgdb_deprecated_osgsim \
-losgdb_deprecated_osgterrain \
-losgdb_deprecated_osgtext \
-losgdb_deprecated_osgviewer \
-losgdb_deprecated_osgvolume \
-losgdb_deprecated_osgwidget \
-losgdb_dot \
-losgdb_dxf \
-losgdb_freetype \
-losgdb_glsl \
-losgdb_gz \
-losgdb_hdr \
-losgdb_ive \
-losgdb_jpeg \
-losgdb_ktx \
-losgdb_logo \
-losgdb_lwo \
-losgdb_lws \
-losgdb_mdl \
-losgdb_normals \
-losgdb_obj \
-losgdb_openflight \
-losgdb_osc \
-losgdb_osg \
-losgdb_osga \
-losgdb_osgshadow \
-losgdb_osgterrain \
-losgdb_osgtgz \
-losgdb_osgviewer \
-losgdb_pic \
-losgdb_ply \
-losgdb_png \
-losgdb_pnm \
-losgdb_pov \
-losgdb_pvr \
-losgdb_revisions \
-losgdb_rgb \
-losgdb_rot \
-losgdb_scale \
-losgdb_serializers_osg \
-losgdb_serializers_osganimation \
-losgdb_serializers_osgfx \
-losgdb_serializers_osgga \
-losgdb_serializers_osgmanipulator \
-losgdb_serializers_osgparticle \
-losgdb_serializers_osgshadow \
-losgdb_serializers_osgsim \
-losgdb_serializers_osgterrain \
-losgdb_serializers_osgtext \
-losgdb_serializers_osgviewer \
-losgdb_serializers_osgvolume \
-losgdb_shp \
-losgdb_stl \
-losgdb_tga \
-losgdb_tgz \
-losgdb_tiff \
-losgdb_trans \
-losgdb_trk \
-losgdb_txf \
-losgdb_txp \
-losgdb_vtf \
-losgdb_x \
-losgdb_zip

# opencv
OPENCV_LIB_PATH ?= ${OPENCV_SDK_PATH}/lib
LDFLAGS += -L${OPENCV_LIB_PATH} \
-lopencv_core \
-lopencv_imgproc \
-lopencv_highgui \
-lopencv_imgcodecs \
-lopencv_features2d \
-lopencv_dnn \
-lopencv_flann \
-lopencv_ml \
-lopencv_objdetect \
-lopencv_calib3d \
-lopencv_photo \
-lopencv_shape \
-lopencv_stitching \
-lopencv_superres \
-lopencv_video \
-lopencv_videoio \
-lopencv_aruco \
-lopencv_bgsegm \
-lopencv_bioinspired \
-lopencv_calib3d \
-lopencv_ccalib \
-lopencv_datasets \
-lopencv_dnn_objdetect \
-lopencv_dnn_superres \
-lopencv_dpm \
-lopencv_face \
-lopencv_fuzzy \
-lopencv_gapi \
-lopencv_hfs \
-lopencv_img_hash \
-lopencv_intensity_transform \
-lopencv_line_descriptor \
-lopencv_mcc \
-lopencv_optflow \
-lopencv_phase_unwrapping \
-lopencv_plot \
-lopencv_quality \
-lopencv_rapid \
-lopencv_reg \
-lopencv_rgbd \
-lopencv_saliency \
-lopencv_signal \
-lopencv_stereo \
-lopencv_structured_light \
-lopencv_surface_matching \
-lopencv_text \
-lopencv_tracking \
-lopencv_videostab \
-lopencv_wechat_qrcode \
-lopencv_xfeatures2d \
-lopencv_ximgproc \
-lopencv_xobjdetect \
-lopencv_xphoto

OPENCV_3RD_LIB ?= ${OPENCV_SDK_PATH}/lib/opencv4/3rdparty/
LDFLAGS += -L${OPENCV_3RD_LIB}  -ltegra_hal \
-littnotify \
-llibpng \
-llibprotobuf \
-llibtiff \
-llibwebp \
-lm \
-lzlib \
-llibjpeg-turbo \
-lade \
-llibopenjp2

XML2_LIB_PATH ?= ${LINUX_SDK_PATH}/lib64/libxml2.so.2
LDFLAGS += ${XML2_LIB_PATH}
LDFLAGS += -Wl,--end-group

LDFLAGS += -L${LINUX_SDK_PATH}/lib64/ -lfreetype

else ifeq ($(COMPILER_NAME),clang)
CFLAGS += -DSUSE_OSG_STATIC_LIB
CXXFLAGS += -DSUSE_OSG_STATIC_LIB

CFLAGS += -DUSE_BUILD_IN_SHARDER_CODE #open: use build-in shader source code instead of cc/shaderstatic pc/svs
CXXFLAGS += -DUSE_BUILD_IN_SHARDER_CODE #open: use build-in shader source code instead of cc/shaderstatic pc/svs

CFLAGS += -DANDROID_NDK  #open: open slog; close: log to console
CXXFLAGS += -DANDROID_NDK  #open: open slog; close: log to console

# add lib
OSG_LIB ?= ${OSG_SDK_PATH}/obj/local/arm64-v8a
LDFLAGS += -L${OSG_LIB}/ -lOpenThreads \
-losg -losgAnimation -losgdb_3dc -losgDB -losgdb_ac -losgdb_bmp -losgdb_bsp -losgdb_bvh  -losgdb_dds -losgdb_deprecated_osg \
-losgdb_deprecated_osganimation -losgdb_deprecated_osgfx -losgdb_deprecated_osgparticle -losgdb_deprecated_osgshadow -losgdb_deprecated_osgsim \
-losgdb_deprecated_osgterrain -losgdb_deprecated_osgtext -losgdb_deprecated_osgviewer -losgdb_deprecated_osgvolume -losgdb_deprecated_osgwidget \
-losgdb_dot  -losgdb_dxf -losgdb_freetype -losgdb_glsl -losgdb_gz -losgdb_hdr -losgdb_ive -losgdb_jpeg -losgdb_ktx \
-losgdb_logo -losgdb_lwo -losgdb_lws  -losgdb_mdl -losgdb_normals -losgdb_obj -losgdb_openflight -losgdb_osc -losgdb_osg \
-losgdb_osga -losgdb_osgshadow -losgdb_osgterrain -losgdb_osgtgz -losgdb_osgviewer -losgdb_pic -losgdb_ply -losgdb_png \
-losgdb_pnm -losgdb_pov -losgdb_pvr -losgdb_revisions -losgdb_rgb -losgdb_rot -losgdb_scale -losgdb_serializers_osg -losgdb_serializers_osganimation \
-losgdb_serializers_osgfx -losgdb_serializers_osgga -losgdb_serializers_osgmanipulator -losgdb_serializers_osgparticle -losgdb_serializers_osgshadow \
-losgdb_serializers_osgsim -losgdb_serializers_osgterrain -losgdb_serializers_osgtext -losgdb_serializers_osgviewer -losgdb_serializers_osgvolume -losgdb_shp \
-losgdb_stl -losgdb_tga -losgdb_tgz -losgdb_trans -losgdb_trk -losgdb_txf -losgdb_txp -losgdb_vtf -losgdb_x -losgdb_zip -losgFX -losgGA \
-losgManipulator -losgParticle  -losgShadow -losgSim -losgTerrain -losgText -losgUtil -losgViewer -losgVolume -losgWidget

OSG_LIB_OTHER ?= ${OSG_SDK_PATH}/usr/lib
LDFLAGS += -L${OSG_LIB_OTHER}/ -lfreetype -ljpeg -lpng16 -lturbojpeg -lxml2

ifeq ($(USE_CXX_DYNAMIC_LIB), ON)
LDFLAGS += -lc++_shared
else
LDFLAGS += -lc++_static
LDFLAGS += ${ANDROID_SDK_PATH}/sysroot/usr/lib/aarch64-linux-android/libz.a
endif

# LDFLAGS += -lc++_static
# # use libz static zlib


# opencv
OPENCV_LIB_PATH ?= ${OPENCV_SDK_PATH}/staticlibs/arm64-v8a
# LDFLAGS += -L${OPENCV_LIB}/  -lopencv_core -lopencv_imgproc -lopencv_highgui -lopencv_objdetect -lopencv_features2d
LDFLAGS += -L${OPENCV_LIB_PATH} -lopencv_calib3d \
-lopencv_core \
-lopencv_dnn \
-lopencv_features2d \
-lopencv_flann \
-lopencv_highgui \
-lopencv_imgcodecs \
-lopencv_imgproc \
-lopencv_ml \
-lopencv_objdetect \
-lopencv_photo \
-lopencv_shape \
-lopencv_stitching \
-lopencv_superres \
-lopencv_video \
-lopencv_videoio

OPENCV_3RD_LIB ?= ${OPENCV_SDK_PATH}/3rdparty/libs/arm64-v8a
LDFLAGS += -L${OPENCV_3RD_LIB}  -ltegra_hal \
-lcpufeatures \
-lIlmImf \
-littnotify \
-llibjasper \
-llibjpeg-turbo \
-llibpng \
-llibprotobuf \
-llibtiff \
-llibwebp \
-llog

# For vhm lib
VHM_LIB := $(PROJECT_ROOT)/bin/pmasip
$(info VHM_LIB is set to: !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!$(VHM_LIB))
LDFLAGS += -L${VHM_LIB} -lpma_sip

else
$(warning "invalid COMPILER_NAME: $(COMPILER_NAME).")
endif

# Component libraries unit-tests
hw_ut_$(UT_MODULE)_LIB += stdc++
hw_ut_$(UT_MODULE)_LIB += rt
hw_ut_$(UT_MODULE)_LIB += pthread
hw_ut_$(UT_MODULE)_LIB += OpenThreads
hw_ut_$(UT_MODULE)_LIB += osg
hw_ut_$(UT_MODULE)_LIB += osgDB
hw_ut_$(UT_MODULE)_LIB += osgUtil
hw_ut_$(UT_MODULE)_LIB += osgGA
hw_ut_$(UT_MODULE)_LIB += osgViewer
hw_ut_$(UT_MODULE)_LIB += osgText
hw_ut_$(UT_MODULE)_LIB += osgFX
hw_ut_$(UT_MODULE)_LIB += osgGA
hw_ut_$(UT_MODULE)_LIB += osgDB
hw_ut_$(UT_MODULE)_LIB += osgAnimation
hw_ut_$(UT_MODULE)_LIB += osgSim
hw_ut_$(UT_MODULE)_LIB += m
hw_ut_$(UT_MODULE)_LIB += GLESv2
hw_ut_$(UT_MODULE)_LIB += EGL
hw_ut_$(UT_MODULE)_LIB += drm
hw_ut_$(UT_MODULE)_LIB += xml2
hw_ut_$(UT_MODULE)_LIB += z
hw_ut_$(UT_MODULE)_LIB += opencv_core
hw_ut_$(UT_MODULE)_LIB += opencv_calib3d
hw_ut_$(UT_MODULE)_LIB += opencv_imgproc
hw_ut_$(UT_MODULE)_LIB += opencv_imgcodecs
# some compilers does not have jpeg lib support
NON_JPEG_COMPILERS=gcc
ifneq ($(COMPILER_NAME),$(filter $(COMPILER_NAME),$(NON_JPEG_COMPILERS)))
hw_ut_$(UT_MODULE)_LIB += jpeg
endif
hw_ut_$(UT_MODULE)_LIB += png

#-------------------------------------------------------------------------------
# Project modules
SV3D_MODULES :=
SV3D_MODULES += cc_assets_augmentedview
# SV3D_MODULES += cc_assets_background
SV3D_MODULES += cc_assets_baseplate
SV3D_MODULES += cc_assets_common
SV3D_MODULES += cc_assets_corner
SV3D_MODULES += cc_assets_splineoverlay
SV3D_MODULES += cc_assets_tileoverlay
SV3D_MODULES += cc_assets_parkingspots
SV3D_MODULES += cc_assets_parkingslot
# SV3D_MODULES += cc_assets_caliboverlay
SV3D_MODULES += cc_assets_button
SV3D_MODULES += cc_assets_rctaoverlay
SV3D_MODULES +=
SV3D_MODULES += cc_assets_trajectory
# SV3D_MODULES += cc_assets_virtualreality
# ifeq ($(COMPILER_NAME),gcc)
# SV3D_MODULES += cc_assets_virtualreality
# endif
# ifeq ($(COMPILER_NAME),msvc)
# SV3D_MODULES += cc_assets_virtualreality
# endif

SV3D_MODULES += cc_assets_stb
SV3D_MODULES += cc_assets_viewbowl
SV3D_MODULES += cc_assets_impostor
SV3D_MODULES += cc_assets_overlaycallback
SV3D_MODULES += cc_assets_customfloorplategenerator
SV3D_MODULES += cc_assets_uielements
SV3D_MODULES += cc_assets_streetoverlay
SV3D_MODULES += cc_assets_superTransparentOverlay
SV3D_MODULES += cc_assets_fisheyeassets
SV3D_MODULES += cc_assets_fisheyetransition
SV3D_MODULES += cc_assets_freeparkingoverlay
SV3D_MODULES += cc_assets_parkingspace
SV3D_MODULES += cc_assets_ECALprogressoverlay
SV3D_MODULES += cc_assets_debugoverlay
# SV3D_MODULES += cc_assets_drivablepath
SV3D_MODULES += cc_assets_dynamicgearoverlay
SV3D_MODULES += cc_factory
SV3D_MODULES += cc_assets_vehicle2dwheels
SV3D_MODULES += cc_assets_rimprotectionline
SV3D_MODULES += cc_assets_dynamicwheelmask
SV3D_MODULES += cc_assets_e3parkingoverlay
ifeq ($(TARGET_MODULE),target_standalone)
SV3D_MODULES += cc_util_touchmanipulator
	ifdef IMGUI
		SV3D_MODULES += cc_imgui
	endif
endif

SV3D_MODULES += cc_assets_ptsoverlay
SV3D_MODULES += cc_assets_pdc
SV3D_MODULES += cc_core
SV3D_MODULES += cc_cpc
SV3D_MODULES += cc_daddy
SV3D_MODULES += cc_shaderstatic
SV3D_MODULES += cc_sm_viewmode
SV3D_MODULES += cc_$(TARGET_MODULE)
SV3D_MODULES += cc_util_cli
SV3D_MODULES += cc_util_beziercurve
SV3D_MODULES += cc_util_polygonmath
SV3D_MODULES += cc_util_time
SV3D_MODULES += cc_util_logging
SV3D_MODULES += cc_util_common
ifeq ($(TARGET_MODULE),target_android)
SV3D_MODULES += cc_vhm
endif
ifeq ($(TARGET_MODULE),target_linux)
SV3D_MODULES += cc_vhm
endif
SV3D_MODULES += cc_views_panoramaview
SV3D_MODULES += cc_views_driveassistview
SV3D_MODULES += cc_views_bonnetview
SV3D_MODULES += cc_views_daynightview
SV3D_MODULES += cc_views_nfsengineeringview
SV3D_MODULES += cc_views_customrawfisheyeview
SV3D_MODULES += cc_views_customwarpfisheyeview
SV3D_MODULES += cc_views_planview
SV3D_MODULES += cc_views_surroundview
SV3D_MODULES += cc_views_parkview
SV3D_MODULES += cc_views_combinedview
SV3D_MODULES += cc_views_callback
SV3D_MODULES += cc_virtcam
SV3D_MODULES += cc_worker_core
ifeq ($(TARGET_MODULE),target_standalone)
SV3D_MODULES += cc_test
endif
SV3D_MODULES +=
SV3D_MODULES += hw_version
SV3D_MODULES += hw_daddy
ifeq ($(TARGET_MODULE),$(filter $(TARGET_MODULE),$(HARDWARE_TARGETS)))
SV3D_MODULES += hw_ipcsync
endif
SV3D_MODULES +=
SV3D_MODULES += pc_generic_core
SV3D_MODULES += pc_generic_$(OS_MODULE)_com
SV3D_MODULES += pc_generic_$(OS_MODULE)_core
SV3D_MODULES += pc_generic_$(OS_MODULE)_logging
SV3D_MODULES += pc_generic_$(OS_MODULE)_sys
SV3D_MODULES += pc_generic_util_chrono
SV3D_MODULES += pc_generic_util_cli
SV3D_MODULES += pc_generic_util_coding
SV3D_MODULES += pc_generic_util_com
SV3D_MODULES += pc_generic_util_logging
SV3D_MODULES +=
SV3D_MODULES += pc_svs_animation
SV3D_MODULES += pc_svs_assets_imageoverlays
SV3D_MODULES += pc_svs_assets_vehiclemodel
SV3D_MODULES += pc_svs_assets_floorplate
SV3D_MODULES += pc_svs_c2w
SV3D_MODULES += pc_svs_core
SV3D_MODULES += pc_svs_daddy
SV3D_MODULES += pc_svs_factory
ifdef CHAMAELEON
SV3D_MODULES += pc_svs_imp_chamaeleon
SV3D_MODULES += pc_svs_imp_cwd
SV3D_MODULES += pc_svs_imp_iq
SV3D_MODULES += pc_svs_imp_sh
SV3D_MODULES += pc_svs_imp_tnf
endif
ifeq ($(TARGET_MODULE),target_standalone)
SV3D_MODULES += cc_imc_standalone
endif
SV3D_MODULES += pc_svs_texfloor_core
SV3D_MODULES += pc_svs_texfloor_odometry
SV3D_MODULES += pc_svs_util_cli
SV3D_MODULES += pc_svs_util_linestring
SV3D_MODULES += pc_svs_util_logging
SV3D_MODULES += pc_svs_util_math
SV3D_MODULES += pc_svs_util_osgx
SV3D_MODULES += pc_svs_vehicle
SV3D_MODULES += pc_svs_views_engineeringview
SV3D_MODULES += pc_svs_views_perfview
SV3D_MODULES += pc_svs_views_fusiview
SV3D_MODULES += pc_svs_views_warpfisheyeview
SV3D_MODULES += pc_svs_views_rawfisheyeview
SV3D_MODULES += pc_svs_virtcam
SV3D_MODULES += pc_svs_worker_bowlshaping
SV3D_MODULES += pc_svs_worker_core
SV3D_MODULES += pc_svs_worker_fusion
SV3D_MODULES += pc_svs_worker_stitching


#only add the unit test module to the project modules list if UT is defined
ifdef UT
SV3D_MODULES += hw_ut_$(UT_MODULE)
endif


EXCLUDE_MODULES_FROM_ALL:= pc_svs_tools_shaderfactory

#-------------------------------------------------------------------------------
# Include SW modules makefiles

# Include tool makefiles

include $(addsuffix /Makefile,$(subst _,/,$(SV3D_MODULES)))

#-------------------------------------------------------------------------------
# Link dependencies
SV3D_BINARIES :=
ifeq ($(TARGET_MODULE),target_standalone)
SV3D_BINARIES += $(cc_imc_standalone_BINARY)
endif
SV3D_BINARIES += $(pc_generic_$(OS_MODULE)_core_BINARY)
SV3D_BINARIES += $(pc_generic_$(OS_MODULE)_com_BINARY)
SV3D_BINARIES += $(pc_generic_$(OS_MODULE)_logging_BINARY)
SV3D_BINARIES += $(pc_generic_$(OS_MODULE)_sys_BINARY)
SV3D_BINARIES += $(pc_generic_util_chrono_BINARY)
SV3D_BINARIES += $(pc_generic_util_cli_BINARY)
SV3D_BINARIES +=
SV3D_BINARIES += $(pc_generic_core_BINARY)
SV3D_BINARIES += $(pc_generic_util_coding_BINARY)
SV3D_BINARIES += $(pc_generic_util_com_BINARY)
SV3D_BINARIES += $(pc_generic_util_logging_BINARY)
SV3D_BINARIES +=
SV3D_BINARIES += $(pc_svs_animation_BINARY)
SV3D_BINARIES += $(pc_svs_assets_imageoverlays_BINARY)
SV3D_BINARIES += $(pc_svs_assets_vehiclemodel_BINARY)
SV3D_BINARIES += $(pc_svs_assets_floorplate_BINARY)
SV3D_BINARIES += $(pc_svs_c2w_BINARY)
SV3D_BINARIES += $(pc_svs_core_BINARY)
SV3D_BINARIES += $(pc_svs_daddy_BINARY)
SV3D_BINARIES += $(pc_svs_factory_BINARY)
ifdef CHAMAELEON
SV3D_BINARIES += $(pc_svs_imp_chamaeleon_BINARY)
SV3D_BINARIES += $(pc_svs_imp_cwd_BINARY)
SV3D_BINARIES += $(pc_svs_imp_iq_BINARY)
SV3D_BINARIES += $(pc_svs_imp_sh_BINARY)
SV3D_BINARIES += $(pc_svs_imp_tnf_BINARY)
endif
SV3D_BINARIES += $(pc_svs_texfloor_core_BINARY)
SV3D_BINARIES += $(pc_svs_texfloor_odometry_BINARY)
SV3D_BINARIES += $(pc_svs_util_cli_BINARY)
SV3D_BINARIES += $(pc_svs_util_linestring_BINARY)
SV3D_BINARIES += $(pc_svs_util_logging_BINARY)
SV3D_BINARIES += $(pc_svs_util_math_BINARY)
SV3D_BINARIES += $(pc_svs_util_osgx_BINARY)
SV3D_BINARIES += $(pc_svs_vehicle_BINARY)
SV3D_BINARIES += $(pc_svs_views_engineeringview_BINARY)
SV3D_BINARIES += $(pc_svs_views_perfview_BINARY)
SV3D_BINARIES += $(pc_svs_views_fusiview_BINARY)
SV3D_BINARIES += $(pc_svs_views_warpfisheyeview_BINARY)
SV3D_BINARIES += $(pc_svs_views_rawfisheyeview_BINARY)
SV3D_BINARIES += $(pc_svs_virtcam_BINARY)
SV3D_BINARIES += $(pc_svs_worker_bowlshaping_BINARY)
SV3D_BINARIES += $(pc_svs_worker_core_BINARY)
SV3D_BINARIES += $(pc_svs_worker_fusion_BINARY)
SV3D_BINARIES += $(pc_svs_worker_stitching_BINARY)
SV3D_BINARIES +=
SV3D_BINARIES += $(cc_assets_augmentedview_BINARY)
# SV3D_BINARIES += $(cc_assets_background_BINARY)
SV3D_BINARIES += $(cc_assets_baseplate_BINARY)
SV3D_BINARIES += $(cc_assets_common_BINARY)
SV3D_BINARIES += $(cc_assets_corner_BINARY)
SV3D_BINARIES += $(cc_assets_splineoverlay_BINARY)
SV3D_BINARIES += $(cc_assets_tileoverlay_BINARY)
SV3D_BINARIES += $(cc_assets_parkingspots_BINARY)
SV3D_BINARIES += $(cc_assets_parkingslot_BINARY)
# SV3D_BINARIES += $(cc_assets_caliboverlay_BINARY)
SV3D_BINARIES += $(cc_assets_button_BINARY)
SV3D_BINARIES += $(cc_assets_rctaoverlay_BINARY)
SV3D_BINARIES += $(cc_assets_trajectory_BINARY)
# SV3D_BINARIES += $(cc_assets_virtualreality_BINARY)

# ifeq ($(COMPILER_NAME),gcc)
# SV3D_BINARIES += $(cc_assets_virtualreality_BINARY)
# endif
# ifeq ($(COMPILER_NAME),msvc)
# SV3D_BINARIES += $(cc_assets_virtualreality_BINARY)
# endif

SV3D_BINARIES += $(cc_assets_stb_BINARY)
SV3D_BINARIES += $(cc_assets_viewbowl_BINARY)
SV3D_BINARIES += $(cc_assets_impostor_BINARY)
SV3D_BINARIES += $(cc_assets_overlaycallback_BINARY)
SV3D_BINARIES += $(cc_assets_customfloorplategenerator_BINARY)
SV3D_BINARIES += $(cc_assets_uielements_BINARY)
SV3D_BINARIES += $(cc_assets_streetoverlay_BINARY)
SV3D_BINARIES += $(cc_assets_superTransparentOverlay_BINARY)
SV3D_BINARIES += $(cc_assets_fisheyeassets_BINARY)
SV3D_BINARIES += $(cc_assets_fisheyetransition_BINARY)
SV3D_BINARIES += $(cc_assets_freeparkingoverlay_BINARY)
SV3D_BINARIES += $(cc_assets_parkingspace_BINARY)
SV3D_BINARIES += $(cc_assets_ECALprogressoverlay_BINARY)
SV3D_BINARIES += $(cc_assets_debugoverlay_BINARY)
# SV3D_BINARIES += $(cc_assets_drivablepath_BINARY)
SV3D_BINARIES += $(cc_assets_dynamicgearoverlay_BINARY)
SV3D_BINARIES += $(cc_factory_BINARY)
SV3D_BINARIES += $(cc_assets_vehicle2dwheels_BINARY)
SV3D_BINARIES += $(cc_assets_rimprotectionline_BINARY)
SV3D_BINARIES += $(cc_assets_dynamicwheelmask_BINARY)
SV3D_BINARIES += $(cc_assets_e3parkingoverlay_BINARY)
ifeq ($(TARGET_MODULE),target_standalone)
SV3D_BINARIES += $(cc_util_touchmanipulator_BINARY)
	ifdef IMGUI
		SV3D_BINARIES += $(cc_imgui_BINARY)
	endif
endif

SV3D_BINARIES += $(cc_assets_ptsoverlay_BINARY)
SV3D_BINARIES += $(cc_assets_pdc_BINARY)
SV3D_BINARIES += $(cc_core_BINARY)
SV3D_BINARIES += $(cc_cpc_BINARY)
SV3D_BINARIES += $(cc_daddy_BINARY)
SV3D_BINARIES += $(cc_sm_viewmode_BINARY)
SV3D_BINARIES += $(cc_$(TARGET_MODULE)_BINARY)
SV3D_BINARIES += $(cc_util_cli_BINARY)
SV3D_BINARIES += $(cc_util_beziercurve_BINARY)
SV3D_BINARIES += $(cc_util_polygonmath_BINARY)
SV3D_BINARIES += $(cc_util_time_BINARY)
SV3D_BINARIES += $(cc_util_logging_BINARY)
SV3D_BINARIES += $(cc_util_common_BINARY)
ifeq ($(TARGET_MODULE),target_android)
SV3D_BINARIES += $(cc_vhm_BINARY)
endif
ifeq ($(TARGET_MODULE),target_linux)
SV3D_BINARIES += $(cc_vhm_BINARY)
endif
SV3D_BINARIES += $(cc_views_panoramaview_BINARY)
SV3D_BINARIES += $(cc_views_driveassistview_BINARY)
SV3D_BINARIES += $(cc_views_bonnetview_BINARY)
SV3D_BINARIES += $(cc_views_daynightview_BINARY)
SV3D_BINARIES += $(cc_views_nfsengineeringview_BINARY)
SV3D_BINARIES += $(cc_views_customrawfisheyeview_BINARY)
SV3D_BINARIES += $(cc_views_customwarpfisheyeview_BINARY)
SV3D_BINARIES += $(cc_views_planview_BINARY)
SV3D_BINARIES += $(cc_views_surroundview_BINARY)
SV3D_BINARIES += $(cc_views_combinedview_BINARY)
SV3D_BINARIES += $(cc_views_parkview_BINARY)
SV3D_BINARIES += $(cc_views_callback_BINARY)
SV3D_BINARIES += $(cc_virtcam_BINARY)

SV3D_BINARIES += $(cc_worker_core_BINARY)
ifeq ($(TARGET_MODULE),target_standalone)
SV3D_BINARIES += $(cc_test_BINARY)
endif
SV3D_BINARIES +=
SV3D_BINARIES += $(hw_version_BINARY)
SV3D_BINARIES += $(hw_daddy_BINARY)
ifeq ($(TARGET_MODULE),$(filter $(TARGET_MODULE),$(HARDWARE_TARGETS)))
SV3D_BINARIES += $(hw_ipcsync_BINARY)
endif

ifdef UT
_UT_BINARY := $(hw_ut_$(UT_MODULE)_UTBINARY)
$(_UT_BINARY):               \
$(SV3D_BINARIES)           \
$(hw_ut_$(UT_MODULE)_BINARY) \
$(UTOBJLIST)
endif




_COMPONENT_BINARIES += $(cc_$(TARGET_MODULE)_BINARY)

ifeq  ($(SOC),android)
$(cc_$(TARGET_MODULE)_BINARY): $(SV3D_BINARIES)
else
$(cc_$(TARGET_MODULE)_BINARY): $(SV3D_BINARIES)
endif

endif

#EOF
