/// @copyright (C) 2023 Robert <PERSON> GmbH.
/// The reproduction, distribution and utilization of this file as well as the
/// communication of its contents to others without express authorization is
/// prohibited. Offenders will be held liable for the payment of damages. All
/// rights reserved in the event of the grant of a patent, utility model or
/// design.
/// @file

#include "pc/svs/imp/chamaeleon/inc/chamaeleon_base_signals.hpp"

namespace rbp
{
namespace vis
{
namespace imp
{
namespace chamaeleon
{
void ChamaeleonBaseSignals::setParameters(const ChamaeleonBaseSignalsData& f_params)
{
    m_ignoreCarDoorState     = f_params.m_ignoreCarDoorState;
    m_ignoreMirrorState      = f_params.m_ignoreMirrorState;
    m_ignoreDegradationState = f_params.m_ignoreDegradationState;
    m_ignoreCalibState       = f_params.m_ignoreCalibState;
}

void ChamaeleonBaseSignals::setDoorState(const pc::daddy::DoorStateDaddy* const f_doorState)
{
    // REAR_LEFT
    if ((static_cast<pc::daddy::ECarDoorState>(
             f_doorState->m_Data[static_cast<vfc::size_t>(pc::daddy::ECarDoorID::CARDOOR_REAR_LEFT)]) ==
         pc::daddy::ECarDoorState::CARDOORSTATE_CLOSED) &&
        (static_cast<pc::daddy::ECarDoorState>(
             f_doorState->m_Data[static_cast<vfc::size_t>(pc::daddy::ECarDoorID::CARDOOR_FRONT_LEFT)]) ==
         pc::daddy::ECarDoorState::CARDOORSTATE_CLOSED) &&
        (static_cast<pc::daddy::ECarDoorState>(
             f_doorState->m_Data[static_cast<vfc::size_t>(pc::daddy::ECarDoorID::CARDOOR_TRUNK)]) ==
         pc::daddy::ECarDoorState::CARDOORSTATE_CLOSED))
    {
        m_roiStatus[ETabWorldOverlapROI::WORLD_REAR_LEFT].m_doorState = pc::daddy::ECarDoorState::CARDOORSTATE_CLOSED;
    }
    else if (
        (static_cast<pc::daddy::ECarDoorState>(
             f_doorState->m_Data[static_cast<vfc::size_t>(pc::daddy::ECarDoorID::CARDOOR_REAR_LEFT)]) ==
         pc::daddy::ECarDoorState::CARDOORSTATE_INVALID) ||
        (static_cast<pc::daddy::ECarDoorState>(
             f_doorState->m_Data[static_cast<vfc::size_t>(pc::daddy::ECarDoorID::CARDOOR_FRONT_LEFT)]) ==
         pc::daddy::ECarDoorState::CARDOORSTATE_INVALID) ||
        (static_cast<pc::daddy::ECarDoorState>(
             f_doorState->m_Data[static_cast<vfc::size_t>(pc::daddy::ECarDoorID::CARDOOR_TRUNK)]) ==
         pc::daddy::ECarDoorState::CARDOORSTATE_INVALID))
    {
        m_roiStatus[ETabWorldOverlapROI::WORLD_REAR_LEFT].m_doorState = pc::daddy::ECarDoorState::CARDOORSTATE_INVALID;
    }
    else if (
        (static_cast<pc::daddy::ECarDoorState>(
             f_doorState->m_Data[static_cast<vfc::size_t>(pc::daddy::ECarDoorID::CARDOOR_REAR_LEFT)]) ==
         pc::daddy::ECarDoorState::CARDOORSTATE_NOT_PLAUSIBLE) ||
        (static_cast<pc::daddy::ECarDoorState>(
             f_doorState->m_Data[static_cast<vfc::size_t>(pc::daddy::ECarDoorID::CARDOOR_FRONT_LEFT)]) ==
         pc::daddy::ECarDoorState::CARDOORSTATE_NOT_PLAUSIBLE) ||
        (static_cast<pc::daddy::ECarDoorState>(
             f_doorState->m_Data[static_cast<vfc::size_t>(pc::daddy::ECarDoorID::CARDOOR_TRUNK)]) ==
         pc::daddy::ECarDoorState::CARDOORSTATE_NOT_PLAUSIBLE))
    {
        m_roiStatus[ETabWorldOverlapROI::WORLD_REAR_LEFT].m_doorState =
            pc::daddy::ECarDoorState::CARDOORSTATE_NOT_PLAUSIBLE;
    }
    else if (
        (static_cast<pc::daddy::ECarDoorState>(
             f_doorState->m_Data[static_cast<vfc::size_t>(pc::daddy::ECarDoorID::CARDOOR_REAR_LEFT)]) ==
         pc::daddy::ECarDoorState::CARDOORSTATE_OPEN) ||
        (static_cast<pc::daddy::ECarDoorState>(
             f_doorState->m_Data[static_cast<vfc::size_t>(pc::daddy::ECarDoorID::CARDOOR_FRONT_LEFT)]) ==
         pc::daddy::ECarDoorState::CARDOORSTATE_OPEN) ||
        (static_cast<pc::daddy::ECarDoorState>(
             f_doorState->m_Data[static_cast<vfc::size_t>(pc::daddy::ECarDoorID::CARDOOR_TRUNK)]) ==
         pc::daddy::ECarDoorState::CARDOORSTATE_OPEN))
    {
        m_roiStatus[ETabWorldOverlapROI::WORLD_REAR_LEFT].m_doorState = pc::daddy::ECarDoorState::CARDOORSTATE_OPEN;
    }
    else
    {
        m_roiStatus[ETabWorldOverlapROI::WORLD_REAR_LEFT].m_doorState = pc::daddy::ECarDoorState::CARDOORSTATE_NONE;
    }

    // FRONT_LEFT
    if ((static_cast<pc::daddy::ECarDoorState>(
             f_doorState->m_Data[static_cast<vfc::size_t>(pc::daddy::ECarDoorID::CARDOOR_REAR_LEFT)]) ==
         pc::daddy::ECarDoorState::CARDOORSTATE_CLOSED) &&
        (static_cast<pc::daddy::ECarDoorState>(
             f_doorState->m_Data[static_cast<vfc::size_t>(pc::daddy::ECarDoorID::CARDOOR_FRONT_LEFT)]) ==
         pc::daddy::ECarDoorState::CARDOORSTATE_CLOSED))
    {
        m_roiStatus[ETabWorldOverlapROI::WORLD_FRONT_LEFT].m_doorState = pc::daddy::ECarDoorState::CARDOORSTATE_CLOSED;
    }
    else if (
        (static_cast<pc::daddy::ECarDoorState>(
             f_doorState->m_Data[static_cast<vfc::size_t>(pc::daddy::ECarDoorID::CARDOOR_REAR_LEFT)]) ==
         pc::daddy::ECarDoorState::CARDOORSTATE_INVALID) ||
        (static_cast<pc::daddy::ECarDoorState>(
             f_doorState->m_Data[static_cast<vfc::size_t>(pc::daddy::ECarDoorID::CARDOOR_FRONT_LEFT)]) ==
         pc::daddy::ECarDoorState::CARDOORSTATE_INVALID))
    {
        m_roiStatus[ETabWorldOverlapROI::WORLD_FRONT_LEFT].m_doorState = pc::daddy::ECarDoorState::CARDOORSTATE_INVALID;
    }
    else if (
        (static_cast<pc::daddy::ECarDoorState>(
             f_doorState->m_Data[static_cast<vfc::size_t>(pc::daddy::ECarDoorID::CARDOOR_REAR_LEFT)]) ==
         pc::daddy::ECarDoorState::CARDOORSTATE_NOT_PLAUSIBLE) ||
        (static_cast<pc::daddy::ECarDoorState>(
             f_doorState->m_Data[static_cast<vfc::size_t>(pc::daddy::ECarDoorID::CARDOOR_FRONT_LEFT)]) ==
         pc::daddy::ECarDoorState::CARDOORSTATE_NOT_PLAUSIBLE))
    {
        m_roiStatus[ETabWorldOverlapROI::WORLD_FRONT_LEFT].m_doorState =
            pc::daddy::ECarDoorState::CARDOORSTATE_NOT_PLAUSIBLE;
    }
    else if (
        (static_cast<pc::daddy::ECarDoorState>(
             f_doorState->m_Data[static_cast<vfc::size_t>(pc::daddy::ECarDoorID::CARDOOR_REAR_LEFT)]) ==
         pc::daddy::ECarDoorState::CARDOORSTATE_OPEN) ||
        (static_cast<pc::daddy::ECarDoorState>(
             f_doorState->m_Data[static_cast<vfc::size_t>(pc::daddy::ECarDoorID::CARDOOR_FRONT_LEFT)]) ==
         pc::daddy::ECarDoorState::CARDOORSTATE_OPEN))
    {
        m_roiStatus[ETabWorldOverlapROI::WORLD_FRONT_LEFT].m_doorState = pc::daddy::ECarDoorState::CARDOORSTATE_OPEN;
    }
    else
    {
        m_roiStatus[ETabWorldOverlapROI::WORLD_FRONT_LEFT].m_doorState = pc::daddy::ECarDoorState::CARDOORSTATE_NONE;
    }

    // FRONT_RIGHT
    if ((static_cast<pc::daddy::ECarDoorState>(
             f_doorState->m_Data[static_cast<vfc::size_t>(pc::daddy::ECarDoorID::CARDOOR_REAR_RIGHT)]) ==
         pc::daddy::ECarDoorState::CARDOORSTATE_CLOSED) &&
        (static_cast<pc::daddy::ECarDoorState>(
             f_doorState->m_Data[static_cast<vfc::size_t>(pc::daddy::ECarDoorID::CARDOOR_FRONT_RIGHT)]) ==
         pc::daddy::ECarDoorState::CARDOORSTATE_CLOSED))
    {
        m_roiStatus[ETabWorldOverlapROI::WORLD_FRONT_RIGHT].m_doorState = pc::daddy::ECarDoorState::CARDOORSTATE_CLOSED;
    }
    else if (
        (static_cast<pc::daddy::ECarDoorState>(
             f_doorState->m_Data[static_cast<vfc::size_t>(pc::daddy::ECarDoorID::CARDOOR_REAR_RIGHT)]) ==
         pc::daddy::ECarDoorState::CARDOORSTATE_INVALID) ||
        (static_cast<pc::daddy::ECarDoorState>(
             f_doorState->m_Data[static_cast<vfc::size_t>(pc::daddy::ECarDoorID::CARDOOR_FRONT_RIGHT)]) ==
         pc::daddy::ECarDoorState::CARDOORSTATE_INVALID))
    {
        m_roiStatus[ETabWorldOverlapROI::WORLD_FRONT_RIGHT].m_doorState =
            pc::daddy::ECarDoorState::CARDOORSTATE_INVALID;
    }
    else if (
        (static_cast<pc::daddy::ECarDoorState>(
             f_doorState->m_Data[static_cast<vfc::size_t>(pc::daddy::ECarDoorID::CARDOOR_REAR_RIGHT)]) ==
         pc::daddy::ECarDoorState::CARDOORSTATE_NOT_PLAUSIBLE) ||
        (static_cast<pc::daddy::ECarDoorState>(
             f_doorState->m_Data[static_cast<vfc::size_t>(pc::daddy::ECarDoorID::CARDOOR_FRONT_RIGHT)]) ==
         pc::daddy::ECarDoorState::CARDOORSTATE_NOT_PLAUSIBLE))
    {
        m_roiStatus[ETabWorldOverlapROI::WORLD_FRONT_RIGHT].m_doorState =
            pc::daddy::ECarDoorState::CARDOORSTATE_NOT_PLAUSIBLE;
    }
    else if (
        (static_cast<pc::daddy::ECarDoorState>(
             f_doorState->m_Data[static_cast<vfc::size_t>(pc::daddy::ECarDoorID::CARDOOR_REAR_RIGHT)]) ==
         pc::daddy::ECarDoorState::CARDOORSTATE_OPEN) ||
        (static_cast<pc::daddy::ECarDoorState>(
             f_doorState->m_Data[static_cast<vfc::size_t>(pc::daddy::ECarDoorID::CARDOOR_FRONT_RIGHT)]) ==
         pc::daddy::ECarDoorState::CARDOORSTATE_OPEN))
    {
        m_roiStatus[ETabWorldOverlapROI::WORLD_FRONT_RIGHT].m_doorState = pc::daddy::ECarDoorState::CARDOORSTATE_OPEN;
    }
    else
    {
        m_roiStatus[ETabWorldOverlapROI::WORLD_FRONT_RIGHT].m_doorState = pc::daddy::ECarDoorState::CARDOORSTATE_NONE;
    }

    // REAR_RIGHT
    if ((static_cast<pc::daddy::ECarDoorState>(
             f_doorState->m_Data[static_cast<vfc::size_t>(pc::daddy::ECarDoorID::CARDOOR_REAR_RIGHT)]) ==
         pc::daddy::ECarDoorState::CARDOORSTATE_CLOSED) &&
        (static_cast<pc::daddy::ECarDoorState>(
             f_doorState->m_Data[static_cast<vfc::size_t>(pc::daddy::ECarDoorID::CARDOOR_FRONT_RIGHT)]) ==
         pc::daddy::ECarDoorState::CARDOORSTATE_CLOSED) &&
        (static_cast<pc::daddy::ECarDoorState>(
             f_doorState->m_Data[static_cast<vfc::size_t>(pc::daddy::ECarDoorID::CARDOOR_TRUNK)]) ==
         pc::daddy::ECarDoorState::CARDOORSTATE_CLOSED))
    {
        m_roiStatus[ETabWorldOverlapROI::WORLD_REAR_RIGHT].m_doorState = pc::daddy::ECarDoorState::CARDOORSTATE_CLOSED;
    }
    else if (
        (static_cast<pc::daddy::ECarDoorState>(
             f_doorState->m_Data[static_cast<vfc::size_t>(pc::daddy::ECarDoorID::CARDOOR_REAR_RIGHT)]) ==
         pc::daddy::ECarDoorState::CARDOORSTATE_INVALID) ||
        (static_cast<pc::daddy::ECarDoorState>(
             f_doorState->m_Data[static_cast<vfc::size_t>(pc::daddy::ECarDoorID::CARDOOR_FRONT_RIGHT)]) ==
         pc::daddy::ECarDoorState::CARDOORSTATE_INVALID) ||
        (static_cast<pc::daddy::ECarDoorState>(
             f_doorState->m_Data[static_cast<vfc::size_t>(pc::daddy::ECarDoorID::CARDOOR_TRUNK)]) ==
         pc::daddy::ECarDoorState::CARDOORSTATE_INVALID))
    {
        m_roiStatus[ETabWorldOverlapROI::WORLD_REAR_RIGHT].m_doorState = pc::daddy::ECarDoorState::CARDOORSTATE_INVALID;
    }
    else if (
        (static_cast<pc::daddy::ECarDoorState>(
             f_doorState->m_Data[static_cast<vfc::size_t>(pc::daddy::ECarDoorID::CARDOOR_REAR_RIGHT)]) ==
         pc::daddy::ECarDoorState::CARDOORSTATE_NOT_PLAUSIBLE) ||
        (static_cast<pc::daddy::ECarDoorState>(
             f_doorState->m_Data[static_cast<vfc::size_t>(pc::daddy::ECarDoorID::CARDOOR_FRONT_RIGHT)]) ==
         pc::daddy::ECarDoorState::CARDOORSTATE_NOT_PLAUSIBLE) ||
        (static_cast<pc::daddy::ECarDoorState>(
             f_doorState->m_Data[static_cast<vfc::size_t>(pc::daddy::ECarDoorID::CARDOOR_TRUNK)]) ==
         pc::daddy::ECarDoorState::CARDOORSTATE_NOT_PLAUSIBLE))
    {
        m_roiStatus[ETabWorldOverlapROI::WORLD_REAR_RIGHT].m_doorState =
            pc::daddy::ECarDoorState::CARDOORSTATE_NOT_PLAUSIBLE;
    }
    else if (
        (static_cast<pc::daddy::ECarDoorState>(
             f_doorState->m_Data[static_cast<vfc::size_t>(pc::daddy::ECarDoorID::CARDOOR_REAR_RIGHT)]) ==
         pc::daddy::ECarDoorState::CARDOORSTATE_OPEN) ||
        (static_cast<pc::daddy::ECarDoorState>(
             f_doorState->m_Data[static_cast<vfc::size_t>(pc::daddy::ECarDoorID::CARDOOR_FRONT_RIGHT)]) ==
         pc::daddy::ECarDoorState::CARDOORSTATE_OPEN) ||
        (static_cast<pc::daddy::ECarDoorState>(
             f_doorState->m_Data[static_cast<vfc::size_t>(pc::daddy::ECarDoorID::CARDOOR_TRUNK)]) ==
         pc::daddy::ECarDoorState::CARDOORSTATE_OPEN))
    {
        m_roiStatus[ETabWorldOverlapROI::WORLD_REAR_RIGHT].m_doorState = pc::daddy::ECarDoorState::CARDOORSTATE_OPEN;
    }
    else
    {
        m_roiStatus[ETabWorldOverlapROI::WORLD_REAR_RIGHT].m_doorState = pc::daddy::ECarDoorState::CARDOORSTATE_NONE;
    }
}

void ChamaeleonBaseSignals::setMirrorState(const pc::daddy::MirrorStateDaddy* const f_mirrorState)
{
    m_roiStatus[ETabWorldOverlapROI::WORLD_REAR_LEFT].m_mirrorState = static_cast<pc::daddy::EMirrorState>(
        f_mirrorState->m_Data[static_cast<vfc::size_t>(pc::daddy::ESideMirrorID::SIDEMIRROR_LEFT)]);
    m_roiStatus[ETabWorldOverlapROI::WORLD_FRONT_LEFT].m_mirrorState = static_cast<pc::daddy::EMirrorState>(
        f_mirrorState->m_Data[static_cast<vfc::size_t>(pc::daddy::ESideMirrorID::SIDEMIRROR_LEFT)]);
    m_roiStatus[ETabWorldOverlapROI::WORLD_FRONT_RIGHT].m_mirrorState = static_cast<pc::daddy::EMirrorState>(
        f_mirrorState->m_Data[static_cast<vfc::size_t>(pc::daddy::ESideMirrorID::SIDEMIRROR_RIGHT)]);
    m_roiStatus[ETabWorldOverlapROI::WORLD_REAR_RIGHT].m_mirrorState = static_cast<pc::daddy::EMirrorState>(
        f_mirrorState->m_Data[static_cast<vfc::size_t>(pc::daddy::ESideMirrorID::SIDEMIRROR_RIGHT)]);
}

void ChamaeleonBaseSignals::setDegradationState(const pc::daddy::CameraDegradationMaskDaddy* const f_degradationState)
{
    const bool l_isFrontCameraDegraded =
        (f_degradationState->m_Data & (1u << static_cast<vfc::uint32_t>(pc::core::sysconf::Cameras::FRONT_CAMERA)));
    const bool l_isRightCameraDegraded =
        (f_degradationState->m_Data & (1u << static_cast<vfc::uint32_t>(pc::core::sysconf::Cameras::RIGHT_CAMERA)));
    const bool l_isRearCameraDegraded =
        (f_degradationState->m_Data & (1u << static_cast<vfc::uint32_t>(pc::core::sysconf::Cameras::REAR_CAMERA)));
    const bool l_isLeftCameraDegraded =
        (f_degradationState->m_Data & (1u << static_cast<vfc::uint32_t>(pc::core::sysconf::Cameras::LEFT_CAMERA)));

    // REAR_LEFT
    if (l_isRearCameraDegraded || l_isLeftCameraDegraded)
    {
        m_roiStatus[ETabWorldOverlapROI::WORLD_REAR_LEFT].m_degradationState = EDegradationState::DEGRADED;
    }
    else
    {
        m_roiStatus[ETabWorldOverlapROI::WORLD_REAR_LEFT].m_degradationState = EDegradationState::NOMINAL;
    }

    // FRONT_LEFT
    if (l_isFrontCameraDegraded || l_isLeftCameraDegraded)
    {
        m_roiStatus[ETabWorldOverlapROI::WORLD_FRONT_LEFT].m_degradationState = EDegradationState::DEGRADED;
    }
    else
    {
        m_roiStatus[ETabWorldOverlapROI::WORLD_FRONT_LEFT].m_degradationState = EDegradationState::NOMINAL;
    }

    // FRONT_RIGHT
    if (l_isFrontCameraDegraded || l_isRightCameraDegraded)
    {
        m_roiStatus[ETabWorldOverlapROI::WORLD_FRONT_RIGHT].m_degradationState = EDegradationState::DEGRADED;
    }
    else
    {
        m_roiStatus[ETabWorldOverlapROI::WORLD_FRONT_RIGHT].m_degradationState = EDegradationState::NOMINAL;
    }

    // REAR_RIGHT
    if (l_isRearCameraDegraded || l_isRightCameraDegraded)
    {
        m_roiStatus[ETabWorldOverlapROI::WORLD_REAR_RIGHT].m_degradationState = EDegradationState::DEGRADED;
    }
    else
    {
        m_roiStatus[ETabWorldOverlapROI::WORLD_REAR_RIGHT].m_degradationState = EDegradationState::NOMINAL;
    }
}

void ChamaeleonBaseSignals::setCalibState(const pc::daddy::CalibrationStsDaddy* f_calibState)
{
    m_calibState = f_calibState->m_Data;
    m_roiStatus[ETabWorldOverlapROI::WORLD_REAR_LEFT].m_calibState = m_calibState;
    m_roiStatus[ETabWorldOverlapROI::WORLD_FRONT_LEFT].m_calibState = m_calibState;
    m_roiStatus[ETabWorldOverlapROI::WORLD_FRONT_RIGHT].m_calibState = m_calibState;
    m_roiStatus[ETabWorldOverlapROI::WORLD_REAR_RIGHT].m_calibState = m_calibState;
}

pc::daddy::ECarDoorState ChamaeleonBaseSignals::getCarDoorState(ETabWorldOverlapROI f_worldOverlapRoi) const
{
    return m_roiStatus[f_worldOverlapRoi].m_doorState;
}

pc::daddy::EMirrorState ChamaeleonBaseSignals::getMirrorState(ETabWorldOverlapROI f_worldOverlapRoi) const
{
    return m_roiStatus[f_worldOverlapRoi].m_mirrorState;
}

ChamaeleonBaseSignals::EDegradationState
ChamaeleonBaseSignals::getDegradationState(ETabWorldOverlapROI f_worldOverlapRoi) const
{
    return m_roiStatus[f_worldOverlapRoi].m_degradationState;
}

bool ChamaeleonBaseSignals::getCalibState(ETabWorldOverlapROI f_worldOverlapRoi) const
{
    return m_roiStatus[f_worldOverlapRoi].m_calibState;
}

bool ChamaeleonBaseSignals::getCalibState() const
{
    return m_calibState;
}

bool ChamaeleonBaseSignals::isWorldROIvalid(ETabWorldOverlapROI f_worldOverlapRoi) const
{
    return ((getCarDoorState(f_worldOverlapRoi) == pc::daddy::CARDOORSTATE_CLOSED) || m_ignoreCarDoorState) &&
           ((getMirrorState(f_worldOverlapRoi) == pc::daddy::MIRRORSTATE_NOT_FLAPPED) || m_ignoreMirrorState) &&
           ((getDegradationState(f_worldOverlapRoi) == EDegradationState::NOMINAL) || m_ignoreDegradationState) &&
           ((getCalibState(f_worldOverlapRoi) == true) || m_ignoreCalibState) ;
}

osg::Vec4f ChamaeleonBaseSignals::getWorldROIValidityMask() const
{
    osg::Vec4f retval{};
    for (const auto worldOverlap : CWorldOverlapIndex::ALL)
    {
        retval[static_cast<vfc::uint32_t>(worldOverlap.asInteger())] =
            isWorldROIvalid(worldOverlap.asEnum()) ? 1.F : 0.F;
    }
    return retval;
}

} // namespace chamaeleon
} // namespace imp
} // namespace vis
} // namespace rbp
