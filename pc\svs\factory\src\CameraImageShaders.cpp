//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON> GmbH. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include "pc/svs/factory/inc/CameraImageShaders.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h"
#include "CustomSystemConf.h" //target+cc specific

#include <cassert>
#include <fstream>
#include <iostream>
#include <sstream>
#include <cctype>

namespace pc
{
namespace factory
{

const static std::string s_RawTexConf_frag = 
"#version 300 es \n"
" \n"
"#ifdef USE_SAMPLER_EXTERNAL_OES \n"
"  #extension GL_OES_EGL_image_external_essl3 : require \n"
"  #if defined ENABLE_SHARPNESS_HARMONIZATION || defined ENABLE_TNF \n"
"    #extension GL_EXT_YUV_target : require \n"
"  #endif \n"
"#endif \n"
" \n"
"  precision highp float; \n"
" \n"
"#ifdef USE_SAMPLER_EXTERNAL_OES \n"
"  #if defined ENABLE_SHARPNESS_HARMONIZATION || defined ENABLE_TNF \n"
"    uniform __samplerExternal2DY2YEXT u_tex0; \n"
"  #else \n"
"    uniform samplerExternalOES u_tex0; \n"
"  #endif \n"
"#else \n"
"  uniform sampler2D u_tex0; \n"
"#endif \n"
" \n"
"#ifdef ENABLE_TNF \n"
"  uniform sampler2D u_filteredY; \n"
"#endif \n"
" \n"
"in vec2 v_texCoord; \n"
" \n"
"out vec4 FragColor; \n"
" \n"
"uniform int u_cam; \n"
"uniform int u_isTexCombine; \n"
"uniform float u_brightFactor; \n"
" \n"
"vec2 getUv(vec2 f_uv) \n"
"{ \n"
"  if (1 == u_isTexCombine) \n"
"  { \n"
"    vec2 l_uv = vec2(0.0, f_uv.y); \n"
"    if (0 == u_cam)  // front \n"
"    { \n"
"      l_uv.x = 0.75 + f_uv.x * 0.25; \n"
"    } \n"
"    else if (1 == u_cam)  // right \n"
"    { \n"
"      l_uv.x = 0.5 + f_uv.x * 0.25; \n"
"    } \n"
"    else if (2 == u_cam)  // rear \n"
"    { \n"
"      l_uv.x = f_uv.x * 0.25; \n"
"    } \n"
"    else  // left \n"
"    { \n"
"      l_uv.x = 0.25 + f_uv.x * 0.25; \n"
"    } \n"
"    return l_uv; \n"
"  } \n"
"  else \n"
"  { \n"
"    return f_uv; \n"
"  } \n"
"} \n"
" \n"
"// for the SIL \n"
"#ifndef USE_SAMPLER_EXTERNAL_OES \n"
"  vec4 yuv_2_rgb_sil(vec4 src) \n"
"  { \n"
"      vec4 yuva = vec4((src.x-16.0/255.0), (src.y - 0.5), (src.z - 0.5), 1.0); \n"
" \n"
"      vec4 res; \n"
"      res.r = 1.164 * yuva.x                  + 1.596 * yuva.z; \n"
"      res.g = 1.164 * yuva.x - 0.392 * yuva.y - 0.813 * yuva.z; \n"
"      res.b = 1.164 * yuva.x + 2.017 * yuva.y; \n"
" \n"
"      res.a = src.a; \n"
" \n"
"      return res; \n"
"  } \n"
" \n"
" \n"
"  vec4 rgb_2_yuv_sil(vec4 src) \n"
"  { \n"
"      vec4 res; \n"
"      res.x =  0.257 * src.r + 0.504 * src.g + 0.098 * src.b + 16.0/255.0; \n"
"      res.y = -0.148 * src.r - 0.291 * src.g + 0.439 * src.b + 0.5; \n"
"      res.z =  0.439 * src.r - 0.368 * src.g - 0.071 * src.b + 0.5; \n"
" \n"
"      res.a = src.a; \n"
" \n"
"      return res; \n"
"  } \n"
"#endif \n"
" \n"
"#ifdef DEBUG_SHARPNESS_HARMONIZATION \n"
"  vec4 sixColors(float x) \n"
"  { \n"
"    float r, g, b; \n"
"    float d; \n"
" \n"
"    if (x < 0.0) \n"
"    { \n"
"      r = 0.0; \n"
"      g = 1.0; \n"
"      b = 1.0; \n"
"    } \n"
"    else if (x < 0.2) \n"
"    { \n"
"      d = ((x - 0.0) * 5.0); \n"
"      r = 0.0 * (1.0-d) + 0.0 * d; \n"
"      g = 1.0; \n"
"      b = 1.0 * (1.0-d) + 0.0 * d; \n"
"    } \n"
"    else if (x < 0.4) \n"
"    { \n"
"      d = ((x-0.2) * 5.0); \n"
"      r = 0.0 * (1.0-d) + 1.0 * d; \n"
"      g = 1.0; \n"
"      b = 0.0; \n"
"    } \n"
"    else if (x < 0.6) \n"
"    { \n"
"      d = ((x-0.4) * 5.0); \n"
"      r = 1.0; \n"
"      g = 1.0 * (1.0-d) + 0.0 * d; \n"
"      b = 0.0; \n"
"    } \n"
"    else if (x < 0.8) \n"
"    { \n"
"      d = ((x-0.6) * 5.0); \n"
"      r = 1.0; \n"
"      g = 0.0; \n"
"      b = 0.0 * (1.0-d) + 1.0 * d; \n"
"    } \n"
"    else if (x < 1.0) \n"
"    { \n"
"      d = ((x-0.8) * 5.0); \n"
"      r = 1.0 * (1.0-d) + 0.0 * d; \n"
"      g = 0.0; \n"
"      b = 1.0; \n"
"    } \n"
"    else \n"
"    { \n"
"      r = 0.0; \n"
"      g = 0.0; \n"
"      b = 1.0; \n"
"    } \n"
"    return vec4(r, g, b, 1.0); \n"
"  } \n"
"#endif // DEBUG_SHARPNESS_HARMONIZATION \n"
" \n"
"#ifdef ENABLE_SHARPNESS_HARMONIZATION \n"
"  uniform vec2 u_sharpeningSmoothingFactors; \n"
"  uniform float u_maxSharpening; \n"
" \n"
"  vec4 getCameraTexture(vec2 f_uv, vec2 f_uv_tnf) \n"
"  { \n"
"    vec2 sizeOfTexture = vec2(textureSize(u_tex0, 0)); \n"
"    vec2 texelSize = 1.0 / sizeOfTexture; \n"
" \n"
"    // pixel density stuff for adaptive factors and right pixel selection \n"
"    highp vec2 deltaX = dFdx(f_uv); \n"
"    highp vec2 deltaY = dFdy(f_uv); \n"
" \n"
"    // Normalize the direction vectors \n"
"    vec2 dirU = normalize(deltaX); \n"
"    vec2 dirV = normalize(deltaY); \n"
" \n"
"    #ifndef USE_SAMPLER_EXTERNAL_OES \n"
"      vec4 center    = texture(u_tex0, f_uv); //RGBA \n"
"      vec3 centerYUV = rgb_2_yuv_sil(center).rgb; \n"
"    #else \n"
"      vec3 centerYUV = texture(u_tex0, f_uv).rgb; // texture stays in YUV due to __samplerExternal2DY2YEXT \n"
"    #endif \n"
" \n"
"    #ifdef ENABLE_TNF \n"
"      float centerY = texture(u_filteredY, f_uv_tnf).r; \n"
"      // Fetch neighboring pixels using the direction vectors scaled by texel size \n"
"      float leftY_U  = texture(u_filteredY, f_uv_tnf - dirU * texelSize.x).r; \n"
"      float rightY_U = texture(u_filteredY, f_uv_tnf + dirU * texelSize.x).r; \n"
"      float upY_V    = texture(u_filteredY, f_uv_tnf + dirV * texelSize.y).r; \n"
"      float downY_V  = texture(u_filteredY, f_uv_tnf - dirV * texelSize.y).r; \n"
"    #else \n"
"      float centerY = centerYUV.r; \n"
"      #ifndef USE_SAMPLER_EXTERNAL_OES \n"
"        vec4 left_U    = texture(u_tex0, f_uv - dirU * texelSize.x); \n"
"        vec4 right_U   = texture(u_tex0, f_uv + dirU * texelSize.x); \n"
"        vec4 up_V      = texture(u_tex0, f_uv + dirV * texelSize.y); \n"
"        vec4 down_V    = texture(u_tex0, f_uv - dirV * texelSize.y); \n"
" \n"
"        float leftY_U  = rgb_2_yuv_sil(left_U).r; \n"
"        float rightY_U = rgb_2_yuv_sil(right_U).r; \n"
"        float upY_V    = rgb_2_yuv_sil(up_V).r; \n"
"        float downY_V  = rgb_2_yuv_sil(down_V).r; \n"
"      #else \n"
"        float leftY_U   = texture(u_tex0, f_uv - dirU * texelSize.x).r; \n"
"        float rightY_U  = texture(u_tex0, f_uv + dirU * texelSize.x).r; \n"
"        float upY_V     = texture(u_tex0, f_uv + dirV * texelSize.y).r; \n"
"        float downY_V   = texture(u_tex0, f_uv - dirV * texelSize.y).r; \n"
"      #endif \n"
"    #endif \n"
" \n"
"    // gaussian blur filter, 1/4, 1/2, 1/4 \n"
"    float blurX = (leftY_U + 2.0 * centerY + rightY_U ) / 4.0; \n"
"    float blurY = (upY_V + 2.0 * centerY + downY_V ) / 4.0; \n"
" \n"
"    float highPassX = centerY - blurX; \n"
"    float highPassY = centerY - blurY; \n"
" \n"
"    // input pixels per output fragment \n"
"    float ratioX = length(deltaX)*sizeOfTexture.x; \n"
"    float ratioY = length(deltaY)*sizeOfTexture.y; \n"
" \n"
"    int needsSmoothingX = int(step(1.0, ratioX)); \n"
"    int needsSmoothingY = int(step(1.0, ratioY)); \n"
" \n"
"    float factorX = 0.0; \n"
"    float factorY = 0.0; \n"
" \n"
"    // use linear interpolation (from mix function) to find a sharpness/smoothing setting corresponding to the calculated pixel density \n"
"    // according to formula sharpening_or_smoothing_factor * (1 - ratio) \n"
"    // The clamping of the factors to -1.0 limits the applied degree of smoothing \n"
"    factorX = clamp(mix(u_sharpeningSmoothingFactors[needsSmoothingX], 0.0, ratioX), -1.0, u_maxSharpening); \n"
"    factorY = clamp(mix(u_sharpeningSmoothingFactors[needsSmoothingY], 0.0, ratioY), -1.0, u_maxSharpening); \n"
"    #ifdef DEBUG_SHARPNESS_HARMONIZATION \n"
"      // The pixel density visualization overlaid by the original image is selected when sharpening and smoothing factors are zero \n"
"      bvec2 isZero = equal(u_sharpeningSmoothingFactors, vec2(0.0, 0.0)); \n"
"      if(isZero.x && isZero.y) \n"
"      { \n"
"        // input pixels per output fragment \n"
"        float ratio = ratioX * ratioY; \n"
"        // display texture value together with color \n"
"        #ifndef USE_SAMPLER_EXTERNAL_OES \n"
"          return sixColors(ratio) * yuv_2_rgb_sil(vec4(centerYUV, 1.0)); \n"
"        #else \n"
"          return sixColors(ratio) * vec4(yuv_2_rgb(centerYUV, itu_601_full_range), 1.0); \n"
"        #endif \n"
"      } \n"
"      else // NORMALIZED \n"
"      { \n"
"        // normalized outputs of pixel densities in x- and y-direction on red and green channels \n"
"        // e.g. a red value of 140 corresponds to pixel density in X of 1.4, a green value of 36 corresponds to a pixel \n"
"        // density in Y of 0.36 \n"
"        return vec4(ratioX * 100.0 / 255.0, ratioY * 100.0 / 255.0, 0.0, 1.0); \n"
"        // return vec4(needsSmoothingX * 255, needsSmoothingY * 255, 0.0, 1.0); \n"
"      } \n"
"    #else \n"
"      float yFiltered = centerY + factorX * highPassX + factorY * highPassY; \n"
"      vec3 yuvFinal = vec3(yFiltered, centerYUV.g, centerYUV.b); \n"
"      #ifndef USE_SAMPLER_EXTERNAL_OES \n"
"        return yuv_2_rgb_sil(vec4(yuvFinal, 1.0)); \n"
"      #else \n"
"        return vec4(yuv_2_rgb(yuvFinal, itu_601_full_range), 1.0); \n"
"      #endif \n"
"    #endif // DEBUG_SHARPNESS_HARMONIZATION \n"
"  } \n"
" \n"
"#else \n"
"  vec4 getCameraTexture(vec2 f_uv, vec2 f_uv_tnf) \n"
"  { \n"
"  #ifdef ENABLE_TNF \n"
"    float filteredY = texture(u_filteredY, f_uv_tnf).r; \n"
"    #ifndef USE_SAMPLER_EXTERNAL_OES \n"
"      vec2 UV = rgb_2_yuv_sil(texture(u_tex0, f_uv)).gb; \n"
"      return yuv_2_rgb_sil(vec4(filteredY, UV.x, UV.y, 1.0)); \n"
"    #else \n"
"      vec2 UV = texture(u_tex0, f_uv).gb; \n"
"      return vec4(yuv_2_rgb(vec3(filteredY, UV.x, UV.y), itu_601_full_range), 1.0); \n"
"    #endif \n"
"  #else \n"
"    return texture(u_tex0, f_uv); \n"
"  #endif \n"
"  } \n"
"#endif // ENABLE_SHARPNESS_HARMONIZATION \n"
" \n"
"void main() \n"
"{ \n"
"  vec3 l_color = getCameraTexture(getUv(v_texCoord), v_texCoord).rgb; \n"
" \n"
"  FragColor = vec4(clamp((l_color).rgb * u_brightFactor, 0.0, 1.0), 1.0); \n"
"} \n"
;

const static std::string s_RawTexConf_vert =
"#version 300 es \n"
" \n"
"in vec4 osg_Vertex; \n"
"in vec2 osg_MultiTexCoord0; \n"
" \n"
"uniform mat4 osg_ModelViewProjectionMatrix; \n"
" \n"
"out vec2 v_texCoord; \n"
" \n"
"void main() \n"
"{ \n"
"  v_texCoord = osg_MultiTexCoord0; \n"
"  gl_Position = osg_ModelViewProjectionMatrix * osg_Vertex; \n"
"} \n"
;

const static std::string s_SingleCam_frag =
"#version 300 es \n"
" \n"
"#ifdef USE_SAMPLER_EXTERNAL_OES \n"
"  #extension GL_OES_EGL_image_external_essl3 : require \n"
"  #if defined ENABLE_SHARPNESS_HARMONIZATION || defined ENABLE_TNF \n"
"    #extension GL_EXT_YUV_target : require \n"
"  #endif \n"
"#endif \n"
" \n"
"precision highp float; \n"
" \n"
"in highp vec2 v_texCam; \n"
" \n"
"#define CAM_ON 0 \n"
"#define CAM_DISABLED 1 \n"
"#define CAM_OFF 2 \n"
" \n"
"#if (SOURCE != TARGET) \n"
"uniform float u_FadeFactor; \n"
"#endif \n"
" \n"
"#ifdef ENABLE_CHAMAELEON \n"
"in float v_cham_weightLeftRight; \n"
"in vec3 v_cham_correctionLeft; \n"
"in vec3 v_cham_correctionRight; \n"
"#endif // ENABLE_CHAMAELEON \n"
" \n"
"uniform vec4 u_camDisabledColor; \n"
"uniform vec4 u_camOffColor; \n"
"uniform int u_cam; \n"
"uniform int u_isTexCombine; \n"
"uniform float u_brightFactor; \n"
" \n"
"#ifdef USE_SAMPLER_EXTERNAL_OES \n"
"  #if defined ENABLE_SHARPNESS_HARMONIZATION || defined ENABLE_TNF \n"
"    uniform __samplerExternal2DY2YEXT s_texCams; \n"
"  #else \n"
"    uniform samplerExternalOES s_texCams; \n"
"  #endif \n"
"#else \n"
"  uniform sampler2D s_texCams; \n"
"#endif \n"
" \n"
"vec2 getUv(vec2 f_uv) \n"
"{ \n"
"  if (1 == u_isTexCombine) \n"
"  { \n"
"    vec2 l_uv = vec2(0.0, f_uv.y); \n"
"    if (0 == u_cam)  // front \n"
"    { \n"
"      l_uv.x = 0.75 + f_uv.x * 0.25; \n"
"    } \n"
"    else if (1 == u_cam)  // right \n"
"    { \n"
"      l_uv.x = 0.5 + f_uv.x * 0.25; \n"
"    } \n"
"    else if (2 == u_cam)  // rear \n"
"    { \n"
"      l_uv.x = f_uv.x * 0.25; \n"
"    } \n"
"    else  // left \n"
"    { \n"
"      l_uv.x = 0.25 + f_uv.x * 0.25; \n"
"    } \n"
"    return l_uv; \n"
"  } \n"
"  else \n"
"  { \n"
"    return f_uv; \n"
"  } \n"
"} \n"
" \n"
"#ifdef ENABLE_TNF \n"
"  uniform sampler2D s_filteredY; \n"
"#endif \n"
" \n"
"// for the SIL \n"
"#ifndef USE_SAMPLER_EXTERNAL_OES \n"
"  vec4 yuv_2_rgb_sil(vec4 src) \n"
"  { \n"
"      vec4 yuva = vec4((src.x-16.0/255.0), (src.y - 0.5), (src.z - 0.5), 1.0); \n"
" \n"
"      vec4 res; \n"
"      res.r = 1.164 * yuva.x                  + 1.596 * yuva.z; \n"
"      res.g = 1.164 * yuva.x - 0.392 * yuva.y - 0.813 * yuva.z; \n"
"      res.b = 1.164 * yuva.x + 2.017 * yuva.y; \n"
" \n"
"      res.a = src.a; \n"
" \n"
"      return res; \n"
"  } \n"
" \n"
" \n"
"  vec4 rgb_2_yuv_sil(vec4 src) \n"
"  { \n"
"      vec4 res; \n"
"      res.x =  0.257 * src.r + 0.504 * src.g + 0.098 * src.b + 16.0/255.0; \n"
"      res.y = -0.148 * src.r - 0.291 * src.g + 0.439 * src.b + 0.5; \n"
"      res.z =  0.439 * src.r - 0.368 * src.g - 0.071 * src.b + 0.5; \n"
" \n"
"      res.a = src.a; \n"
" \n"
"      return res; \n"
"  } \n"
"#endif \n"
" \n"
"#ifdef DEBUG_SHARPNESS_HARMONIZATION \n"
" \n"
"vec4 sixColors(float x) \n"
"{ \n"
"    float r, g, b; \n"
"    float d; \n"
" \n"
"    if (x < 0.0) \n"
"    { \n"
"        r = 0.0; \n"
"        g = 1.0; \n"
"        b = 1.0; \n"
"    } \n"
"    else if (x < 0.2) \n"
"    { \n"
"        d = ((x - 0.0) * 5.0); \n"
"        r = 0.0 * (1.0 - d) + 0.0 * d; \n"
"        g = 1.0; \n"
"        b = 1.0 * (1.0 - d) + 0.0 * d; \n"
"    } \n"
"    else if (x < 0.4) \n"
"    { \n"
"        d = ((x - 0.2) * 5.0); \n"
"        r = 0.0 * (1.0 - d) + 1.0 * d; \n"
"        g = 1.0; \n"
"        b = 0.0; \n"
"    } \n"
"    else if (x < 0.6) \n"
"    { \n"
"        d = ((x - 0.4) * 5.0); \n"
"        r = 1.0; \n"
"        g = 1.0 * (1.0 - d) + 0.0 * d; \n"
"        b = 0.0; \n"
"    } \n"
"    else if (x < 0.8) \n"
"    { \n"
"        d = ((x - 0.6) * 5.0); \n"
"        r = 1.0; \n"
"        g = 0.0; \n"
"        b = 0.0 * (1.0 - d) + 1.0 * d; \n"
"    } \n"
"    else if (x < 1.0) \n"
"    { \n"
"        d = ((x - 0.8) * 5.0); \n"
"        r = 1.0 * (1.0 - d) + 0.0 * d; \n"
"        g = 0.0; \n"
"        b = 1.0; \n"
"    } \n"
"    else \n"
"    { \n"
"        r = 0.0; \n"
"        g = 0.0; \n"
"        b = 1.0; \n"
"    } \n"
"    return vec4(r, g, b, 1.0); \n"
"} \n"
" \n"
"#endif // DEBUG_SHARPNESS_HARMONIZATION \n"
" \n"
"#ifdef ENABLE_SHARPNESS_HARMONIZATION \n"
"uniform vec2 u_sharpeningSmoothingFactors; \n"
"uniform float u_maxSharpening; \n"
" \n"
"vec4 getCameraTexture(vec2 f_uv, vec2 f_uv_tnf) \n"
"{ \n"
"    vec2 sizeOfTexture = vec2(textureSize(s_texCams, 0)); \n"
"    vec2 texelSize = 1.0 / sizeOfTexture; \n"
" \n"
"    // pixel density stuff for adaptive factors and right pixel selection \n"
"    highp vec2 deltaX = dFdx(f_uv); \n"
"    highp vec2 deltaY = dFdy(f_uv); \n"
" \n"
"    // Normalize the direction vectors \n"
"    vec2 dirU = normalize(deltaX); \n"
"    vec2 dirV = normalize(deltaY); \n"
" \n"
"    #ifndef USE_SAMPLER_EXTERNAL_OES \n"
"      vec4 center    = texture(s_texCams, f_uv); //RGBA \n"
"      vec3 centerYUV = rgb_2_yuv_sil(center).rgb; \n"
"    #else \n"
"      vec3 centerYUV = texture(s_texCams, f_uv).rgb; // texture stays in YUV due to __samplerExternal2DY2YEXT \n"
"    #endif \n"
" \n"
"    #ifdef ENABLE_TNF \n"
"      float centerY = texture(s_filteredY, f_uv_tnf).r; \n"
"      // Fetch neighboring pixels using the direction vectors scaled by texel size \n"
"      float leftY_U  = texture(s_filteredY, f_uv_tnf - dirU * texelSize.x).r; \n"
"      float rightY_U = texture(s_filteredY, f_uv_tnf + dirU * texelSize.x).r; \n"
"      float upY_V    = texture(s_filteredY, f_uv_tnf + dirV * texelSize.y).r; \n"
"      float downY_V  = texture(s_filteredY, f_uv_tnf - dirV * texelSize.y).r; \n"
"    #else \n"
"      float centerY = centerYUV.r; \n"
"      #ifndef USE_SAMPLER_EXTERNAL_OES \n"
"        vec4 left_U    = texture(s_texCams, f_uv - dirU * texelSize.x); \n"
"        vec4 right_U   = texture(s_texCams, f_uv + dirU * texelSize.x); \n"
"        vec4 up_V      = texture(s_texCams, f_uv + dirV * texelSize.y); \n"
"        vec4 down_V    = texture(s_texCams, f_uv - dirV * texelSize.y); \n"
" \n"
"        float leftY_U  = rgb_2_yuv_sil(left_U).r; \n"
"        float rightY_U = rgb_2_yuv_sil(right_U).r; \n"
"        float upY_V    = rgb_2_yuv_sil(up_V).r; \n"
"        float downY_V  = rgb_2_yuv_sil(down_V).r; \n"
"      #else \n"
"        float leftY_U   = texture(s_texCams, f_uv - dirU * texelSize.x).r; \n"
"        float rightY_U  = texture(s_texCams, f_uv + dirU * texelSize.x).r; \n"
"        float upY_V     = texture(s_texCams, f_uv + dirV * texelSize.y).r; \n"
"        float downY_V   = texture(s_texCams, f_uv - dirV * texelSize.y).r; \n"
"      #endif \n"
"    #endif \n"
" \n"
"    // gaussian blur filter, 1/4, 1/2, 1/4 \n"
"    float blurX = (leftY_U + 2.0 * centerY + rightY_U ) / 4.0; \n"
"    float blurY = (upY_V + 2.0 * centerY + downY_V ) / 4.0; \n"
" \n"
"    float highPassX = centerY - blurX; \n"
"    float highPassY = centerY - blurY; \n"
" \n"
"    // input pixels per output fragment \n"
"    float ratioX = length(deltaX)*sizeOfTexture.x; \n"
"    float ratioY = length(deltaY)*sizeOfTexture.y; \n"
" \n"
"    int needsSmoothingX = int(step(1.0, ratioX)); \n"
"    int needsSmoothingY = int(step(1.0, ratioY)); \n"
" \n"
"    float factorX = 0.0; \n"
"    float factorY = 0.0; \n"
" \n"
"    // use linear interpolation (from mix function) to find a sharpness/smoothing setting corresponding to the calculated pixel density \n"
"    // according to formula sharpening_or_smoothing_factor * (1 - ratio) \n"
"    // The clamping of the factors to -1.0 limits the applied degree of smoothing \n"
"    factorX = clamp(mix(u_sharpeningSmoothingFactors[needsSmoothingX], 0.0, ratioX), -1.0, u_maxSharpening); \n"
"    factorY = clamp(mix(u_sharpeningSmoothingFactors[needsSmoothingY], 0.0, ratioY), -1.0, u_maxSharpening); \n"
"#ifdef DEBUG_SHARPNESS_HARMONIZATION \n"
"    // The pixel density visualization overlaid by the original image is selected when sharpening and smoothing factors are zero \n"
"    bvec2 isZero = equal(u_sharpeningSmoothingFactors, vec2(0.0, 0.0)); \n"
"    if (isZero.x && isZero.y) \n"
"    { \n"
"        // input pixels per output fragment \n"
"        float ratio = ratioX * ratioY; \n"
"        // display texture value together with color \n"
"        #ifndef USE_SAMPLER_EXTERNAL_OES \n"
"          return sixColors(ratio) * yuv_2_rgb_sil(vec4(centerYUV, 1.0)); \n"
"        #else \n"
"          return sixColors(ratio) * vec4(yuv_2_rgb(centerYUV, itu_601_full_range), 1.0); \n"
"        #endif \n"
"    } \n"
"    else // NORMALIZED \n"
"    { \n"
"        // normalized outputs of pixel densities in x- and y-direction on red and green channels \n"
"        // e.g. a red value of 140 corresponds to pixel density in X of 1.4, a green value of 36 corresponds to a pixel \n"
"        // density in Y of 0.36 \n"
"        return vec4(ratioX * 100.0 / 255.0, ratioY * 100.0 / 255.0, 0.0, 1.0); \n"
"        // return vec4(needsSmoothingX * 255, needsSmoothingY * 255, 0.0, 1.0); \n"
"    } \n"
"#else \n"
"        float yFiltered = centerY + factorX * highPassX + factorY * highPassY; \n"
"        vec3 yuvFinal = vec3(yFiltered, centerYUV.g, centerYUV.b); \n"
"        #ifndef USE_SAMPLER_EXTERNAL_OES \n"
"          return yuv_2_rgb_sil(vec4(yuvFinal, 1.0)); \n"
"        #else \n"
"          return vec4(yuv_2_rgb(yuvFinal, itu_601_full_range), 1.0); \n"
"        #endif \n"
"#endif // DEBUG_SHARPNESS_HARMONIZATION \n"
"} \n"
" \n"
"#else \n"
" \n"
"vec4 getCameraTexture(vec2 f_uv, vec2 f_uv_tnf) \n"
"{ \n"
"    #ifdef ENABLE_TNF \n"
"      float filteredY = texture(s_filteredY, f_uv_tnf).r; \n"
"      #ifndef USE_SAMPLER_EXTERNAL_OES \n"
"        vec2 UV = rgb_2_yuv_sil(texture(s_texCams, f_uv)).gb; \n"
"        return yuv_2_rgb_sil(vec4(filteredY, UV.x, UV.y, 1.0)); \n"
"      #else \n"
"        vec2 UV = texture(s_texCams, f_uv).gb; \n"
"        return vec4(yuv_2_rgb(vec3(filteredY, UV.x, UV.y), itu_601_full_range), 1.0); \n"
"      #endif \n"
"    #else \n"
"      return texture(s_texCams, f_uv); \n"
"    #endif \n"
"} \n"
"#endif // ENABLE_SHARPNESS_HARMONIZATION \n"
" \n"
"vec4 getCameraTextureBlended(vec2 f_uv) \n"
"{ \n"
"    // Blending case: \n"
"    // Get source color \n"
"#if (SOURCE == CAM_DISABLED) \n"
"    vec4 sourceColor = getCameraTexture(getUv(f_uv), f_uv) * u_camDisabledColor; \n"
"#elif (SOURCE == CAM_OFF) \n"
"    vec4 sourceColor = u_camOffColor; \n"
"#else \n"
"    vec4 sourceColor = getCameraTexture(getUv(f_uv), f_uv); \n"
"#ifdef ENABLE_CHAMAELEON \n"
"    vec3 sourceColorLeft = v_cham_correctionLeft *  sourceColor.rgb; \n"
"    vec3 sourceColorRight = v_cham_correctionRight * sourceColor.rgb; \n"
"    sourceColor = vec4(mix(sourceColorLeft, sourceColorRight, v_cham_weightLeftRight), 1.); \n"
"#endif // ENABLE_CHAMAELEON \n"
"#endif \n"
" \n"
"#if (SOURCE == TARGET) \n"
"    return sourceColor; \n"
"#else \n"
"    // Get target color \n"
"#if (TARGET == CAM_DISABLED) \n"
"    vec4 targetColor = getCameraTexture(getUv(f_uv), f_uv) * u_camDisabledColor; \n"
"#elif (TARGET == CAM_OFF) \n"
"    vec4 targetColor = u_camOffColor; \n"
"#else \n"
"    vec4 targetColor = getCameraTexture(getUv(f_uv), f_uv); \n"
"#ifdef ENABLE_CHAMAELEON \n"
"    vec3 targetColorLeft = v_cham_correctionLeft *  targetColor.rgb; \n"
"    vec3 targetColorRight = v_cham_correctionRight * targetColor.rgb; \n"
"    targetColor = vec4(mix(targetColorLeft, targetColorRight, v_cham_weightLeftRight), 1.); \n"
"#endif // ENABLE_CHAMAELEON \n"
"#endif \n"
"    return mix(sourceColor, targetColor, u_FadeFactor); \n"
"#endif \n"
"} \n"
" \n"
"out vec4 FragColor; \n"
" \n"
"void main() \n"
"{ \n"
"    vec4 l_color = getCameraTextureBlended(v_texCam); \n"
" \n"
"    FragColor = vec4(clamp((l_color).rgb * u_brightFactor, 0.0, 1.0), 1.0); \n"
"} \n"
;

const static std::string s_SingleCam_vert =
"#version 300 es \n"
" \n"
" \n"
"#define CAM_ON 0 \n"
"#define CAM_DISABLED 1 \n"
"#define CAM_OFF 2 \n"
" \n"
"#if (SOURCE == 0) || (TARGET == 0) \n"
"// hatching is currently not implemented (just constant gray), so no need for hatch setup \n"
"// #define ENABLE_HATCH \n"
"#endif \n"
" \n"
" \n"
"out vec2 v_texCam; \n"
" \n"
" \n"
"#ifdef ENABLE_HATCH \n"
"out float v_incrementXY; \n"
"#ifdef IS_WALL \n"
"out float v_incrementWall; \n"
"#endif \n"
"#endif \n"
" \n"
"in vec3 osg_Vertex; \n"
"in vec2 osg_MultiTexCoord0; \n"
"uniform highp mat4 osg_ModelViewProjectionMatrix; \n"
" \n"
"#ifdef ENABLE_CHAMAELEON \n"
" \n"
"uniform vec2 u_cham_direction_left; \n"
"uniform vec2 u_cham_direction_right; \n"
" \n"
"uniform mediump vec3 u_cham_origin_left; \n"
"uniform mediump vec3 u_cham_origin_right; \n"
" \n"
"out float v_cham_weightLeftRight; \n"
" \n"
"out vec3 v_cham_correctionLeft; \n"
"out vec3 v_cham_correctionRight; \n"
" \n"
"uniform sampler2D s_chamGainsAsTexture; \n"
"uniform ivec2 u_cham_worldRoiIdx; \n"
" \n"
"float getDistToLine(vec2 pt1, vec2 pt2, vec2 testPt) \n"
"{ \n"
"  vec2 lineDir = pt2 - pt1; \n"
"  vec2 perpDir = vec2(lineDir.y, -lineDir.x); \n"
"  vec2 dirToPt1 = pt1 - testPt; \n"
"  return abs(dot(normalize(perpDir), dirToPt1)); \n"
"} \n"
" \n"
"float crossVec2(vec2 v, vec2 w) \n"
"{ \n"
"  return (v.x * w.y) - (v.y * w.x); \n"
"} \n"
" \n"
"float getColorCorrectionWeight(vec2 f_root0, vec2 f_line0, \n"
"                               vec2 f_root1, vec2 f_line1, \n"
"                               vec2 f_vertTemp) \n"
"{ \n"
"  float u0 = crossVec2((f_vertTemp - f_root0), f_line0) / (crossVec2(f_line0, f_root1 - f_root0)); \n"
"  float u1 = crossVec2((f_vertTemp - f_root1), f_line1) / (crossVec2(f_line1, f_root1 - f_root0)); \n"
" \n"
"  vec2 intersectionLine0 = f_vertTemp + u0 * (f_root1 - f_root0); \n"
"  vec2 intersectionLine1 = f_vertTemp + u1 * (f_root1 - f_root0); \n"
" \n"
"  float distance0 = distance(intersectionLine0, f_vertTemp); \n"
"  float distance1 = distance(intersectionLine1, f_vertTemp); \n"
"  float distanceAll = distance(intersectionLine0, intersectionLine1); \n"
" \n"
"  float diff = distance1 - min(distanceAll, distance1); \n"
" \n"
"  return clamp(((distance0 - diff) / distanceAll), 0.0, 1.0); \n"
"} \n"
"#endif // ENABLE_CHAMAELEON \n"
" \n"
"#ifdef ENABLE_SHARPNESS_HARMONIZATION \n"
"// currently no special content except OpenGL ES 3 conformant shader \n"
"#endif // ENABLE_SHARPNESS_HARMONIZATION \n"
" \n"
"void main() \n"
"{ \n"
"  v_texCam = osg_MultiTexCoord0; \n"
" \n"
"#ifdef ENABLE_HATCH \n"
"  v_incrementXY = osg_Vertex.y - osg_Vertex.x; \n"
" \n"
"#ifdef IS_WALL \n"
"  vec2 l_V = normalize (osg_Vertex.xy); \n"
"  vec2 l_XDirection = vec2 (1, 0); \n"
"  vec2 l_XDirectionNormalized = normalize (l_XDirection); \n"
"  float l_CosineAngle = dot (l_V, l_XDirectionNormalized); \n"
"  float l_Angle = acos (l_CosineAngle); \n"
"  v_incrementWall = float (l_Angle) + osg_Vertex.z; \n"
"#endif \n"
"#endif \n"
" \n"
"#ifdef ENABLE_CHAMAELEON \n"
"  v_cham_weightLeftRight = getColorCorrectionWeight(u_cham_origin_left.xy, u_cham_direction_left, u_cham_origin_right.xy, u_cham_direction_right, osg_Vertex.xy); \n"
" \n"
"  v_cham_correctionLeft = texelFetch(s_chamGainsAsTexture, ivec2(u_cham_worldRoiIdx[0],0), 0).rgb + vec3(0.5); \n"
"  v_cham_correctionRight = texelFetch(s_chamGainsAsTexture, ivec2(u_cham_worldRoiIdx[1],0), 0).rgb + vec3(0.5); \n"
"#endif // ENABLE_CHAMAELEON \n"
" \n"
"  gl_Position = osg_ModelViewProjectionMatrix * vec4(osg_Vertex, 1.0); \n"
"} \n"
;

const static std::string s_TwoCam_frag =
"#version 300 es \n"
" \n"
"#ifdef USE_SAMPLER_EXTERNAL_OES \n"
"  #extension GL_OES_EGL_image_external_essl3 : require \n"
"  #if defined ENABLE_SHARPNESS_HARMONIZATION || defined ENABLE_TNF \n"
"    #extension GL_EXT_YUV_target : require \n"
"  #endif \n"
"#endif \n"
" \n"
"precision highp float; \n"
" \n"
"in highp vec2 v_texLeftCam; \n"
"in highp vec2 v_texRightCam; \n"
" \n"
"#if !defined(LEFT_SOURCE) || !defined(RIGHT_SOURCE) || !defined(LEFT_TARGET) || !defined(RIGHT_TARGET) \n"
"#error Shader not configured correctly \n"
"#endif \n"
" \n"
"#define CAM_ON 0 \n"
"#define CAM_DISABLED 1 \n"
"#define CAM_OFF 2 \n"
" \n"
"in vec2 v_alphaCam; \n"
" \n"
"#ifdef ENABLE_CHAMAELEON \n"
"in vec3 v_cham_correctionMidLeft; \n"
"in vec3 v_cham_correctionMidRight; \n"
"in vec3 v_cham_correctionLeft; \n"
"in vec3 v_cham_correctionRight; \n"
" \n"
"in float v_cham_weightRightMid; \n"
"in float v_cham_weightMidLeft; \n"
"#endif // ENABLE_CHAMAELEON \n"
" \n"
"#ifdef USE_SAMPLER_EXTERNAL_OES \n"
"  #if defined ENABLE_SHARPNESS_HARMONIZATION || defined ENABLE_TNF \n"
"    uniform __samplerExternal2DY2YEXT s_texCamsLeft; \n"
"    uniform __samplerExternal2DY2YEXT s_texCamsRight; \n"
"  #else \n"
"    uniform samplerExternalOES s_texCamsLeft; \n"
"    uniform samplerExternalOES s_texCamsRight; \n"
"  #endif \n"
"#else \n"
"  uniform sampler2D s_texCamsLeft; \n"
"  uniform sampler2D s_texCamsRight; \n"
"#endif \n"
" \n"
"#ifdef ENABLE_TNF \n"
"  uniform sampler2D s_filteredYLeft; \n"
"  uniform sampler2D s_filteredYRight; \n"
"#endif \n"
" \n"
"#if (LEFT_SOURCE != LEFT_TARGET) || (RIGHT_SOURCE != RIGHT_TARGET) \n"
"uniform float u_FadeFactor; \n"
"#endif \n"
" \n"
"uniform vec4 u_camDisabledColor; \n"
"uniform vec4 u_camOffColor; \n"
" \n"
"uniform int u_camLeft; \n"
"uniform int u_camRight; \n"
"uniform int u_isTexCombine; \n"
"uniform float u_brightFactor; \n"
" \n"
"vec2 getUv(vec2 f_uv, int f_cam) \n"
"{ \n"
"  if (1 == u_isTexCombine) \n"
"  { \n"
"    vec2 l_uv = vec2(0.0, f_uv.y); \n"
"    if (0 == f_cam)  // front \n"
"    { \n"
"      l_uv.x = 0.75 + f_uv.x * 0.25; \n"
"    } \n"
"    else if (1 == f_cam)  // right \n"
"    { \n"
"      l_uv.x = 0.5 + f_uv.x * 0.25; \n"
"    } \n"
"    else if (2 == f_cam)  // rear \n"
"    { \n"
"      l_uv.x = f_uv.x * 0.25; \n"
"    } \n"
"    else  // left \n"
"    { \n"
"      l_uv.x = 0.25 + f_uv.x * 0.25; \n"
"    } \n"
"    return l_uv; \n"
"  } \n"
"  else \n"
"  { \n"
"    return f_uv; \n"
"  } \n"
"} \n"
" \n"
"// for the SIL \n"
"#ifndef USE_SAMPLER_EXTERNAL_OES \n"
"  vec4 yuv_2_rgb_sil(vec4 src) \n"
"  { \n"
"      vec4 yuva = vec4((src.x-16.0/255.0), (src.y - 0.5), (src.z - 0.5), 1.0); \n"
" \n"
"      vec4 res; \n"
"      res.r = 1.164 * yuva.x                  + 1.596 * yuva.z; \n"
"      res.g = 1.164 * yuva.x - 0.392 * yuva.y - 0.813 * yuva.z; \n"
"      res.b = 1.164 * yuva.x + 2.017 * yuva.y; \n"
" \n"
"      res.a = src.a; \n"
" \n"
"      return res; \n"
"  } \n"
" \n"
" \n"
"  vec4 rgb_2_yuv_sil(vec4 src) \n"
"  { \n"
"      vec4 res; \n"
"      res.x =  0.257 * src.r + 0.504 * src.g + 0.098 * src.b + 16.0/255.0; \n"
"      res.y = -0.148 * src.r - 0.291 * src.g + 0.439 * src.b + 0.5; \n"
"      res.z =  0.439 * src.r - 0.368 * src.g - 0.071 * src.b + 0.5; \n"
" \n"
"      res.a = src.a; \n"
" \n"
"      return res; \n"
"  } \n"
"#endif \n"
" \n"
"#ifdef DEBUG_SHARPNESS_HARMONIZATION \n"
" \n"
"vec4 sixColors(float x) \n"
"{ \n"
"    float r, g, b; \n"
"    float d; \n"
" \n"
"    if (x < 0.0) \n"
"    { \n"
"        r = 0.0; \n"
"        g = 1.0; \n"
"        b = 1.0; \n"
"    } \n"
"    else if (x < 0.2) \n"
"    { \n"
"        d = ((x - 0.0) * 5.0); \n"
"        r = 0.0 * (1.0 - d) + 0.0 * d; \n"
"        g = 1.0; \n"
"        b = 1.0 * (1.0 - d) + 0.0 * d; \n"
"    } \n"
"    else if (x < 0.4) \n"
"    { \n"
"        d = ((x - 0.2) * 5.0); \n"
"        r = 0.0 * (1.0 - d) + 1.0 * d; \n"
"        g = 1.0; \n"
"        b = 0.0; \n"
"    } \n"
"    else if (x < 0.6) \n"
"    { \n"
"        d = ((x - 0.4) * 5.0); \n"
"        r = 1.0; \n"
"        g = 1.0 * (1.0 - d) + 0.0 * d; \n"
"        b = 0.0; \n"
"    } \n"
"    else if (x < 0.8) \n"
"    { \n"
"        d = ((x - 0.6) * 5.0); \n"
"        r = 1.0; \n"
"        g = 0.0; \n"
"        b = 0.0 * (1.0 - d) + 1.0 * d; \n"
"    } \n"
"    else if (x < 1.0) \n"
"    { \n"
"        d = ((x - 0.8) * 5.0); \n"
"        r = 1.0 * (1.0 - d) + 0.0 * d; \n"
"        g = 0.0; \n"
"        b = 1.0; \n"
"    } \n"
"    else \n"
"    { \n"
"        r = 0.0; \n"
"        g = 0.0; \n"
"        b = 1.0; \n"
"    } \n"
"    return vec4(r, g, b, 1.0); \n"
"} \n"
" \n"
"#endif // DEBUG_SHARPNESS_HARMONIZATION \n"
" \n"
"#ifdef ENABLE_SHARPNESS_HARMONIZATION \n"
"uniform vec2 u_sharpeningSmoothingFactors; \n"
"uniform float u_maxSharpening; \n"
" \n"
"vec4 getCameraTextureLeft(vec2 f_uv, vec2 f_uv_tnf) \n"
"{ \n"
"    vec2 sizeOfTexture = vec2(textureSize(s_texCamsLeft, 0)); \n"
"    vec2 texelSize = 1.0 / sizeOfTexture; \n"
" \n"
"    // pixel density stuff for adaptive factors and right pixel selection \n"
"    highp vec2 deltaX = dFdx(f_uv); \n"
"    highp vec2 deltaY = dFdy(f_uv); \n"
" \n"
"    // Normalize the direction vectors \n"
"    vec2 dirU = normalize(deltaX); \n"
"    vec2 dirV = normalize(deltaY); \n"
" \n"
"    #ifndef USE_SAMPLER_EXTERNAL_OES \n"
"      vec4 center    = texture(s_texCamsLeft, f_uv); //RGBA \n"
"      vec3 centerYUV = rgb_2_yuv_sil(center).rgb; \n"
"    #else \n"
"      vec3 centerYUV = texture(s_texCamsLeft, f_uv).rgb; // texture stays in YUV due to __samplerExternal2DY2YEXT \n"
"    #endif \n"
" \n"
"    #ifdef ENABLE_TNF \n"
"      float centerY = texture(s_filteredYLeft, f_uv_tnf).r; \n"
"      // Fetch neighboring pixels using the direction vectors scaled by texel size \n"
"      float leftY_U  = texture(s_filteredYLeft, f_uv_tnf - dirU * texelSize.x).r; \n"
"      float rightY_U = texture(s_filteredYLeft, f_uv_tnf + dirU * texelSize.x).r; \n"
"      float upY_V    = texture(s_filteredYLeft, f_uv_tnf + dirV * texelSize.y).r; \n"
"      float downY_V  = texture(s_filteredYLeft, f_uv_tnf - dirV * texelSize.y).r; \n"
"    #else \n"
"      float centerY = centerYUV.r; \n"
"      #ifndef USE_SAMPLER_EXTERNAL_OES \n"
"        vec4 left_U    = texture(s_texCamsLeft, f_uv - dirU * texelSize.x); \n"
"        vec4 right_U   = texture(s_texCamsLeft, f_uv + dirU * texelSize.x); \n"
"        vec4 up_V      = texture(s_texCamsLeft, f_uv + dirV * texelSize.y); \n"
"        vec4 down_V    = texture(s_texCamsLeft, f_uv - dirV * texelSize.y); \n"
" \n"
"        float leftY_U  = rgb_2_yuv_sil(left_U).r; \n"
"        float rightY_U = rgb_2_yuv_sil(right_U).r; \n"
"        float upY_V    = rgb_2_yuv_sil(up_V).r; \n"
"        float downY_V  = rgb_2_yuv_sil(down_V).r; \n"
"      #else \n"
"        float leftY_U   = texture(s_texCamsLeft, f_uv - dirU * texelSize.x).r; \n"
"        float rightY_U  = texture(s_texCamsLeft, f_uv + dirU * texelSize.x).r; \n"
"        float upY_V     = texture(s_texCamsLeft, f_uv + dirV * texelSize.y).r; \n"
"        float downY_V   = texture(s_texCamsLeft, f_uv - dirV * texelSize.y).r; \n"
"      #endif \n"
"    #endif \n"
" \n"
"    // gaussian blur filter, 1/4, 1/2, 1/4 \n"
"    float blurX = (leftY_U + 2.0 * centerY + rightY_U ) / 4.0; \n"
"    float blurY = (upY_V + 2.0 * centerY + downY_V ) / 4.0; \n"
" \n"
"    float highPassX = centerY - blurX; \n"
"    float highPassY = centerY - blurY; \n"
" \n"
"    // input pixels per output fragment \n"
"    float ratioX = length(deltaX)*sizeOfTexture.x; \n"
"    float ratioY = length(deltaY)*sizeOfTexture.y; \n"
" \n"
"    int needsSmoothingX = int(step(1.0, ratioX)); \n"
"    int needsSmoothingY = int(step(1.0, ratioY)); \n"
" \n"
"    float factorX = 0.0; \n"
"    float factorY = 0.0; \n"
" \n"
"    // use linear interpolation (from mix function) to find a sharpness/smoothing setting corresponding to the calculated pixel density \n"
"    // according to formula sharpening_or_smoothing_factor * (1 - ratio) \n"
"    // The clamping of the factors to -1.0 limits the applied degree of smoothing \n"
"    factorX = clamp(mix(u_sharpeningSmoothingFactors[needsSmoothingX], 0.0, ratioX), -1.0, u_maxSharpening); \n"
"    factorY = clamp(mix(u_sharpeningSmoothingFactors[needsSmoothingY], 0.0, ratioY), -1.0, u_maxSharpening); \n"
"#ifdef DEBUG_SHARPNESS_HARMONIZATION \n"
"    // The pixel density visualization overlaid by the original image is selected when sharpening and smoothing factors are zero \n"
"    bvec2 isZero = equal(u_sharpeningSmoothingFactors, vec2(0.0, 0.0)); \n"
"    if (isZero.x && isZero.y) \n"
"    { \n"
"        // input pixels per output fragment \n"
"        float ratio = ratioX * ratioY; \n"
"        // display texture value together with color \n"
"        #ifndef USE_SAMPLER_EXTERNAL_OES \n"
"          return sixColors(ratio) * yuv_2_rgb_sil(vec4(centerYUV, 1.0)); \n"
"        #else \n"
"          return sixColors(ratio) * vec4(yuv_2_rgb(centerYUV, itu_601_full_range), 1.0); \n"
"        #endif \n"
"    } \n"
"    else // NORMALIZED \n"
"    { \n"
"        // normalized outputs of pixel densities in x- and y-direction on red and green channels \n"
"        // e.g. a red value of 140 corresponds to pixel density in X of 1.4, a green value of 36 corresponds to a pixel \n"
"        // density in Y of 0.36 \n"
"        return vec4(ratioX * 100.0 / 255.0, ratioY * 100.0 / 255.0, 0.0, 1.0); \n"
"        // return vec4(needsSmoothingX * 255, needsSmoothingY * 255, 0.0, 1.0); \n"
"    } \n"
"#else \n"
"        float yFiltered = centerY + factorX * highPassX + factorY * highPassY; \n"
"        vec3 yuvFinal = vec3(yFiltered, centerYUV.g, centerYUV.b); \n"
"        #ifndef USE_SAMPLER_EXTERNAL_OES \n"
"          return yuv_2_rgb_sil(vec4(yuvFinal, 1.0)); \n"
"        #else \n"
"          return vec4(yuv_2_rgb(yuvFinal, itu_601_full_range), 1.0); \n"
"        #endif \n"
"#endif // DEBUG_SHARPNESS_HARMONIZATION \n"
"} \n"
" \n"
"vec4 getCameraTextureRight(vec2 f_uv, vec2 f_uv_tnf) \n"
"{ \n"
"    vec2 sizeOfTexture = vec2(textureSize(s_texCamsRight, 0)); \n"
"    vec2 texelSize = 1.0 / sizeOfTexture; \n"
" \n"
"    // pixel density stuff for adaptive factors and right pixel selection \n"
"    highp vec2 deltaX = dFdx(f_uv); \n"
"    highp vec2 deltaY = dFdy(f_uv); \n"
" \n"
"    // Normalize the direction vectors \n"
"    vec2 dirU = normalize(deltaX); \n"
"    vec2 dirV = normalize(deltaY); \n"
" \n"
"    #ifndef USE_SAMPLER_EXTERNAL_OES \n"
"      vec4 center    = texture(s_texCamsRight, f_uv); //RGBA \n"
"      vec3 centerYUV = rgb_2_yuv_sil(center).rgb; \n"
"    #else \n"
"      vec3 centerYUV = texture(s_texCamsRight, f_uv).rgb; // texture stays in YUV due to __samplerExternal2DY2YEXT \n"
"    #endif \n"
" \n"
"    #ifdef ENABLE_TNF \n"
"      float centerY = texture(s_filteredYRight, f_uv_tnf).r; \n"
"      // Fetch neighboring pixels using the direction vectors scaled by texel size \n"
"      float leftY_U  = texture(s_filteredYRight, f_uv_tnf - dirU * texelSize.x).r; \n"
"      float rightY_U = texture(s_filteredYRight, f_uv_tnf + dirU * texelSize.x).r; \n"
"      float upY_V    = texture(s_filteredYRight, f_uv_tnf + dirV * texelSize.y).r; \n"
"      float downY_V  = texture(s_filteredYRight, f_uv_tnf - dirV * texelSize.y).r; \n"
"    #else \n"
"      float centerY = centerYUV.r; \n"
"      #ifndef USE_SAMPLER_EXTERNAL_OES \n"
"        vec4 left_U    = texture(s_texCamsRight, f_uv - dirU * texelSize.x); \n"
"        vec4 right_U   = texture(s_texCamsRight, f_uv + dirU * texelSize.x); \n"
"        vec4 up_V      = texture(s_texCamsRight, f_uv + dirV * texelSize.y); \n"
"        vec4 down_V    = texture(s_texCamsRight, f_uv - dirV * texelSize.y); \n"
" \n"
"        float leftY_U  = rgb_2_yuv_sil(left_U).r; \n"
"        float rightY_U = rgb_2_yuv_sil(right_U).r; \n"
"        float upY_V    = rgb_2_yuv_sil(up_V).r; \n"
"        float downY_V  = rgb_2_yuv_sil(down_V).r; \n"
"      #else \n"
"        float leftY_U   = texture(s_texCamsRight, f_uv - dirU * texelSize.x).r; \n"
"        float rightY_U  = texture(s_texCamsRight, f_uv + dirU * texelSize.x).r; \n"
"        float upY_V     = texture(s_texCamsRight, f_uv + dirV * texelSize.y).r; \n"
"        float downY_V   = texture(s_texCamsRight, f_uv - dirV * texelSize.y).r; \n"
"      #endif \n"
"    #endif \n"
" \n"
"    // gaussian blur filter, 1/4, 1/2, 1/4 \n"
"    float blurX = (leftY_U + 2.0 * centerY + rightY_U ) / 4.0; \n"
"    float blurY = (upY_V + 2.0 * centerY + downY_V ) / 4.0; \n"
" \n"
"    float highPassX = centerY - blurX; \n"
"    float highPassY = centerY - blurY; \n"
" \n"
"    // input pixels per output fragment \n"
"    float ratioX = length(deltaX)*sizeOfTexture.x; \n"
"    float ratioY = length(deltaY)*sizeOfTexture.y; \n"
" \n"
"    int needsSmoothingX = int(step(1.0, ratioX)); \n"
"    int needsSmoothingY = int(step(1.0, ratioY)); \n"
" \n"
"    float factorX = 0.0; \n"
"    float factorY = 0.0; \n"
" \n"
"    // use linear interpolation (from mix function) to find a sharpness/smoothing setting corresponding to the calculated pixel density \n"
"    // according to formula sharpening_or_smoothing_factor * (1 - ratio) \n"
"    // The clamping of the factors to -1.0 limits the applied degree of smoothing \n"
"    factorX = clamp(mix(u_sharpeningSmoothingFactors[needsSmoothingX], 0.0, ratioX), -1.0, u_maxSharpening); \n"
"    factorY = clamp(mix(u_sharpeningSmoothingFactors[needsSmoothingY], 0.0, ratioY), -1.0, u_maxSharpening); \n"
"#ifdef DEBUG_SHARPNESS_HARMONIZATION \n"
"    // The pixel density visualization overlaid by the original image is selected when sharpening and smoothing factors are zero \n"
"    bvec2 isZero = equal(u_sharpeningSmoothingFactors, vec2(0.0, 0.0)); \n"
"    if (isZero.x && isZero.y) \n"
"    { \n"
"        // input pixels per output fragment \n"
"        float ratio = ratioX * ratioY; \n"
"        // display texture value together with color \n"
"        #ifndef USE_SAMPLER_EXTERNAL_OES \n"
"          return sixColors(ratio) * yuv_2_rgb_sil(vec4(centerYUV, 1.0)); \n"
"        #else \n"
"          return sixColors(ratio) * vec4(yuv_2_rgb(centerYUV, itu_601_full_range), 1.0); \n"
"        #endif \n"
"    } \n"
"    else // NORMALIZED \n"
"    { \n"
"        // normalized outputs of pixel densities in x- and y-direction on red and green channels \n"
"        // e.g. a red value of 140 corresponds to pixel density in X of 1.4, a green value of 36 corresponds to a pixel \n"
"        // density in Y of 0.36 \n"
"        return vec4(ratioX * 100.0 / 255.0, ratioY * 100.0 / 255.0, 0.0, 1.0); \n"
"        // return vec4(needsSmoothingX * 255, needsSmoothingY * 255, 0.0, 1.0); \n"
"    } \n"
"#else \n"
"        float yFiltered = centerY + factorX * highPassX + factorY * highPassY; \n"
"        vec3 yuvFinal = vec3(yFiltered, centerYUV.g, centerYUV.b); \n"
"        #ifndef USE_SAMPLER_EXTERNAL_OES \n"
"          return yuv_2_rgb_sil(vec4(yuvFinal, 1.0)); \n"
"        #else \n"
"          return vec4(yuv_2_rgb(yuvFinal, itu_601_full_range), 1.0); \n"
"        #endif \n"
"#endif // DEBUG_SHARPNESS_HARMONIZATION \n"
"} \n"
" \n"
"#else \n"
" \n"
"vec4 getCameraTextureLeft(vec2 f_uv, vec2 f_uv_tnf) \n"
"{ \n"
"    #ifdef ENABLE_TNF \n"
"      float filteredY = texture(s_filteredYLeft, f_uv_tnf).r; \n"
"      #ifndef USE_SAMPLER_EXTERNAL_OES \n"
"        vec2 UV = rgb_2_yuv_sil(texture(s_texCamsLeft, f_uv)).gb; \n"
"        return yuv_2_rgb_sil(vec4(filteredY, UV.x, UV.y, 1.0)); \n"
"      #else \n"
"        vec2 UV = texture(s_texCamsLeft, f_uv).gb; \n"
"        return vec4(yuv_2_rgb(vec3(filteredY, UV.x, UV.y), itu_601_full_range), 1.0); \n"
"      #endif \n"
"    #else \n"
"      return texture(s_texCamsLeft, f_uv); \n"
"    #endif \n"
"} \n"
" \n"
"vec4 getCameraTextureRight(vec2 f_uv, vec2 f_uv_tnf) \n"
"{ \n"
"    #ifdef ENABLE_TNF \n"
"      float filteredY = texture(s_filteredYRight, f_uv_tnf).r; \n"
"      #ifndef USE_SAMPLER_EXTERNAL_OES \n"
"        vec2 UV = rgb_2_yuv_sil(texture(s_texCamsRight, f_uv)).gb; \n"
"        return yuv_2_rgb_sil(vec4(filteredY, UV.x, UV.y, 1.0)); \n"
"      #else \n"
"        vec2 UV = texture(s_texCamsRight, f_uv).gb; \n"
"        return vec4(yuv_2_rgb(vec3(filteredY, UV.x, UV.y), itu_601_full_range), 1.0); \n"
"      #endif \n"
"    #else \n"
"      return texture(s_texCamsRight, f_uv); \n"
"    #endif \n"
"} \n"
"#endif // ENABLE_SHARPNESS_HARMONIZATION \n"
" \n"
"vec4 getLeftCameraBlended(vec2 f_uv) \n"
"{ \n"
"    // Blending case: \n"
"    // Get source color \n"
"#if (LEFT_SOURCE == CAM_DISABLED) \n"
"    vec4 sourceColor = getCameraTextureLeft(getUv(f_uv, u_camLeft), f_uv) * u_camDisabledColor; \n"
"#elif (LEFT_SOURCE == CAM_OFF) \n"
"    vec4 sourceColor = u_camOffColor; \n"
"#else \n"
"    vec4 sourceColor = getCameraTextureLeft(getUv(f_uv, u_camLeft), f_uv); \n"
"#ifdef ENABLE_CHAMAELEON \n"
"    vec3 sourceColorMidLeft = v_cham_correctionMidLeft * sourceColor.rgb; \n"
"    vec3 sourceColorLeft = v_cham_correctionLeft * sourceColor.rgb; \n"
"    sourceColor = vec4(mix(sourceColorLeft, sourceColorMidLeft, v_cham_weightMidLeft), 1.); \n"
"#endif // ENABLE_CHAMAELEON \n"
"#endif \n"
" \n"
"#if (LEFT_SOURCE == LEFT_TARGET) \n"
"    return sourceColor; \n"
"#else \n"
"    // Get target color \n"
"#if (LEFT_TARGET == CAM_DISABLED) \n"
"    vec4 targetColor = getCameraTextureLeft(getUv(f_uv, u_camLeft), f_uv) * u_camDisabledColor; \n"
"#elif (LEFT_TARGET == CAM_OFF) \n"
"    vec4 targetColor = u_camOffColor; \n"
"#else \n"
"    vec4 targetColor = getCameraTextureLeft(getUv(f_uv, u_camLeft), f_uv); \n"
"#ifdef ENABLE_CHAMAELEON \n"
"    vec3 targetColorMidLeft = v_cham_correctionMidLeft * targetColor.rgb; \n"
"    vec3 targetColorLeft = v_cham_correctionLeft * targetColor.rgb; \n"
"    targetColor = vec4(mix(targetColorLeft, targetColorMidLeft, v_cham_weightMidLeft), 1.); \n"
"#endif // ENABLE_CHAMAELEON \n"
"#endif \n"
" \n"
"    return mix(sourceColor, targetColor, u_FadeFactor); \n"
"#endif \n"
"} \n"
" \n"
"vec4 getRightCameraBlended(vec2 f_uv) \n"
"{ \n"
"    // Get source color \n"
"#if (RIGHT_SOURCE == CAM_DISABLED) \n"
"    vec4 sourceColor = getCameraTextureRight(getUv(f_uv, u_camRight), f_uv) * u_camDisabledColor; \n"
"#elif (RIGHT_SOURCE == CAM_OFF) \n"
"    vec4 sourceColor = u_camOffColor; \n"
"#else \n"
"    vec4 sourceColor = getCameraTextureRight(getUv(f_uv, u_camRight), f_uv); \n"
"#ifdef ENABLE_CHAMAELEON \n"
"    vec3 sourceColorMidRight = v_cham_correctionMidRight * sourceColor.rgb; \n"
"    vec3 sourceColorRight = v_cham_correctionRight * sourceColor.rgb; \n"
"    sourceColor = vec4(mix(sourceColorMidRight, sourceColorRight, v_cham_weightRightMid), 1.);    \n"
"#endif // ENABLE_CHAMAELEON \n"
"#endif \n"
" \n"
"#if RIGHT_SOURCE == RIGHT_TARGET \n"
"    // No blending necessary \n"
"    return sourceColor; \n"
"#else \n"
"    // Get target color \n"
"#if (RIGHT_TARGET == CAM_DISABLED) \n"
"    vec4 targetColor = getCameraTextureRight(getUv(f_uv, u_camRight), f_uv) * u_camDisabledColor; \n"
"#elif (RIGHT_TARGET == CAM_OFF) \n"
"    vec4 targetColor = u_camOffColor; \n"
"#else \n"
"    vec4 targetColor = getCameraTextureRight(getUv(f_uv, u_camRight), f_uv); \n"
"#ifdef ENABLE_CHAMAELEON \n"
"    vec3 targetColorMidRight = v_cham_correctionMidRight * targetColor.rgb; \n"
"    vec3 targetColorRight = v_cham_correctionRight * targetColor.rgb; \n"
"    targetColor = vec4(mix(targetColorMidRight, targetColorRight, v_cham_weightRightMid), 1.);    \n"
"#endif // ENABLE_CHAMAELEON \n"
"#endif \n"
"    return mix(sourceColor, targetColor, u_FadeFactor); \n"
"#endif \n"
"} \n"
" \n"
"out vec4 FragColor; \n"
" \n"
"void main() \n"
"{ \n"
"    vec4 l_colorRight = getRightCameraBlended(v_texRightCam.xy); \n"
"    vec4 l_colorLeft  = getLeftCameraBlended(v_texLeftCam.xy); \n"
" \n"
"    // mix the alpha colors \n"
"    vec4 l_color; \n"
"    vec2 alphaCam = clamp(v_alphaCam, vec2(0.0), vec2(1.0)); \n"
" \n"
"    l_color = alphaCam.x * l_colorLeft; \n"
"    l_color += alphaCam.y * l_colorRight; \n"
" \n"
"    FragColor = vec4(clamp((l_color).rgb * u_brightFactor, 0.0, 1.0), 1.0); \n"
"} \n"
;

const static std::string s_TwoCam_vert =
"#version 300 es \n"
" \n"
" \n"
"out vec2 v_alphaCam; \n"
" \n"
"out vec2 v_texLeftCam; \n"
"out vec2 v_texRightCam; \n"
" \n"
"#define CAM_ON 0 \n"
"#define CAM_DISABLED 1 \n"
"#define CAM_OFF 2 \n"
" \n"
"#if (LEFT_SOURCE == CAM_DISABLED) || (LEFT_TARGET == CAM_DISABLED) || (RIGHT_SOURCE == CAM_DISABLED) || (RIGHT_TARGET == CAM_DISABLED) \n"
"// CAM_DISABLEDing is currently not implemented, so no need for CAM_DISABLED setup \n"
"//#define ENABLE_CAM_DISABLED \n"
"#endif \n"
" \n"
"#ifdef ENABLE_CAM_DISABLED \n"
"out float v_incrementXY; \n"
"#ifdef IS_WALL \n"
"out float v_incrementWall; \n"
"#endif \n"
"#endif \n"
" \n"
"#ifdef ENABLE_STITCHING_LINE \n"
"uniform vec3 u_stitch_line; \n"
"#ifdef IS_WALL \n"
"uniform bool u_enableDynamicWallModelMatrix; \n"
"uniform mat4 u_dynamicWallModelMatrix; \n"
"#endif \n"
"#endif \n"
" \n"
" \n"
"in highp vec4 osg_Vertex; \n"
"in vec2 osg_MultiTexCoord0; \n"
"in vec2 osg_MultiTexCoord1; \n"
" \n"
"in float a_stitchFactors; \n"
"uniform highp mat4 osg_ModelViewProjectionMatrix; \n"
" \n"
"uniform vec3 u_ortho_line; \n"
"uniform float u_stitchBand; \n"
" \n"
"#ifdef ENABLE_CHAMAELEON \n"
" \n"
"out vec3 v_cham_correctionMidLeft; \n"
"out vec3 v_cham_correctionMidRight; \n"
"out vec3 v_cham_correctionLeft; \n"
"out vec3 v_cham_correctionRight; \n"
" \n"
"out float v_cham_weightRightMid; \n"
"out float v_cham_weightMidLeft; \n"
" \n"
"uniform vec2 u_cham_direction_mid_left; \n"
"uniform vec2 u_cham_direction_mid_right; \n"
"uniform vec2 u_cham_direction_left; \n"
"uniform vec2 u_cham_direction_right; \n"
" \n"
"uniform vec3 u_cham_origin_mid; \n"
"uniform vec3 u_cham_origin_left; \n"
"uniform vec3 u_cham_origin_right; \n"
" \n"
"uniform sampler2D s_chamGainsAsTexture; \n"
"uniform ivec4 u_cham_worldRoiIdx; \n"
" \n"
"float getDistToLine(vec2 pt1, vec2 pt2, vec2 testPt) \n"
"{ \n"
"  vec2 lineDir = pt2 - pt1; \n"
"  vec2 perpDir = vec2(lineDir.y, -lineDir.x); \n"
"  vec2 dirToPt1 = pt1 - testPt; \n"
"  return abs(dot(normalize(perpDir), dirToPt1)); \n"
"} \n"
" \n"
"float crossVec2(vec2 v, vec2 w) \n"
"{ \n"
"  return (v.x * w.y) - (v.y * w.x); \n"
"} \n"
" \n"
"float getColorCorrectionWeight(vec2 f_root0, vec2 f_line0, \n"
"                               vec2 f_root1, vec2 f_line1, \n"
"                               vec2 f_vertTemp) \n"
"{ \n"
"  float u0 = crossVec2((f_vertTemp - f_root0), f_line0) / (crossVec2(f_line0, f_root1 - f_root0)); \n"
"  float u1 = crossVec2((f_vertTemp - f_root1), f_line1) / (crossVec2(f_line1, f_root1 - f_root0)); \n"
" \n"
"  vec2 intersectionLine0 = f_vertTemp + u0 * (f_root1 - f_root0); \n"
"  vec2 intersectionLine1 = f_vertTemp + u1 * (f_root1 - f_root0); \n"
" \n"
"  float distance0 = distance(intersectionLine0, f_vertTemp); \n"
"  float distance1 = distance(intersectionLine1, f_vertTemp); \n"
"  float distanceAll = distance(intersectionLine0, intersectionLine1); \n"
" \n"
"  float diff = distance1 - min(distanceAll, distance1); \n"
" \n"
"  return clamp(((distance0 - diff) / distanceAll), 0.0, 1.0); \n"
"} \n"
"#endif // ENABLE_CHAMAELEON \n"
" \n"
"#ifdef ENABLE_SHARPNESS_HARMONIZATION \n"
"// currently no special content except OpenGL ES 3 conformant shader \n"
"#endif // ENABLE_SHARPNESS_HARMONIZATION \n"
" \n"
"uniform float u_stitchHeight; \n"
"uniform float u_stitchParam; \n"
" \n"
"uniform bool u_disableBorderBlending; // workaround for NRCSTWO-76310 \n"
" \n"
"float getStitchFactor(float f_blendingBandwidth, float f_distance) \n"
"{ \n"
"  return f_distance*0.5/(f_blendingBandwidth+0.01) + 0.5; \n"
"} \n"
" \n"
"void main() \n"
"{ \n"
"  v_texLeftCam = osg_MultiTexCoord0; \n"
"  v_texRightCam = osg_MultiTexCoord1; \n"
" \n"
"#ifdef ENABLE_CAM_DISABLED \n"
"  v_incrementXY = osg_Vertex.y - osg_Vertex.x; \n"
" \n"
"#ifdef IS_WALL \n"
"  vec2 l_V = normalize (osg_Vertex.xy); \n"
"  vec2 l_XDirection = vec2 (1, 0); \n"
"  vec2 l_XDirectionNormalized = normalize (l_XDirection); \n"
"  float l_CosineAngle = dot (l_V, l_XDirectionNormalized); \n"
"  float l_Angle = acos (l_CosineAngle); \n"
"  v_incrementWall = float (l_Angle) + osg_Vertex.z; \n"
"#endif \n"
" \n"
"#endif \n"
" \n"
"#ifndef ENABLE_STITCHING_LINE \n"
"  v_alphaCam = vec2(a_stitchFactors, 1.0-a_stitchFactors); \n"
"#else \n"
"#ifdef IS_WALL \n"
"  vec3 l_vertLocal; \n"
"  if (u_enableDynamicWallModelMatrix) \n"
"  { \n"
"    l_vertLocal = (u_dynamicWallModelMatrix*osg_Vertex).xyz; \n"
"  } \n"
"  else \n"
"  { \n"
"    l_vertLocal = osg_Vertex.xyz; \n"
"  } \n"
"#else \n"
"  vec3 l_vertLocal = osg_Vertex.xyz; \n"
"#endif \n"
"  vec3 l_vertTemp = vec3(l_vertLocal.xy, 1.0); \n"
"  // calculate the distance from the line to the point \n"
"  float l_dist2line = dot(u_ortho_line, l_vertTemp); \n"
"  float l_dist2Origin = abs(dot(u_stitch_line, l_vertTemp))/2.; \n"
"  float l_stitchBandNew = ((l_dist2Origin + 0.01)*u_stitchBand); \n"
" \n"
"#ifdef IS_WALL \n"
"  // stitch-line for wall (with border, height dependent stiching band width) \n"
"  // decide the stitch factor depending on the stitch line \n"
"  l_stitchBandNew += (u_stitchBand * u_stitchParam * (l_vertLocal.z / u_stitchHeight)); \n"
" \n"
"  if (u_disableBorderBlending) \n"
"  { \n"
"    float l_StitchFactor = getStitchFactor(l_stitchBandNew, l_dist2line); \n"
"    v_alphaCam.x = l_StitchFactor; \n"
"    v_alphaCam.y = 1.0-l_StitchFactor; \n"
"  } \n"
"  else \n"
"  { \n"
"    float l_StitchFactor = clamp(getStitchFactor(l_stitchBandNew, l_dist2line), 0.0, 1.0); \n"
"    vec2 l_border = smoothstep(0.0, 0.2, vec2(a_stitchFactors, 1.0-a_stitchFactors)); \n"
"    v_alphaCam = min(l_border, vec2(l_StitchFactor, (1.0-l_StitchFactor))); \n"
"    v_alphaCam/= (v_alphaCam.x + v_alphaCam.y); \n"
"  } \n"
"#else \n"
"  float l_StitchFactor = getStitchFactor(l_stitchBandNew, l_dist2line); \n"
"  v_alphaCam.x = l_StitchFactor; \n"
"  v_alphaCam.y = 1.0-l_StitchFactor; \n"
"#endif // IS_WALL \n"
" \n"
" \n"
"#endif // ENABLE_STITCHING_LINE \n"
" \n"
"#ifdef ENABLE_CHAMAELEON \n"
"    v_cham_correctionLeft = texelFetch(s_chamGainsAsTexture, ivec2(u_cham_worldRoiIdx[0],0), 0).rgb + vec3(0.5); \n"
"    v_cham_correctionMidRight = texelFetch(s_chamGainsAsTexture, ivec2(u_cham_worldRoiIdx[1],0), 0).rgb + vec3(0.5); \n"
"    v_cham_correctionMidLeft = texelFetch(s_chamGainsAsTexture, ivec2(u_cham_worldRoiIdx[2],0), 0).rgb + vec3(0.5); \n"
"    v_cham_correctionRight = texelFetch(s_chamGainsAsTexture, ivec2(u_cham_worldRoiIdx[3],0), 0).rgb + vec3(0.5); \n"
" \n"
"    v_cham_weightRightMid = getColorCorrectionWeight(u_cham_origin_mid.xy, u_cham_direction_mid_right, u_cham_origin_right.xy, u_cham_direction_right, osg_Vertex.xy); \n"
"    v_cham_weightMidLeft  = getColorCorrectionWeight(u_cham_origin_left.xy , u_cham_direction_left, u_cham_origin_mid.xy, u_cham_direction_mid_left, osg_Vertex.xy); \n"
"#endif // ENABLE_CHAMAELEON \n"
" \n"
"  gl_Position = osg_ModelViewProjectionMatrix * osg_Vertex; \n"
"} \n"
;


///
/// ConfigurableShaderSource
///
ConfigurableShaderSource::ConfigurableShaderSource(const std::string& f_filename)
  : m_valid(false)
{
#ifdef USE_BUILD_IN_SHARDER_CODE

  if(f_filename.find("RawTexConf.vert") != std::string::npos)
  {
    m_shaderSource = s_RawTexConf_vert;
    m_valid = true;
  }
  else if(f_filename.find("RawTexConf.frag") != std::string::npos)
  {
    m_shaderSource = s_RawTexConf_frag;
    m_valid = true;
  }
  else if(f_filename.find("SingleCam.vert") != std::string::npos)
  {
    m_shaderSource = s_SingleCam_vert;
    m_valid = true;
  }
  else if(f_filename.find("SingleCam.frag") != std::string::npos)
  {
    m_shaderSource = s_SingleCam_frag;
    m_valid = true;
  }
  else if(f_filename.find("TwoCam.vert") != std::string::npos)
  {
    m_shaderSource = s_TwoCam_vert;
    m_valid = true;
  }
  else if(f_filename.find("TwoCam.frag") != std::string::npos)
  {
    m_shaderSource = s_TwoCam_frag;
    m_valid = true;
  }
  else
  {
    XLOG_ERROR(pc::util::logging::g_EngineContext, "Failed to find in shader source: " << f_filename);
  }

#else //USE_BUILD_IN_SHARDER_CODE

  std::ifstream fs(f_filename, std::ios::in);
  if (fs)
  {
    std::string line;
    while (std::getline(fs, line))
    {
      m_shaderSource += line + "\n";
    }
    m_valid = true;
  }
  else
  {
    XLOG_ERROR(pc::util::logging::g_EngineContext, "Failed to load shader source file: " << f_filename);
  }

#endif //USE_BUILD_IN_SHARDER_CODE
}

bool ConfigurableShaderSource::isValid() const
{
  return m_valid;
}

///
/// CameraImageShaders
///
namespace
{

const std::string getShaderInputName(CameraShaderInput f_input)
{
  switch (f_input)
  {
    case SHADER_CAM_OFF:      return "Off";
    case SHADER_CAM_DISABLED: return "Disabled";
    case SHADER_CAM_ON:       return "On";
  }
  return "invalid";
}

//! Returns macro value to use if the given shader input should be activated
const std::string getShaderInputMacroValue(CameraShaderInput f_input)
{
  switch (f_input)
  {
    default:
    case SHADER_CAM_ON:       return "0";
    case SHADER_CAM_DISABLED: return "1";
    case SHADER_CAM_OFF:      return "2";
  }
}

} // namespace

CameraImageShaders::CameraImageShaders(const std::string& f_path)
  : m_rawTexFragmentShader(f_path + "RawTexConf.frag")
  , m_rawTexVertexShader(f_path + "RawTexConf.vert")
  , m_singleCamFragmentShader(f_path + "SingleCam.frag")
  , m_singleCamVertexShader(f_path + "SingleCam.vert")
  , m_twoCamFragmentShader(f_path + "TwoCam.frag")
  , m_twoCamVertexShader(f_path + "TwoCam.vert")
{
}

void CameraImageShaders::configureShader(ConfigurableShaderSource& f_shaderSource, const RawTexShaderParameters& f_shaderParameters) const
{
  // Parameter dependent settings
  if (f_shaderParameters.m_EnableSharpnessHarmonization)
  {
    f_shaderSource.define("ENABLE_SHARPNESS_HARMONIZATION");
  }
  if (f_shaderParameters.m_DebugSharpnessHarmonization)
  {
    f_shaderSource.define("DEBUG_SHARPNESS_HARMONIZATION");
  }
  if (f_shaderParameters.m_EnableTemporalNoiseFilter)
  {
    f_shaderSource.define("ENABLE_TNF");
  }
}

void CameraImageShaders::configureShader(ConfigurableShaderSource& f_shaderSource, const SingleCamShaderParameters& f_shaderParameters) const
{
  // Parameter dependent settings
  if (f_shaderParameters.m_IsWall)
  {
    f_shaderSource.define("IS_WALL");
  }
  if (f_shaderParameters.m_EnableChamaeleon)
  {
    f_shaderSource.define("ENABLE_CHAMAELEON");
  }
  if (f_shaderParameters.m_EnableSharpnessHarmonization)
  {
    f_shaderSource.define("ENABLE_SHARPNESS_HARMONIZATION");
  }
  if (f_shaderParameters.m_DebugSharpnessHarmonization)
  {
    f_shaderSource.define("DEBUG_SHARPNESS_HARMONIZATION");
  }
  if (f_shaderParameters.m_EnableTemporalNoiseFilter)
  {
    f_shaderSource.define("ENABLE_TNF");
  }

  f_shaderSource.define("SOURCE", getShaderInputMacroValue(f_shaderParameters.m_SourceInput));
  f_shaderSource.define("TARGET", getShaderInputMacroValue(f_shaderParameters.m_TargetInput));
}

void CameraImageShaders::configureShader(ConfigurableShaderSource& f_shaderSource, const TwoCamShaderParameters& f_shaderParameters) const
{
  // Parameter dependent settings
  if (f_shaderParameters.m_IsWall)
  {
    f_shaderSource.define("IS_WALL");
  }

  if (f_shaderParameters.m_EnableChamaeleon)
  {
    f_shaderSource.define("ENABLE_CHAMAELEON");
  }
  if (f_shaderParameters.m_EnableSharpnessHarmonization)
  {
    f_shaderSource.define("ENABLE_SHARPNESS_HARMONIZATION");
  }
  if (f_shaderParameters.m_DebugSharpnessHarmonization)
  {
    f_shaderSource.define("DEBUG_SHARPNESS_HARMONIZATION");
  }
  if (f_shaderParameters.m_EnableTemporalNoiseFilter)
  {
    f_shaderSource.define("ENABLE_TNF");
  }
  if (f_shaderParameters.m_EnableStitching)
  {
    f_shaderSource.define("ENABLE_STITCHING_LINE");
  }

  f_shaderSource.define("LEFT_SOURCE",  getShaderInputMacroValue(f_shaderParameters.m_SourceInputLeft));
  f_shaderSource.define("LEFT_TARGET",  getShaderInputMacroValue(f_shaderParameters.m_TargetInputLeft));
  f_shaderSource.define("RIGHT_SOURCE", getShaderInputMacroValue(f_shaderParameters.m_SourceInputRight));
  f_shaderSource.define("RIGHT_TARGET", getShaderInputMacroValue(f_shaderParameters.m_TargetInputRight));
}

ShaderSources CameraImageShaders::getRawTexShaderSources(const RawTexShaderParameters& f_shaderParameters) const
{
  ConfigurableShaderSource vertexShaderSource = m_rawTexVertexShader;
  ConfigurableShaderSource fragmentShaderSource = m_rawTexFragmentShader;

  configureShader(vertexShaderSource,   f_shaderParameters);
  configureShader(fragmentShaderSource, f_shaderParameters);

  const std::size_t l_vertexInsertPosition = pc::core::ShaderConfigurator::locatePosition(vertexShaderSource.getSourceCode());
  const std::size_t l_fragmentInsertPosition = pc::core::ShaderConfigurator::locatePosition(fragmentShaderSource.getSourceCode());
  vertexShaderSource.configure(vertexShaderSource.getSourceCode(), l_vertexInsertPosition);
  fragmentShaderSource.configure(fragmentShaderSource.getSourceCode(), l_fragmentInsertPosition);

  ShaderSources result;
  result.m_vertexShaderSource   = vertexShaderSource.getSourceCode();
  result.m_fragmentShaderSource = fragmentShaderSource.getSourceCode();
  result.m_areValid             = (vertexShaderSource.isValid()) && (fragmentShaderSource.isValid());
  return result;
}

ShaderSources CameraImageShaders::getSingleCamShaderSources(const SingleCamShaderParameters& f_shaderParameters) const
{
  ConfigurableShaderSource vertexShaderSource = m_singleCamVertexShader;
  ConfigurableShaderSource fragmentShaderSource = m_singleCamFragmentShader;

  configureShader(vertexShaderSource,   f_shaderParameters);
  configureShader(fragmentShaderSource, f_shaderParameters);

  const std::size_t l_vertexInsertPosition = pc::core::ShaderConfigurator::locatePosition(vertexShaderSource.getSourceCode());
  const std::size_t l_fragmentInsertPosition = pc::core::ShaderConfigurator::locatePosition(fragmentShaderSource.getSourceCode());
  vertexShaderSource.configure(vertexShaderSource.getSourceCode(), l_vertexInsertPosition);
  fragmentShaderSource.configure(fragmentShaderSource.getSourceCode(), l_fragmentInsertPosition);

  ShaderSources result;
  result.m_vertexShaderSource   = vertexShaderSource.getSourceCode();
  result.m_fragmentShaderSource = fragmentShaderSource.getSourceCode();
  result.m_areValid             = (vertexShaderSource.isValid()) && (fragmentShaderSource.isValid());
  return result;
}

ShaderSources CameraImageShaders::getTwoCamShaderSources(const TwoCamShaderParameters& f_shaderParameters) const
{
  ConfigurableShaderSource vertexShaderSource = m_twoCamVertexShader;
  ConfigurableShaderSource fragmentShaderSource = m_twoCamFragmentShader;

  configureShader(vertexShaderSource, f_shaderParameters);
  configureShader(fragmentShaderSource, f_shaderParameters);

  const std::size_t l_vertexInsertPosition = pc::core::ShaderConfigurator::locatePosition(vertexShaderSource.getSourceCode());
  const std::size_t l_fragmentInsertPosition = pc::core::ShaderConfigurator::locatePosition(fragmentShaderSource.getSourceCode());
  vertexShaderSource.configure(vertexShaderSource.getSourceCode(), l_vertexInsertPosition);
  fragmentShaderSource.configure(fragmentShaderSource.getSourceCode(), l_fragmentInsertPosition);

  ShaderSources result;
  result.m_vertexShaderSource   = vertexShaderSource.getSourceCode();
  result.m_fragmentShaderSource = fragmentShaderSource.getSourceCode();
  result.m_areValid             = (vertexShaderSource.isValid()) && (fragmentShaderSource.isValid());
  return result;
}

std::string getShaderIdentifier(const RawTexShaderParameters& f_shaderParameters)
{
  std::string shaderIdentifier = "rawTexConf";
  if (f_shaderParameters.m_EnableSharpnessHarmonization)
  {
    shaderIdentifier += "SharpnessHarmonization";
  }
  if (f_shaderParameters.m_DebugSharpnessHarmonization)
  {
    shaderIdentifier += "SharpnessDebug";
  }
  if (f_shaderParameters.m_EnableTemporalNoiseFilter)
  {
    shaderIdentifier += "Tnf";
  }
  return shaderIdentifier;
}

std::string getShaderIdentifier(const SingleCamShaderParameters& f_shaderParameters)
{
  std::string shaderIdentifier = "singleCam";
  if (f_shaderParameters.m_IsWall)
  {
    shaderIdentifier += "Wall";
  }
  else
  {
    shaderIdentifier+= "Floor";
  }
  if (f_shaderParameters.m_EnableChamaeleon)
  {
    shaderIdentifier+= "Chamaeleon";
  }
  if (f_shaderParameters.m_EnableSharpnessHarmonization)
  {
    shaderIdentifier+= "SharpnessHarmonization";
  }
  if (f_shaderParameters.m_DebugSharpnessHarmonization)
  {
    shaderIdentifier+= "SharpnessDebug";
  }
  if (f_shaderParameters.m_EnableTemporalNoiseFilter)
  {
    shaderIdentifier += "Tnf";
  }

  shaderIdentifier+= getShaderInputName(f_shaderParameters.m_SourceInput);
  if (f_shaderParameters.m_SourceInput != f_shaderParameters.m_TargetInput)
  {
    shaderIdentifier+= getShaderInputName(f_shaderParameters.m_TargetInput);
  }
  return shaderIdentifier;
}

std::string getShaderIdentifier(const TwoCamShaderParameters& f_shaderParameters)
{
  std::string shaderIdentifier = "twoCam";
  if(f_shaderParameters.m_IsWall)
  {
    shaderIdentifier+= "Wall";
  }
  else
  {
    shaderIdentifier+= "Floor";
  }
  if (f_shaderParameters.m_EnableStitching)
  {
    shaderIdentifier+= "Stitching";
  }
  if (f_shaderParameters.m_EnableChamaeleon)
  {
    shaderIdentifier+= "Chamaeleon";
  }
  if (f_shaderParameters.m_EnableSharpnessHarmonization)
  {
    shaderIdentifier+= "SharpnessHarmonization";
  }
  if (f_shaderParameters.m_DebugSharpnessHarmonization)
  {
    shaderIdentifier+= "SharpnessDebug";
  }
  if (f_shaderParameters.m_EnableTemporalNoiseFilter)
  {
    shaderIdentifier += "Tnf";
  }
  shaderIdentifier += getShaderInputName(f_shaderParameters.m_SourceInputLeft);
  shaderIdentifier += getShaderInputName(f_shaderParameters.m_TargetInputLeft);
  shaderIdentifier += getShaderInputName(f_shaderParameters.m_SourceInputRight);
  shaderIdentifier += getShaderInputName(f_shaderParameters.m_TargetInputRight);
  return shaderIdentifier;
}


} // namespace factory
} // namespace pc
