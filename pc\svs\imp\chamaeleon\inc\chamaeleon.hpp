/// @copyright (C) 2023 Robert <PERSON> GmbH.
/// The reproduction, distribution and utilization of this file as well as the
/// communication of its contents to others without express authorization is
/// prohibited. Offenders will be held liable for the payment of damages. All
/// rights reserved in the event of the grant of a patent, utility model or
/// design.
/// @file

#ifndef PC_SVS_IMP_CHAMAELEON_H
#define PC_SVS_IMP_CHAMAELEON_H

#include "pc/svs/core/inc/Framework.h"
#include "pc/svs/factory/inc/RenderManager.h"
#include "pc/svs/factory/inc/SV3DNode.h"
#include "pc/svs/imp/chamaeleon/inc/chamaeleon_base_signals.hpp"
#include "pc/svs/imp/chamaeleon/inc/chamaeleon_data.hpp"
#include "pc/svs/imp/chamaeleon/inc/chamaeleon_estimator.hpp"
#include "pc/svs/imp/chamaeleon/inc/chamaeleon_rois.hpp"
#include "pc/svs/imp/chamaeleon/inc/chamaeleon_tabs.hpp"
#include "pc/svs/imp/chamaeleon/inc/chamaeleon_wb_gains.hpp"
#include "pc/svs/imp/chamaeleon/inc/visu/chamaeleon_visu.hpp"
#include "pc/svs/virtcam/inc/VirtualCam.h"

#include "osg/Group"

namespace rbp
{
namespace vis
{
namespace imp
{
namespace chamaeleon_test
{
template <typename TEngine>
class TChamaeleonFixture;
} // namespace chamaeleon_test
namespace chamaeleon
{
namespace visu
{
class ChamaeleonVisu;
} // namespace visu

///
/// Chamaeleon
///
class Chamaeleon : public osg::Group
{
public:
    Chamaeleon(
        pc::core::Framework*                    f_pFramework,
        pc::factory::RenderManagerRegistry*     f_pRegistry,
        pc::factory::SV3DNode*                  f_pFloor,
        const pc::virtcam::VirtualCamera* const f_virtCam);

    void traverse(osg::NodeVisitor& f_nv) override;

    void                  getChamaeleonRois() && = delete;
    const ChamaeleonRois& getChamaeleonRois() const&
    {
        return m_chamaeleonRois;
    }

protected:
    ~Chamaeleon() override = default;

private:
    // The utf fixture class is a friend to be able to access private members
    // qacpp-2107-R1: Friend declaration for testing purposes.
    template <typename TEngine>
    friend class chamaeleon_test::TChamaeleonFixture; // PRQA S 2107 # R1

    Chamaeleon();
    Chamaeleon(const Chamaeleon&)            = delete;
    Chamaeleon(Chamaeleon&&)                 = delete;
    Chamaeleon& operator=(const Chamaeleon&) = delete;
    Chamaeleon& operator=(Chamaeleon&&)      = delete;

    void setParameters();

    void receiveInputs();

    void deliverOutput() const;

    void init();

    ChamaeleonBaseSignals m_chamaeleonBaseSignals{};
    ChamaeleonRois        m_chamaeleonRois;
    ChamaeleonEstimator   m_chamaeleonEsti{};
    ChamaeleonWbGains     m_chamaeleonWBG{};

    bool m_initialized{false};

    pc::core::Framework*               m_framework{nullptr}; // must not be a ref ptr to avoid pointer loop
    osg::ref_ptr<visu::ChamaeleonVisu> m_chamaeleonVisu{nullptr};
};

} // namespace chamaeleon
} // namespace imp
} // namespace vis
} // namespace rbp

#endif // PC_SVS_IMP_CHAMAELEON_H
