//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: MADCSZH MAO Donny (CC-DA/EAV2-CN)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  NfsCalibrationScreen.cpp
/// @brief
//=============================================================================

#include "pc/svs/views/engineeringview/inc/ScreenOutputStream.h"
#include "pc/svs/core/inc/Framework.h"
#include "pc/svs/core/inc/SystemConf.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"
#include "pc/svs/core/inc/ShaderManager.h"

#include "cc/views/nfsengineeringview/inc/NfsCalibrationScreen.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/core/inc/CustomFramework.h"

#include <iomanip>
#include <iostream>
#include <sstream>

namespace cc
{
namespace views
{
namespace nfsengineeringview
{

const char* getCameraName(vfc::uint32_t f_index)  //PRQA S 2428
{
    switch (f_index)
    {
        case pc::core::sysconf::FRONT_CAMERA:
        {
            return "front";
        }
        case pc::core::sysconf::RIGHT_CAMERA:
        {
            return "right";
        }
        case pc::core::sysconf::REAR_CAMERA:
        {
            return "rear";
        }
        case pc::core::sysconf::LEFT_CAMERA:
        {
            return "left";
        }
        default:
        {
            return "";
        }
    }
}

//!
//! NfsCalibrationScreen
//!
NfsCalibrationScreen::NfsCalibrationScreen()
  : EngineeringScreen("NFS Calibration screen") // PRQA S 4050
  , m_sequenceNumber(0u)
  , m_counterCurrentCalib(0u)
  , m_sequenceNumberSpeed(0u)
  , m_sequenceNumberRA(0u)
  , m_textPos{}
{
    osg::StateSet* const l_stateSet = getOrCreateStateSet();
    l_stateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF); //PRQA S 3143
    pc::core::TextureShaderProgramDescriptor l_basicTexShader("colorText");
    l_basicTexShader.apply(l_stateSet);  // PRQA S 3803

    const vfc::float32_t l_mainViewWidth    = static_cast<vfc::float32_t>(cc::core::g_views->m_mainViewport.m_size.x());
    const vfc::float32_t l_mainViewHeight   = static_cast<vfc::float32_t>(cc::core::g_views->m_mainViewport.m_size.y());
    const vfc::float32_t l_planViewWidth    = static_cast<vfc::float32_t>(cc::core::g_views->m_planViewport.m_size.x());
    const vfc::float32_t l_fisheyeViewWidth = static_cast<vfc::float32_t>(cc::core::g_views->m_fisheyeViewport.m_size.x());
    const vfc::float32_t l_sysViewHeight    = static_cast<vfc::float32_t>(pc::core::g_systemConf->m_mainViewport.m_size.y());

    m_textPos = std::vector<osg::Vec2f>(4u);                          // vector for calibration textbox positions
    osg::Vec3f l_posOther(24.0f, l_sysViewHeight - 60.0f, 0.0f);      // set speed and road wheel angle signals textbox pos default in left hand drive

    const bool lhd = pc::vehicle::g_mechanicalData->m_leftHandDrive;
    getCamCalibTextPos(lhd, l_mainViewWidth, l_mainViewHeight, l_planViewWidth, l_fisheyeViewWidth, l_sysViewHeight);
    if (!lhd)
    {
        // set the other signals textbox pos in right hand drive
        l_posOther.x() += l_mainViewWidth;
    }

    // set 4 cameras textbox
    for (vfc::uint32_t i = 0u; i < 4u; ++i)
    {
        const osg::Vec3f l_pos(m_textPos[i].x() + 20.0f, m_textPos[i].y() - 20.0f, 0.0f);
        pc::views::engineeringview::KeyValueTextBox *const l_pTextBox =
            new pc::views::engineeringview::KeyValueTextBox(l_pos, osg::Vec4f(0.0f, 0.0f, 0.0f, 1.0f), 15.0f, 16u);
        addTextBox(l_pTextBox);
        l_pTextBox->getOrCreateEntry("Calibration Availability: ")->setValue("");
        l_pTextBox->getOrCreateEntry(" ")->setValue("N/A");                          // use another line to avoid too long displaying
        l_pTextBox->getOrCreateEntry("Calibration State: ")->setValue("");
        l_pTextBox->getOrCreateEntry("  ")->setValue("N/A");                         // use another line to avoid too long displaying
    }

    // set speed and road wheel angle textbox
    pc::views::engineeringview::KeyValueTextBox *const l_pTextBoxOther =
        new pc::views::engineeringview::KeyValueTextBox(l_posOther, osg::Vec4f(0.0f, 0.0f, 0.0f, 1.0f), 20.0f, 24u);
    addTextBox(l_pTextBoxOther);

    l_pTextBoxOther->getOrCreateEntry("Speed: ")->setValue("N/A");
    l_pTextBoxOther->getOrCreateEntry("Road Wheel Angle: ")->setValue("N/A");
}

NfsCalibrationScreen::~NfsCalibrationScreen() = default;

void NfsCalibrationScreen::getCamCalibTextPos(
    const bool f_lhd_b,
    vfc::float32_t f_mainViewWidth,
    vfc::float32_t f_mainViewHeight,
    vfc::float32_t f_planViewWidth,
    vfc::float32_t f_fisheyeViewWidth,
    vfc::float32_t f_sysViewHeight
)
{
    for (vfc::uint32_t i = 0u; i < 4u; ++i)
    {
        // set the calibration textbox position of 4 cameras
        switch (i)
        {
            // front
            case 0:
            {
                if (f_lhd_b)
                {
                    m_textPos[i].x() = f_planViewWidth;
                }
                else
                {
                    m_textPos[i].x() = f_mainViewWidth - f_fisheyeViewWidth;
                }
                m_textPos[i].y() = f_sysViewHeight;
                break;
            }
            // right
            case 1:
            {
                if (f_lhd_b)
                {
                    m_textPos[i].x() = f_planViewWidth + f_fisheyeViewWidth / 2.0f;
                }
                else
                {
                    m_textPos[i].x() = f_mainViewWidth - f_fisheyeViewWidth / 2.0f;
                }

                m_textPos[i].y() = f_sysViewHeight - f_mainViewHeight / 2.0f;
                break;
            }
            // rear
            case 2:
            {
                if (f_lhd_b)
                {
                    m_textPos[i].x() = f_planViewWidth + f_fisheyeViewWidth / 2.0f;
                }
                else
                {
                    m_textPos[i].x() = f_mainViewWidth - f_fisheyeViewWidth / 2.0f;
                }

                m_textPos[i].y() = f_sysViewHeight;
                break;
            }
            // left
            case 3:
            {
                if (f_lhd_b)
                {
                    m_textPos[i].x() = f_planViewWidth;
                }
                else
                {
                    m_textPos[i].x() = f_mainViewWidth - f_fisheyeViewWidth;
                }

                m_textPos[i].y() = f_sysViewHeight - f_mainViewHeight / 2.0f;
                break;
            }
            default:
            {
                m_textPos[i].x() = 0.0f;
                m_textPos[i].y() = f_sysViewHeight;
                break;
            }

        }
    }

}

std::string NfsCalibrationScreen::getCamAvailabilityString(vfc::uint32_t f_camAvail)
{
    // map the m_camAvailability value to meaningful string
    std::string l_retString;
    switch (f_camAvail)
    {
        case 0:
        {
            l_retString = "Calib is sleeping";
            break;
        }
        case 1:
        {
            l_retString = "Calib is awake";
            break;
        }
        case 2:
        {
            l_retString = "Calib in error";
            break;
        }
        default:
        {
            l_retString = "N/A";
            break;
        }
    }

    return l_retString;
}

std::string NfsCalibrationScreen::getCalibStateString(vfc::uint32_t f_calibState)
{
    // map the m_calibrationState value to meaningful string
    std::string l_retString;
    switch (f_calibState)
    {
        case 0:
        {
            l_retString = "Not Calibrated";
            break;
        }
        case 1:
        {
            l_retString = "Partially Calibrated";
            break;
        }
        case 2:
        {
            l_retString = "Fully Calibrated";
            break;
        }
        case 3:
        {
            l_retString = "Calibration Not start";
            break;
        }
        default:
        {
            l_retString = "N/A";
            break;
        }
    }

    return l_retString;
}

bool NfsCalibrationScreen::update(pc::core::Framework* f_framework)
{
    if (nullptr == f_framework)
    {
        return false;
    }
    bool l_dataChanged = false;
    const pc::daddy::CameraCalibrationDaddy* const l_calib = f_framework->m_cameraCalibrationReceiver.getData();
    if ((nullptr != l_calib) && (l_calib->m_sequenceNumber != m_sequenceNumber))
    {
        m_sequenceNumber = l_calib->m_sequenceNumber;
        pc::views::engineeringview::ScreenOutputStream l_screenOutputStream(*this);
        for (vfc::uint32_t i = 0u; i < 4u; ++i)
        {
            l_screenOutputStream.setTextBoxIndex(i);
            const std::string l_camName = getCameraName(i);
            //! extrinsic
            const pc::c2w::ExtrinsicCalibration l_extrinsicTemp = l_calib->m_Data[i].getExtrinsicCalibration();
            pc::c2w::ExtrinsicCalibration l_extrinsic;
            // change the extrinsic x , y, z units from m to mm
            l_extrinsic.set(
                l_extrinsicTemp.getX() * 1000.0f,
                l_extrinsicTemp.getY() * 1000.0f,
                l_extrinsicTemp.getZ() * 1000.0f,
                l_extrinsicTemp.getYaw(),
                l_extrinsicTemp.getPitch(),
                l_extrinsicTemp.getRoll());

            l_screenOutputStream.setPrefix(l_camName + ".ext.");
            l_screenOutputStream.serialize(&l_extrinsic);
        }
        l_dataChanged = true;
    }

    // Camera calibration status
    if ( true == f_framework->asCustomFramework()->m_camCalibrationStatus_ReceiverPort.isConnected() )
    {
        const cc::daddy::C2WCalibStatusDaddy_t* const l_pCalibStatus = f_framework->asCustomFramework()->m_camCalibrationStatus_ReceiverPort.getData();

        if ((nullptr != l_pCalibStatus) && (l_pCalibStatus->m_sequenceNumber != m_counterCurrentCalib))
        {
            m_counterCurrentCalib = l_pCalibStatus->m_sequenceNumber;

            for (vfc::uint32_t i = 0u; i < 4u; ++i)
            {
                pc::views::engineeringview::TextBox *const l_pTextBox = getTextBox(i);
                pc::views::engineeringview::TextBox::Entry *const l_pTextBoxCalibAvail = l_pTextBox->getOrCreateEntry(" ");    // the line below m_camAvailability
                const std::string l_retAvail = getCamAvailabilityString(static_cast<vfc::uint32_t>(l_pCalibStatus->m_Data.m_calibstatus[i].m_camAvailability));
                l_pTextBoxCalibAvail->setValue(l_retAvail);

                pc::views::engineeringview::TextBox::Entry *const l_pTextBoxCalibState = l_pTextBox->getOrCreateEntry("  ");   // the line below m_calibrationState
                const std::string l_retState = getCalibStateString(static_cast<vfc::uint32_t>(l_pCalibStatus->m_Data.m_calibstatus[i].m_calibrationState));
                l_pTextBoxCalibState->setValue(l_retState);
            }

            l_dataChanged = true;
        }

    }

    //! Speed and road wheel angle textbox
    pc::views::engineeringview::TextBox *const l_pTextBox = getTextBox(4u);
    const pc::daddy::SpeedDaddy* const l_speedDaddy = f_framework->m_speedReceiver.getData();
    if ((nullptr != l_speedDaddy) && (l_speedDaddy->m_sequenceNumber != m_sequenceNumberSpeed))
    {
        m_sequenceNumberSpeed = l_speedDaddy->m_sequenceNumber;
        pc::views::engineeringview::TextBox::Entry* const l_entrySpeed = l_pTextBox->getOrCreateEntry("Speed: ");
        // set the vfc::float32_t value with 2 precision
        std::stringstream sSp;
        sSp << std::fixed << std::setprecision(2) << l_speedDaddy->m_Data;  // PRQA S 3803
        const std::string sSpeed = sSp.str();
        l_entrySpeed->setValue(sSpeed);

        l_dataChanged = true;
    }

    const pc::daddy::SteeringAngleDaddy* const l_pSteeringAngle = f_framework->m_steeringAngleFrontReceiver.getData();
    if ((nullptr != l_pSteeringAngle) && (l_pSteeringAngle->m_sequenceNumber != m_sequenceNumberRA))
    {
        m_sequenceNumberRA = l_pSteeringAngle->m_sequenceNumber;
        pc::views::engineeringview::TextBox::Entry *const l_pEntrySteerAng = l_pTextBox->getOrCreateEntry("Road Wheel Angle: ");
        // set the vfc::float32_t value with 2 precision
        std::stringstream sSa;
        sSa << std::fixed << std::setprecision(2) << l_pSteeringAngle->m_Data.value();  // PRQA S 3803
        const std::string sSteerAng = sSa.str();
        l_pEntrySteerAng->setValue(sSteerAng);

        l_dataChanged = true;
    }

    return l_dataChanged;
}

} // namespace nfsengineeringview
} // namespace views
} // namespace cc

