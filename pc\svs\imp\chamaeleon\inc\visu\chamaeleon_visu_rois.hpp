/// @copyright (C) 2023 Robert <PERSON> GmbH.
/// The reproduction, distribution and utilization of this file as well as the
/// communication of its contents to others without express authorization is
/// prohibited. Offenders will be held liable for the payment of damages. All
/// rights reserved in the event of the grant of a patent, utility model or
/// design.
/// @file

#ifndef PC_SVS_IMP_VISUCHAMAELEONVISUROIS_H
#define PC_SVS_IMP_VISUCHAMAELEONVISUROIS_H

#include "pc/svs/texfloor/core/inc/TextureDisplayCamera.h"
#include "pc/svs/imp/chamaeleon/inc/chamaeleon_data.hpp"
#include "pc/svs/imp/chamaeleon/inc/chamaeleon_rois.hpp"
#include "pc/svs/imp/chamaeleon/inc/chamaeleon_tabs.hpp"

#include "osg/Group"

namespace rbp
{
namespace vis
{
namespace imp
{
namespace chamaeleon
{
namespace visu
{

///
/// ChamaeleonVisuRois
///
// qacpp-2119-R1: OSG API is intentionally declaring the destructors as protected
class ChamaeleonVisuRois : public osg::Group // PRQA S 2119 # R1
{
public:
    explicit ChamaeleonVisuRois(const ChamaeleonRois& f_chamaeleonRois);

    ~ChamaeleonVisuRois() override = default;

    void setViewport(const ChamaeleonVisuViewport& f_visuViewport);

    void traverse(osg::NodeVisitor& f_nv) override;

private:
    ChamaeleonVisuRois();
    ChamaeleonVisuRois(const ChamaeleonVisuRois&)            = delete;
    ChamaeleonVisuRois& operator=(const ChamaeleonVisuRois&) = delete;

    const ChamaeleonRois&                                  m_chamaeleonRois;
    osg::ref_ptr<pc::texfloor::core::TextureDisplayCamera> m_roiDebugCam{};
};

} // namespace visu
} // namespace chamaeleon
} // namespace imp
} // namespace vis
} // namespace rbp

#endif // PC_SVS_IMP_VISUCHAMAELEONVISUROIS_H
