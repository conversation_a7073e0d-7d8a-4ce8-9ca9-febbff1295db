/// @copyright (C) 2024 Robert <PERSON> GmbH.
/// The reproduction, distribution and utilization of this file as well as the
/// communication of its contents to others without express authorization is
/// prohibited. Offenders will be held liable for the payment of damages. All
/// rights reserved in the event of the grant of a patent, utility model or
/// design.
/// @file

#ifndef PC_SVS_IMP_CHAMAELEON_WB_GAINS_H
#define PC_SVS_IMP_CHAMAELEON_WB_GAINS_H

#include "pc/svs/daddy/inc/BaseDaddyTypes.h"
#include "pc/svs/imp/chamaeleon/inc/chamaeleon_data.hpp"
#include "pc/svs/imp/chamaeleon/inc/chamaeleon_tabs.hpp"
#include "pc/svs/imp/common/inc/indexed_array.hpp"

namespace rbp
{
namespace vis
{
namespace imp
{
namespace chamaeleon
{

///
/// ChamaeleonWbGains
///
class ChamaeleonWbGains
{
public:
    using ArraySingleCamGains = common::TArraySingleCamArea<TArrayColorSpaceRGB<vfc::float32_t>>;

    static constexpr vfc::float32_t DEFAULT_WB_GAIN{1.F};

    ChamaeleonWbGains();

    ~ChamaeleonWbGains() = default;

    //------------------------------------------------------------------------------------------------------------------
    /// @brief Set the parameters of the chamaeleon wb gains sent by the coding (including a range check).
    ///
    /// @param[in] f_params  Reference to the chamaeleon wb gains parameter
    //------------------------------------------------------------------------------------------------------------------
    void setParameters(const ChamaeleonWbGainsData& f_params = ChamaeleonWbGainsData());

    //------------------------------------------------------------------------------------------------------------------
    /// @brief Sets the white balance gains for each camera and color channel based on the provided ISP data.
    ///
    /// @param[in] f_ispDataReceiverData A pointer to the ISP data receiver data containing the white balance gains for
    /// each camera and color channel.
    //------------------------------------------------------------------------------------------------------------------
    void setIspData(const pc::daddy::ISPDataDaddy* const f_ispDataReceiverData);

    //------------------------------------------------------------------------------------------------------------------
    /// @brief Updates the white balance gains and inverse white balance gain coefficients.
    //------------------------------------------------------------------------------------------------------------------
    void update();

    void                       getWbgSideCam() && = delete;
    const ArraySingleCamVec3f& getWbgSideCam() const&
    {
        return m_wbgSideCam;
    }

    void                  getRawGainsCam() && = delete;
    const osg::Matrix4x3& getRawGainsCam() const&
    {
        return m_wbRawGains;
    }

private:
    ChamaeleonWbGains(const ChamaeleonWbGains&)            = delete;
    ChamaeleonWbGains& operator=(const ChamaeleonWbGains&) = delete;

    osg::Matrix4x3      m_wbRawGains;
    vfc::float32_t      m_minGain{0.4F};
    vfc::float32_t      m_maxGain{1.6F};
    vfc::float32_t      m_exponent{1.F};
    ArraySingleCamVec3f m_wbgSideCam{};

    vfc::float32_t setAndClampWbGain(vfc::uint32_t f_wbGain) const;
};

} // namespace chamaeleon
} // namespace imp
} // namespace vis
} // namespace rbp

#endif // PC_SVS_IMP_CHAMAELEON_WB_GAINS_H
