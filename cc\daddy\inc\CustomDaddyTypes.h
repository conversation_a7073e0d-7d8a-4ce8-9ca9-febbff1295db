//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: EIK2LR Karim Eid (CC-DA/EAV1)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  CustomDaddyTypes.h
/// @brief
//=============================================================================

#ifndef CC_DADDYTYPES_H
#define CC_DADDYTYPES_H

#include "vfc/core/vfc_types.hpp"
#include "vfc/core/vfc_siunits_convenienttypes.hpp"

#include "pc/svs/daddy/inc/BaseDaddyTypes.h"
#include "pc/svs/core/inc/SystemConf.h"
#include "pc/svs/c2w/inc/SatCam.h"

#include "cc/target/common/inc/BccReadInterface.h"
#include "cc/target/common/inc/BccWriteInterface.h"
#include "cc/target/common/inc/linux_svs_interface.hpp"
#include "cc/cpc/inc/defines.hpp"
#include "cc/target/common/inc/ttactl_output_svs.h"
#include "cc/sm/viewmode/inc/ViewModexViewStateMachine_R2015b_types.h"
#include "cc/target/common/inc/freeparking_types.h"
#include "cc/assets/tileoverlay/inc/TileSpline.h"
#include "cc/assets/ptsoverlay/inc/pashmi_output_types.hpp"

namespace cc
{
namespace daddy // PRQA S 2502
{

//! parameter interface
// TODO: just define a dummy type for the bringup
typedef pc::daddy::CDaddy<int> ParamDataDaddy;

enum class SolidBasePlateState : int
{
  deactivated = 0,
  activated = 1
};

typedef pc::daddy::CDaddy< SolidBasePlateState                        > SolidBasePlateStateDaddy;

//! View Mode State
struct VMState_St
{
  // Application state
  int mode;
};

struct SystemState_St
{
  int systemAvailability;
  bool systemacitve;
};

struct FreeparkingData
{
  vfc::uint32_t m_type;
  vfc::float32_t        m_angle;
  vfc::uint32_t m_entryType;
};

typedef pc::daddy::CDaddy<FreeparkingData> FreeparkingData_Daddy;

//! Socket command default status
struct Socket_Default_St
{
  vfc::uint32_t m_setcalibsts;
};
typedef pc::daddy::CDaddy< Socket_Default_St > SocketCmdDaddy_t;

//! PP & SDW status
typedef pc::daddy::CDaddy< bool > PasButtonStatusDaddy_t;
typedef pc::daddy::CDaddy< cc::target::common::EPasStatus > PasStatusDaddy_t;
typedef pc::daddy::CDaddy< cc::target::common::ESdwStatus > SdwStatusDaddy_t;
typedef pc::daddy::CDaddy< bool > SdwStatusFDaddy_t;
typedef pc::daddy::CDaddy< bool > SdwStatusFMDaddy_t;
typedef pc::daddy::CDaddy< bool > SdwStatusRMDaddy_t;
typedef pc::daddy::CDaddy< bool > SdwStatusRDaddy_t;
typedef pc::daddy::CDaddy< cc::target::common::EPASWarnToneR2L > PasWarnToneDaddy_t;

//! Parking status output to parkhmi
typedef pc::daddy::CDaddy< cc::target::common::EPARkPasMuteButton > PasButtonPressedStDaddy_t;
typedef pc::daddy::CDaddy< ESettingSts >        SettingAutoCamStDaddy_t;
typedef pc::daddy::CDaddy< cc::target::common::EPARkConfirmButton > ParkConfirmButtonStDaddy_t;
typedef pc::daddy::CDaddy< cc::target::common::EPARkMode >          ParkModeSelectedStDaddy_t;
typedef pc::daddy::CDaddy< cc::target::common::EPARkType >          ParkTypeSelectedStDaddy_t;
typedef pc::daddy::CDaddy< cc::target::common::EPARkSlot >          ParkSlotSelectedStDaddy_t;
typedef pc::daddy::CDaddy< cc::target::common::EPARkDrvFuncOff    > ParkDrvFuncOffStDaddy_t;
typedef pc::daddy::CDaddy< cc::target::common::EPARkPauseButton >   ParkPauseButton_t;
typedef pc::daddy::CDaddy< cc::target::common::EPARKDirection >     PARkDirection_t;
typedef pc::daddy::CDaddy< cc::target::common::EParkOutSide >       ParkOutSideButtonStDaddy_t;
typedef pc::daddy::CDaddy< freeparking::EFreeParkingSpaceType >  FreeParkingSpaceTypeButtonStDaddy_t;
typedef pc::daddy::CDaddy< cc::target::common::EFreeParkingConfirmButtonSts >        FreeParkingConfirmButtonStDaddy_t;
typedef pc::daddy::CDaddy< cc::target::common::EPARkFreeParkCancel >                 FreeParkingReturnButtonStDaddy_t;


//! Park status input from parkhmi
typedef pc::daddy::CDaddy< cc::target::common::ParkSlot_st[cc::target::common::g_parkSlotQuantity] >  ParkSlotDaddy_t;
typedef pc::daddy::CDaddy< cc::target::common::EPARKStatusR2L >      ParkStatusDaddy_t;
typedef pc::daddy::CDaddy< cc::target::common::EPARKTypeR2L >        ParkTypeDaddy_t;
typedef pc::daddy::CDaddy< cc::target::common::EPARKTypeVariantR2L > ParkTypeVariantDaddy_t;
typedef pc::daddy::CDaddy< cc::target::common::EPARKModeR2L >        ParkModeDaddy_t;
typedef pc::daddy::CDaddy< cc::target::common::EPARKSideR2L >        ParkSideDaddy_t;
typedef pc::daddy::CDaddy< cc::target::common::EPARKFunctionIndR2L > ParkFunctionIndDaddy_t;
typedef pc::daddy::CDaddy< cc::target::common::EPARKQuitIndR2L >     ParkQuitIndDaddy_t;
typedef pc::daddy::CDaddy< cc::target::common::EPARKQuitIndR2LExt >  ParkQuitIndExtDaddy_t;
typedef pc::daddy::CDaddy< cc::target::common::EPARKRecoverIndR2L >  ParkRecoverIndDaddy_t;
typedef pc::daddy::CDaddy< cc::target::common::EPARKDriverIndR2L >   ParkDriverIndDaddy_t;
typedef pc::daddy::CDaddy< cc::target::common::EPARKDriverIndExtR2L >   ParkDriverIndExtDaddy_t;
typedef pc::daddy::CDaddy< bool >                ParkOutSideAvlDaddy_t;
typedef pc::daddy::CDaddy< bool >                ParkReqReleaseBtnDaddy_t;
typedef pc::daddy::CDaddy< cc::target::common::EPARKObjectExistR2L > ParkFrontObjDaddy_t;
typedef pc::daddy::CDaddy< bool >                ParkRPADriverSelectedDaddy_t;
typedef pc::daddy::CDaddy< bool >                ParkBreakPedalBeenReleasedBf_t;
typedef pc::daddy::CDaddy< cc::target::common::EBrkPedlAppldFlg >    ParkbrkPedlAppld_t;
typedef pc::daddy::CDaddy< cc::target::common::EPARKDriverIndSearchR2L >   ParkDriverIndSearchDaddy_t;
typedef pc::daddy::CDaddy< cc::target::common::StrippedParkhmiPositionSearching >    ParkEndPositionSearchingDaddy_t;
typedef pc::daddy::CDaddy< cc::target::common::StrippedParkhmiTargetPosition >       ParkFinalEndPositionDaddy_t;
typedef pc::daddy::CDaddy< cc::target::common::StrippedParkhmiTargetPosition >       ParkCurMoveTargetPositionDaddy_t;
typedef pc::daddy::CDaddy< uint8_t >                             ParkCarmoveNumberDaddy_t;
typedef pc::daddy::CDaddy< int16_t >                             ParkCartravelDistDesiredDaddy_t;
typedef pc::daddy::CDaddy< bool >                                ParkIsLastMoveDaddy_t;


typedef pc::daddy::CDaddy< cc::target::common::StrippedEAPAParkSpace[cc::target::common::l_L_ParkSpace_side][cc::target::common::l_L_ParkSpace_NumberPerside] > ParkAPA_ParkSpace_t;     // cc::target::common::l_L_ParkSpace_side: 0~leftside 1~rightside,
typedef pc::daddy::CDaddy< cc::target::common::StrippedParkhmiPositionSearching[cc::target::common::l_L_ParkSpace_side][cc::target::common::l_L_ParkSpace_NumberPerside] > ParkAPA_ParkSpaceMark_t;     // cc::target::common::l_L_ParkSpace_side: 0~leftside 1~rightside,
                                                                            // cc::target::common::l_L_ParkSpace_NumberPerside: number of parking spaces, total number: 4
typedef pc::daddy::CDaddy< cc::target::common::rbp_Type_ParkManeuverType_en > ParkPSDirectionSelected_t; // outPsxManeuverType_en: The final parking direction (forward, backward, confirming)
typedef pc::daddy::CDaddy< bool > ParkFreeParkingActive_t;
typedef pc::daddy::CDaddy< bool > FreeParkingButtonPress_t;
typedef pc::daddy::CDaddy< bool > ButtonPress_t;
typedef pc::daddy::CDaddy< cc::target::common::EParkngTypeSeld > ParkParkngTypeSeld_t;
typedef pc::daddy::CDaddy< cc::target::common::EAPAPARKMODE > ParkAPAPARKMODE_t;

typedef pc::daddy::CDaddy< cc::target::common::RPAAvailable > ParkRPAAvaliableDaddy_t;
typedef pc::daddy::CDaddy<cc::assets::tileoverlay::TileUpdateVisitor::PointArrayZone> TileSplineInterpolateArrayDaddy;

//! PTS HMI State
struct PtsSideSegmentActivation
{
  enum SideSegment : unsigned int
  {
    SIDE_SEGMENT_FRONT_LEFT,
    SIDE_SEGMENT_FRONT_RIGHT,
    SIDE_SEGMENT_REAR_LEFT,
    SIDE_SEGMENT_REAR_RIGHT
  };

  enum SideSegmentMask : unsigned int
  {
    SIDE_SEGMENT_BIT_FRONT_LEFT = 1u << SIDE_SEGMENT_FRONT_LEFT,
    SIDE_SEGMENT_BIT_FRONT_RIGHT = 1u << SIDE_SEGMENT_FRONT_RIGHT,
    SIDE_SEGMENT_BIT_REAR_LEFT = 1u << SIDE_SEGMENT_REAR_LEFT,
    SIDE_SEGMENT_BIT_REAR_RIGHT = 1u << SIDE_SEGMENT_REAR_RIGHT,
    ALL_SEGMENTS_MASK = SIDE_SEGMENT_BIT_FRONT_LEFT | SIDE_SEGMENT_BIT_FRONT_RIGHT |
                        SIDE_SEGMENT_BIT_REAR_LEFT | SIDE_SEGMENT_BIT_REAR_RIGHT
  };

  PtsSideSegmentActivation(unsigned int f_mask = ALL_SEGMENTS_MASK)
    : m_activationMask(f_mask)
  {
  }

  inline bool get(SideSegment f_segment) const
  {
    return 0u < (m_activationMask & (1u << f_segment));
  }

  void set(SideSegment f_segment, bool f_state)
  {
    if (f_state)
    {
      m_activationMask |= (1u << f_segment);
    }
    else
    {
      m_activationMask &= ~(1u << f_segment);
    }
  }

  unsigned int m_activationMask;
};

struct PtsHmiStateOutput
{
  bool operator == (const PtsHmiStateOutput& f_other) const
  {
    return (m_pashmiState == f_other.m_pashmiState) &&
           (m_errorReaction == f_other.m_errorReaction) &&
           (m_disturbanceReactionFront == f_other.m_disturbanceReactionFront) &&
           (m_disturbanceReactionRear == f_other.m_disturbanceReactionRear) &&
           (m_sideSegmentActivation.m_activationMask == f_other.m_sideSegmentActivation.m_activationMask);
  }

  bool operator != (const PtsHmiStateOutput& f_other) const
  {
    return !(*this == f_other);
  }

  unsigned int convertFromPasStatus(const cc::target::common::EPasStatus& f_pasStatus) const
  {
    switch (f_pasStatus)
    {
        case cc::target::common::EPasStatus::PAS_Off:
        case cc::target::common::EPasStatus::PAS_Standby:
            return static_cast<unsigned>(pashmi::EPtsHmiState::PTS_HMI_SYSTEM_OFF);
        case cc::target::common::EPasStatus::PAS_FrontRearActive:
            return static_cast<unsigned>(pashmi::EPtsHmiState::PTS_HMI_WARNING_ACTIVE);
        case cc::target::common::EPasStatus::PAS_FActiveRFailure:
            return static_cast<unsigned>(pashmi::EPtsHmiState::PTS_HMI_DISTURBANCE_REAR);
        case cc::target::common::EPasStatus::PAS_RActiveFFailure:
            return static_cast<unsigned>(pashmi::EPtsHmiState::PTS_HMI_DISTURBANCE_FRONT);
        case cc::target::common::EPasStatus::PAS_SystemFailure:
            return static_cast<unsigned>(pashmi::EPtsHmiState::PTS_HMI_ERROR);
        case cc::target::common::EPasStatus::PAS_Reversed:
        default:
            return static_cast<unsigned>(pashmi::EPtsHmiState::PTS_HMI_SYSTEM_OFF);
    }
  }

  unsigned int m_pashmiState;
  bool m_errorReaction;
  bool m_disturbanceReactionFront;
  bool m_disturbanceReactionRear;
  PtsSideSegmentActivation m_sideSegmentActivation;
};

typedef pc::daddy::CDaddy<PtsHmiStateOutput> PtsHmiStateOutputDaddy;

struct PtsStateBase
{
  enum ColorCode
  {
    PTS_GREY        = 0,
    PTS_BLUE        = 1,
    PTS_YELLOW_ONE  = 2,
    PTS_YELLOW_TWO  = 3,
    PTS_ORANGE_ONE  = 4,
    PTS_ORANGE_TWO  = 5,
    PTS_RED_ONE     = 6,
    PTS_RED_TWO     = 7,
    NUM_PTS_COLORS
  };
};

//! Parking Space Information
struct APA_ParkingSpace_St
{
  vfc::float32_t  m_APA_ParkSlotAng_1L;
  vfc::float32_t  m_APA_ParkSlotAng_2L;
  vfc::float32_t  m_APA_ParkSlotAng_3L;
  vfc::float32_t  m_APA_ParkSlotAng_1R;
  vfc::float32_t  m_APA_ParkSlotAng_2R;
  vfc::float32_t  m_APA_ParkSlotAng_3R;
  vfc::float32_t  m_APA_ParkSlotX_1L;
  vfc::float32_t  m_APA_ParkSlotX_2L;
  vfc::float32_t  m_APA_ParkSlotX_3L;
  vfc::float32_t  m_APA_ParkSlotX_1R;
  vfc::float32_t  m_APA_ParkSlotX_2R;
  vfc::float32_t  m_APA_ParkSlotX_3R;
  vfc::uint8_t    m_APA_ParkSlotSt_1L;
  vfc::uint8_t    m_APA_ParkSlotSt_1R;
  vfc::uint8_t    m_APA_ParkSlotSt_2L;
  vfc::uint8_t    m_APA_ParkSlotSt_2R;
  vfc::uint8_t    m_APA_ParkSlotSt_3L;
  vfc::uint8_t    m_APA_ParkSlotSt_3R;
};
typedef pc::daddy::CDaddy< APA_ParkingSpace_St > ParkSpaceDaddy_t;

// Park display status to touch SM
enum EParkDisp2Touch  : vfc::uint8_t
{
  PARK_DISP2TOUCH_NOT_AVAILABLE   = 0,
  PARK_DISP2TOUCH_AVAILABLE       = 1
};

enum EParkModeDisp2Touch  : vfc::uint8_t
{
  PARK_MODE_DISP2TOUCH_NOT_AVAILABLE                              = 0,
  PARK_MODE_DISP2TOUCH_PARKIN_PARKOUT_FREEPARKING_AVAILABLE       = 1,
  PARK_MODE_DISP2TOUCH_APA_RPA_AVAILABLE                          = 2
};


struct ParkDisp2TouchSts_st
{
  EParkModeDisp2Touch      m_ButtonParkModeDispSts;
  EParkDisp2Touch          m_ButtonParkStartDispSts;
  EParkDisp2Touch          m_ButtonParkContinueDispSts;
  EParkDisp2Touch          m_ButtonParkPauseDispSts;
  EParkDisp2Touch          m_ButtonParkQuitDispSts;
  EParkDisp2Touch          m_ButtonParkOutDirectionDispSts;
  EParkDisp2Touch          m_ButtonFreeParkingConfirmDispSts;
  EParkDisp2Touch          m_ButtonFreeParkingSpaceTypeDispSts;
  EParkDisp2Touch          m_ButtonParkInTypeDispSts;
};

typedef pc::daddy::CDaddy< ParkDisp2TouchSts_st > ParkDisp2TouchStsDaddy_t;

typedef pc::daddy::CDaddy< EParkDisp2Touch > ParkDisp2TouchSlotSelectionDaddy_t;

struct ParkDispLowPolyModelSts_st
{
  bool m_isCarAvailabe;
  bool m_isAnimationAvailabe;
};

typedef pc::daddy::CDaddy< ParkDispLowPolyModelSts_st > ParkDispLowPolyModelStsDaddy_t;

// SVS to State Machine
struct UISpotData_St
{
  osg::Vec2f m_iconCenter;
  osg::Vec2f m_responseArea;
  cc::target::common::EFAPAParkSlotType m_spotType;
  // vfc::uint32_t m_spotIndex;
};

struct UISpotData_Array_St
{
  UISpotData_St m_SlotPos1L;
  UISpotData_St m_SlotPos2L;
  UISpotData_St m_SlotPos3L;
  UISpotData_St m_SlotPos4L;
  UISpotData_St m_SlotPos1R;
  UISpotData_St m_SlotPos2R;
  UISpotData_St m_SlotPos3R;
  UISpotData_St m_SlotPos4R;
};

enum EStateDoorLock : vfc::uint8_t
{
  DoorLock_OPEN = 0u,
  DoorLock_CLOSED = 1u,
  DoorLock_INVALID = 2u
};

typedef pc::daddy::CDaddy< bool > ParkUIAvailableParkingSlot_t;

typedef pc::daddy::CDaddy< UISpotData_Array_St > ParkUISpotData_t;

//! park confirm interface exist or not , default none
typedef pc::daddy::CDaddy< bool > ParkConfirmInterfaceExist_t;

// State machine switch of obstacle
typedef pc::daddy::CDaddy< vfc::uint8_t > SMObstacleSwitchDaddy_t;

// vehicle bus
typedef pc::daddy::CDaddy< vfc::float32_t > DriverSteeringWheelAngleDaddy_t;

// Dynamic Gear
typedef pc::daddy::CDaddy< bool > DynamicGearActive_t;

// Notice rolling
typedef pc::daddy::CDaddy< bool > NoticeRolling_t;
// HU input
typedef pc::daddy::CDaddy< vfc::uint8_t > HUDislayModeSwitchDaddy_t;
typedef pc::daddy::CDaddy< cc::target::common::EHUDisplayMode5x > HUDislayModeView5xDaddy_t;
typedef pc::daddy::CDaddy< vfc::uint8_t > HUDislayModeExpandDaddy_t;
typedef pc::daddy::CDaddy< vfc::uint8_t > HUImageWorkModeDaddy_t;
typedef pc::daddy::CDaddy< cc::target::common::ETransparentMode >   HUTransparencyModeDaddy_t;
typedef pc::daddy::CDaddy< cc::target::common::EhuRadarWallButton > HURadarWallButtonDaddy_t;
typedef pc::daddy::CDaddy< vfc::uint8_t > HUDislayModeExpandNewDaddy_t;
typedef pc::daddy::CDaddy< vfc::uint8_t > HURotateStatusDaddy_t;
typedef pc::daddy::CDaddy< vfc::uint8_t > HUShoWReqDaddy_t;
typedef pc::daddy::CDaddy< vfc::uint8_t > HURemoteScreenReqDaddy_t;
typedef pc::daddy::CDaddy< vfc::uint8_t > HUShoWSuspendDaddy_t;
typedef pc::daddy::CDaddy< vfc::uint8_t > HUselSVSModeDaddy_t;
typedef pc::daddy::CDaddy< vfc::uint8_t > HUsvsAutoCamDaddy_t;
typedef pc::daddy::CDaddy< vfc::uint8_t > HUsvsIntegtOpenDaddy_t;
typedef pc::daddy::CDaddy< vfc::uint8_t > HUtouchEvenTypeDaddy_t;
typedef pc::daddy::CDaddy< vfc::uint8_t > HUTwoFingerTouchEvenTypeDaddy_t;
typedef pc::daddy::CDaddy< vfc::uint8_t > HUShowStsDaddy_t;
typedef pc::daddy::CDaddy< vfc::uint8_t > HUrestoreSVSSetDaddy_t;
typedef pc::daddy::CDaddy< vfc::uint8_t > HUvehTransReq_t;
typedef pc::daddy::CDaddy< vfc::uint8_t > HUvehTransLevel_t;
typedef pc::daddy::CDaddy< vfc::uint8_t > HUbasePlateReqDaddy_t;
typedef pc::daddy::CDaddy< vfc::uint8_t > HUoverlayDistReqDaddy_t;
typedef pc::daddy::CDaddy< vfc::uint8_t > HUoverlayReqDaddy_t;
typedef pc::daddy::CDaddy< vfc::uint8_t > HU3DCruseDaddy_t;
typedef pc::daddy::CDaddy< vfc::uint8_t > HUgestureStatusDaddy_t;
typedef pc::daddy::CDaddy< vfc::uint32_t > HUFreemodeAngleDaddy_t;

//HU output
typedef pc::daddy::CDaddy< vfc::uint8_t > SVSShowReqDaddy_t;
typedef pc::daddy::CDaddy< vfc::uint8_t > SVSCurrentViewDaddy_t;
typedef pc::daddy::CDaddy< vfc::uint8_t > SVSIntegtOpenStsDaddy_t;
typedef pc::daddy::CDaddy< vfc::uint8_t > SVSAutoCamStsDaddy_t;
typedef pc::daddy::CDaddy< vfc::uint8_t > SVSViewModeStsDaddy_t;
typedef pc::daddy::CDaddy< vfc::uint8_t > SVSResolution_t;
//typedef pc::daddy::CDaddy< cc::target::common::ESVSUnavlMsgs > SVSUnavlMsgsDaddy_t;
typedef pc::daddy::CDaddy< vfc::uint8_t > SVSOverlayDistStsDaddy_t;
typedef pc::daddy::CDaddy< vfc::uint8_t > SVSDynOverlayStsDaddy_t;
typedef pc::daddy::CDaddy< vfc::uint8_t > SVSBasePlateStsDaddy_t;
typedef pc::daddy::CDaddy< vfc::uint8_t > SVSVehTransStsDaddy_t;
typedef pc::daddy::CDaddy< bool > SVSVehTransStsInternalDaddy_t;
//typedef pc::daddy::CDaddy< vfc::uint8_t > SVSVehColorStsDaddy_t;
typedef pc::daddy::CDaddy< vfc::uint8_t > SVS3DCruStsDaddy_t;
typedef pc::daddy::CDaddy< vfc::uint8_t > SVSViewStsDaddy_t;
typedef pc::daddy::CDaddy< vfc::uint8_t > SVSzoomStsDaddy_t;
typedef pc::daddy::CDaddy< bool         > VRSwitchFailStDaddy_t;

//BYD
typedef pc::daddy::CDaddy< vfc::uint8_t > SVSViewStateDaddy_t;
typedef pc::daddy::CDaddy< vfc::uint8_t > SVSWorkModeDaddy_t;
typedef pc::daddy::CDaddy< vfc::uint8_t > SVSOnOffStateDaddy_t;
typedef pc::daddy::CDaddy< vfc::uint8_t > SVSvidoutModeDaddy_t;
typedef pc::daddy::CDaddy< vfc::uint8_t > SVSRotateStatusDaddy_t;
typedef pc::daddy::CDaddy< cc::target::common::EThemeTypeDayNight> DayNightThemeDaddy_t;
typedef pc::daddy::CDaddy< vfc::uint8_t > SVSTrajCfgStateDaddy_t;
typedef pc::daddy::CDaddy< vfc::uint8_t > SVSLVDSvidOutModeDaddy_t;
typedef pc::daddy::CDaddy< vfc::uint8_t > SVSImageConfigDaddy_t;
typedef pc::daddy::CDaddy< vfc::uint8_t > SVSCarBodyDaddy_t;
typedef pc::daddy::CDaddy< vfc::uint8_t > SVSExpandedViewStateDaddy_t;
typedef pc::daddy::CDaddy< vfc::uint8_t > SVSNewExpandedViewStateDaddy_t;
typedef pc::daddy::CDaddy< vfc::uint8_t > SVSVehColorAckDaddy_t;
typedef pc::daddy::CDaddy< vfc::uint8_t > SVSUnavlMsgsDaddy_t;
typedef pc::daddy::CDaddy< vfc::uint8_t > HUCalibrationFlagDaddy_t;

typedef pc::daddy::CDaddy< bool         > CpcOverlaySwitchDaddy_t;
typedef pc::daddy::CDaddy< bool         > SwVersionShowSwitchDaddy_t;
typedef pc::daddy::CDaddy< cc::target::common::StrippedCpjSWInfo > SWInfoDaddy_t;
typedef pc::daddy::CDaddy< cc::target::common::StrippedCpjDoorLockSts > DoorLockStsDaddy_t;
typedef pc::daddy::CDaddy< bool >         TrailerModeDaddy_t;

//Pdm
typedef pc::daddy::CDaddy< cc::target::common::EPdmSvsSetting > Pdm3DCruStsDaddy_t;
typedef pc::daddy::CDaddy< cc::target::common::EPdmSvsSetting > PdmIntegtOpenStsDaddy_t;
typedef pc::daddy::CDaddy< cc::target::common::EPdmSvsSetting > PdmAutoCamStsDaddy_t;
typedef pc::daddy::CDaddy< cc::target::common::EPdmSvsSetting > PdmDynOverlayStsDaddy_t;
typedef pc::daddy::CDaddy< cc::target::common::EPdmSvsSetting > PdmOverlayDistStsDaddy_t;
typedef pc::daddy::CDaddy< cc::target::common::EPdmSvsSetting > PdmBasePlateStsDaddy_t;
typedef pc::daddy::CDaddy< cc::target::common::EPdmSvsSetting > PdmVehTransStsDaddy_t;

struct HU_ManualVideoSetupReq_St
{
  vfc::uint8_t m_ScreenIDPIVI_u8;
  vfc::uint8_t m_OverlayStatus_u8;
  vfc::uint8_t m_index_u8;
};

typedef vfc::float32_t meter_float_t;

//!TrailerConnected
typedef pc::daddy::CDaddy< int                                        > TrailerConnectedDaddy_t;

//!ThreatDetected
typedef pc::daddy::CDaddy< int                                        > ThreatDetectedDaddy_t;

//! ViewModeState
typedef pc::daddy::CDaddy<VMState_St                                  > VMStateDaddy_t;

//! System State (Availability: 0 = Unavailable, 1 = Available, 2 = Partially Available)
typedef pc::daddy::CDaddy<SystemState_St                              > SystemStateDaddy;

//! User Movement Intention (for automatic activation)
typedef pc::daddy::CDaddy< int                                        > UserMovementIntentionDaddy_t;

//! LSMG Vehicle Movement
//! to be cleared
typedef vfc::uint8_t EVehicleMovement;
typedef pc::daddy::CDaddy< EVehicleMovement                           > LSMG_VehicleMovementDaddy_t;

//! Vehicle Alpha
typedef pc::daddy::CDaddy< vfc::float32_t                                      > VehicleAlphaDaddy;

//! Vehicle Lights
typedef pc::daddy::CDaddy< cc::target::common::StrippedCpjEnvVeh                          > CustomVehicleLightsDaddy;


struct color_t
{
    int R;
    int G;
    int B;
    int A;
};

//! Vehicle Diffuse Color
typedef pc::daddy::CDaddy< color_t                                    > ColorDaddy;

//! Vehicle Color Index
typedef pc::daddy::CDaddy< vfc::uint8_t                               > ColorIndexDaddy;

enum EColorCode : vfc::uint32_t
{
INVALID                 = 0x00,
SMOKY_CRYSTAL_BLACK     = 0x01,
TIME_GRAY               = 0x02,
RED_EMPEROR             = 0x03,
SNOWY_WHITE             = 0x04,
TIANSHAN_WHITE          = 0x05,
TEHRAN                  = 0x06,
MOUNTAIN_ASH            = 0x07,
INKSTONE_BLUE           = 0x08,
SILVERSAND_BLACK        = 0x09,
TITANIUM_SILVER         = 0x0A,
STRATEGIC_BLUE          = 0x0B,
TITANIUM_EMPTY_GRAY     = 0x0C,
CRYSTAL_WHITE           = 0x0D,
FLANGE_RED              = 0x0E,
SKY_BLUE                = 0x0F,
VIBRANT_ORANGE          = 0x10,
WISDOM_BLUE             = 0x11,
HONEY_ORANGE            = 0x12,
RUSTLE_GREEN            = 0x13,
PUFFFAN                 = 0x14,
SPARKLING_BLUE          = 0x15,
SURFBUE                 = 0x16,
XUANKONG_BLACK          = 0x17,
QIANSHAN_CUI            = 0x18,
AZURE                   = 0x19,
CYAN_SMOKE              = 0x1A,
DOME_WHITE              = 0x1B,
AURORA_WHITE            = 0x1C,
ROSEMARY_GRAY           = 0x1D,
LOSTATLANTIS            = 0x1E,
BLUEOCEANLENS           = 0x1F,
MERCURY_BLUE            = 0x20,
TROLLGRASS_GREEN        = 0x21,
ROCKY_GREEN             = 0x22,
DEMON_BlACK             = 0x23,
SEA_BLUE                = 0x24,
ROSE_GOLD               = 0x25,
SANDALWOOD_PURPLE       = 0x26,
SUNRISE_GOLD            = 0x27,
DOME_BLUE               = 0x28,
RESERVED_COLOR1         = 0x29,
RESERVED_COLOR2         = 0x2A,
MUSHAN_PINK             = 0x2B,
SILVER_GLAZE_WHITE      = 0x4A,
DU_DU_WHITE             = 0x4B,
JUN_WARE_GRAY           = 0x64,
WINTER_GREY             = 0x65, //Z9gt HTEBU
MOON_WHITE              = 0x66,
WU_SONG_GOLDEN          = 0x67,
YAO_SHI_BLACK           = 0x68,
HERMES_GREEN            = 0x69,
BLACK_GOLD              = 0x6A,
SGHC_HERMES_GREEN       = 0x6B,
SGHC_OBSIDIAN_BLACK     = 0x6C,
SGHC_SNOW_WHITHE        = 0x6D,
RESERVED_COLOR          = 0xFF
};

inline EColorCode EColorCode_Convert_uint8ToEnum(vfc::uint8_t f_value) // PRQA S 6040
{
  switch (static_cast<vfc::int32_t>(f_value))
  {
    case SMOKY_CRYSTAL_BLACK:
      return SMOKY_CRYSTAL_BLACK;
    case TIME_GRAY:
      return TIME_GRAY;
    case RED_EMPEROR:
      return RED_EMPEROR;
    case SNOWY_WHITE:
      return SNOWY_WHITE;
    case TIANSHAN_WHITE:
      return TIANSHAN_WHITE;
    case TEHRAN:
      return TEHRAN;
    case MOUNTAIN_ASH:
      return MOUNTAIN_ASH;
    case INKSTONE_BLUE:
      return INKSTONE_BLUE;
    case SILVERSAND_BLACK:
      return SILVERSAND_BLACK;
    case TITANIUM_SILVER:
      return TITANIUM_SILVER;
    case STRATEGIC_BLUE:
      return STRATEGIC_BLUE;
    case TITANIUM_EMPTY_GRAY:
      return TITANIUM_EMPTY_GRAY;
    case CRYSTAL_WHITE:
      return CRYSTAL_WHITE;
    case FLANGE_RED:
      return FLANGE_RED;
    case SKY_BLUE:
      return SKY_BLUE;
    case VIBRANT_ORANGE:
      return VIBRANT_ORANGE;
    case WISDOM_BLUE:
      return WISDOM_BLUE;
    case HONEY_ORANGE:
      return HONEY_ORANGE;
    case RUSTLE_GREEN:
      return RUSTLE_GREEN;
    case PUFFFAN:
      return PUFFFAN;
    case SPARKLING_BLUE:
      return SPARKLING_BLUE;
    case SURFBUE:
      return SURFBUE;
    case XUANKONG_BLACK:
      return XUANKONG_BLACK;
    case QIANSHAN_CUI:
      return QIANSHAN_CUI;
    case AZURE:
      return AZURE;
    case CYAN_SMOKE:
      return CYAN_SMOKE;
    case DOME_WHITE:
      return DOME_WHITE;
    case AURORA_WHITE:
      return AURORA_WHITE;
    case ROSEMARY_GRAY:
      return ROSEMARY_GRAY;
    case LOSTATLANTIS:
      return LOSTATLANTIS;
    case BLUEOCEANLENS:
      return BLUEOCEANLENS;
    case MERCURY_BLUE:
      return MERCURY_BLUE;
    case TROLLGRASS_GREEN:
      return TROLLGRASS_GREEN;
    case ROCKY_GREEN:
      return ROCKY_GREEN;
    case DEMON_BlACK:
      return DEMON_BlACK;
    case SEA_BLUE:
      return SEA_BLUE;
    case ROSE_GOLD:
      return ROSE_GOLD;
    case SANDALWOOD_PURPLE:
      return SANDALWOOD_PURPLE;
    case SUNRISE_GOLD:
      return SUNRISE_GOLD;
    case DOME_BLUE:
      return DOME_BLUE;
    case MUSHAN_PINK:
      return MUSHAN_PINK;
    case SILVER_GLAZE_WHITE:
      return SILVER_GLAZE_WHITE;
    case DU_DU_WHITE:
      return DU_DU_WHITE;
    case JUN_WARE_GRAY:
      return JUN_WARE_GRAY;
    case WINTER_GREY:
      return WINTER_GREY;
    case MOON_WHITE:
      return MOON_WHITE;
    case WU_SONG_GOLDEN:
      return WU_SONG_GOLDEN;
    case YAO_SHI_BLACK:
      return YAO_SHI_BLACK;
    case HERMES_GREEN:
      return HERMES_GREEN;
    case BLACK_GOLD:
      return BLACK_GOLD;
    case SGHC_HERMES_GREEN:
      return SGHC_HERMES_GREEN;
    case SGHC_OBSIDIAN_BLACK:
      return SGHC_OBSIDIAN_BLACK;
    case SGHC_SNOW_WHITHE:
      return SGHC_SNOW_WHITHE;
    default:
      return TIME_GRAY;
  }
}

struct veh_model_param_t
{
    veh_model_param_t()
      : roughness1(100)
      , roughness2(60)
      , specPower1(1)
      , specPower2(1)
      , reflectionPower(1)
      , fresnel(3)
    {
    }

    int roughness1;
    int roughness2;
    int specPower1;
    int specPower2;
    int reflectionPower;
    int fresnel;
};
typedef pc::daddy::CDaddy< veh_model_param_t                          > VehicleModelParamDaddy;

//! Closest obstacle
struct ClosestObstacleSt
{
  meter_float_t distance[4]; //FIXME move to avoid dependencies SoundExtractor::Quadrant::NUM_QUADRANTS
};
typedef pc::daddy::CDaddy< ClosestObstacleSt                          > ClosestObstacleDaddy;

typedef vfc::linalg::TVector3<vfc::float32_t> v3_float;

//! User setting automatic camera
enum EAutoCam : int
{
  AUTOCAM_DISABLED = 0,
  AUTOCAM_ENABLED
};
typedef pc::daddy::CDaddy< int > AutoCam_Daddy;

//! Day Night mode
enum EDayNightMode : int
{
  DAY_MODE = 0,
  NIGHT_MODE = 1
};

//! Ambient Light Dipped Beam State
enum EDippedBeamState : int
{
  DAYLIGHT_MODE = 0,
  LOWLIGHT_MODE = 1
};

typedef pc::daddy::CDaddy< EDippedBeamState                           > DippedBeamStateDaddy;


enum ECarMode : vfc::uint32_t
{
  NORMAL = 0,
  FACTORY = 1,
  UNUSED2 = 2,
  TRANSPORT = 3,
  UNUSED4 = 4,
  CRASH = 5
};

typedef pc::daddy::CDaddy< ECarMode                                    > CarModeDaddy_t;

enum EPowerMode : vfc::uint32_t // PRQA S 2502
{
  KEY_OUT = 0,
  KEY_RECENTLY_OUT = 1,
  KEY_APPROVED = 2,
  POST_ACCESSORY = 3,
  ACCESSORY = 4,
  POST_IGNITION = 5,
  IGNITION_ON = 6,
  RUNNING = 7,
  CRANK = 8
};

typedef pc::daddy::CDaddy< EPowerMode                                  > PowerModeDaddy_t;

typedef pc::daddy::CDaddy< cc::target::common::EFCTARightWarnLevel                         > FCTARightDaddy_t;

typedef pc::daddy::CDaddy< cc::target::common::EFCTALeftWarnLevel                          > FCTALeftDaddy_t;

typedef pc::daddy::CDaddy< cc::target::common::ERCTARightWarnLevel                         > RCTARightDaddy_t;

typedef pc::daddy::CDaddy< cc::target::common::ERCTALeftWarnLevel                          > RCTALeftDaddy_t;

typedef pc::daddy::CDaddy< bool                                        > VRSwitchSVMDaddy_t;

typedef pc::daddy::CDaddy< bool                                        > VRSwitchFailStDaddy_t;

typedef pc::daddy::CDaddy< bool                                        > FCP_SVMButtonPressed_t;

typedef pc::daddy::CDaddy< bool                                        > FreeparkingParkable_t;

enum EAebVmcOpMode : vfc::uint32_t
{
  AEB_MGR_OPMODE_OFF              = 0,
  AEB_MGR_OPMODE_INACTIVE         = 1,
  AEB_MGR_OPMODE_PREFILL          = 2,
  AEB_MGR_OPMODE_AEB              = 3,
  AEB_MGR_OPMODE_FINISH           = 4,
  AEB_MGR_OPMODE_ERROR            = 5,
  AEB_MGR_OPMODE_VEHSECURE        = 6,
  AEB_MGR_OPMODE_DRIVEROVERRIDE   = 7,

  AEB_MGR_OPMODE_NUM              = 8
};

typedef pc::daddy::CDaddy< EAebVmcOpMode                               > AebVmcOpModeDaddy_t;

struct CamCalibStatus_st
{
  cc::target::common::StrippedCalibStatus_st m_calibstatus[pc::core::sysconf::NUMBER_OF_CAMERAS];
};

struct CamNorminalpara_st
{
  cc::target::common::NominalCalibValues_st m_norminalValues[pc::core::sysconf::NUMBER_OF_CAMERAS];
};

enum EStopLineLocation : int
{
  FRONT = 0,
  REAR  = 1
};
typedef pc::daddy::CDaddy< EStopLineLocation                           > StopLineLocationDaddy ;

typedef pc::daddy::CDaddy< int                                         > AutomaticParkingDaddy ; //TODO clean up

typedef pc::daddy::CDaddy < cc::target::common::StrippedCcf_st                             > CcfDaddy;

typedef pc::daddy::CDaddy < CStrippedLcf_st                            > LcfDaddy;

typedef pc::daddy::CDaddy < cc::target::common::StrippedVariant_st                         > VariantDaddy;

//! LMStatusDaddy_t will be cleared
enum ELMStatus : vfc::uint32_t
{
  ELMStatus_NOT_ACTIVE = 0,
  ELMStatus_SCANNING = 1,
  ELMStatus_SELECTING = 2,
  ELMStatus_CONFIRMING  = 3,
  ELMStatus_MANOUEVERING = 4,
  ELMStatus_INTERUPTED = 5,
  ELMStatus_FINISHING = 6,
  ELMStatus_ENDED =7,
  ELMStatus_RC_START_STOP = 8,
  ELMStatus_HOLDING = 9,
  ELMStatus_CANCELLED =10
};
typedef pc::daddy::CDaddy< ELMStatus                                   > LMStatusDaddy_t;

typedef pc::daddy::CDaddy< vfc::float32_t                                       > DistanceToStopDaddy ;

typedef pc::daddy::CDaddy< bool                                        > IsOffroadDaddy_t;

//! TrailerSvs
typedef pc::daddy::CDaddy< ttactl::CTrailerSvsStripped                 > TrailerSvsDaddy;

//! Backchannel Rx Signals, remaining in case being used in other logic
typedef pc::daddy::CDaddy< HU_ManualVideoSetupReq_St                   > PIVI_ManualVideoSetupReq_t;
typedef pc::daddy::CDaddy<backchannel::PIVI_DayNightThemeReq           > PIVI_DayNightThemeReq_t;
typedef pc::daddy::CDaddy<backchannel::PIVI_ViewBufferStatusACK        > PIVI_ViewBufferStatusACK_t;


//! Backchannel Tx Signals, remaining in case being used in other logic
typedef pc::daddy::CDaddy<backchannel::NFSM_MovementDirectionUpdate    > NFSM_MovementDirectionUpdate_t;
typedef pc::daddy::CDaddy<backchannel::NFSM_ViewBufferStatus           > NFSM_ViewBufferStatus_t;

// FlexRay Signal
typedef pc::daddy::CDaddy<cc::target::common::ELSMGActivationSetReq                        > LSMGActivationSetReq_t   ;

// C2W - Calibration Status
typedef pc::daddy::CDaddy<CamCalibStatus_st                            > C2WCalibStatusDaddy_t;

typedef pc::daddy::CDaddy<CamNorminalpara_st                           > NormalCalibrationDaddy;
// Diag Routine
typedef pc::daddy::CDaddy<cc::target::common::StrippedDiagroutine_st                       > DiagRoutine_t;

// SVS Degradation FIDs
typedef pc::daddy::CDaddy<cc::target::common::StrippedSvsFid_st                            > DegradationFid_t;

//! 3D vehicle model impostor transparency
typedef vfc::float32_t ImpostorTransparency;
typedef pc::daddy::CDaddy<ImpostorTransparency> ImpostorTransparencyDaddy;

//! GBC Vehicle Transparency
typedef pc::daddy::CDaddy<vfc::float32_t> GbcVehicleTransparency_t;

//! GBC Wheel Transparency
typedef pc::daddy::CDaddy<vfc::float32_t> GbcWheelTransparency_t;

//! View Animation Completed
enum EAanimState : int
{
  ANIM_INIT     = 0,
  ANIM_ONGOING  = 1,
  ANIM_FINISHED = 2
};
struct AnimState_St
{
  EAanimState m_state;
  int m_screenId;
};
typedef pc::daddy::CDaddy<AnimState_St> ViewAnimationCompleted_t;

enum USS_ZoneFlags
{
    USS_ZONE_FLAG_OK = 0                  //  from 0 to 500
  , USS_ZONE_FLAG_ERROR_FAILURE           //  653
  , USS_ZONE_FLAG_ERROR_UNDEFINED         //  654
  , USS_ZONE_FLAG_NO_OBSTACLE             //  655
  , USS_ZONE_FLAG_UNNAMED_FLAG            //  everything else (between [501 and 652] or bigger than 655)
};

inline USS_ZoneFlags getUltraSonicZoneFlagFromDist(vfc::uint32_t f_distance)
{
    switch(f_distance)
    {
      case 653u:       return USS_ZONE_FLAG_ERROR_FAILURE;
      case 654u:       return USS_ZONE_FLAG_ERROR_UNDEFINED;
      case 655u:       return USS_ZONE_FLAG_NO_OBSTACLE;
      default:
      {
        return f_distance <= 500u ? USS_ZONE_FLAG_OK : USS_ZONE_FLAG_UNNAMED_FLAG;
      }
    }
}

//! HMI data
struct HmiData
{
  int m_modeRq; //! View Mode Command, EModeRq enum in state machine header
  vfc::uint32_t m_remLocalListRq;
  vfc::uint32_t m_remLocalPosRq;
  // vfc::uint32_t m_camPosAxis1Rq;
  // vfc::uint32_t m_camPosAxis2Rq;
  vfc::uint16_t m_huX;
  vfc::uint16_t m_huY;
  vfc::uint16_t m_huTwoFinger1X;
  vfc::uint16_t m_huTwoFinger1Y;
  vfc::uint16_t m_huTwoFinger2X;
  vfc::uint16_t m_huTwoFinger2Y;
  vfc::uint32_t m_zoomFactorRq;
  vfc::float32_t m_touchSwipeSpeed;

  // valin_cfg::EParkCtrlHardKey m_hardKeyRq;
  // valin_cfg::EHmiKeyStatus m_hmiConfKeyStatus;
};

typedef pc::daddy::CDaddy<HmiData> HmiData_Daddy;

//! Hemisphere camera controller return channel
struct HemisphereParameterData
{
  vfc::uint32_t m_camPosAxis1Rq;
  vfc::uint32_t m_camPosAxis2Rq;
  vfc::uint32_t m_zoomFactorRq;
};

typedef pc::daddy::CDaddy<HemisphereParameterData> HemisphereParameterData_Daddy;

// head unit touch command emulation
struct HUCameraCommands
{
  HUCameraCommands()
    : hemisphere3D()
    , hemisphere3DSet(false)
    , zoom3D(0u)
    , zoom3DSet(false)
  {
  }

  osg::Vec2i   hemisphere3D; // Hemispherical coordinates (elevation + azimuth, range [0..449]x[0..1799])
  bool         hemisphere3DSet;
  vfc::uint32_t zoom3D;       // Zoom level in the range [0..14]
  bool         zoom3DSet;
};

typedef pc::daddy::CDaddy<HUCameraCommands> HUCameraCommandsDaddy;

// free view camera position
struct CameraPosition
{
  osg::Vec2i   hemisphere3D; // Hemispherical coordinates (elevation + azimuth, range [0..449]x[0..1799])
  vfc::uint8_t zoomSts;       // Zoom level in the range [0..14]
};

typedef pc::daddy::CDaddy<CameraPosition> CameraPositionDaddy_t;

// SVS displayed view id
typedef pc::daddy::CDaddy<EScreenID> SVSDisplayedViewDaddy_t;

// SVS free view mode status
typedef pc::daddy::CDaddy<bool> SVSFreeModeStDaddy_t;

struct ParkingSpot
{

  enum Type : vfc::uint32_t
  {
    PARKINGTYPE_PARALLEL = 0,
    PARKINGTYPE_PERPENDICULAR = 1,
    PARKINGTYPE_DIAGONAL = 2,
    PARKINGTYPE_EXPMODE  = 3,
    PARKINGTYPE_INVALID  = 4
  };

  //side of the parking slot, used in the parking out use case
  enum Side : vfc::uint32_t
  {
    PARKINGSIDE_LEFT_SIDE_PSX  = 0,
    PARKINGSIDE_RIGHT_SIDE_PSX = 1,
    PARKINGSIDE_LEFT_BOTH_PSX  = 2,
    PARKINGSIDE_INVALID_PSX    = 3
  };

  enum ParkState : vfc::uint32_t
  {
    PARKINGSTATE_NONE = 0,
    PARKINGSTATE_UNSUITABLE = 1,
    PARKINGSTATE_PARKABLE = 2,
    PARKINGSTATE_PARKABLE_POS_OK = 3
  };

  enum ETypeOfChanges : vfc::uint32_t
  {
    NO_CHANGES          = 0,
    EXPMODEONLY_CHANGES = 1,
    PARKINGSLOT_CHANGES = 2
  };

  //! ParkingSpot
  ParkingSpot()
    : m_uid(0u)
    , m_type(PARKINGTYPE_INVALID)
    , m_side(PARKINGSIDE_LEFT_SIDE_PSX)
    , m_stateForward(PARKINGSTATE_NONE)
    , m_stateBackward(PARKINGSTATE_NONE)
    , m_position(0.0f, 0.0f)
    , m_length(0.0f)
    , m_radiusPhi(0.0f)
  {
  }

  bool isValid() const
  {
    return m_type != INVALID;
  }

  vfc::uint32_t getState() const
  {
    vfc::uint32_t l_state = PARKINGSTATE_NONE;
    switch (m_type)
    {
      case PARKINGTYPE_PERPENDICULAR :
      {
        //if cross: One of them is parkable then parkable
        if ((m_stateForward == PARKINGSTATE_PARKABLE_POS_OK) || (m_stateBackward == PARKINGSTATE_PARKABLE_POS_OK))
        {
          l_state = PARKINGSTATE_PARKABLE_POS_OK;
        }
        else
        {
          l_state = m_stateBackward;
        }
      }
      break;
      case PARKINGTYPE_PARALLEL :
      {
        l_state = m_stateBackward;
      }
      break;
      case PARKINGTYPE_EXPMODE :
      {
        l_state = m_stateForward;
      }
      break;
      default :
      {
        l_state = PARKINGSTATE_NONE;
      }
      break;
    }
    return l_state;
  }

  vfc::uint32_t m_uid;
  vfc::uint32_t m_type;
  vfc::uint32_t m_side;
  vfc::uint32_t m_stateForward;
  vfc::uint32_t m_stateBackward;
  //! world coordinates
  osg::Vec2f m_position;
  vfc::float32_t m_length;
  vfc::float32_t m_radiusPhi;
  vfc::uint32_t m_spotState;
};


enum EParkingSpot : vfc::uint32_t
{
  PARKING_SPOTS_LEFT = 0,
  PARKING_SPOTS_RIGHT = 1,
  PARKING_EXP_MODE    = 2,
  PARKING_SPOTS_LIST_SIZE = 2,
  PARKING_SPOTS_NUM_LISTS = 2,
  PARKING_EXTRA_FEAT_NUM  = 1,
  PARKING_SPOTS_MAX_NUM_ELEMENTS = PARKING_SPOTS_LIST_SIZE * PARKING_SPOTS_NUM_LISTS
};

typedef pc::core::Array<ParkingSpot, PARKING_SPOTS_LIST_SIZE> ParkingSpotList;

typedef pc::core::Array<ParkingSpotList, PARKING_SPOTS_NUM_LISTS> ParkingSpotListArray;

typedef pc::core::Array<ParkingSpotList, PARKING_SPOTS_NUM_LISTS + PARKING_EXTRA_FEAT_NUM> ParkingSpotListArrayExtended;

typedef pc::daddy::CDaddy<ParkingSpotListArray> ParkingSpotsListDaddy;

typedef pc::daddy::CDaddy<ParkingSpot> ParkingSpotDaddy;

typedef vfc::float32_t PMA_TravelDistDesired;

typedef pc::daddy::CDaddy<cc::target::common::CAPGStripped> APG_VehiclePathDaddy;

typedef pc::daddy::CDaddy<cc::target::common::StrippedPmactlmrgr_st> PmaCtlMrgrDaddy;

// Odometry raw data
struct CustomVhmAbstRaw
{
  vfc::int32_t                        m_posXRaw;
  vfc::int32_t                        m_posYRaw;
  vfc::CSI::si_radian_f32_t           m_yawAngleRaw;
};

typedef pc::daddy::CDaddy<CustomVhmAbstRaw> CustomVhmAbstRaw_t;

//! Stripped freeparking info output
typedef pc::daddy::CDaddy< freeparking::CFreeParkingRectStripped > FreeparkingRectInfo_Daddy;

struct APSlotOrientation
{
  osg::Vec2f        m_CenterPos;
  vfc::float32_t             m_yawAngleRaw;
};

typedef pc::daddy::CDaddy< APSlotOrientation > FreeparkingSocket_Daddy;

// cpc data & status
// typedef pc::daddy::CDaddy<cc::cpc::CpcCornerDetectionStatus> CpcCornerDetectionStatus_t;
// typedef pc::daddy::CDaddy< cc::cpc::CpcCamCalibStatus > CpcStatus_st_t;

using CpcToCpcWrapper_t = pc::daddy::CDaddy<cc::cpc::CpcToCpcWrapper>;
using CpcWrapperToCpc_t = pc::daddy::CDaddy<cc::cpc::CpcWrapperToCpc>;

using CpcToSvsOverlay_t = pc::daddy::CDaddy<cc::cpc::CpcToSvsOverlay>;
using SvsOverlayToCpc_t = pc::daddy::CDaddy<cc::cpc::SvsOverlayToCpc>;

enum PlanViewEnlargeStatus
{
  NO_ENLARGE,
  ENLARGE_FRONT,
  ENLARGE_REAR,
  ENLARGE_MIDDLE
};

typedef pc::daddy::CDaddy<PlanViewEnlargeStatus> PlanViewEnlargeStatusDaddy;

enum SideViewEnableStatus : vfc::uint8_t
{
  SIDEVIEW_DISABLE,
  SIDEVIEW_LEFT_ENABLE,
  SIDEVIEW_RIGHT_ENABLE,
  SIDEVIEW_FRONT_ENABLE,
  SIDEVIEW_REAR_ENABLE,
  SIDEVIEW_LEFT_RIGHT_ENABLE,
  SIDEVIEW_LEFT_REAR_ENABLE,
  SIDEVIEW_RIGHT_REAR_ENABLE,
  SIDEVIEW_LEFT_RIGHT_REAR_ENABLE,
};

typedef pc::daddy::CDaddy<SideViewEnableStatus> SideViewEnableStatusDaddy;

enum TopViewEnableStatus : vfc::uint8_t
{
  TOPVIEW_DISABLE,
  TOPVIEW_FRONT_ENABLE,
  TOPVIEW_REAR_ENABLE,
  TOPVIEW_FRONTJUNCTION_ENABLE,
  TOPVIEW_REARJUNCTION_ENABLE,
  PARK_FRONT_VIEW,
  PARK_REAR_VIEW
};

enum WheelViewEnableStatus : vfc::uint8_t
{
  WHEELVIEW_DISABLE,
  WHEELVIEW_FRONT_ENABLE,
  WHEELVIEW_REAR_ENABLE,
};


typedef pc::daddy::CDaddy<TopViewEnableStatus> TopViewEnableStatusDaddy;

typedef pc::daddy::CDaddy<cc::target::common::EViewAnimation> EViewAnimationDaddy;

struct FreeModeAngle
{
  vfc::int32_t  m_Hori;
  vfc::int32_t  m_Vert;
};

typedef pc::daddy::CDaddy<FreeModeAngle> CamPosAxis2RqDaddy;

enum ComponentTestSwitch : vfc::uint8_t
{
  TEST_NONE,
  TEST_ON
};

struct FreeParkingCorners
{
    osg::Vec2f m_frontRight;
    osg::Vec2f m_frontLeft;
    osg::Vec2f m_rearLeft;
    osg::Vec2f m_rearRight;
};

typedef pc::daddy::CDaddy<ComponentTestSwitch> ComponentTestSwitchDaddy;

// Fusion object
typedef pc::daddy::CDaddy< cc::target::common::StrippedFusionObject_st>  FusionObjectDaddy_t;

// SitOcp
typedef pc::daddy::CDaddy< cc::target::common::StrippedSitOcp_st >  SitOcpDaddy_t;

// PedestrianObj
typedef pc::daddy::CDaddy< cc::target::common::SelectedPedestrianObj_st >  PedestrianDaddy_t;

typedef pc::daddy::CDaddy< cc::target::common::ParkhmiToSvs >  ParkhmiToSvs_t;
typedef pc::daddy::CDaddy< cc::target::common::SvsToParkhmi >  SvsToParkhmi_t;
typedef pc::daddy::CDaddy< cc::target::common::FreeParkingSlot >  FreeParkingSlot_t;
typedef pc::daddy::CDaddy< FreeParkingCorners >  FreeParkingCorners_t;
typedef pc::daddy::CDaddy< vfc::int16_t >  SlotSelectedId_t;
typedef pc::daddy::CDaddy< cc::target::common::EPocDirSel >  ParkoutSelectedDirection_t;
typedef pc::daddy::CDaddy< cc::target::common::AirSuspensionHeight >  AirSuspensionHeight_t;
typedef pc::daddy::CDaddy< cc::target::common::E3DZoomLevel >  ZoomLevel_t;
typedef pc::daddy::CDaddy< bool >  RemoveDistortion_t;
typedef pc::daddy::CDaddy< bool >  BirdEyeViewSwitch_t;
typedef pc::daddy::CDaddy< vfc::int32_t >  SurroundViewRotateAngle_t;
typedef pc::daddy::CDaddy< cc::target::common::StrippedCrabGuideline_st >  CrabGuideline_t;
typedef pc::daddy::CDaddy<bool > GoldenEmblem_t;

enum class ScaleEvent
{
    ScaleStart,
    ScaleProcessing,
    ScaleEnd
};
struct ZoomScaleSt
{
    vfc::float32_t m_scaleFactor;
    ScaleEvent m_scaleEvent;
};
typedef pc::daddy::CDaddy< ZoomScaleSt >  ZoomScale_t;

} //namespace daddy
} //namespace cc

#endif //CC_DADDYTYPES_H
