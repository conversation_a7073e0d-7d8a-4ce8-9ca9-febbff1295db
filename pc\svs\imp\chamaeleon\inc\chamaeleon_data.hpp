/// @copyright (C) 2023 Robert <PERSON>.
/// The reproduction, distribution and utilization of this file as well as the
/// communication of its contents to others without express authorization is
/// prohibited. Offenders will be held liable for the payment of damages. All
/// rights reserved in the event of the grant of a patent, utility model or
/// design.
/// @file

#ifndef PC_SVS_IMP_CHAMAELEONDATA_H
#define PC_SVS_IMP_CHAMAELEONDATA_H

#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/coding/inc/ISerializable.h"
#include "vfc/core/vfc_types.hpp"

namespace rbp
{
namespace vis
{
namespace imp
{
namespace chamaeleon
{

///
/// ChamaeleonBaseSignalsData
///
class ChamaeleonBaseSignalsData : public pc::util::coding::ISerializable
{
public:
    ChamaeleonBaseSignalsData();

    SERIALIZABLE(ChamaeleonBaseSignalsData)
    {
        ADD_MEMBER(bool, ignoreCarDoorState);
        ADD_MEMBER(bool, ignoreMirrorState);
        ADD_MEMBER(bool, ignoreDegradationState);
        ADD_MEMBER(bool, ignoreCalibState);
    }
    bool m_ignoreCarDoorState;
    bool m_ignoreMirrorState;
    bool m_ignoreDegradationState;
    bool m_ignoreCalibState;
};

///
/// ChamaeleonEstimatorData
///
class ChamaeleonEstimatorData : public pc::util::coding::ISerializable
{
public:
    ChamaeleonEstimatorData();

    SERIALIZABLE(ChamaeleonEstimatorData)
    {
        ADD_MEMBER(vfc::float32_t, rampFac);
        ADD_MEMBER(bool, useWbGainsAsFallback);
        ADD_MEMBER(vfc::float32_t, thresholdLowerDeltaL);
        ADD_MEMBER(vfc::float32_t, thresholdUpperDeltaL);
        ADD_MEMBER(vfc::float32_t, thresholdLowerDeltaAB);
        ADD_MEMBER(vfc::float32_t, thresholdUpperDeltaAB);
        ADD_MEMBER(vfc::float32_t, minOutputGain);
        ADD_MEMBER(vfc::float32_t, maxOutputGain);
    }

    /// Ramping of the color correction mapping. 0.0 for complete takeover of new image (time constant = 0). 1.0 for
    /// complete takeover of old image (time constant = infinity)
    vfc::float32_t m_rampFac{0.8F};
    bool           m_useWbGainsAsFallback{false};
    vfc::float32_t m_thresholdLowerDeltaL{25.F};
    vfc::float32_t m_thresholdUpperDeltaL{26.F};
    vfc::float32_t m_thresholdLowerDeltaAB{3.F};
    vfc::float32_t m_thresholdUpperDeltaAB{4.F};
    vfc::float32_t m_minOutputGain{0.6F};
    vfc::float32_t m_maxOutputGain{1.4F};
};

///
/// ChamaeleonWbGainsData
///
class ChamaeleonWbGainsData : public pc::util::coding::ISerializable
{
public:
    ChamaeleonWbGainsData();

    SERIALIZABLE(ChamaeleonWbGainsData)
    {
        ADD_MEMBER(vfc::float32_t, minGain);
        ADD_MEMBER(vfc::float32_t, maxGain);
        ADD_MEMBER(vfc::float32_t, exponent);
    }

    vfc::float32_t m_minGain{0.4F};
    vfc::float32_t m_maxGain{1.6F};
    vfc::float32_t m_exponent{1.F};
};

///
/// ChamaeleonRoisData
///
class ChamaeleonRoisData : public pc::util::coding::ISerializable
{
public:
    ChamaeleonRoisData();

    SERIALIZABLE(ChamaeleonRoisData)
    {
        ADD_MEMBER(bool, useFilterRois);
        ADD_MEMBER(vfc::float32_t, filterDeltaE);
        ADD_MEMBER(vfc::float32_t, stitchingLineFactor);
        ADD_MEMBER(vfc::float32_t, roiSizeWidth);
        ADD_MEMBER(vfc::float32_t, roiSizeHight);
        ADD_MEMBER(bool, useOrthoLine);
    }
    bool           m_useFilterRois{false};
    vfc::float32_t m_filterDeltaE{15.F};
    /// position of the roi on the stitching line, relative to the root of the stitching line.
    vfc::float32_t m_stitchingLineFactor{1.6F};
    /// roi width in openGl coordinates
    vfc::float32_t m_roiSizeWidth{1.8F};
    /// roi height in openGl coordinates
    vfc::float32_t m_roiSizeHight{1.8F};
    bool           m_useOrthoLine{false};
};

///
/// ChamaeleonVisuViewport
///
class ChamaeleonVisuViewport : public pc::util::coding::ISerializable
{
public:
    ChamaeleonVisuViewport();

    ChamaeleonVisuViewport(
        vfc::int32_t f_visuViewportX,
        vfc::int32_t f_visuViewportY,
        vfc::int32_t f_visuViewportWidth,
        vfc::int32_t f_visuViewportHeight);

    SERIALIZABLE(ChamaeleonVisuViewport)
    {
        ADD_MEMBER(vfc::int32_t, visuViewportX);
        ADD_MEMBER(vfc::int32_t, visuViewportY);
        ADD_MEMBER(vfc::int32_t, visuViewportWidth);
        ADD_MEMBER(vfc::int32_t, visuViewportHeight);
    }

    vfc::int32_t m_visuViewportX{0};
    vfc::int32_t m_visuViewportY{400};
    vfc::int32_t m_visuViewportWidth{150};
    vfc::int32_t m_visuViewportHeight{150};
};

///
/// ChamaeleonVisuRoiData
///
class ChamaeleonVisuRoiData : public pc::util::coding::ISerializable
{
public:
    ChamaeleonVisuRoiData();

    SERIALIZABLE(ChamaeleonVisuRoiData)
    {
        ADD_MEMBER(bool, enable);
        ADD_MEMBER(ChamaeleonVisuViewport, viewport);
    }

    bool                   m_enable{false};
    ChamaeleonVisuViewport m_viewport{0, 400, 150, 150};
};

///
/// ChamaeleonVisuData
///
class ChamaeleonVisuData : public pc::util::coding::ISerializable
{
public:
    ChamaeleonVisuData() = default;

    SERIALIZABLE(ChamaeleonVisuData)
    {
        ADD_MEMBER(ChamaeleonVisuRoiData, visuRoiData);
    }

    ChamaeleonVisuRoiData m_visuRoiData{};
};

///
/// ChamaeleonData
///
class ChamaeleonData : public pc::util::coding::ISerializable
{
public:
    ChamaeleonData();

    void setEnable(bool f_enable);

    SERIALIZABLE(ChamaeleonData)
    {
        ADD_MEMBER(bool, enableChamaeleon);
        ADD_MEMBER(bool, enableSideBySide);
        ADD_MEMBER(ChamaeleonBaseSignalsData, baseSignalData);
        ADD_MEMBER(ChamaeleonRoisData, roisData);
        ADD_MEMBER(ChamaeleonWbGainsData, wbGains);
        ADD_MEMBER(ChamaeleonEstimatorData, estimatorData);
        ADD_MEMBER(ChamaeleonVisuData, visu);
    }

    bool                    m_enableChamaeleon{false};
    bool                    m_enableSideBySide{false};
    ChamaeleonBaseSignalsData m_baseSignalData;
    ChamaeleonRoisData      m_roisData{};
    ChamaeleonWbGainsData   m_wbGains{};
    ChamaeleonEstimatorData m_estimatorData{};
    ChamaeleonVisuData      m_visu{};
};

extern pc::util::coding::Item<ChamaeleonData> g_settings;

} // namespace chamaeleon
} // namespace imp
} // namespace vis
} // namespace rbp

#endif // PC_SVS_IMP_CHAMAELEONDATA_H
